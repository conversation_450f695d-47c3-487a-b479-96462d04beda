/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.base;

/**
 * Exception when creating a manual decision
 */
public class ManualDecisionException extends IfsException {

   /**
    * Create a new instance
    * @param msg error message
    * @param p1  placeholder texts
    * @since 4.1.0
    */
   public ManualDecisionException(final FndTranslatableText msg, final String... p1) {
      super(MANUAL_DECISION, msg, p1);
   }

   //
   // To be obsolete
   //

   /**
    * Create a new instance
    * @param msg error message
    * @param p1  placeholder texts
    */
   public ManualDecisionException(final String msg, final String... p1) {
      super(MANUAL_DECISION, msg, p1);
   }
}