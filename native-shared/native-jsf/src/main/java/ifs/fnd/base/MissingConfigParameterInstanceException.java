/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIA<PERSON>LITY TO PAY DAMAGES.
 */

package ifs.fnd.base;

/**
 * Class for denoting missing configuration parameter instance.
 * Used to find out if stand-alone server configuration is present or not.
 */
public class MissingConfigParameterInstanceException extends ApplicationException
{
   /**
    * Construct the exception with a message
    */
   public MissingConfigParameterInstanceException( String message )
   {
      super(message);
   }
}

