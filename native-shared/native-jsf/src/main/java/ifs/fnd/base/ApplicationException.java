/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.base;

/**
 * Exception to raise when anything goes wrong within the application code.
 * Error message should be sent in the constructor. You can have have
 * up to 5 placeholders to send arguments to the error message.
 * Indicate the placeholders with &amp;number in the error message.
 * Example:
 * <pre>
 *    throw new ApplicationException("ERRORONE:Customer &1 is invalid.", name);
 * </pre>
 * You can also indicate what caused this exception by adding the causing exception.
 * Example:
 * <pre>
 *    catch (IOException e) {
 *       throw new ApplicationException(e, "ERRORONE:Customer &1 is invalid.", name);
 *    }
 * </pre>
 * ApplicationException (and it's subclasses) are the only IfsExceptions application
 * code can catch.
 */
public class ApplicationException extends IfsException {

   /**
    * Create a new instance
    * @param msg error message
    * @param p1  placeholder texts
    * @since 4.1.0
    */
   public ApplicationException(final FndTranslatableText msg, final String... p1) {
      super(APPLICATION_ERROR, msg, p1);
   }

   /**
    * Create an instance and add the exception causing this exception
    * @param cause the causing exception
    * @param msg error message
    * @param p1  placeholder texts
    * @since 4.1.0
    */
   public ApplicationException(final Throwable cause, final FndTranslatableText msg, final String... p1) {
      super(cause, APPLICATION_ERROR, msg, p1);
   }

   /**
    * Create a new instance
    * @param msg error message
    * @param p1 placeholder texts
    */
   public ApplicationException(final String msg, final String... p1) {
      super(APPLICATION_ERROR, msg, p1);
   }

   /**
    * Create an instance and add the exception causing this exception
    * @param cause the causing exception
    * @param msg error message
    * @param p1 placeholder texts
    */
   public ApplicationException(final Throwable cause, final String msg, final String... p1) {
      super(cause, APPLICATION_ERROR, msg, p1);
   }
}