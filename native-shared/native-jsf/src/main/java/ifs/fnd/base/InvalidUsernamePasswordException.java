/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.base;

/**
 * Exception thrown when an invalid username or password was specified in an
 * attempt to log on to the database.
 */
public class InvalidUsernamePasswordException extends SystemException {


   /**
    * Create a new instance
    * @param cause exception causing this exception to be thrown
    * @param p1 First placeholder text
    * @since 4.1.0
    */
   public InvalidUsernamePasswordException(final Throwable cause, final FndTranslatableText msg, final String p1) {
      super(cause, msg, p1);
   }


   //
   // To be obsolete
   //

   /**
    * Create a new instance
    * @param cause exception causing this exception to be thrown
    * @param msg error message
    */
   public InvalidUsernamePasswordException(final Throwable cause, final String msg, final String p1) {
      super(cause, msg, p1);
   }
}