/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.base;

/**
 * This class contains translatable texts used by the framework.
 */
final class Texts {
   
   private static final String PKG = Texts.class.getPackage().getName();
   
   private Texts() {
      //Prevent instantiations
   }
   
   //FndContext
   static final FndTranslatableText BADSERIALMODE = new FndTranslatableText("BADSERIALMODE", "Invalid value [&1] for serialization mode", PKG);
   static final FndTranslatableText INVALIDRFC3066 = new FndTranslatableText("INVALIDRFC3066", "'&1' is not a valid 2-letter RFC 3066 language/country code", PKG);
   static final FndTranslatableText INVALIDISO639 = new FndTranslatableText("INVALIDISO639", "'&1' is not a valid 2-letter ISO 639 language code", PKG);
   static final FndTranslatableText INVALIDISO3166 = new FndTranslatableText("INVALIDISO3166", "'&1' is not a valid 2-letter ISO 3166 country code", PKG);
   static final FndTranslatableText DSCHANGED2DEF = new FndTranslatableText("DSCHANGED2DEF","Client has requested to run in invalid locale '&1'.\n English, 'en-US' will be used instead. \n Correct your Locale Setting." , PKG);
   
   //FndDebug
   static final FndTranslatableText ENCRYPTERROR = new FndTranslatableText("ENCRYPTERROR", "Error when encrypting attribute (&1) in record (&2): &3", PKG);
   static final FndTranslatableText SIMPLEARRAYCONVERSION = new FndTranslatableText("SIMPLEARRAYCONVERSION", "Error during conversion of SimpleArray: &1", PKG);
   
}
