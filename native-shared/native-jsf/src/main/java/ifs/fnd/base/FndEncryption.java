/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.base;

import ifs.fnd.service.IfsEncryption;
import java.io.IOException;

/**
 * Encryption and decryption routines.
 */
public final class FndEncryption {

   /**
    * Creates a new instance of FndEncryption. Private to disable instantiation.
    */
   private FndEncryption() {
   }

   /**
    * Decrypts a string previously encrypted with
    * {@link #encrypt(java.lang.String) encrypt(String}.
    * @param   cipherText  the encrypted text to decrypt.
    * @return  a <code>String</code> with the decrypted text. This must be
    * Base64 encoded data.
    * @throws  UnsupportedEncodingException  if the current Java platform does
    * not support UTF-8 character encoding.
    * @throws  IOException if there is a problem with decoding the Base64
    * data.
    * @throws  EncryptionException  if there is a problem decrypting the text.
    * With this exception there is a parent exception available (via
    * <code>getCause()</code>) that is the real problem that occurred.
    */
   public static String decrypt(String cipherText) throws IOException, EncryptionException {
      try {
         return IfsEncryption.decrypt(cipherText);
      }
      catch(IfsEncryption.EncryptionException ex) {
         throw new EncryptionException(ex.getCause(), ex.getMessage());
      }
   }

   /**
    * Decrypts data previously encrypted with
    * {@link #encrypt(byte[]) encrypt(byte[]}.
    * @param   cipherText  the encrypted data to decrypt.
    * @return  a <code>byte</code>-array with the decrypted data.
    * @throws  EncryptionException  if there is a problem decrypting the data.
    * With this exception there is a parent exception available (via
    * <code>getCause()</code>) that is the real problem that occurred.
    */
   public static byte[] decrypt(byte[] cipherText) throws EncryptionException {
      try {
         return IfsEncryption.decrypt(cipherText);
      }
      catch(IfsEncryption.EncryptionException ex) {
         throw new EncryptionException(ex.getCause(), ex.getMessage());
      }
   }

   /**
    * Encrypts a <code>String</code> value.
    * @param   clearText   the string to encrypt.
    * @return  the encrypted string.
    * @throws  UnsupportedEncodingException  if the current Java platform does
    * not support UTF-8 character encoding.
    * @throws  IOException if there is a problem with decoding the Base64
    * data.
    * @throws  EncryptionException  if there is a problem decrypting the text.
    * With this exception there is a parent exception available (via
    * <code>getCause()</code>) that is the real problem that occurred.
    * @see  #decrypt(java.lang.String)
    */
   public static String encrypt(String clearText) throws IOException, EncryptionException {
      try {
         return IfsEncryption.encrypt(clearText);
      }
      catch(IfsEncryption.EncryptionException ex) {
         throw new EncryptionException(ex.getCause(), ex.getMessage());
      }
   }

   /**
    * Encrypts data.
    * @param   clearText   the data to encrypt.
    * @return  the encrypted data.
    * @throws  EncryptionException  if there is a problem decrypting the text.
    * With this exception there is a parent exception available (via
    * <code>getCause()</code>) that is the real problem that occurred.
    * @see  #decrypt(byte[])
    */
   public static byte[] encrypt(byte[] clearText) throws EncryptionException {
      try {
         return IfsEncryption.encrypt(clearText);
      }
      catch(IfsEncryption.EncryptionException ex) {
         throw new EncryptionException(ex.getCause(), ex.getMessage());
      }
   }


   @SuppressWarnings("PMD")
   public static void main(String[] args) throws EncryptionException, IOException {
      // utility for encrypting string values. Not really official, hence no javadoc.
      for(int i=0; i<args.length; i++) {
         System.out.println(encrypt(args[0]));
      }
   }
}
