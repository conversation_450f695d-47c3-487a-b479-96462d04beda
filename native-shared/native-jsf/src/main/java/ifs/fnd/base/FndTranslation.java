/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.base;

import ifs.fnd.util.Str;

/** Support for language independent messages. A message should be build as:
 * UNIQUEIDENTIFIER: Message to be sent
 * A message may contain parameter (designated by <code>&amp;&lt;number&gt;</code>).
 * A message constructed can be sent to the translate method in order
 * to translate it to other languages.
 * This function is only a stub for the moment, lacking implementation.
 */
public final class FndTranslation {

   /**
    * Private constructor to disable instantiation.
    */
   private FndTranslation() {}

   /**
    * Get text id of a message (the unique identifier).
    * Used internally by the framework.
    * @param   text  the text to get the id from.
    * @return  the unique identifier for the text (if one exists).
    */
   public static String getTextId(String text) {
      if(text==null)
         return "NOKEY";

      int pos  = text.indexOf(": ");
      int pos2 = text.indexOf(' '); //spaces not allowed in text key

      if(pos < 0)
         pos = text.indexOf(':');

      if((pos < 1) || (pos2 < pos))
         return "NOKEY";
      else
         return text.substring(0, pos);
   }

   /**
    * Translate a message and replace parameters, if there are any.
    * @param   text  the text to translate.
    * @param   p1    the parameter values.
    * @return  the translated message with parameters replaced with their values.
    */
   public static String translate(String text, String... p1) {

      Object[] params = p1==null ? null : new Object[p1.length];
      if(p1!=null)
         System.arraycopy(p1, 0, params, 0, p1.length);
      return translateMessage(text, params);
   }

   /**
    * Translates the message and replaces the parameters.
    */
   private static String translateMessage(String text, Object... params) {
      if(text==null)
         return null;
      int pos  = text.indexOf(": ");
      int pos2 = text.indexOf(' '); //spaces not allowed in text key

      if(pos < 0)
         pos = text.indexOf(':');

      if((pos < 0) || (pos2 < pos) || text.startsWith("ORA-") || text.startsWith("[")) //datadirect messages start with a '['
         return Str.formatMessage(text, true, params);
      else {
         if(text.charAt(pos+1) == ' ')
            pos++;
         return Str.formatMessage(text.substring(pos+1), true, params);
      }
   }
}