/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.storage;

import ifs.fnd.record.FndRecordMeta;
import java.lang.reflect.Field;
import java.util.HashMap;

/**
 *
 */
public final class FndFilterEnabledEntities {

   private final HashMap<String, FndRecordMeta> mappings;

   public FndFilterEnabledEntities() {
      mappings = new HashMap<>(4);
   }

   /**
    * adds a new meta entry to the list. If already exists nothing is done
    * @param meta
    */
   public void addEntry(FndRecordMeta meta) {
      String entityName = meta.getEntity();
      if (!mappings.containsKey(entityName)) {
         mappings.put(entityName, meta);
      }
   }

   /**
    * @param entityName
    * @return the meta instance for the entityName
    * @throws ClassNotFoundException
    * @throws NoSuchFieldException
    * @throws SecurityException
    * @throws IllegalArgumentException
    * @throws IllegalAccessException
    */
   public FndRecordMeta getEntry(String entityName) throws ClassNotFoundException, NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
      FndRecordMeta meta = mappings.get(entityName);
      if (meta == null) {
         String entityClassName = "ifs.entity." + entityName.toLowerCase() + "." + entityName + "Entity";
         Class<?> entityClass = Class.forName(entityClassName);
         Field viewMetaField = entityClass.getField("viewMeta");
         meta = (FndRecordMeta) viewMetaField.get(null);

         addEntry(meta);
      }
      return meta;
   }
}
