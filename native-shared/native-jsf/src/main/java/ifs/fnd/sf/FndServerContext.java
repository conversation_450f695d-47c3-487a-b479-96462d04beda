/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf;

import ifs.fnd.base.FndConstants;
import ifs.fnd.base.FndContext;
import ifs.fnd.base.IfsException;
import ifs.fnd.base.ParseException;
import ifs.fnd.base.SecurityException;
import ifs.fnd.base.SystemException;
import ifs.fnd.base.capability.ApplicationCapability;
import ifs.fnd.capability.CapabilitySet;
import ifs.fnd.entities.fnduser.FndUser;
import ifs.fnd.entities.fnduser.FndUserCache;
import ifs.fnd.entities.languagecode.LanguageCode;
import ifs.fnd.entities.languagecode.LanguageCodeArray;
import ifs.fnd.entities.languagecode.LanguageCodeHandler;
import ifs.fnd.entities.languagecode.LanguageCodeHandlerFactory;
import ifs.fnd.internal.FndAttributeInternals;
import ifs.fnd.internal.FndRecordInternals;
import ifs.fnd.log.LogMgr;
import ifs.fnd.log.Logger;
import ifs.fnd.record.FndAttribute;
import ifs.fnd.record.FndQueryRecord;
import ifs.fnd.record.FndRecord;
import ifs.fnd.record.FndRecordMeta;
import ifs.fnd.record.FndSqlType;
import ifs.fnd.record.FndSqlValue;
import ifs.fnd.record.FndStreamManager;
import ifs.fnd.remote.FndRemoteContext;
import ifs.fnd.service.Util;
import ifs.fnd.services.plsqlserver.service.SecuritySupervisor;
import ifs.fnd.sf.storage.FndConnection;
import ifs.fnd.sf.storage.FndConnectionList;
import ifs.fnd.sf.storage.FndConnectionManager;
import ifs.fnd.sf.storage.FndFilterEnabledEntities;
import ifs.fnd.sf.storage.FndFilterMap;
import ifs.fnd.sf.storage.FndSqlStorage.Context;
import ifs.fnd.sf.storage.FndStatement;
import ifs.fnd.sf.storage.FndStorage;
import ifs.fnd.sf.storage.FndTransaction;
import ifs.fnd.sf.storage.FndValidationMap;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.sql.Connection;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.UUID;

/**
 * Server-side extension of FndContext.
 * Represents the context that is sent with every request to the server.
 * The context is thread local. "Current context"
 * refers to the context in the current thread. All methods that start with
 * getCurrent- or setCurrent- are static (can be called using
 * FndContext.getCurrentSomething) and operate on current context.
 * <P>
 * Most methods in this class are framework-internal. The following non-internal
 * public methods are supposed to be used by application code:
 * <DL>
 * <DT>
 * <DD>{@link #getCurrentServerContext}</DD>
 * <DD>{@link #getFndUserView}</DD>
 * <DD>{@link #getCurrentFndUserView}</DD>
 * </DT>
 * </DL>
 */
public abstract class FndServerContext extends FndRemoteContext {

   /**
    * The connection manager valid for this context.
    */
   private transient FndConnectionManager connectionMgr;

   /**
    * Execution context of a standard storage operation.
    */
   private transient Context storageContext;

   /**
    * Map with active FndFilters per meta-view
    */
   private transient FndFilterMap filterMap;

   /**
    * Map with active FndValidations per view
    */
   private transient FndValidationMap validationMap;

   /**
    * The list with instances of FndStreamManager for the current transaction.
    */
   private transient List<FndStreamManager> streamManagerList;

   /**
    * The list with database connections opened during current request.
    */
   private transient FndConnectionList connectionList;

   /**
    * Table mapping the IFS specific- and RFC 3066 language/country-codes. The
    * arrays first dimension denotes a language mapping. Index <code>0</code>
    * in the second dimension is the IFS specific code. Index <code>1</code>
    * in the second dimension is the RFC 3066 code.
    */
   private static String[][] languageTable;

   /**
    * Transient set with not-installed database objects. Used by storage logic
    * to skip repeated warnings printed to log output.
    */
   private transient Set<FndRecordMeta> notInstalledObjects;

   /**
    * Flag indicating if currently executed code is inside an activity.
    */
   private transient boolean insideActivity;

   /**
    * Flag indicating if method security in FNDBAS is enabled.
    * The flag is true by default.
    * It may be set to false (security disabled) by calling a protected method in this class.
    * It may be set to true (security enabled) by calling a public method in this class.
    * @see #enableBaseServerMethodSecurity()
    * @see #setBaseServerMethodSecurity(boolean)
    */
   private transient boolean baseServerMethodSecurityEnabled = true;

   /**
    * License overview snapshot stored by the license validation procedure.
    */
   private transient Object licenseSnapshot;

   /**
    * Encoding capabilities for this request.
    */
   private final transient CapabilitySet<ApplicationCapability> supportedCapabilities =
           CapabilitySet.noneOf(ApplicationCapability.class);

   /**
    * Number of logical calls to Login_SYS.Init_Fnd_Session_ during current request.
    */
   private transient int initFndSessionCount;

   /**
    * Number of database calls to Login_SYS.Init_Fnd_Session_ during current request.
    */
   private transient int initFndSessionDbCount;

   /**
    * Record with details of the last PLSQL error.
    */
   private transient FndRecord plsqlErrorDetails;

   private transient FndFilterEnabledEntities recordMetaOfEntityCache;

   /**
    * Flag indicating if currently executed code is inside FndSecurityCheckpoint.validateReauthenticationResponse().
    */
   private transient boolean validatingReauthenticationResponse;

   protected FndServerContext() {
      Logger log = getClassLogger();
      if(log.debug) {
         log.debug("New FndServerContext created (&1)", this.hashCode());
      }
   }

   @Override
   protected void debugToRecord(FndRecord rec) {
      super.debugToRecord(rec);
      FndRecord ctx = new FndRecord("FND_SERVER_CONTEXT");
      addToDebugRecord(connectionMgr                     , "CONNECTION_MGR"                      , ctx);
      addToDebugRecord(storageContext                    , "STORAGE_CONTEXT"                     , ctx);
      addToDebugRecord(filterMap                         , "FILTER_MAP"                          , ctx);
      addToDebugRecord(validationMap                     , "VALIDATION_MAP"                      , ctx);
      addToDebugRecord(streamManagerList                 , "STREAM_MANAGER_LIST"                 , ctx);
      addToDebugRecord(connectionList                    , "CONNECTION_LIST"                     , ctx);
      addToDebugRecord(notInstalledObjects               , "NOT_INSTALLED_OBJECTS"               , ctx);
      addToDebugRecord(insideActivity                    , "INSIDE_ACTIVITY"                     , ctx);
      addToDebugRecord(baseServerMethodSecurityEnabled   , "BASE_SERVER_METHOD_SECURITY_ENABLED" , ctx);
      addToDebugRecord(supportedCapabilities             , "SUPPORTED_CAPABILITIES"              , ctx);
      addToDebugRecord(initFndSessionCount               , "INIT_FND_SESSION_COUNT"              , ctx);
      addToDebugRecord(initFndSessionDbCount             , "INIT_FND_SESSION_DB_COUNT"           , ctx);
      addToDebugRecord(validatingReauthenticationResponse, "VALIDATING_REAUTHENTICATION_RESPONSE", ctx);
      addToDebugRecord(recordMetaOfEntityCache           , "RECORD_META_OF_ENTITY_CACHE"         , ctx);
      addToDebugRecord(plsqlErrorDetails, ctx);
      addToDebugRecord(ctx, rec);
   }

   /**
    * Serialize the state of this object to a stream.
    * This class has only transient instance variable (no state to serialize),
    * so the formal parameter is ignored.
    */
   @SuppressWarnings("PMD.UnusedFormalParameter")
   private void writeObject(ObjectOutputStream out) throws IOException {
      Logger log = LogMgr.getClassLogger(java.io.Serializable.class);
      if (storageContext != null || (connectionList!=null && connectionList.size() > 0) ) {
         throw new IOException("Serialization of FndServerContext not allowed because of opened database connections");
      }
      if(log.debug) {
         log.debug("Server context serialized");
      }
   }

   /**
    * De-serialize the state of this object from a stream.
    * This class has only transient instance variable (no state to de-serialize),
    * so the formal parameter is ignored.
    */
   @SuppressWarnings("PMD.UnusedFormalParameter")
   private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
      Logger log = LogMgr.getClassLogger(java.io.Serializable.class);
      if(log.debug) {
         log.debug("Server context deserialized");
      }
   }

   /**
    * Returns the current context. The context is thread local.
    *
    * @return Context
    */
   public static FndServerContext getCurrentServerContext() {
      return (FndServerContext) FndContext.getCurrentContext();
   }

   public void setConnectionManager(FndConnectionManager mgr) {
      connectionMgr = mgr;
   }

   /**
    * <B>Framework internal method:</B> Returns a connection manager valid for this context.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    * @return
    */
   public FndConnectionManager getConnectionManager() {
      if(connectionMgr == null) {
         connectionMgr = FndServerFramework.getServerFramework().newConnectionManager();
      }
      return connectionMgr;
   }

   /**
    * <B>Framework internal method:</B> Returns a list with database connections opened during current request.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    * @return
    */
   public FndConnectionList getConnectionList() {
      if (connectionList == null) {
         connectionList = new FndConnectionList();
      }
      return connectionList;
   }


   public FndFilterEnabledEntities getFilterEnabledEntities() {
      if (this.recordMetaOfEntityCache == null) {
		   this.recordMetaOfEntityCache = new FndFilterEnabledEntities();
	   }
	   return this.recordMetaOfEntityCache;
   }

   /**
    * <B>Framework internal method:</B> Returns the map with currently active FndFilters per meta-view.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    * @return
    */
   public FndFilterMap getFilterMap() {
      if (filterMap == null) {
         filterMap = new FndFilterMap();
      }
      return filterMap;
   }

   /**
    * <B>Framework internal method:</B> Returns the map with currently active FndValidations per view.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    * @return
    */
   public FndValidationMap getValidationMap() {
      if (validationMap == null) {
         validationMap = new FndValidationMap();
      }
      return validationMap;
   }

   /**
    * <B>Framework internal method:</B> Returns an implementation of FndStorage that should be used instead of
    * FndSqlStorage. This method returns null, which means that standard storage
    * code should be called. The method is overridden by J2EE framework to call a
    * standard storage bean.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    *
    * @return an implementation of FndStorage interface, or null
    * @throws ifs.fnd.base.SystemException
    */
   public FndStorage getStandardStorageWrapper() throws SystemException {
      return null;
   }

   /**
    * <B>Framework internal method:</B> Returns the storage context assigned to the current server context.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    * @return
    */
   public Context getStorageContext() {
      return storageContext;
   }

   /**
    * <B>Framework internal method:</B> Assign a storage context to the current server context.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    * @param context
    */
   public void setStorageContext(Context context) {
      storageContext = context;
   }

   /**
    * Returns a whole FndUser view for the context's foundation user.
    * @return FndUser view
    * @throws ifs.fnd.base.SystemException
    */
   public FndUser getFndUserView() throws SystemException {
      return ifs.fnd.entities.fnduser.FndUserCache.getFndUser(this.directoryId);
   }

   /**
    * Gets FndUser view for the current context.
    * @return FndUser view
    * @throws ifs.fnd.base.SystemException
    */
   public static FndUser getCurrentFndUserView() throws SystemException {
      FndServerContext ctx = (FndServerContext) getCurrentContext();
      return ctx.getFndUserView();
   }

   /**
    * Gets the identity of the context's foundation user.
    * @return FND user identity
    * @throws ifs.fnd.base.SystemException
    */
   public String getFndUserIdentity() throws SystemException {
      return ifs.fnd.entities.fnduser.FndUserCache.getFndUserIdentity(this.directoryId);
   }

   /**
    * Gets the identity of the current foundation user.
    * @return identity of the current FND user
    * @throws ifs.fnd.base.SystemException
    */
   public static String getCurrentFndUserIdentity() throws SystemException {
      FndServerContext ctx = (FndServerContext) getCurrentContext();
      return ctx.getFndUserIdentity();
   }

   // Locale-related stuff
   /**
    * <B>Framework internal method:</B> Sets the language and the locale for the current context.
    * The method maps IFS specific language codes to RFC 3066 language/country codes.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    *
    * @param language
    *           IFS specific language code
    * @throws ParseException
    *            if the language cannot be mapped to a valid ISO 639 language code
    */
   public void setLanguageAndLocale(String language) throws ParseException {
      setIfsLanguage(language);
      if(locale != null) {
         locale.setNull();
      }
      validateLocaleAndLanguage();
   }

   /**
    * <B>Framework internal method:</B> Sets the locale for the current context.
    * The method maps IFS specific language codes to RFC 3066 language/country codes.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    * @param loc
    * @throws ParseException
    */
   public void setLocaleAndValidate(Locale loc) throws ParseException {
      setLocale(loc);
      validateLocaleAndLanguage();
   }
   /**
    * <B>Framework internal method:</B> Checks that the locale & language set are valid.
    * <P>
    * In addition to the super-class version of this method, this
    * implementation also maps between IFS specific language codes and RFC 3066
    * language/country codes.
    * </P>.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    * @throws ifs.fnd.base.ParseException
    */
   @Override
   protected void validateLocaleAndLanguage() throws ParseException {
      super.validateLocaleAndLanguage(); // this validates and sets the
      // javaLocale reference.

      // Check if the language code mappings are loaded
      if (languageTable == null) {
         try {
            // Fetch code mappings using the default language-locale pair,
            // other valid pairs are not known at this point
            FndAttribute currLanguage = null;
            FndAttribute currLocale = null;
            try {
               if(language != null) {
                  currLanguage = (FndAttribute) language.clone();
               }
               if(locale != null) {
                  currLocale = (FndAttribute) locale.clone();
               }
            }
            catch (CloneNotSupportedException e) {
               assert false; // cannot happen
            }
            setCurrentLanguage(FndConstants.defaultLanguage);
            setCurrentLocale(FndConstants.defaultLocale);
            loadLanguageCodeMappings();
            language = currLanguage;
            locale = currLocale;
         }
         catch (IfsException ex) {
            throw new ParseException(ex, "Error loading language code mappings: &1", ex.getMessage());
         }
      }

      // If locale is set, find the corresponding IFS language and set it.
      // If the IFS specific language code is set, find the RFC 3066 locale
      // and set it. If both are set, locale takes precedence.

      if (locale != null && locale.hasValue()) {
         String rfc3066Code = FndAttributeInternals.internalGetValue(locale).toString();
         if (rfc3066Code.length() > 0) {
            String ifsLangCode = findIfsLangCode(rfc3066Code);
            if (ifsLangCode == null) {
                 ifsLangCode=changeLocaleToDefault(rfc3066Code).getLanguage();
            }
            setIfsLanguage(ifsLangCode);
         }
      }
      else if (language != null && language.hasValue()) {
         String ifsLangCode = FndAttributeInternals.internalGetValue(language).toString();
         if (ifsLangCode.length() > 0) {
            String rfc3066Code = findRfc3066Code(ifsLangCode);
            if (rfc3066Code == null) {
               Locale newLocale=changeLocaleToDefault(ifsLangCode);
               rfc3066Code=newLocale.getLanguage()+"_"+newLocale.getCountry();
            }
            try{
               setLocale(parseRfc3066String(rfc3066Code));
            }
            catch(ParseException e) {
               setLocale(changeLocaleToDefault(rfc3066Code));
            }
         }
      }
   }

   /**
    * <B>Framework internal method:</B> Utility method to find a RFC3066 language/country code for a given IFS
    * specific language code.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    *
    * @param ifsLangCode
    *           the IFS specific language code to find the RFC 3066 code for.
    * @return the correct RFC 3066 code or <code>null</code> if an invalid IFS
    *         language code was given.
    */
   protected static String findRfc3066Code(String ifsLangCode) {
      if (languageTable == null) {
         return null;
      }
      for(String[] languageTable1 : languageTable) {
         if(ifsLangCode.equalsIgnoreCase(languageTable1[0])) {
            return languageTable1[1];
         }
      }
      return null;
   }

   /**
    * <B>Framework internal method:</B> Utility method to find a IFS specific language code for a given RFC3066
    * language/country code.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    *
    * @param rfc3066Code
    *           the RFC 3066 code to find the IFS specific language code for.
    * @return the correct IFS language code or <code>null</code> if an invalid
    *         RFC 3066 code was given.
    */
   protected static String findIfsLangCode(String rfc3066Code) {
      if (languageTable == null) {
         return null;
      }
      for(String[] languageTable1 : languageTable) {
         if (rfc3066Code.equalsIgnoreCase(languageTable1[1])) {
            return languageTable1[0];
         }
      }
      return null;
   }

   /**
    * <B>Framework internal method:</B> Fills the <code>languageTable</code> array with IFS to RFC 3066
    * language/' country code mappings.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    */
   private static void loadLanguageCodeMappings() throws IfsException {
      Logger log = LogMgr.getFrameworkLogger();
      if (log.debug) {
         log.debug("Loading language code mappings");
      }

      LanguageCode languageCode = new LanguageCode();
      languageCode.excludeQueryResults();
      languageCode.langCode.include();
      languageCode.langCodeRfc3066.include();
      LanguageCodeHandler handler = LanguageCodeHandlerFactory.getHandler();
      LanguageCodeArray result = handler.query(new FndQueryRecord(languageCode));

      languageTable = new String[result.size()][2];
      for (int i = 0; i < result.size(); i++) {
         languageCode = result.get(i);
         languageTable[i][0] = languageCode.langCode.getValue();
         languageTable[i][1] = languageCode.langCodeRfc3066.getValue();
      }
      if (log.debug) {
         log.debug("Found &1 language code mappings", Integer.toString(languageTable.length));
      }
   }


   private static final String STMT_LANGUAGE_SYS_LOOKUP  =
      "BEGIN ? := LANGUAGE_SYS.LOOKUP (?, ?, ?, ?, ?); END;";


   private ThreadLocal isCurrentlyTranslating;

   /**
    * <B>Framework internal method:</B> Utility method to find the translated text for
    * given text.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    *
    * @param   path  Unique path for translation.
    * @param   text  the text message to translate.
    * @return  the translated text message (or original text message to translate if not found).
    */
   @Override
   protected String getTranslatedText(String path, String text) {
      if (isCurrentlyTranslating==null) {
         Logger log = LogMgr.getFrameworkLogger();
         try {
            isCurrentlyTranslating = new ThreadLocal() {
               @Override
               protected synchronized Object initialValue() {
                  return Boolean.TRUE;
               }
            };

            FndConnection c = null;
            FndStatement stmt = null;
            try {
               c = getConnectionManager().getPlsqlConnection();
               stmt = c.createStatement();
               stmt.setRecursive();
               stmt.defineOutParameter("F_RESULT", FndSqlType.TEXT);
               stmt.defineParameter(new FndSqlValue("TYPE_", "Message"));
               stmt.defineParameter(new FndSqlValue("PATH_", path));
               stmt.defineParameter(new FndSqlValue("ATTRIBUTE_", "Text"));
               stmt.defineParameter(new FndSqlValue("LANG_CODE_", getCurrentLanguage()));
               stmt.defineParameter(new FndSqlValue("MAIN_TYPE_", "JAVA"));
               stmt.prepareCall(STMT_LANGUAGE_SYS_LOOKUP);
               /**
                * Turn off PLSQL trace to enable translation using an uninitialized database connection.
                * Client_SYS.Get_Trace, used for spooling the trace, is a security protected PL/SQL method.
                */
               stmt.setSpoolPlsqlTrace(false);
               stmt.execute(false);
               String translatedText = stmt.getString(1);

               if(log.debug) {
                  log.debug("FndTranslatableText: getTranslatedText() Result:");
                  log.debug("  Path          : &1", path);
                  log.debug("  Message Text  : &1", text);
                  log.debug("  Result Text   : &1", translatedText != null ? translatedText : "<null>");
               }

               if(translatedText == null) {
                  translatedText = text;
               }

               return translatedText;
            }
            finally {
               try {
                  if(stmt != null)
                     stmt.close();
               }
               finally {
                  if(c != null) {
                     c.close();
                  }
               }
            }
         }
         catch(IfsException e){
            log.error(e, "FndServerContext: Error while translating text");
            return text;
         }
         catch(RuntimeException e){
            log.error(e, "FndServerContext: Runtime exception while translating text");
            return text;
         }
         finally {
            isCurrentlyTranslating = null;
         }
      }
      else
         return null;
   }

   /**
    * <B>Framework internal method:</B> Checks if the current thread is performing a translation of a text message.</B>
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    *
    * @return true if the current thread is inside getTranslatedText() method, false otherwise
    */
   public boolean isCurrentlyTranslating() {
      return isCurrentlyTranslating != null && isCurrentlyTranslating.get() == Boolean.TRUE;
   }

   private void initNotInstalledObjects() {
      if (notInstalledObjects == null) {
         notInstalledObjects = Util.newHashSet();
      }
   }

   /**
    * <B>Framework internal method:</B> Add an object to the current set with not-installed database objects. The
    * method is used by storage logic to skip repeated warnings printed to log output.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    *
    * @param meta meta record representing not installed table or tables
    * @see #containsNotInstalledObject(FndRecordMeta)
    */
   public void addNotInstalledObject(FndRecordMeta meta) {
      initNotInstalledObjects();
      notInstalledObjects.add(meta);
   }

   /**
    * <B>Framework internal method:</B> Checks if an object is contained in the current set of not-installed
    * objects. The method is used by storage logic to skip repeated warnings printed to log output.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    *
    * @param meta meta record representing not installed table or tables
    * @return true if the specified object exists in the current set, false
    *         otherwise
    * @see #addNotInstalledObject(FndRecordMeta)
    */
   public boolean containsNotInstalledObject(FndRecordMeta meta) {
      initNotInstalledObjects();
      return notInstalledObjects.contains(meta);
   }

   /**
    * <B>Framework internal method:</B> Adds a stream manager to the list with active stream managers.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    * @param mgr an instance of FndStreamManager managing an open stream
    */
   public void addStreamManager(FndStreamManager mgr) {
      if(streamManagerList == null) {
         streamManagerList = Util.newArrayList();
      }
      streamManagerList.add(mgr);
   }

   /**
    * <B>Framework internal method:</B> Notifies all active stream managers about completion of the current transaction.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    * @param success true if the transaction has been committed,
    *                false if the transaction has been rolled back.
    * @throws SystemException if at least one FndStreamManager aborts notification procedure
    */
   public void notifyStreamManagers(boolean success) throws SystemException {
      if(streamManagerList == null) {
         return;
      }
      SystemException error = null;
      for(FndStreamManager mgr : streamManagerList) {
         try {
            mgr.done(success);
         }
         catch(SystemException any) {
            error = any; // store and then rethrow the last error
         }
      }
      streamManagerList = null; // clear the list
      if(error != null) {
         throw error; //new SystemException(error, "STREAMMGRDONE: Error while notifying a stream manager: &1", error.toString());
      }
   }

   /**
    * <B>Framework internal method:</B> Sets the flag indicating if the currently executed code is inside an activity.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    * @param flag boolean value to be set
    */
   public void setInsideActivity(boolean flag) {
      insideActivity = flag;
   }

   /**
    * <B>Framework internal method:</B> Checks if the currently executed code is inside an activity.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    * @return boolean value of the internal flag
    * @see #setInsideActivity(boolean)
    */
   public boolean isInsideActivity() {
      return insideActivity;
   }

   /**
    * <B>Framework internal method:</B> Enables method security in FNDBAS.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    * Method security checks are enabled by default.
    * They are disabled in J2EE framework.
    * They may be enabled by calling this public method.
    * @see #isBaseServerMethodSecurityEnabled()
    */
   public void enableBaseServerMethodSecurity() {
      baseServerMethodSecurityEnabled = true;
   }

   /**
    * <B>Framework internal method:</B> Sets the flag indicating if method security in FNDBAS is enabled.
    * Method security checks are enabled by default.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    * @param flag boolean flag
    * @see #enableBaseServerMethodSecurity()
    * @see #isBaseServerMethodSecurityEnabled()
    */
   protected void setBaseServerMethodSecurity(boolean flag) {
      baseServerMethodSecurityEnabled = flag;
   }

   /**
    * <B>Framework internal method:</B> Indicates if method security in FNDBAS is enabled.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    * @return true if method security in FNDBAS is enabled, false otherwise.
    * @see #enableBaseServerMethodSecurity()
    * @see #setBaseServerMethodSecurity(boolean)
    */
   public final boolean isBaseServerMethodSecurityEnabled() {
      return baseServerMethodSecurityEnabled;
   }

   /**
    * <B>Framework internal method:</B> Clears the security cache in the current instance of PlsqlGateway.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    * @throws IfsException if the method fails for any reason
    */
   public void clearPlsqlGatewaySecurityCache() throws IfsException {
      (new SecuritySupervisor(null)).clearCache();
   }

   /**
    * <B>Framework internal method:</B> Gets the current activity/service operation.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    *
    * @return the current activity operation in form "&lt;activity&gt;.&lt;method&gt;", or null if
    *         the current activity operation is undefined.
    */
   public String getActivityOperation() {
      if (operation != null && !operation.isNull()) {
         return (String) FndAttributeInternals.internalGetValue(operation);
      } else {
         return null;
      }
   }

   /**
    * <B>Framework internal method:</B> Sets the current activity/service operation.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    *
    * @param activity the name of current activity/service
    * @param method the name of current method
    */
   public void setActivityOperation(String activity, String method) {
      if (operation == null) {
         operation = FndAttributeInternals.newAttribute("OPERATION");
         FndRecordInternals.add(sysContext, operation);
      }
      if(activity == null && method == null) {
         operation.setNull();
      } else {
         FndAttributeInternals.internalSetValue(operation, activity + "." + method);
      }
   }

   /**
    * <B>Framework internal method:</B> This method may re-throw a RuntimeException as a SystemException with a more user friendly message.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    * @param err an instance of RuntimeException that has been caught
    * @param param an optional parameter that may be used in the new error message
    * @throws SystemException with a new user friendly message
    */
   public void checkRuntimeException(RuntimeException err, String param) throws SystemException {
   }

   /**
    * Set current user for this context.
    * @param directoryId directory ID for the current user.
    */
   public void setApplicationUser(String directoryId) {
      this.directoryId = directoryId;
   }

   public abstract String getCurrentImplementation();

   public String getContextClass() {
      return this.getClass().getName();
   }

   /**
    * <B>Framework internal method:</B>
    * Sets a license overview snapshot during execution of the license validation procedure.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    * @param snapshot
    */
   public void setLicenseSnapshot(Object snapshot) {
      licenseSnapshot = snapshot;
   }

   /**
    * <B>Framework internal method:</B>
    * Gets the license overview snapshot stored during execution of the license validation procedure.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    *
    * @return license overview snapshot
    */
   public Object getLicenseSnapshot() {
      return licenseSnapshot;
   }

   /**
    * <B>Framework internal method:</B> Gets the supported capability set for
    * the current request.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not
    * guaranteed.</B>
    *
    * @return supported capability set
    */
   @Override
   public Set<ApplicationCapability> getSupportedCapabilities() {
       return Collections.unmodifiableSet(supportedCapabilities);
   }

   /**
    * <B>Framework internal method:</B> Sets the supported capability set for
    * the current request.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not
    * guaranteed.</B>
    *
    * @param set
    *           supported capability set
    */
   @Override
   public void setSupportedCapabilities(CapabilitySet<ApplicationCapability> set) {
      /*
       * necessary to make sure that the supportedEncodingSet is empty as
       * FndContext is a ThreadLocal variable.
       */
       this.supportedCapabilities.clear();
       this.supportedCapabilities.addAll(set);
   }

   /**
    * <B>Framework internal method:</B>
    * Clears the internal counters for logical and database calls to Login_SYS.Init_Fnd_Session_.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    */
   public void clearInitFndSessionCounters() {
      initFndSessionCount = 0;
      initFndSessionDbCount = 0;
   }

   /**
    * <B>Framework internal method:</B>
    * Increments by one the number of logical calls to Login_SYS.Init_Fnd_Session_ during current activity call.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    */
   public void incInitFndSessionCount() {
      initFndSessionCount++;
   }

   /**
    * <B>Framework internal method:</B>
    * Gets the number of logical calls to Login_SYS.Init_Fnd_Session_ during current activity call.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    * @return
    */
   public int getInitFndSessionCount() {
      return initFndSessionCount;
   }

   /**
    * <B>Framework internal method:</B>
    * Increments by one the number of database calls to Login_SYS.Init_Fnd_Session_ during current activity call.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    */
   public void incInitFndSessionDbCount() {
      initFndSessionDbCount++;
   }

   /**
    * <B>Framework internal method:</B>
    * Gets the number of database calls to Login_SYS.Init_Fnd_Session_ during current activity call.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    * @return
    */
   public int getInitFndSessionDbCount() {
      return initFndSessionDbCount;
   }

   /**
    * <B>Framework internal method:</B>
    * Gets the current transaction.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    *
    * @return current transaction, or null if current transaction is undefined
    */
   public FndTransaction getCurrentTransaction() {
      return null;
   }

   /**
    * Gets a record with details of the last PLSQL error.
    * @return record with error details
    */
   @Override
   public FndRecord getPlsqlErrorDetails() {
      return plsqlErrorDetails;
   }

   /**
    * Sets a record with details of the last PLSQL error.
    * @param rec record with error details
    */
   public void setPlsqlErrorDetails(FndRecord rec) {
      this.plsqlErrorDetails = rec;
   }

   /**
    * Change current application user if the current user is allowed to do it.
    * @param directoryId directory ID for the new user, if null the method will do nothing
    * @throws SecurityException if the current user lacks the necessary system privilege to perform the operation
    *          IfsException      if the method fails for some other reason
    */
   public void impersonateApplicationUser(String directoryId) throws IfsException {
      if (directoryId != null) {
         if (FndUserCache.isSystemPrivilegeGranted(FndConstants.IMPERSONATE_USER)) {
            setApplicationUser(directoryId);
         }
         else {
            Logger secLog = LogMgr.getSecurityLogger();
            if (secLog.warning) {
               secLog.warning("User '" + getApplicationUser() + "' lacks the necessary system privilege to run as user '" + directoryId + "'");
            }
            throw new SecurityException(Texts.IMPERSONATEUSER);
         }
      }
   }

   /**
    * Set the current request identifier, if not already set by the client.
    */
   public void setRequestId() {
      if(FndContext.getRequestId() == null) {
         String id = UUID.randomUUID().toString();
         requestId = FndAttributeInternals.newAttribute("REQUEST_ID");
         FndRecordInternals.add(sysContext, requestId);
         FndAttributeInternals.internalSetValue(requestId, id);
      }
   }

   public String getAppContextParameter(String name) {
      return appContext==null ? null : (String)appContext.getAttributeValue(name);
   }

   public void setAppContextParameter(String name, String value) {
      if(value==null && appContext==null)
         return;

      FndRecord appCtx = getAppContext();
      FndAttribute attr = appCtx.getAttribute(name);
      if(attr==null && value!=null) {
         appCtx.add(name, value);
      }
      else if(attr!=null) {
         FndAttributeInternals.internalSetValue(attr, value);
      }
   }

   /**
    * Sets the flag indicating if currently executed code is inside FndSecurityCheckpoint.validateReauthenticationResponse().
    * @param flag boolean flag
    */
   void setValidatingReauthenticationResponse(boolean flag) {
      validatingReauthenticationResponse = flag;
   }

   /**
    * Gets the flag indicating if currently executed code is inside FndSecurityCheckpoint.validateReauthenticationResponse().
    * @return boolean flag
    */
   boolean isValidatingReauthenticationResponse() {
      return validatingReauthenticationResponse;
   }

   /**
    * Get current JDBC Connection
    */
   public Connection getJdbcConnection() {
      return null;
   }
}
