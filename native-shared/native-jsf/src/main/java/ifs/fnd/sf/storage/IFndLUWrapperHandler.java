/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.storage;

import ifs.fnd.base.IfsException;
import ifs.fnd.record.FndAbstractArray;
import ifs.fnd.record.FndLUWrapperView;
import ifs.fnd.record.FndQueryRecord;

/**
 * Interface used to provide same entity handler methods to LU Wrapper Views.
 */
public interface IFndLUWrapperHandler {

   /**
    * Used to find multiple instances satisfying a query condition specified on the argument view
    * @param condition - Query Condition
    * @return - Query Result
    * @throws ifs.fnd.base.IfsException - Exception thrown
    */
   FndAbstractArray query(FndQueryRecord condition) throws IfsException;

   /**
    * Stores a LU Wrapper View instance in persistent storage
    * @param view - LU Wrapper View to be saved
    * @throws ifs.fnd.base.IfsException - Exception thrown
    */
   void save(FndLUWrapperView view) throws IfsException;

   /**
    * Checks if an LU Wrapper View instance exists in persistent storage
    * @param inRecord - LU Wrapper View to be searched
    * @return - Returns boolean value (true if exists, false if not)
    * @throws ifs.fnd.base.IfsException - Exception thrown
    */
   boolean exist(FndLUWrapperView inRecord) throws IfsException;

   /**
    * Populates a LU Wrapper View with data.
    * It retrieves from storage included parts of a view structure.
    * @param inRecord - LU Wrapper View to be populated.
    * @return - Populated LU Wrapper View
    * @throws ifs.fnd.base.IfsException - Exception thrown
    */
   FndLUWrapperView populate(FndLUWrapperView inRecord) throws IfsException;

   /**
    * Returns a single LU Wrapper View instance from persistent storage
    * @param inRecord - LU Wrapper View to be fetched
    * @return - LU Wrapper View instance
    * @throws ifs.fnd.base.IfsException - Exception thrown
    */
   FndLUWrapperView get(FndLUWrapperView inRecord) throws IfsException;

   /**
    * Gets the default values for an LU Wrapper view by calling
    * the corresponding PL/SQL method New__ with parameter action = "PREPARE".
    * @param inRecord - LU Wrapper View to be prepared.
    * @throws ifs.fnd.base.IfsException - Exception thrown
    */
   void prepare(FndLUWrapperView inRecord) throws IfsException;

}
