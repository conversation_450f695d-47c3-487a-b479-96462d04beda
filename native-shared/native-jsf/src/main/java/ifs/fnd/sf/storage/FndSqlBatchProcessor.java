/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.storage;

import ifs.fnd.base.*;
import ifs.fnd.internal.FndAttributeInternals;
import ifs.fnd.log.Logger;
import ifs.fnd.record.*;
import ifs.fnd.record.serialization.FndAutoString;
import ifs.fnd.services.plsqlserver.AttributeString;
import ifs.fnd.services.plsqlserver.service.PlsqlUtil;
import ifs.fnd.sf.admin.FndAbortList;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 * Class that performs batch save operations.
 */
class FndSqlBatchProcessor {

   /**
    * Database logger used for debug output.
    */
   private Logger log;

   /**
    * Owner of this instance of FndSqlBatchProcessor.
    */
   private FndSqlStorage storage;

   /**
    * Utility class used by standard storage operations.
    */
   private FndSqlStorageUtil util;

   /**
    * Database connection used by current operation.
    */
   private FndConnection connection;

   /**
    * Database statement used by current operation.
    */
   private FndStatement stmt;

   /**
    * Current PLSQL block generated by batchSaveLU operation.
    */
   private FndAutoString plsql;

   /**
    * Variable used to generate sequence numbers for nested PLSQL blocks.
    */
   private int nextPlsqlBlockNr;

   /**
    * Application owner prefix for database tables, views and PLSQL packages.
    */
   private String aoPrefix;

   /**
    * Constructs a FndSqlBatchProcessor instance owned by specified FndSqlStorage.
    */
   FndSqlBatchProcessor(FndSqlStorage storage, FndSqlStorageUtil util, Logger log) {
      this.storage = storage;
      this.util = util;
      this.log = log;
   }

   /**
    * Update a set of entities with the same attribute values.
    * The method performs a query based on the condition record and then
    * updates every entity in the result set with the attribute values defined in the value record.
    * <p>
    * The method will do different things depending on state of the value record (and optionally its details).
    * <pre>
    *    REMOVED_RECORD:
    *       Remove entities from database.
    *
    *    NEW_RECORD, MODIFIED_RECORD:
    *       Modify existing entities by setting the values of persistent,
    *       dirty attributes specified in the value record.
    *
    *    QUERY_RECORD:
    *       Only OBJVERSION will be updated.
    * </pre>
    * The type of the condition record must match the type of the value record.
    * Also, the structure of the condition record must match the structure of the value record,
    * which means that if a value record contains a detail record then the condition record must contain
    * the corresponding (possibly empty) detail record of the same type.
    * In case of arrays in value-record only the first element is important; it is used to
    * update/remove all corresponding element records returned by the query.
    * <p>
    * Include query result flags on condition record are ignored, all attributes necessary to
    * properly perform the operation (OBJID, OBJVERSION, primary and parent keys) are included automatically.
    *
    * @param value record defining attribute values to set
    * @param condition record with query condition defining the entity set to be updated
    * @param targetState not used (entity state of LU entities is modified by PLSQL code)
    * @throws IfsException if the operation fails for any reason
    */
   void batchSave(FndBaseEntityView value, FndBaseEntityView condition, FndEntityState.Enum targetState) throws IfsException {
      assert(targetState == null);

      this.connection = storage.context.getConnection(condition);

      // analyze value record structure
      BatchSaveInputFlags input = new BatchSaveInputFlags();
      input.analyzeInputRecord(value);
      if(log.debug)
         log.debug("FndSqlBatchProcessor.batchSave(): analyzeInputRecord: &1", input.toString());

      if(!input.nativeRecordExists && !input.dirtyLobExists && !input.customStorageExists)
         batchSaveLU((FndLUEntityView)value, (FndLUEntityView)condition);
      else
         batchSaveCursor(value, condition, null);
   }

   /**
    * Perform standard save operation on a set of (LU or native) entities defined by a query condition.
    *
    * The method performs a query based on the condition record and then in a loop
    * updates/removes every entity (and its details) in the result set.
    * The method performs validation in the java layer.
    * Dirty LOB attributes in the value record are supported.
    *
    * @param value record defining attribute values to set
    * @param condition record with query condition defining the entity set to be updated
    * @param targetState not used (entity state of LU entities is modified by PLSQL code)
    * @throws IfsException if there is a problem saving the entities
    */
   private void batchSaveCursor(FndPersistentView value, FndPersistentView condition, FndEntityState.Enum targetState) throws IfsException {
      assert(targetState == null);

      if(log.debug)
         log.debug("FndSqlBatchProcessor.batchSaveCursor(&1, &2, null)", value.getName(), condition.getName());

      this.stmt = storage.createStatement(connection, condition);
      boolean alias = true;
      boolean remove = value.getState() == FndRecordState.REMOVED_RECORD;

      try {
         // include OBJIDs, OBJVERSIONs, primary key, parent keys ...etc
         prepareBatchSaveCursor(value, condition, true);

         // generate select statement as in standard query (no ORDER BY, what about GROUP BY?, what about JOIN?)
         FndAutoString sql = new FndAutoString(256);
         FndAutoString sqlOrderBy = new FndAutoString(0);
         FndSqlStorage.SelectStatementContext stmtCtx = new FndSqlStorage.SelectStatementContext(stmt);
         boolean conditionAdded = storage.createBaseSelectStatement(condition, null, stmtCtx, sql, null, sqlOrderBy, connection);
         if (sql.length() > 0) {
            if (util.appendAdvancedConditions(condition, sql, stmt, !conditionAdded, alias, connection))
               conditionAdded = true;
            util.appendSimpleConditions(condition, sql, stmt, !conditionAdded, alias);
            //util.appendGroupBy(stmtCtx, condition, sql, alias, connection); // probably should not be allowed

            stmt.prepare(sql);
            stmt.setFetchSize(FndSqlStorage.DEFAULT_FETCH_SIZE);

            stmt.executeQuery();

            ResultSet result = stmt.getResult();

            int j = 0;
            while(true) {
               if (++j % FndSqlStorage.MAX_FETCH_SIZE * 5 == 0) {
                  //Check if request should be aborted every MAX_FETCH_SIZE * 5 rows
                  if (FndAbortList.getInstance().checkAbortStatus(FndContext.getRequestId())) {
                     throw new UserAbortException(Texts.FETCHABORT);
                  }
               }

               FndBaseEntityView view = (FndBaseEntityView) storage.fetch(result, stmtCtx, condition);
               if (view == null)
                  break;
               storage.queryCompoundAttributes(stmtCtx, connection, condition, view);
               view.setState(FndRecordState.QUERY_RECORD);

               // applyRules on queried record
               FndEntityHandler.applyRules(view);

               // set values on master and detail records
               copyDirtyAttributes(value, view);

               // applyRules on dirty record
                  FndEntityHandler.applyRules(view);

               //Validate view before saving
               storage.markRecordsToRemove(view, false);
               view.validate(false);
               view.abortIfInvalid();

               storage.save(connection, view, util.isIndependentEntity(view), null);
            }
         }
      }
      finally {
         if(stmt != null)
            stmt.close();
      }
   }

   /**
    * Copy contents of simple dirty attributes from one view structure to another.
    * The specified views must be instances of the same view type.
    * In case of compound attributes only the first element of from-array/aggregate is applyied
    * on all elements of to-array/aggregate.
    * If a record in the from-structure is in state REMOVED_RECORD then all the corresponding records
    * in the to-structure are marked as removed.
    */
   private void copyDirtyAttributes(FndAbstractRecord fromView, FndAbstractRecord toView) {

      if(fromView.getState() == FndRecordState.REMOVED_RECORD) {
         toView.setState(FndRecordState.REMOVED_RECORD);
         return;
      }

      int count = Math.min(fromView.getAttributeCount(), toView.getAttributeCount());
      for(int i=0; i<count; i++) {
         FndAttribute fromAttr = fromView.getAttribute(i);
         FndAttribute toAttr = toView.getAttribute(i);
         if(fromAttr.isCompound()) {
            FndPersistentView fromElem = getFirstElement((FndCompoundAttribute) fromAttr);
            if(fromElem != null) {
               if(toAttr.isVector()) {
                  FndAbstractArray toArr = (FndAbstractArray) toAttr;
                  for(int j = 0; j < toArr.size(); j++) {
                     FndAbstractRecord toElem = FndAttributeInternals.internalGet(toArr, j);
                     copyDirtyAttributes(fromElem, toElem);
                  }
               }
               else {
                  FndAbstractAggregate toAgg = (FndAbstractAggregate) toAttr;
                  FndAbstractRecord toElem = FndAttributeInternals.internalGetRecord(toAgg);
                  copyDirtyAttributes(fromElem, toElem);
               }
            }
         }
         else {
            if(fromAttr.isDirty())
               FndAttributeInternals.internalSetValue(toAttr, FndAttributeInternals.internalGetValue(fromAttr));
         }
      }
   }

   /**
    * Perform standard save operation on a set of LU entities defined by a query condition.
    *
    * The method generates one PLSQL block that performs all the work in the database.
    * Nested PLSQL blocks are used to query and Modify__/Remove__ details.
    * The method performs no validation in the java layer,
    * all validations are (assumed to be) performed in the database.
    * LOB attributes in the value record are ignored.
    *
    * @param value record defining attribute values to set
    * @param condition record with query condition defining the entity set to be updated
    * @throws IfsException if there is a problem saving the entities
    */
   private void batchSaveLU(FndLUEntityView value, FndLUEntityView condition) throws IfsException {

      if(log.debug)
         log.debug("FndSqlBatchProcessor.batchSaveLU(&1, &2)", value.getName(), condition.getName());

      this.stmt = storage.createStatement(connection, condition);
      this.plsql = new FndAutoString(256);

      nextPlsqlBlockNr++;

      try {
         plsql.append("\n\ndeclare\n");
         plsql.append("   tmpinfo_  varchar2(32767);\n");
         plsql.append("   tmpattr_  varchar2(32767);\n");
         plsql.append("   allinfo_  clob;\n");
         plsql.append("   totcount_ integer := 0;\n");
         plsql.append("begin\n");

         generateBatchSaveLU(value, condition, nextPlsqlBlockNr, "   ");

         plsql.append("   ? := allinfo_;\n");
         plsql.append("end;\n");

         // define the last bind variable: CLOB with concatenated INFOs/WARNINGs
         FndSqlValue outinfo = new FndSqlValue("INFO", null, true, true);
         outinfo.setDirection(FndSqlValue.DIRECTION_OUT);
         stmt.defineParameter(outinfo);

         // execute
         stmt.prepareCall(plsql);
         stmt.execute();

         // retrieve INFO messages
         String info = stmt.getLongText(stmt.getParameterCount());
         if(log.debug)
            log.debug("   INFO: &1", info);
         PlsqlUtil.processInfo(info);
      }
      finally {
         if(stmt != null)
            stmt.close();
      }
   }

   /**
    * Generate PLSQL code that performs batch save operation on a master or detail LU record.
    * The value record defines the action to be performed (Modify__ or Remove__).
    * The condition record defines the set of records to be modified or removed.
    *
    * @param value record defining attribute values to set
    * @param condition record with query condition defining the record set to be updated
    * @param parentPlsqlBlockNr sequence number identifying the parent PLSQL block
    * @param indent indentation string corresponding to current recursion level
    * @throws IfsException if there is a problem saving the entities
    */
   private void generateBatchSaveLU(FndLUEntityView value,
                                    FndLUEntityView condition,
                                    int parentPlsqlBlockNr,
                                    String indent) throws IfsException {

      verifySameType(value, condition);

      // generate new PLSQL block nr
      int plBlockNr = nextPlsqlBlockNr++;

      FndRecordState state = value.getState();
      boolean modify = state != FndRecordState.REMOVED_RECORD;
      String loopVar = "R" + plBlockNr;

      plsql.append(indent, "declare\n");
      if(modify) {
         // create and bind attribute string based on persitent, dirty attributes in value record
         AttributeString attrString = new AttributeString(value, false);
         String attrStrValue = attrString.toString();
         int attrStrLen = Math.max(attrStrValue.length(), 1);
         stmt.defineInParameter(new FndSqlValue("ATTR", attrStrValue, false, false));
         plsql.append(indent, "   attr_  constant varchar2(").appendInt(attrStrLen).append(") := ?;\n");
      }
      plsql.append(indent, "   count_ integer := 0;\n");
      plsql.append(indent, "begin\n");
      plsql.append(indent, "   for ", loopVar, " in\n(\n ");

      // include OBJID, OBJVERSION in the query result
      condition.excludeQueryResults();
      condition.objId.include();
      condition.objVersion.include();

      // include primary key (this is needed only for debugging)
      // include parent-key-in-parent references acting as parent keys for details
      List<FndCompoundReference> parentKeys = new ArrayList<>();
      parentKeys.add(condition.getPrimaryKey());
      addNonEmptyPersistentDetails(value, parentKeys);
      for(int i = 0; i < parentKeys.size(); i++)
         ((FndCompoundReference) parentKeys.get(i)).include();

      // generate select statement as in standard query (no ORDER BY, no GROUP BY, no JOIN because aggregates are excluded)
      FndAutoString select = new FndAutoString(256);
      FndAutoString sqlOrderBy = new FndAutoString(0);
      FndSqlStorage.SelectStatementContext stmtCtx = new FndSqlStorage.SelectStatementContext(stmt);
      boolean conditionAdded = storage.createBaseSelectStatement(condition, null, stmtCtx, select, null, sqlOrderBy, connection);

      // append parent key condition (if value record is a detail)
      FndCompoundAttribute container = condition.getContainer();
      if(container != null && plBlockNr > 1) {
         FndCompoundAttributeMeta metaContainer = condition.getContainer().getCompoundMeta();
         appendParentKeyConditionLU(parentPlsqlBlockNr, metaContainer, condition, select, !conditionAdded);
         conditionAdded = true;
      }

      // add other query conditions
      if (util.appendAdvancedConditions(condition, select, stmt, !conditionAdded, true, connection))
         conditionAdded = true;
      util.appendSimpleConditions(condition, select, stmt, !conditionAdded, true);

      plsql.append(select).append("\n)\n");
      plsql.append(indent, "   loop\n");
      plsql.append(indent, "      count_ := count_ + 1;\n");
      plsql.append(indent, "      totcount_ := totcount_ + 1;\n");
      if(log.debug)
         debugLuEntityKeys(loopVar, indent, parentKeys);

      // uncomment to test INFO/WARNING
      //plsql.append(indent, "      ifsapp.Client_SYS.Add_Warning('X1', 'WARNING: Tesing manual decision...');\n");
      //plsql.append(indent, "      ifsapp.Client_SYS.Add_Info('PLSQL', 'BATCH_SAVE:").append(modify ? "Modify__" : "Remove__")
      //   .append(" entity ", value.getName(), " with rowid [:P1]', ", loopVar, ".objid);\n");

      if(modify) {
         plsql.append(indent, "      ", "tmpattr_ := attr_;\n");

         if(log.debug) {
            plsql.append(indent, "      --\n");
            plsql.append(indent, "      -- Modifying ", value.getName(), " (", loopVar, ")\n");
            plsql.append(indent, "      --\n");
         }

         plsql.append(indent, "      ");
         util.appendPlsqlPackageName(value, plsql);
         plsql.append(".Modify__(tmpinfo_, ", loopVar, ".objid, ", loopVar, ".objversion, tmpattr_, 'DO');\n");

         plsql.append(indent, "      allinfo_ := allinfo_ || tmpinfo_;\n");
         if(log.debug)
            plsql.append(indent, "      ", aoPrefix, "Trace_SYS.Message('');\n");

         // generate batch save for non-empty compound attributes
         FndCompoundAttribute.Iterator vDetails = value.persistentDetails();
         FndCompoundAttribute.Iterator cDetails = condition.persistentDetails();
         while (vDetails.hasNext()) {
            FndCompoundAttribute vAttr = vDetails.next();
            FndCompoundAttribute cAttr = cDetails.next();

            FndLUEntityView detailValue = getFirstLuElement(vAttr);
            if(detailValue == null)
               continue;

            FndLUEntityView detailCondition = getFirstLuElement(cAttr);
            if(detailCondition == null)
               throw new SystemException("FNDBATCHSAVE_MISSCOND:Cannot perform batchSave. Missing query condition record in detail attribute &1",
                                         cAttr.getMeta().getFullName());

            generateBatchSaveLU(detailValue, detailCondition, plBlockNr, indent + "      ");
         }

         if(log.debug) {
            plsql.append(indent, "      --\n");
            plsql.append(indent, "      -- Modified ", value.getName(), " (", loopVar, ")\n");
            plsql.append(indent, "      --\n");
         }
      }
      else {
         FndLUEntityView view = (FndLUEntityView) value.newInstance();
         generateBatchRemoveLU(view, plBlockNr, indent + "      ");
      }

      plsql.append(indent, "   end loop; -- ", loopVar, "\n");
      plsql.append(indent, "end;\n");
   }

   /**
    * Generate PLSQL code that performs batch remove operation on a master or detail LU record.
    * The method queries and removes all persistent details of the specified record
    * before removing the master record itself.
    *
    * @param view record representing entity to be removed (with no values and conditions set)
    * @param parentPlsqlBlockNr sequence number identifying the parent PLSQL block
    * @param indent indentation string corresponding to current recursion level
    * @throws IfsException if there is a problem saving the entities
    */
   private void generateBatchRemoveLU(FndLUEntityView view,
                                      int parentPlsqlBlockNr,
                                      String indent) throws IfsException {

      String parentLoopVar = "R" + parentPlsqlBlockNr;

      if(log.debug) {
         plsql.append(indent, "--\n");
         plsql.append(indent, "-- Removing ", view.getName(), " (", parentLoopVar, ")\n");
         plsql.append(indent, "--\n");
      }

      // lock the master record
      plsql.append(indent);
      util.appendPlsqlPackageName(view, plsql);
      plsql.append(".Lock__(tmpinfo_, ", parentLoopVar, ".objid, ", parentLoopVar, ".objversion);\n");
      plsql.append(indent, "allinfo_ := allinfo_ || tmpinfo_;\n");
      if(log.debug)
         plsql.append(indent, aoPrefix, "Trace_SYS.Message('');\n");

      // generate batch remove for compound attributes
      // create a list with cascade-delete compound attributes and visit the list in reversed order
      ArrayList<FndCompoundAttribute> attrToRemove = null;
      FndCompoundAttribute.Iterator details = view.persistentCascadeDeleteDetails();
      while (details.hasNext()) {
         FndCompoundAttribute attr = details.next();

         // Skip read-only details.
         // Meta-element points to the abstract class, but isReadOnly and isLU are common
         // to the whole meta inheritance tree.
         FndRecordMeta elemMeta = attr.getCompoundMeta().getElement();
         if(elemMeta.isReadOnly())
            continue;

         if(attrToRemove == null)
            attrToRemove = new ArrayList<>();
         attrToRemove.add(attr);
      }
      int attrToRemoveCount = attrToRemove == null ? 0 : attrToRemove.size();
      for(int d = attrToRemoveCount - 1; d >= 0; d--) {
         FndCompoundAttribute attr = (FndCompoundAttribute) attrToRemove.get(d);

         // generate new PLSQL block nr
         int plBlockNr = nextPlsqlBlockNr++;

         String loopVar = "R" + plBlockNr;

         plsql.append(indent, "declare\n");
         plsql.append(indent, "   count_ integer := 0;\n");
         plsql.append(indent, "begin\n");
         plsql.append(indent, "   for ", loopVar, " in\n(\n ");

         // create a new empty instance of the detail view
         FndLUEntityView detailView = null;
         if (attr.isVector()) { //Array
            FndAbstractArray arr = (FndAbstractArray) attr;
            detailView = (FndLUEntityView) arr.newRecord();
            FndAttributeInternals.internalAdd(arr, detailView);
         }
         else if (attr.isCompound()) { //Aggregate
            FndAbstractAggregate agg = (FndAbstractAggregate) attr;
            detailView = (FndLUEntityView) FndAttributeInternals.internalGetRecord(agg);
         }

         // include OBJID, OBJVERSION in the query result
         assert(detailView != null);
         detailView.excludeQueryResults();
         detailView.objId.include();
         detailView.objVersion.include();

         // include primary key (this is needed only for debugging)
         // include parent-key-in-parent references acting as parent keys for details
         List<FndCompoundReference> parentKeys = new ArrayList<>();
         parentKeys.add(detailView.getPrimaryKey());
         addPersistentCascadeDeleteDetails(detailView, parentKeys);
         for(int i = 0; i < parentKeys.size(); i++)
           ((FndCompoundReference) parentKeys.get(i)).include();

         // generate select statement as in standard query (no ORDER BY, no GROUP BY, no JOIN because aggregates are excluded)
         FndAutoString select = new FndAutoString(256);
         FndAutoString sqlOrderBy = new FndAutoString(0);
         FndSqlStorage.SelectStatementContext stmtCtx = new FndSqlStorage.SelectStatementContext(stmt);
         boolean conditionAdded = storage.createBaseSelectStatement(detailView, null, stmtCtx, select, null, sqlOrderBy, connection);

         // append parent key condition
         FndCompoundAttributeMeta metaContainer = detailView.getContainer().getCompoundMeta();
         appendParentKeyConditionLU(parentPlsqlBlockNr, metaContainer, detailView, select, !conditionAdded);

         plsql.append(select).append("\n)\n");
         plsql.append(indent, "   loop\n");
         plsql.append(indent, "      count_ := count_ + 1;\n");
         plsql.append(indent, "      totcount_ := totcount_ + 1;\n");
         if(log.debug)
            debugLuEntityKeys(loopVar, indent, parentKeys);

         generateBatchRemoveLU(detailView, plBlockNr, indent + "      ");

         plsql.append(indent, "   end loop; -- ", loopVar, "\n");
         plsql.append(indent, "end;\n");
      }

      plsql.append(indent);
      util.appendPlsqlPackageName(view, plsql);
      plsql.append(".Remove__(tmpinfo_, ", parentLoopVar, ".objid, ", parentLoopVar, ".objversion, 'DO');\n");
      plsql.append(indent, "allinfo_ := allinfo_ || tmpinfo_;\n");
      if(log.debug) {
         plsql.append(indent, aoPrefix, "Trace_SYS.Message('');\n");
         plsql.append(indent, "--\n");
         plsql.append(indent, "-- Removed ", view.getName(), " (", parentLoopVar, ")\n");
         plsql.append(indent, "--\n");
      }
   }


   /**
    * Append parent key condition:    a3.company = R11.company and a3.id = R11.id
    */
   private void appendParentKeyConditionLU(int parentPlBlockNr,
                                           FndCompoundAttributeMeta metaContainer,
                                           FndPersistentView elemView,
                                           FndAutoString select,
                                           boolean first) throws SystemException {

      FndCompoundReferenceMeta parentKey = metaContainer.getParentKeyInParent();
      FndCompoundReferenceMeta elemKey   = metaContainer.getParentKeyInElement();

      String elemPrefix = util.getTableAlias(elemView) + ".";
      String parentPrefix = "R" + parentPlBlockNr + ".";

      if (first)
         select.append("\n WHERE ");
      else
         select.append("\n AND ");

      int count = parentKey.getAttributeCount();
      for (int i = 0; i < count; i++) {
         FndAttributeMeta pkey = parentKey.getAttribute(i);
         FndAttributeMeta ekey = elemKey.getAttribute(i);
         if (i > 0)
            select.append("\n AND ");
         select.append(elemPrefix, ekey.getColumn());
         select.append(" = ");
         select.append(parentPrefix, pkey.getColumn().toLowerCase());
      }
   }

   /**
    * Generate PLSQL code that outputs debugging information about current entity instance.
    */
   private void debugLuEntityKeys(String loopVar, String indent, List parentKeys) {
      plsql.append(indent, "      ", aoPrefix, "Trace_SYS.Message('[' || totcount_ || '] ' || '", loopVar, ".' || count_);\n");
      plsql.append(indent, "      ", aoPrefix, "Trace_SYS.Field('OBJID', ", loopVar, ".objid);\n");
      plsql.append(indent, "      ", aoPrefix, "Trace_SYS.Field('OBJVERSION', ", loopVar, ".objversion);\n");

      // remove duplicates keeping the order of attributes (we ignore performance in debug mode)
      List<String> list = new ArrayList<>();
      for(int i = 0; i < parentKeys.size(); i++) {
         FndCompoundReference ref = (FndCompoundReference) parentKeys.get(i);
         FndAttribute.Iterator iter = ref.iterator();
         while(iter.hasNext()) {
            String name = iter.next().getName();
            if(!list.contains(name))
               list.add(name);
         }
      }

      for(int i = 0; i < list.size(); i++) {
         String name = (String) list.get(i);
         plsql.append(indent, "      ", aoPrefix, "Trace_SYS.Field('");
         plsql.append(name);
         plsql.append("', ", loopVar, ".");
         plsql.append(name.toLowerCase());
         plsql.append(");\n");
      }
   }

   /**
    * Add to the specified list all parent-key-in-parent references
    * corresponding to persistent details containing elements.
    * @param view the master view
    * @param list the destination list for instances of FndCompoundReference
    */
   private static void addNonEmptyPersistentDetails(FndPersistentView view, List<FndCompoundReference> list) {
      FndCompoundAttribute.Iterator details = view.persistentDetails();
      while (details.hasNext()) {
         FndCompoundAttribute attr = details.next();
         if(getFirstElement(attr) != null)
            list.add(attr.getParentKeyInParent());
      }
   }

   /**
    * Include in the condition view structure all attributes needed by batchSaveCursor() operation.
    * The process is controlled by the value view (which acts as in-parameter) and modifies
    * the condition view (out-parameter). The method recursively checks (and modifies)
    * the master record and the nested persistent (non-empty) details.
    * The method includes OBJIDs, OBJVERSIONs, primary keys, parent keys and arrays/aggregates
    * necessary to perform the query and then update or remove records.
    * All other attributes are excluded.
    *
    * @param value record defining attribute values to set
    * @param condition record with query condition defining the record set to be updated
    * @param includeEntityState true if entity state attribute should be included, false otherwise
    */
   private static void prepareBatchSaveCursor(FndPersistentView value, FndPersistentView condition, boolean includeEntityState) throws SystemException {

      verifySameType(value, condition);

      condition.excludeQueryResults();

      // include OBJID, OBJVERSION in the query result
      if(condition instanceof FndLUEntityView) {
         FndLUEntityView lu = (FndLUEntityView) condition;
         lu.objId.include();
         lu.objVersion.include();
      }
      else if(condition instanceof FndEntityView) {
         FndEntityView ent = (FndEntityView) condition;
         ent.objVersion.include();
      }

      // include entity state
      if(includeEntityState && condition instanceof FndBaseEntityView) {
         FndBaseEntityView base = (FndBaseEntityView) condition;
         if(base.entityState() != null)
            base.entityState().include();
      }

      // include primary key
      condition.getPrimaryKey().include();

      // include parent keys and compound attributes
      FndCompoundAttribute.Iterator vDetails = value.persistentDetails();
      FndCompoundAttribute.Iterator cDetails = condition.persistentDetails();
      while (vDetails.hasNext()) {
         FndCompoundAttribute vAttr = vDetails.next();
         FndCompoundAttribute cAttr = cDetails.next();

         FndLUEntityView detailValue = getFirstLuElement(vAttr);
         if(detailValue == null)
            continue;

         FndLUEntityView detailCondition = getFirstLuElement(cAttr);
         if(detailCondition == null)
            throw new SystemException("FNDBATCHSAVE_MISSCOND:Cannot perform batchSave. Missing query condition record in detail attribute &1",
                                      cAttr.getMeta().getFullName());

         cAttr.include();
         cAttr.getParentKeyInParent().include();
         prepareBatchSaveCursor(detailValue, detailCondition, false);
      }
   }

   /**
    * Adds persistent cascade-delete details to the specified list.
    * The method does nor recurse to nested details.
    */
   private static void addPersistentCascadeDeleteDetails(FndAbstractRecord view, List<FndCompoundReference> list) {
      FndCompoundAttribute.Iterator details = view.persistentCascadeDeleteDetails();
      while (details.hasNext()) {
         FndCompoundAttribute attr = details.next();
         list.add(attr.getParentKeyInParent());
      }
   }

   /**
    * Returns the record contained in an aggregate or the first element of an array.
    * @return the first element record or null if there is no such record.
    */
   private static FndPersistentView getFirstElement(FndCompoundAttribute attr) {
      if(attr.isVector()) {
         FndAbstractArray arr = (FndAbstractArray) attr;
         if(arr.size() > 0)
            return (FndLUEntityView) FndAttributeInternals.internalGet(arr, 0);
         else
            return null;
      }
      else {
         FndAbstractAggregate agg = (FndAbstractAggregate) attr;
         if(!agg.isNull())
            return (FndLUEntityView) FndAttributeInternals.internalGetRecord(agg);
      }
      return null;
   }

   /**
    * Returns the LU-record contained in an aggregate or the first element of an array.
    * @return the first element record or null if there is no such record.
    */
   private static FndLUEntityView getFirstLuElement(FndCompoundAttribute attr) {
      return (FndLUEntityView) getFirstElement(attr);
   }

   /**
    * Class describing properties of input to batchSave operation.
    */
   private static class BatchSaveInputFlags {
      boolean luRecordExists;      // at least one record in input record structure is an LU entity
      boolean nativeRecordExists;  // at least one record in input record structure is a native (non-LU) record
      boolean dirtyLobExists;      // at least one dirty persitent LOB exists in input record structure
      boolean customStorageExists; // at least one (non-empty) array/aggregate implements FndSaveStorage or FndQueryStorage

      private void analyzeInputRecord(FndAbstractRecord inputRecord) {
         if(inputRecord instanceof FndLUEntityView)
            luRecordExists = true;
         else
            nativeRecordExists = true;

         int count = inputRecord.getAttributeCount();
         for(int i = 0; i < count; i++) {
            FndAttribute attr = inputRecord.getAttribute(i);
            if(attr.isDirty() && attr.getMeta().isPersistent() && attr.isLong())
               dirtyLobExists = true;

            if(attr.isCompound()) {
               FndAbstractRecord elem = getFirstElement((FndCompoundAttribute) attr);
               if(elem != null) {
                  if(attr instanceof FndSaveStorage || attr instanceof FndQueryStorage)
                     customStorageExists = true;
                  analyzeInputRecord(elem);
               }
            }
         }
      }

      @Override
      public String toString() {
         return "\n\tluRecordExists      = " + luRecordExists      +
                "\n\tnativeRecordExists  = " + nativeRecordExists  +
                "\n\tdirtyLobExists      = " + dirtyLobExists      +
                "\n\tcustomStorageExists = " + customStorageExists;
      }
   }

   private static void verifySameType(FndAbstractRecord value, FndAbstractRecord condition) throws SystemException {
      if(value.getClass() != condition.getClass())
         throw new SystemException("FNDBATCHSAVE_SAMETYPE:Cannot perform batchSave. Value-record [&1] and condition-record [&2] must be of the same type.",
                                   value.getClass().getName(),
                                   condition.getClass().getName());
   }
}
