/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf;

import ifs.fnd.base.*;
import ifs.fnd.sf.storage.*;

/**
 * <B>Framework internal class:</B> Class representing used server framework (j2ee or standalone).
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public abstract class FndServerFramework extends FndFramework {

   /**
    * Returns the instance of FndServerFramework representing the type of currently used framework.
    * @return a singleton instance of a sub-class of FndServerFramework.
    */
   public static FndServerFramework getServerFramework() {
      return (FndServerFramework)FndFramework.getFramework();
   }
   
   /**
    * Create new instance of FndContext valid for current framework.
    * @return a newly created instance of sub-class of FndServerContext.
    */
   @Override
   public abstract FndContext newContext();

   /**
    * Create new instance of FndConnectionManager valid for current framework.
    * @return a newly created instance of class that implements FndConnectionManager.
    */
   public abstract FndConnectionManager newConnectionManager();

   /**
    * Create a new statement valid for current framework.
    * @param connection the database connection to create a statement for
    * @return a subclass of FndStatement valid in current framework
    */
   public abstract FndStatement newStatement(FndConnection connection);
   
   /**
    * Flush the server's credentials cache.
    * Override for frameworks where this makes sense (FndJ2eeFramework).
    */
   public void flushCredentialsCache() {
      //No implementation
   }
}