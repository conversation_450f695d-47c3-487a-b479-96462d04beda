/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.storage;

import ifs.fnd.base.IfsException;
import ifs.fnd.base.SystemException;
import ifs.fnd.log.LogMgr;
import ifs.fnd.log.Logger;
import ifs.fnd.plsql.pool.TaggedConnection;
import ifs.fnd.plsql.pool.TimeEvent;
import ifs.fnd.record.FndSqlType;
import java.sql.SQLException;
import java.util.List;

/**
 * <B>Framework internal class:</B> A database connection in the tagged connection pool.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class FndTaggedConnection extends FndConnection {

   private TaggedConnection taggedConn;

    /**
     * Fatal error that has occurred on this connection.
     */
    private Exception fatalError;

   /** Creates a new instance of FndTaggedConnection */
   FndTaggedConnection(TaggedConnection taggedConn) {
      super(taggedConn.getJdbcConnection());
      this.taggedConn = taggedConn;
   }

    /**
     * Set the statement execution timeout for all statements using this connection.
     * @param statementExecutionTimeout Timeout in seconds, zero means there is no limit.
     */
    final void setStatementExecutionTimeout(int statementExecutionTimeout) {
        this.statementExecutionTimeout = statementExecutionTimeout;
    }

   /**
    * Notifies this connection about a possibly fatal error that has occurred on this connection.
    * If the specified error is a fatal error then this tagged connection will be marked for removal from the connection pool.
    * @param e SQL exception that has been caught on this connection
    */
   @Override
   void checkFatalError(SQLException e) {
      if(isFatalOraError(e)) {
         setFatalError(e);
      }
   }

   private static boolean isFatalOraError(SQLException e) {
      switch(e.getErrorCode()) {
         /**
          * Default Fatal Error Codes as predefined in WebLogic 12c:
          */
         case 3113:  // end-of-file on communication channel
         case 3114:  // not connected to ORACLE
         case 1033:  // ORACLE initialization or shutdown in progress
         case 1034:  // ORACLE not available
         case 1089:  // immediate shutdown in progress - no operations are permitted
         case 1090:  // shutdown in progress - connection is not permitted
         case 17002: // I/O exception
         /**
          * Other fatal errors:
          */
         case 600:   // internal error code
         case 603:   // ORACLE server session terminated by fatal error
         case 4061:  // Package state has been invalidated
            return true;
      }
      //
      // Uncomment the following line to test fatal error handling:
      // if(Math.random() < 0.5) return true;
      //
      return false;
   }


   /**
    * Sets a fatal error that has occurred on this connection.
    * @param e exception that has been classified as a fatal error
    */
   public void setFatalError(Exception e) {
      fatalError = e;
      Logger log = LogMgr.getDatabaseLogger();
      if(log.info) {
         log.info("Connection &1 has been marked with fatal error: &2", taggedConn.shortLabel(), e.getMessage());
      }
   }

   /**
    * Gets a fatal error that has occurred on this connection.
    * @return exception that has been classified as a fatal error
    */
   public Exception getFatalError() {
      return fatalError;
   }

   /**
    * Gets the wrapped TaggedConnection instance.
    */
   TaggedConnection getTaggedConnection() {
      return taggedConn;
   }

   /**
    * Marks a cursor as being accessed by the current request.
    * @param cursorId client-specific ID for a cursor
    */
   public void touchCursor(String cursorId) {
      taggedConn.touchCursor(cursorId);
   }

   /**
    * Gets the end user for this tagged connection.
    */
   public String getDirectoryId() {
      return taggedConn.getDirectoryId();
   }

   /**
    * Gets the language for this tagged connection.
    */
   public String getLanguage() {
      return taggedConn.getLanguage();
   }

   /**
    * Gets the client session ID for this tagged connection.
    */
   public String getSessionId() {
      return taggedConn.getClientSessionId();
   }

   /**
    * Sets the transaction ID for this tagged connection.
    */
   public void setTransactionId(String transactionId) {
      taggedConn.setTransactionId(transactionId);
   }

   /**
    * Gets the transaction ID for this tagged connection.
    */
   public String getTransactionId() {
      return taggedConn.getTransactionId();
   }

   /**
    * Adds an open cursor to this tagged connection.
    */
   public void addCursor(String cursorId, FndStatement stmt, List<FndSqlType> columnTypes) throws IfsException {
      try {
         FndTaggedCursor cursor = new FndTaggedCursor(this, cursorId, stmt, columnTypes);
         getTaggedConnection().addCursor(cursor);
      }
      catch (SQLException ex) {
         Logger log = LogMgr.getDatabaseLogger();
         if(log.debug) {
            log.debug("Error while adding cursor '&1'", ex.getMessage());
         }
         log.error(ex);
         throw new SystemException(ex, Texts.PROXYADDERROR, ex.getMessage());
      }
   }

   /**
    * Verifies that the specified cursor ID is set to a not null value.
    * @param cursorId client-specific ID for a cursor
    * @throws SystemException if the specified cursor ID is null
    */
   public void validateCursorIdNotNull(String cursorId) throws SystemException {
      if(cursorId == null) {
         throw new SystemException(Texts.PROXYCURSORNULL, taggedConn.getClientSessionId());
      }
   }

   /**
    * Throws an exception reporting an invalid cursor ID.
    * @param cursorId client-specific ID for a cursor
    * @throws SystemException if the specified cursor ID does not refer to an open cursor in the current session
    */
   private void cursorNotFoundError(String cursorId) throws IfsException {
      String sessionId = getSessionId();
      throw new SystemException(Texts.PROXYCURSORMISSING, cursorId, sessionId);
   }

   /**
    * Gets an open result set for given cursor ID.
    */
   public FndTaggedCursor getCursor(String cursorId) throws IfsException {
      FndTaggedCursor cursor = (FndTaggedCursor) taggedConn.getCursor(cursorId);
      if(cursor == null) {
         cursorNotFoundError(cursorId);
      }
      return cursor;
   }

   /**
    * Closes a cursor identified by specified cursor ID.
    */
   public void closeCursor(String cursorId) throws IfsException {

      boolean closed = false;

      try {
         closed = taggedConn.closeCursor(cursorId);
      }
      catch (SQLException ex) {
         Logger log = LogMgr.getDatabaseLogger();
         if(log.debug)
            log.debug("Error while closing cursor '&1'", ex.getMessage());
         log.error(ex);
         throw new SystemException(ex, Texts.PROXYCLOSEERROR, ex.getMessage());
      }

      if(!closed) {
         cursorNotFoundError(cursorId);
      }
   }

   /**
    * Removes a cursor from the list with abortable processes.
    * @param cursorId the cursor ID
    */
   public void unregisterCursor(String cursorId) {
      FndTaggedCursor cursor = (FndTaggedCursor) taggedConn.getCursor(cursorId);
      if(cursor != null) {
         cursor.unregisterCursor();
      }
   }

   /**
    * Creates and stores internally a new security checkpoint instance.
    * @param id identifier created by PLSQL, required to pass the security checkpoint gate.
    * @param gateId security checkpoint gate identifier
    */
   public void createSecurityCheckpoint(String id, String gateId, Logger log) {
      taggedConn.createSecurityCheckpoint(id, gateId, log);
   }

   /**
    * Commits the transaction on this connection.
    */
   public void commit() throws SystemException {
      Logger log = LogMgr.getDatabaseLogger();
      try {
         if(log.debug) {
            log.debug("Committing transaction on connection &1", toString());
         }

         taggedConn.commit();
      }
      catch (SQLException ex) {
         if(log.debug) {
            log.debug("Error while committing transaction '&1'", ex.getMessage());
         }
         log.error(ex);
         throw new SystemException(ex, Texts.PROXYCOMMITERROR, ex.getMessage());
      }
   }

   /**
    * Rolls back the transaction on this connection.
    */
   public void rollback() throws SystemException {
      Logger log = LogMgr.getDatabaseLogger();
      try {
         if(log.debug) {
            log.debug("Rolling back transaction on connection\n\t&1", taggedConn.label());
         }

         taggedConn.rollback();
      }
      catch (SQLException ex) {
         if(log.debug) {
            log.debug("Error during transaction rollback '&1'", ex.getMessage());
         }
         log.error(ex);
         throw new SystemException(ex, Texts.PROXYROLLBACKERROR, ex.getMessage());
      }
   }

   /**
    * Rolls back the transaction on this connection without throwing an exception.
    */
   public void safeRollback() {
      Logger log = LogMgr.getDatabaseLogger();
      if(log.debug) {
         log.debug("Rolling back transaction on connection\n\t&1", taggedConn.label());
      }
      taggedConn.safeRollback();
   }

   /**
    * Closes and removes this connection from the tagged connection pool.
    */
   public void destroy() {
      Logger log = LogMgr.getDatabaseLogger();
      taggedConn.destroy(log);
   }

   /**
    * Closes client (possibly dedicated) session.
    */
   public void endClientSession(Logger log) throws SystemException {
      taggedConn.endClientSession(log);
   }

   /**
    * Spool trace from PL/SQL call to the database logger.
    * The method does nothing if the database connection is uninitialized, because
    * Client_SYS.Get_Trace, used for spooling the trace, is a security protected PL/SQL method.
    */
   public void spoolPlSqlTrace(FndStatement stmt) throws IfsException {
      if(stmt.getSpoolPlsqlTrace() && getFndUser() != null) {
         stmt.spoolPlSqlTrace();
      }
   }

   /**
    * Gets time event for EndRequest operation.
    * @return time event
    */
   public TimeEvent getEndRequestEvent() {
      return taggedConn.getEndRequestEvent();
   }

   /**
    * Gets time event for GetTrace operation.
    * @return time event
    */
   public TimeEvent getGetTraceEvent() {
      return taggedConn.getGetTraceEvent();
   }
}
