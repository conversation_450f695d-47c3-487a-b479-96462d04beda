/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.cache;

/**
 * <B>Framework internal class:</B> Item that can be cached by the FndCacheManager.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public final class FndCacheItem implements FndCacheable {

   private java.util.Date dateofExpiration = null;
   private String identifier = null;
   private Object object = null;

   /**
    * Create a new FndCacheItem
    * @param obj Object to cache
    * @param id Item identity
    * @param minutesToLive lifespan in minutes
    */
   public FndCacheItem(Object obj, String id, int minutesToLive) {
      this.object = obj;
      this.identifier = id;

      if (minutesToLive != 0) {
         dateofExpiration = new java.util.Date();
         java.util.Calendar cal = java.util.Calendar.getInstance();
         cal.setTime(dateofExpiration);
         cal.add(java.util.Calendar.MINUTE, minutesToLive);
         dateofExpiration = cal.getTime();
      }
   }

   /**
    * Returns true if the item has expired from cache
    * @return true if the item has expired
    */
   public boolean isExpired() {
      if (dateofExpiration != null) {
         if (dateofExpiration.before(new java.util.Date()))
            return true;
         else
            return false;
      }
      else
         return false;
   }

   /**
    * Get the identifier for a cache item.
    * @return cache identifier
    */
   public String getIdentifier() {
      return identifier;
   }

   /**
    * Get the object held by the cache item
    * @return the cached object
    */
   public Object getObject() {
      return object;
   }
}
