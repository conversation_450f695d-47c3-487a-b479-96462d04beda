/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.storage;

import ifs.fnd.base.IfsException;
import ifs.fnd.services.plsqlserver.*;
import ifs.fnd.sf.FndServerContext;

/**
 * <B>Framework internal class:</B> Class that gives access to PLSQL.
 * In case of standalone framework this class behaves like FndAbstractPlsqlWrapper and calls
 * storage code directly. In case of J2EE framework this class calls storage code through
 * the StandardEntityHandler bean.
 * Note that public method executePLSQLCursor that is inherited from FndAbstractPlsqlWrapper calls
 * storage code directly both in J2EE and in STA frameworks.
 * @see ifs.fnd.sf.FndServerContext#getStandardStorageWrapper
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class FndPlsqlAccess extends FndAbstractPlsqlWrapper implements FndPlsqlStorage {

   /**
    * Performs a PLSQL invocation request.
    * The method first fetches a storage wrapper from the current context. If a not-null wrapper
    * is returned then it is called, otherwise FndAbstractPlsqlWrapper implementation is called.
    * @see ifs.fnd.sf.FndServerContext#getStandardStorageWrapper
    */
   @Override
   public PlsqlInvocation invoke(PlsqlInvocation plsqlInvocation) throws IfsException {
      FndServerContext ctx = FndServerContext.getCurrentServerContext();
      FndPlsqlStorage wrapper = (FndPlsqlStorage) ctx.getStandardStorageWrapper();
      if(wrapper != null) {
         try {
            return wrapper.invoke(plsqlInvocation);
         }
         catch(RuntimeException err) {
            ctx.checkRuntimeException(err, "invoke");
            throw err;
         }
      }
      else
         return super.invoke(plsqlInvocation);
   }

   /**
    * Performs a PLSQL invocation request containing only SELECT statements.
    * The method first fetches a storage wrapper from the current context. If a not-null wrapper
    * is returned then it is called, otherwise FndAbstractPlsqlWrapper implementation is called.
    * @see ifs.fnd.sf.FndServerContext#getStandardStorageWrapper
    */
   @Override
   public PlsqlInvocation invokeSelect(PlsqlInvocation plsqlInvocation) throws IfsException {
      FndServerContext ctx = FndServerContext.getCurrentServerContext();
      FndPlsqlStorage wrapper = (FndPlsqlStorage) ctx.getStandardStorageWrapper();
      if(wrapper != null) {
         return wrapper.invokeSelect(plsqlInvocation);
      }
      else
         return super.invokeSelect(plsqlInvocation);
   }

   /**
    * Performs a PLSQL invocation request containing one command that must not modify database.
    * The method first fetches a storage wrapper from the current context. If a not-null wrapper
    * is returned then it is called, otherwise FndAbstractPlsqlWrapper implementation is called.
    * @see ifs.fnd.sf.FndServerContext#getStandardStorageWrapper
    */
   @Override
   public PlsqlInvocation invokeQuery(PlsqlInvocation plsqlInvocation) throws IfsException {
      FndServerContext ctx = FndServerContext.getCurrentServerContext();
      FndPlsqlStorage wrapper = (FndPlsqlStorage) ctx.getStandardStorageWrapper();
      if(wrapper != null) {
         return wrapper.invokeQuery(plsqlInvocation);
      }
      else
         return super.invokeQuery(plsqlInvocation);
   }
}


