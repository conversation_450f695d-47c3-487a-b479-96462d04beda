/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf;

import ifs.fnd.base.FndTranslatableText;

/**
 * This class contains translatable texts used by the framework.
 */
final class Texts {

   private static final String PKG = Texts.class.getPackage().getName();

   private Texts() {
      //Prevent instantiations
   }

   //FndUserFeedback
   static final FndTranslatableText MANUALDECISIONNEEDED = new FndTranslatableText("MANUALDECISIONNEEDED", "Manual decision needed", PKG);
   static final FndTranslatableText BADANSWER = new FndTranslatableText("BADANSWER", "Invalid answer to question", PKG);
   static final FndTranslatableText NOANSWER = new FndTranslatableText("NOANSWER", "Mandatory manual decision encountered in non-interactive code", PKG);

   //SecurityCheckpoint
   static FndTranslatableText NOJDBCDRIVER      = new FndTranslatableText("NOJDBCDRIVER", "Error when loading JDBC driver", PKG);
   static FndTranslatableText AUTHMETHODMISSING = new FndTranslatableText("AUTHMETHODMISSING", "Missing IFS property defining authentication method", PKG);
   static FndTranslatableText AUTHURLMISSING    = new FndTranslatableText("AUTHURLMISSING", "Missing IFS property defining Authentication provider URL", PKG);
   static FndTranslatableText CHECKPOINT_USERNAME = new FndTranslatableText("CHECKPOINT_USERNAME", "Failure in Security Checkpoint &1, only current user may use terminal.", PKG);
   static FndTranslatableText INVALID_CREDENTIALS = new FndTranslatableText("INVALID_CREDENTIALS", "Invalid username or password.", PKG);

   //FndServerContext
   static final FndTranslatableText IMPERSONATEUSER = new FndTranslatableText("IMPERSONATEUSER","Access denied due to system privilege configuration", PKG);
}