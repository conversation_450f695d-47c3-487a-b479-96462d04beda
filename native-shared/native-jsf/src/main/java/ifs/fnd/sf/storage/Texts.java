/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.storage;

import ifs.fnd.base.FndTranslatableText;

/**
 * This class contains translatable texts used by the framework.
 */
final class Texts {

   private static final String PKG = Texts.class.getPackage().getName();

   private Texts() {
      //Prevent instantiations
   }

   //FndSqlStorage
   static final FndTranslatableText FNDPOPULATENOTFOUND = new FndTranslatableText("FNDPOPULATENOTFOUND", "Instance of view &1 with primary key [&2] not found in database.", PKG);
   static final FndTranslatableText IMPORTNOTALLOWED = new FndTranslatableText("IMPORTNOTALLOWED", "Import of view &1 is not allowed.", PKG);
   static final FndTranslatableText PREPARENOTINST = new FndTranslatableText("PREPARENOTINST", "Module not installed, new objects of type &1 can't be created", PKG);
   static final FndTranslatableText SAVEREADONLY = new FndTranslatableText("SAVEREADONLY", "Read-only view &1 cannot be saved in database", PKG);
   static final FndTranslatableText NOOBJVERSIONREMOVE = new FndTranslatableText("NOOBJVERSIONREMOVE", "Cannot remove entity because &1 has no value", PKG);
   static final FndTranslatableText INMGRNULL = new FndTranslatableText("INMGRNULL", "Null stream returned from FndInputStreamManager for attribute &1", PKG);
   static final FndTranslatableText UPDLOB = new FndTranslatableText("UPDLOB", "Could not get LOB locator (row is missing).", PKG);
   static final FndTranslatableText UPDATELOB = new FndTranslatableText("UPDATELOB", "Unable to update BLOB/CLOB. Message is: &1", PKG);
   static final FndTranslatableText OBJCHANGED = new FndTranslatableText("OBJCHANGED", "&1 has been changed by another user.", PKG);
   static final FndTranslatableText OBJREMOVE = new FndTranslatableText("OBJREMOVE", "&1 has been removed by another user.", PKG);
   static final FndTranslatableText ROWCHNGFAIL = new FndTranslatableText("ROWCHNGFAIL", "Failed checking for changed object", PKG);
   static final FndTranslatableText EXISTNOTINST = new FndTranslatableText("EXISTNOTINST", "Module not installed, existance of an object of type &1 can't be checked", PKG);
   static final FndTranslatableText EXISTFAIL = new FndTranslatableText("EXISTFAIL", "Failed checking for existence of &1 instance. Error is: &2", PKG);
   static final FndTranslatableText GETOBJIDFAIL = new FndTranslatableText("GETOBJIDFAIL", "Failed getting OBJID for &1 instance. Error is: &2", PKG);
   static final FndTranslatableText GETOBJIDNOTFOUND = new FndTranslatableText("GETOBJIDNOTFOUND", "Instance of entity &1 with primary key [&2] not found in database.", PKG);
   static final FndTranslatableText GETNOTINST = new FndTranslatableText("GETNOTINST", "Component not installed, objects of type &1 can't be fetched", PKG);
   static final FndTranslatableText FETCHFAIL = new FndTranslatableText("FETCHFAIL", "Fetching data from database failed", PKG);
   static final FndTranslatableText FETCHABORT = new FndTranslatableText("FETCHABORT", "User requested abort of the current operation", PKG);
   static final FndTranslatableText FETCHFAIL2 = new FndTranslatableText("FETCHFAIL2", "Unable to fetch result from database. Error is: &1", PKG);
   static final FndTranslatableText FETCHATTRCOUNT = new FndTranslatableText("FETCHATTRCOUNT", "Invalid (transformed?) record of type &1 passed to query or get. The number of attributes (&2) is greater than the number of attributes (&3) in a new record of that type. Unexpected attributes: &4", PKG);
   static final FndTranslatableText RESULTSETCLOSE = new FndTranslatableText("RESULTSETCLOSE", "Failed closing result set", PKG);
   static final FndTranslatableText RESULTCACHECLONE = new FndTranslatableText("RESULTCACHECLONE", "Unable to clone record in cache.", PKG);
   static final FndTranslatableText NOOBJID = new FndTranslatableText("NOOBJID", "ObjId attribute has no value", PKG);
   static final FndTranslatableText NOOBJVERSION = new FndTranslatableText("NOOBJVERSION", "ObjVersion attribute has no value", PKG);
   static final FndTranslatableText NOPARENTKEYINPARENT = new FndTranslatableText("NOPARENTKEYINPARENT", "Undefined parent-key in master-detail connection &1 - &2", PKG);

   //FndSqlStorageUtil
   static final FndTranslatableText COLEXCLUDED = new FndTranslatableText("COLEXCLUDED", "Can not add column name for excluded attribute &1", PKG);
   static final FndTranslatableText APPENDSQLFUNC = new FndTranslatableText("APPENDSQLFUNC", "Could not append SQL function: &1", PKG);
   static final FndTranslatableText APPENDSQLFUNCPAT = new FndTranslatableText("APPENDSQLFUNCPAT", "Invalid pattern for SQL function [&1]", PKG);
   static final FndTranslatableText DUPLICATEPK = new FndTranslatableText("DUPLICATEPK", "An instance of &1 with primary key [&2] = [&3] already exists", PKG);
   static final FndTranslatableText NOKEYMOD = new FndTranslatableText("NOKEYMOD", "Key attribute &1 can not be modified.", PKG);
   static final FndTranslatableText NOSTATEMOD = new FndTranslatableText("NOSTATEMOD", "Entity state attribute &1 may not be modified.", PKG);
   static final FndTranslatableText COPYPKFROMPARENT = new FndTranslatableText("COPYPKFROMPARENT", "Cannot copy parent key from parent to detail &1.", PKG);
   static final FndTranslatableText COPYPKTOPARENT = new FndTranslatableText("COPYPKTOPARENT", "Cannot copy parent key from detail &1 to parent.", PKG);
   static final FndTranslatableText PROPAGATEREFCOUNT = new FndTranslatableText("PROPAGATEREFCOUNT", "Attribute count mismatch in references &1 and &2.", PKG);
   static final FndTranslatableText PROPAGATEREFTYPE = new FndTranslatableText("PROPAGATEREFTYPE", "Attribute type mismatch in references &1 and &2.", PKG);
   static final FndTranslatableText DOMAINOP = new FndTranslatableText("DOMAINOP", "Domain operator not allowed.", PKG);
   static final FndTranslatableText APPBETWEENCOND = new FndTranslatableText("APPBETWEENCOND", "Between condition needs two arguments, not &1", PKG);
   static final FndTranslatableText DATNUMLIKECOND = new FndTranslatableText("DATNUMLIKECOND", "Like condition on DATE/NUMBER attribute needs two arguments, not &1", PKG);
   static final FndTranslatableText QRYCONDATTRTYPE = new FndTranslatableText("QRYCONDATTRTYPE", "Incompatible attribute types in query condition: &1, &2", PKG);
   static final FndTranslatableText DETAILCONDERR = new FndTranslatableText("DETAILCONDERR", "A detail condition has been created between two records not having any relation. Records are &1 (master) and &2 (detail).", PKG);
   static final FndTranslatableText LOBGRPBY = new FndTranslatableText("LOBGRPBY", "LOB columns can not be used in GROUP BY clause.", PKG);
   static final FndTranslatableText PKEYNOTSET = new FndTranslatableText("PKEYNOTSET", "Can not append primary key condition since key attribute &1.&2 is not set.", PKG);
   static final FndTranslatableText FNDFILTERINSERTEND1 = new FndTranslatableText("FNDFILTERINSERTEND1", "Missing end @-marker in stereotype filter definition insert value: &1", PKG);
   static final FndTranslatableText FNDFILTERTABLE = new FndTranslatableText("FNDFILTERTABLE", "Table name missing in FROM part of filter: &1", PKG);
   static final FndTranslatableText FNDFILTERALIAS = new FndTranslatableText("FNDFILTERALIAS", "Alias missing in FROM part of filter: &1", PKG);
   static final FndTranslatableText FNDFILTERMANY = new FndTranslatableText("FNDFILTERMANY", "Unexpected token '&1' in FROM part of a filter.", PKG);
   static final FndTranslatableText FNDVALUEPARAMEND = new FndTranslatableText("FNDVALUEPARAMEND", "Missing end @-marker in reference to context parameter: [&1]", PKG);
   static final FndTranslatableText FNDVALUEPARAMBAD = new FndTranslatableText("FNDVALUEPARAMBAD", "Invalid reference to context parameter: [&1]", PKG);
   static final FndTranslatableText RESOLVECTXPARAM = new FndTranslatableText("RESOLVECTXPARAM", "Could not resolve reference to context parameter", PKG);
   static final FndTranslatableText RESOLVECTXPARAMCOMP = new FndTranslatableText("RESOLVECTXPARAMCOMP", "Illegal reference to a compound context attribute from simple attribute value: [&1]", PKG);
   static final FndTranslatableText FNDFILTERPARAMEND = new FndTranslatableText("FNDFILTERPARAMEND", "Missing end @-marker in filter definition: &1", PKG);
   static final FndTranslatableText FNDBADPARENTKEY = new FndTranslatableText("FNDBADPARENTKEY", "The value [&1] in key attribute &2 does not match the database value [&3]", PKG);
   static final FndTranslatableText FNDMISSINGPARENTKEY = new FndTranslatableText("FNDMISSINGPARENTKEY", "Missing parent-key connection to detail record &1.", PKG);
   static final FndTranslatableText FNDBADDETAILKEY = new FndTranslatableText("FNDBADDETAILKEY", "Mismatch in parent key. Master &1 = [&2]. Detail &3 = [&4].", PKG);
   static final FndTranslatableText PREPAREPK = new FndTranslatableText("PREPAREPK", "Cannot access database record since primary key attribute &1 has no value", PKG);
   static final FndTranslatableText NOVALUELOB = new FndTranslatableText("NOVALUELOB", "Can not update LOB columns because &1 has no value", PKG);
   static final FndTranslatableText NOPKCONDITION = new FndTranslatableText("NOPKCONDITION", "Cannot create query condition. Missing primary key for &1", PKG);
   static final FndTranslatableText CRUDEMPTYETAG =  new FndTranslatableText("CRUDEMPTYETAG", "Empty ETag returned from CRUD operation", PKG);
   static final FndTranslatableText CRUDGETPKNOTFOUND = new FndTranslatableText("CRUDGETPKNOTFOUND", "Instance of entity &1 with objid [&2] not found in database.", PKG);
   static final FndTranslatableText CRUDGETPKFAIL = new FndTranslatableText("CRUDGETPKFAIL", "Failed getting primary key for entity &1. Error is: &2", PKG);

   //FndFilterCache
   static final FndTranslatableText GETSTDFILTER = new FndTranslatableText("GETSTDFILTER", "Standard filter [&1] not found in workspace [&2]", PKG);
   static final FndTranslatableText GETPERMSETFILTER = new FndTranslatableText("GETPERMSETFILTER", "Permission Set Filter [&1] not found in workspace [&2]", PKG);
   static final FndTranslatableText GETFILTERENTRY = new FndTranslatableText("GETFILTERENTRY", "Failed to get filter data from database: &1", PKG);

   //FndEntityHandler
   static final FndTranslatableText NOTNULLTARGETSTATE = new FndTranslatableText("NOTNULLTARGETSTATE", "Not null target state passed to FndEntityHandler.&1(), which does not support changing of entity state.", PKG);

   //FndStatement
   static final FndTranslatableText STMTABORTED = new FndTranslatableText("STMTABORTED", "Request has been timed out", PKG);

   //FndAbstractHandler
   static final FndTranslatableText NOACTIVITY = new FndTranslatableText("NOACTIVITY", "Operation &1.&2 is not granted to any user", PKG);
   static final FndTranslatableText NOACTIVITYGRANT = new FndTranslatableText("NOACTIVITYGRANT", "User &1 has not been granted access to activity &2", PKG);
   static final FndTranslatableText NOACTIVITYNAME = new FndTranslatableText("NOACTIVITYNAME", "Activity name missing for &1.&2. Please regenerate the Wrapper class.", PKG);

  //FndStorageUtil
   static final FndTranslatableText GETDIRECTORYID = new FndTranslatableText("GETDIRECTORYID", "Failed to get directory ID from database: &1", PKG);
   static final FndTranslatableText NOFNDUSERID = new FndTranslatableText("NOFNDUSERID", "FND user with identity [&1] does not exists", PKG);

   //FndTaggedConnection
   static final FndTranslatableText PROXYCURSORMISSING = new FndTranslatableText("PROXYCURSORMISSING", "Cursor [&1] is not an open cursor in session [&2].", PKG);
   static final FndTranslatableText PROXYCURSORNULL = new FndTranslatableText("PROXYCURSORNULL", "Null cursor ID has been used to identify a cursor in session [&1].", PKG);
   static final FndTranslatableText PROXYADDERROR = new FndTranslatableText("PROXYADDERROR", "Error while adding cursor '&1'", PKG);
   static final FndTranslatableText PROXYCLOSEERROR = new FndTranslatableText("PROXYCLOSEERROR", "Error while closing cursor '&1'", PKG);
   static final FndTranslatableText PROXYCOMMITERROR = new FndTranslatableText("PROXYCOMMITERROR", "Error while committing transaction '&1'", PKG);
   static final FndTranslatableText PROXYROLLBACKERROR = new FndTranslatableText("PROXYROLLBACKERROR", "Error during transaction rollback '&1'", PKG);

   //FndTaggedConnectionManager
   static final FndTranslatableText CONNECTIONDESTROYEDINIT = new FndTranslatableText("CONNECTIONDESTROYEDINIT", "Database connection has been destroyed because of an error during initialization of the client session and/or request context: &1", PKG);
   static final FndTranslatableText NOSECURITYCHECKPOINT = new FndTranslatableText("NOSECURITYCHECKPOINT", "There is no pending security checkpoint waiting for re-authentication on client session [&1]", PKG);

   //FndClientLogon
   static final FndTranslatableText TOO_MANY_OS_USERS = new FndTranslatableText("TOO_MANY_OS_USERS", "IFS account is already logged in by &1. Contact your system administrator!.", PKG);
}