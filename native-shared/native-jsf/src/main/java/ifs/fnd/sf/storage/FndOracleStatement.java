/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.storage;

import ifs.fnd.base.*;
import ifs.fnd.record.*;
import java.io.*;
import java.sql.*;
import oracle.jdbc.OracleTypes;

/**
 * <B>Framework internal class:</B> Oracle database statement.
 *
 * This class contains code that depends on Oracle JDBC driver.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public abstract class FndOracleStatement extends FndStandardStatement {

    /**
     * Creates a new instance of FndOracleStatement.
     */
    protected FndOracleStatement(FndConnection fndConnection) {
       super(fndConnection);
    }

    /**
     * Update a BLOB
     */
   @Override
   protected void updateBlob(int colNr, byte[] value) throws IfsException {
      InputStream in = new ByteArrayInputStream(value);
      Blob blob = getBlob(colNr);     
        
      try (OutputStream out = blob.setBinaryStream(1L)) {
         byte[] buf = new byte[DEFAULT_BUFFER_SIZE];
         int len;
         while ((len = in.read(buf)) != -1) {
            out.write(buf, 0, len);
         }
         out.flush();
      }
      catch (SQLException | IOException e) {
         if (e instanceof SQLException) {
            checkError((SQLException)e);
         }
         throw new SystemException(e, "STMTUPDBLOB:Could not update BLOB. Message is: &1", e.getMessage());
      }
      finally {
         if (blob != null) {
            try {
               blob.free();
            }
            catch (SQLException e) {
               log.error(e, "Failed freeing blob");
            }
         }
      }
   }

    /**
     * Update a BLOB from an InputStream.
     */
   @Override
   public void updateBlob(int colNr, FndInputStreamManager inputMgr) throws IfsException {    
      Blob blob = getBlob(colNr);
      try (InputStream in = inputMgr.getInputStream(); OutputStream out = getBlobOutputStream(blob)) {
         byte[] buf = new byte[DEFAULT_BUFFER_SIZE];
         int len;
         while ((len = in.read(buf)) != -1) {
            out.write(buf, 0, len);
         }
         out.flush();
         inputMgr.close();
      }
      catch (SQLException | IOException e) {
         if (e instanceof SQLException) { 
            checkError((SQLException)e);
         }
         throw new SystemException(e, "STMTUPDBLOB:Could not update BLOB. Message is: &1", e.getMessage());
      }
      finally {
         if (blob != null) {
            try {
               blob.free();
            }
            catch (SQLException e) {
               log.error(e, "Failed freeing blob");
            }
         }
      }
   }

    /**
     * Update a CLOB.
     */
   @Override
   protected void updateClob(int colNr, String value) throws IfsException {
      Reader in = new StringReader(value);
      Clob clob = getClob(colNr);
      
      try (Writer w = clob.setCharacterStream(1L)) {
         char [] buf = new char[getBufferSize(clob)];
         int len;
         while((len = in.read(buf)) != -1){
            w.write(buf, 0, len);
         }
         w.flush();
      }
      catch (SQLException | IOException e) {
         if (e instanceof SQLException) { 
            checkError((SQLException)e);
         }
         throw new SystemException(e, "STMTUPDCLOB:Could not update long text. Message is: &1", e.getMessage());
      }
      finally {
         if (clob != null) {
            try {
               clob.free();
            }
            catch (SQLException e) {
               log.error(e, "Failed freeing clob");
            }
         }
      }
   }

   private static OutputStream getBlobOutputStream(final java.sql.Blob blob) throws SQLException {
      return blob.setBinaryStream(0L);
   }

   private static Writer getClobOutputStream(final java.sql.Clob clob) throws SQLException {
      return clob.setCharacterStream(0L);
   }

    /**
     * Convert a FndSqlType to it's corresponding Java SQL type.
     * This method maps FndSqlType.TIMESTAMP to Oracle specific type DATE
     * and FndSqlType.REF_CURSOR to OracleTypes.CURSOR.
     * All other types are converted by calling super.toJavaSqlType().
     * @param type Source FndSqlType
     * @return Java SQL type
     * @throws SystemException
     */
   @Override
   protected int toJavaSqlType(FndSqlType type) throws SystemException {
      if (type == FndSqlType.TIMESTAMP) {
         return OracleTypes.DATE;
      }
      else if (type == FndSqlType.REF_CURSOR) {
         return OracleTypes.CURSOR;
      }
      else {
         return super.toJavaSqlType(type);
      }
   }
}
