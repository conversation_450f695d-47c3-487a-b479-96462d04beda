/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.sf.storage;

/**
 * <B>Framework internal class:</B> Class representing the current transaction.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 *
 * This class stores a key identifying the parameters used to initialize the current connection
 * by calling PLSQL procedure Login_SYS.Init_Fnd_Session_.
 *
 * @see ifs.fnd.sf.FndServerContext#getCurrentTransaction()
 */
public class FndTransaction {

   private String initializationKey;

   private String fndUser;

   /**
    * Gets a key identifying the parameters used to initialize the current connection.
    * @return key identifying the parameters passed to <PERSON>gin_SYS.Init_Fnd_Session_,
    *          or null if not initialized
    */
   String getInitializationKey() {
      return initializationKey;
   }

   /**
    * Sets a key identifying the parameters used to initialize the current connection.
    * @param key key identifying the parameters passed to Login_SYS.Init_Fnd_Session_.
    */
   void setInitializationKey(String key) {
      this.initializationKey = key;
   }

   /**
    * Gets FND user returned from Login_SYS.Init_Fnd_Session_ used to initialize the current connection.
    * @return FND user identity
    */
   String getFndUser() {
      return fndUser;
   }

   /**
    * Sets FND user returned from Login_SYS.Init_Fnd_Session_ used to initialize the current connection.
    * @param fndUser FND user identity
    */
   void setFndUser(String fndUser) {
      this.fndUser = fndUser;
   }

   /**
    * Gets an integer identifying this FndTransaction instance in debug output.
    * @return identity hash code of this instance
    */
   public int getId() {
      return System.identityHashCode(this);
   }

   @Override
   public String toString() {
      return "{FndTransaction: id=" + getId() + " key=" + initializationKey + '}';
   }
}
