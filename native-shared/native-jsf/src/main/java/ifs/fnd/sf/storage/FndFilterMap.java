/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.storage;

import ifs.fnd.log.*;
import ifs.fnd.record.*;
import ifs.fnd.util.ResetableListMap;

import java.util.*;

/**
 * <B>Framework internal class:</B> Stack-oriented map with active FndFilters per meta-view (or meta-compound-attribute).
 * Filters are activated (added to the map) by calling addFilter(). The only way to
 * remove (deactivate) filters from the map is to call reset(). To be able to call
 * reset() one must first call mark(), which returns a handle that identifies the
 * state of the map. Unmatched calls to mark/reset will result in a SystemException.
 * Access to this class is not synchronized, because it is instantiated by
 * FndServerContext, which is thread-local.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class FndFilterMap {

   private final ResetableListMap<String, FndFilter> map;   // (pkg.view -> list of FndFilter)

   public FndFilterMap() {
      map = ResetableListMap.newMap("FndFilterMap");
   }

//   /**
//    * Create the key used to identify given view in the filter map: PKG.VIEW
//    */
//   private String createKey(FndRecordMeta metaView) {
//      return "VIEW:" + metaView.getPackage() + "." + metaView.getType();
//   }
//
//   /**
//    * Create the key used to identify given attribute in the filter map: PKG.VIEW.ATTR
//    */
//   private String createKey(FndCompoundAttributeMeta metaAttr) {
//      FndRecordMeta owner = metaAttr.getRecordMeta();
//      return "ATTR:" + owner.getPackage() + "." + owner.getType() + "." + metaAttr.getName();
//   }
//
//   /**
//    * Create the key used to identify given reference in the filter map: PKG.VIEW.REF
//    */
//   private String createKey(FndCompoundReferenceMeta metaRef) {
//      FndRecordMeta owner = metaRef.getOwner();
//      return "REF:" + owner.getPackage() + "." + owner.getType() + "." + metaRef.getName();
//   }
//
   /**
    * Adds a meta-view-to-filter mapping to the map.
    * @param metaView the view to attach a filter to
    * @param filter filter to be attached to the above view
    */
   public void addFilter(FndRecordMeta metaView, FndFilter filter) {
      addFilterImpl(FndFilterCache.Entry.createKey(metaView), filter);
   }

   /**
    * Adds a meta-attribute-to-filter mapping to the map.
    * The filter will be used for elements of the specified meta-array/aggregate.
    * @param metaAttr the compound attribute to attach a filter to
    * @param filter filter to be attached to the above attribute
    */
   public void addFilter(FndCompoundAttributeMeta metaAttr, FndFilter filter) {
      addFilterImpl(FndFilterCache.Entry.createKey(metaAttr), filter);
   }

   /**
    * Adds a meta-reference-to-filter mapping to the map.
    * The filter will be used to validate references during save operation.
    * @param metaRef the meta-reference to attach a filter to
    * @param filter filter to be attached to the above reference
    */
   public void addFilter(FndCompoundReferenceMeta metaRef, FndFilter filter) {
      addFilterImpl(FndFilterCache.Entry.createKey(metaRef), filter);
   }

   /**
    * Adds a meta-reference-to-filter mapping to the map.
    * The filter will be used to validate references during save operation.
    * @param metaRef the meta-reference to attach a filter to
    * @param filter filter to be attached to the above reference
    */
   public void addFilter(FndFilterCache.Entry entry) {
      addFilterImpl(entry.getKey(), entry.getFndFilter());
   }

   /**
    * Adds a filter to the map.
    * @param key string that identifies the meta-view to attach a filter to
    * @param filter filter to be attached to the above view
    */
   private void addFilterImpl(String key, FndFilter filter) {
      boolean skipped = !map.add(key, filter);
      Logger log = LogMgr.getDatabaseLogger();
      if(log.debug) {
         log.debug("ifs.fnd.sf.storage.FndFilterMap.addFilter(): skipped=&1", String.valueOf(skipped));
         log.debug("   view/attr = &1", key);
         log.debug("   filter    = &1.&2", filter.getPackage(), filter.getName());
         log.debug("   from      = &1", filter.getFrom());
         log.debug("   where     = &1", filter.getWhere());
         map.debug(log);
      }
   }

   /**
    * Retrieves all filters attached to given view.
    * @return an iterator over active filters
    */
   public Iterator<FndFilter> getFilters(FndRecordMeta metaView) {
      return map.iterator(FndFilterCache.Entry.createKey(metaView)); // return map.iterator(createKey(metaView));
   }

   /**
    * Retrieves all filters attached to given compound attribute.
    * @return an iterator over active filters
    */
   public Iterator<FndFilter> getFilters(FndCompoundAttributeMeta metaAttr) {
      return map.iterator(FndFilterCache.Entry.createKey(metaAttr)); // return map.iterator(createKey(metaAttr));
   }

   /**
    * Retrieves all filters attached to given reference.
    * @return an iterator over active filters
    */
   public Iterator<FndFilter> getFilters(FndCompoundReferenceMeta metaRef) {
      return map.iterator(FndFilterCache.Entry.createKey(metaRef)); // return map.iterator(createKey(metaRef));
   }

   /**
    * Class that represents a state of the filter map.
    */
   public static class State {
      private final ResetableListMap.State<FndFilter> state;

      State(ResetableListMap.State<FndFilter> state) {
         this.state = state;
      }
   }

   /**
    * Marks the current state of the filter map.
    * @return an instance of nested class State that identifies the current state of the map
    */
   public State mark() {
      return new State(map.mark());
   }

   /**
    * Resets the state of the filter map.
    * @param state an instance of nested class State returned by mark()
    * @throws IllegalStateException if the specified state does not match the state returned by the last call to mark()
    */
   public void reset(State state) throws IllegalStateException {
      map.reset(state.state);
   }

   @Override
   public String toString() {
      return map.toString();
   }
}

