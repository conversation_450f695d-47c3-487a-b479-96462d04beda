/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.sta;

import ifs.fnd.base.FndContext;
import ifs.fnd.sf.FndServerFramework;
import ifs.fnd.sf.storage.FndConnectionManager;
import ifs.fnd.sf.storage.FndStatement;
import ifs.fnd.sf.storage.FndConnection;
import ifs.fnd.sf.storage.FndOracleStatementForOracle;

/**
 * <B>Framework internal class:</B> Class representing standalone server framework.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class FndStaFramework extends FndServerFramework {

   /**
    * Return string representing the type of this server framework.
    * @return Constant FndFramework.STA representing standalone server framework.
    */
   @Override
   public String getType() {
      return STA;
   }

   /**
    * Create new instance of FndContext valid for current framework.
    * @return a newly created extension of FndServerContext
    */
   @Override
   public FndContext newContext() {
      return new FndStaContext();
   }

   /**
    * Create new instance of FndConnectionManager valid for standalone framework.
    * @return a newly created instance that implements FndConnectionManager interface
    */
   @Override
   public FndConnectionManager newConnectionManager() {
      return new FndStaConnectionManager();
   }

   /**
    * Create a new statement valid for standalone framework.
    * @param connection the database connection to create a statement for
    * @return a subclass of FndStatement valid in standalone framework
    */
   @Override
   public FndStatement newStatement(FndConnection connection) {
      return new FndOracleStatementForOracle(connection);
   }
}