/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.storage;

import ifs.fnd.log.*;
import ifs.fnd.base.FndContext;
import ifs.fnd.base.IfsException;
import ifs.fnd.base.SystemException;
import ifs.fnd.base.IfsRuntimeException;
import ifs.fnd.record.FndSqlType;
import ifs.fnd.record.FndSqlValue;
import ifs.fnd.record.FndAttribute;
import ifs.fnd.record.FndRecord;
import ifs.fnd.record.serialization.FndAutoString;
import ifs.fnd.service.ReauthenticationResponse;
import ifs.fnd.sf.FndServerContext;
import ifs.fnd.sf.FndUserFeedback;
import ifs.fnd.util.Message;
import ifs.fnd.entities.fnduser.FndUserCache;
import ifs.fnd.service.IfsConstants;
import ifs.fnd.sf.FndSecurityCheckpoint;
import ifs.fnd.util.Str;

import java.sql.Connection;
import java.sql.SQLException;


/**
 * <B>Framework internal class:</B> Abstract class for management of database connections.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public abstract class FndAbstractConnectionManager implements FndConnectionManager {

   /**
    * Data source name for FNDBAS database
    */
   static final String FNDBAS_DATA_SOURCE = "fndbas";

   /**
    * Array with classes that are allowed to access system database connection.
    */
   private static final Class[] systemConnectionCallers = new Class[] {FndTermDefinitionStorage.class,
                                                                       FndUserCache.DbAccess.class,
                                                                       FndStorageUtil.DbAccess.class};

   /**
    * Framework specific (J2EE or STA) method for getting a database connection.
    * Username and password may be null if this information is included in a configuration file.
    * @param username the database user on whose behalf the connection is being made.
    * @param password the user's password.
    * @return database connection
    */
   protected abstract FndConnection getConnection(String username, String password) throws IfsException;

   @Override
   public boolean isTaggedConnectionStateful() {
      return false;
   }

   @Override
   public boolean isTaggedConnectionMode() {
      return false;
   }

   /**
    * Get a database connection for a PLSQL session.
    * @param user     user name passed to PLSQL initialization procedure
    * @param language this parameter is ignored, instead current language is passed to PLSQL initialization procedure
    * @return database connection
    */
   @Override
   public FndConnection getPlsqlConnection(String user, String language) throws IfsException {
      return getPlsqlConnectionImpl(user);
   }

   /**
    * Get a database connection for a PLSQL session.
    * The method is used by Plsql Gateway.
    * If this method is called in proxy-user mode then the caller must commit/rollback
    * the transaction before closing/returning this connection.
    * @param user user name passed to PLSQL initialization procedure
    * @return database connection
    */
   @Override
   public FndConnection getPlsqlGatewayConnection(String user) throws IfsException {
      return getPlsqlConnectionImpl(user);
   }

   /**
    * Get a database connection for a PLSQL session.
    * @param user        user name passed to PLSQL initialization procedure
    * @return database connection
    */
   private FndConnection getPlsqlConnectionImpl(String user) throws IfsException {

      Logger log = LogMgr.getDatabaseLogger();
      FndServerContext ctx = FndServerContext.getCurrentServerContext();

      if (log.debug) {
         log.debug("(getPlsqlConnection user=&1)", user);
      }

      FndConnection connection = getConnection(null, null);
      connection.setPlsqlConnection();
      connection.setConnectionManager(this);
      if(ctx.isSqlTraceOn()) {
         openSqlTrace(connection);
      }

      return connection;
   }

   /**
    * Get a PLSQL connection for current application user and current language.
    * @return database connection
    */
   @Override
   public FndConnection getPlsqlConnection() throws IfsException {
      String user = FndServerContext.getCurrentApplicationUser();
      return getPlsqlConnectionImpl(user);
   }

   /**
    * Gets a system user (IFSSYS) connection to database.
    * The method will call the callback method setSystemConnection.
    * The created connection will be not initialized by a call to Init_Fnd_Session.
    * Only selected classes in java server framework are allowed to call this method.
    * @param caller an instance that wants to use a system connection
    * @param param an object that will be passed to the callback method setSystemConnection
    * @see FndSystemConnectionAccess#setSystemConnection FndSystemConnectionAccess.setSystemConnection
    */
   @Override
   public void getSystemConnection(FndSystemConnectionAccess caller, Object param) throws IfsException {
      boolean allowed = false;
      Class callerClass = caller.getClass();
      for (Class systemConnectionCaller : systemConnectionCallers) {
         if (callerClass == systemConnectionCaller) {
            allowed = true;
            break;
         }
      }

      if(!allowed) {
         throw new IfsRuntimeException("DBSYSTEMCONNECTIONUSER:The class [&1] is not allowed to acceess database system connection", callerClass.getName());
      }

      FndConnection c = getPlsqlConnectionImpl("ifssys");
      caller.setSystemConnection(c, param);
   }

   /**
    * Returns the connection which is no longer needed.
    */
   @Override
   public void returnConnection(FndConnection con) throws IfsException {
      Logger log = LogMgr.getDatabaseLogger();

      FndServerContext ctx = FndServerContext.getCurrentServerContext();
      if(ctx.isSqlTraceOn()) {
         try {
            closeSqlTrace(con);
         }
         catch(SystemException e) {
            // ignore exception and try to close connection anyway
            if(log.info) {
               log.info("WARNING! &1", e.getMessage());
            }
         }
      }

      ctx.getConnectionList().remove(con);

      try {
         con.setFndUser(null);
         Connection c = con.getConnection();
         if (!c.isClosed()) {
            if(log.debug) {
               log.debug("Closing connection [&1]", con.toString());
            }
            c.close();
         }
      }
      catch (SQLException e) {
         throw new SystemException(e, "DBCLOSEFAIL:Failed closing database connection"); //TODO:Do we have to throw an exception here?
      }
   }

   static void appendInitParam(StringBuilder buf, String str) {
      if(str != null) {
         buf.append(str);
      }
      buf.append('^');
   }

   private static Message createEmptyPlsqlF1Context() {
      Message context = new Message();
      context.setHeader("F1_CONTEXT");
      return context;
   }

   /**
    * Create IFS-Message with F1 context parameters for Init_Fnd_Session.
    */
   private static Message createPlsqlF1Context(FndServerContext ctx, String osUserName, String programName, String machineName, FndConnection connection) throws IfsException {
      Message context = null;

      // Copy remote PLSQL debug parameters
      String debug = ctx.getPlsqlDebugger();
      if(debug != null && debug.length() > 0) {
         int pos = debug.indexOf(':');
         String host = pos < 0 ? debug : debug.substring(0, pos);
         String port = pos < 0 ? null  : debug.substring(pos+1);
         Message dbgMsg = new Message();
         dbgMsg.addAttribute("HOST", host);
         if(port != null) {
            dbgMsg.addAttribute("PORT", port);
         }
         if(context == null) {
            context = createEmptyPlsqlF1Context();
         }
         context.addAttribute("REMOTE_DEBUG", dbgMsg.format());
      }

      // Copy OS_USER_NAME
      if (osUserName != null) {
         if (context == null) {
            context = createEmptyPlsqlF1Context();
         }
         context.addAttribute("OS_USER_NAME", osUserName);
      }

      // Copy PROGRAM
      if (programName != null) {
         if (context == null) {
            context = createEmptyPlsqlF1Context();
         }
         context.addAttribute("PROGRAM", programName);
      }

      // Copy MACHINE
      if (machineName != null) {
         if (context == null) {
            context = createEmptyPlsqlF1Context();
         }
         context.addAttribute("MACHINE", machineName);
      }

      // Copy REQUEST_ID
      String requestId = FndContext.getRequestId();
      if (requestId != null) {
         if (context == null) {
            context = createEmptyPlsqlF1Context();
         }
         context.addAttribute("REQUEST_ID", requestId);
      }

      return context;
   }

   /**
    * Create IFS-Message with application context parameters for Init_Fnd_Session_ and Init_Client_Session_.
    */
   static Message createPlsqlAppContext(FndContext ctx) throws IfsException {
      FndRecord rec = ctx.getAppContext();
      Message context = null;

      int count = rec.getAttributeCount();
      for(int i = 0; i < count; i++) {
         FndAttribute attr = rec.getAttribute(i);
         if(!attr.isCompound()) {
            if(context == null) {
               context = new Message();
               context.setHeader("APP_CONTEXT");
            }
            context.addAttribute(attr.getName(), attr.toString());
         }
      }

      return context;
   }

   /**
    * Converts Database logger log level to PLSQL log level.
    * Only levels DEBUG, TRACE and INFO are sent to the database.
    * WARNING level is not sent because it would result in extra PLSQL calls to retrieve
    * trace information from database in a production environment.
    * @param log database logger
    */
   static String convertJsfLogLevelToPlsqlLogLevel(Logger log) {
      if(log.debug) {
         return "DEBUG";
      }
      else if(log.trace) {
         return "TRACE";
      }
      else if(log.info) {
         return "INFORMATION";
      }
      else {
         return null;
      }
   }

   //==========================================================================================
   // SQL Trace
   //==========================================================================================

   // SQL trace is opened by FndConnectionManager on every new created connection
   // if FndContext.isSqlTraceOn() returns true.
   // SQL trace is closed by FndConnectionManager before a connection is returned to J2EE container.
   // In standalone framework SQL trace is never closed explicitly (closing a connection
   // closes the SQL trace).
   // Trace data will be spooled to file with the following name:
   //
   //    <user_dump_dest>/<instance_name>_ora_<thread_id>_<directory_id>.trc
   //
   // where:
   //    <user_dump_dest> = value of INIT.ORA parameter USER_DUMP_DEST
   //    <instance_name>  = name of the Oracle instance
   //    <thread_id>      = OS specific thread/process ID (V$PROCESS.SPID)
   //    <directory_id>   = directory ID of user opening the database connection (with non-letter
   //                       and non-digit characters replaced with "_")

   /**
    * Opens SQL trace in the specified database connection.
    */
   static void openSqlTrace(FndConnection connection) throws SystemException {
      FndStatement stmt = null;
      try {
         stmt = connection.createStatement();
         String sql = "{call Database_SYS.Set_Sql_Trace__(?,?)}";
         stmt.defineInParameter(new FndSqlValue("ENABLE", "TRUE", false, false));
         stmt.defineInParameter(new FndSqlValue("IDENTIFIER", createTracefileIdentifier(), false, false));
         stmt.prepareCall(sql);
         stmt.execute();
      }
      catch (IfsException e) {
         throw new SystemException(e, "SQLTRACEOPENFAIL:Failed opening SQL trace: &1", e.getMessage());
      }
      finally {
         if (stmt != null) {
            stmt.close();
         }
      }
   }

   private static String createTracefileIdentifier() {
      String id = FndServerContext.getCurrentApplicationUser();
      int len = id.length();
      FndAutoString buf = new FndAutoString(len);
      for(int i=0; i<len; i++) {
         char ch = id.charAt(i);
         if(Character.isLetterOrDigit(ch)) {
            buf.append(ch);
         }
         else {
            buf.append('_');
         }
      }
      return buf.toString();
   }

   /**
    * Closes SQL trace in the specified database connection.
    */
   static void closeSqlTrace(FndConnection connection) throws SystemException {
      FndStatement stmt = null;
      try {
         stmt = connection.createStatement();
         String sql = "{call Database_SYS.Set_Sql_Trace__(?)}";
         stmt.defineInParameter(new FndSqlValue("ENABLE", "FALSE", false, false));
         stmt.prepareCall(sql);
         stmt.execute();
      }
      catch (IfsException e) {
         throw new SystemException(e, "SQLTRACECLOSEFAIL:Failed closing SQL trace: &1", e.getMessage());
      }
      finally {
         if (stmt != null) {
            stmt.close();
         }
      }
   }

   /**
    * Gets current database user name.
    * @return system user (IFSSYS)
    */
   @Override
   public String getCurrentDatabaseUser() {
      return IfsConstants.SYSTEM_USER;
   }
}
