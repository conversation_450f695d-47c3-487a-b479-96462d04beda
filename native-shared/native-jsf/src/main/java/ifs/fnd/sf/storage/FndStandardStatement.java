/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.storage;

import java.sql.*;
import java.io.*;
import ifs.fnd.base.*;
import ifs.fnd.plsql.pool.TimeEvent;
import ifs.fnd.record.*;
import ifs.fnd.record.serialization.FndAutoString;
import ifs.fnd.service.IfsConstants;
import ifs.fnd.sf.FndUserFeedback;
import ifs.fnd.sf.admin.FndAbortList;
import ifs.fnd.service.ReauthenticationRequest;
import ifs.fnd.sf.FndServerContext;
import ifs.fnd.util.Message;
import ifs.fnd.util.InvalidMessageFormatException;
import ifs.fnd.util.Str;

/**
 * <B>Framework internal class:</B> Standard database statement.
 * <p>
 * Note! This class does not depend on Oracle JDBC driver.
 *       Such code should be placed in FndOracleStatement.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public abstract class FndStandardStatement extends FndStatement {

   private static final int UNKNOWN_EXCEPTION = -1;
   private static final int APPLICATION_EXCEPTION = 0;
   private static final int SECURITY_EXCEPTION = 1;
   private static final int VALIDATION_EXCEPTION = 2;
   private static final int SYSTEM_EXCEPTION = 3;
   private static final int USER_ABORT = 4;
   private static final int SECURITY_CHECKPOINT = 5;

   private String sqlError;

   /** Creates a new instance of FndStandardStatement */
   protected FndStandardStatement(FndConnection fndConnection) {
      super(fndConnection);
   }

    /**
     * Check if an SQLException should be rethrown as a Foundation1 exception.
     * @param exception SQLException.
     * @return An integer representing Foundation1 exception types.
     */
   private static int exceptionType(SQLException exception) {
       int errorCode = exception.getErrorCode();
       if (errorCode == 604 && exception.getMessage().contains("ORA-01013")) {
           return USER_ABORT;
       } else {
           return exceptionType(errorCode);
       }
   }

   /**
    * Check if an Oracle error code should be rethrown as a Foundation1 exception.
    * @param errorCode Error number from Oracle.
    * @return An integer representing Foundation1 exception types.
    */
   private static int exceptionType(int errorCode) {
      /**
       * Error codes from FNDBAS:
       * 20100 - System_General           (SYSTEM_EXCEPTION)
       * 20105 - Appl_General             (APPLICATION_EXCEPTION)
       * 20106 - Appl_Access_             (SECURITY_EXCEPTION)
       * 20110 - Record_General           (APPLICATION_EXCEPTION)
       * 20111 - Record_Not_Exist         (APPLICATION_EXCEPTION)
       * 20112 - Record_Exist             (APPLICATION_EXCEPTION)
       * 20113 - Record_Locked            (APPLICATION_EXCEPTION)
       * 20114 - Record_Modified          (APPLICATION_EXCEPTION)
       * 20115 - Record_Removed           (APPLICATION_EXCEPTION)
       * 20116 - Record_Constraint        (APPLICATION_EXCEPTION)
       * 20120 - Item_General             (VALIDATION_EXCEPTION)
       * 20121 - Item_Insert              (VALIDATION_EXCEPTION)
       * 20122 - Item_Update              (VALIDATION_EXCEPTION)
       * 20123 - Item_Update_If_Null      (VALIDATION_EXCEPTION)
       * 20124 - Item_Format              (VALIDATION_EXCEPTION)
       * 20125 - Item_Not_Exist           (VALIDATION_EXCEPTION)
       * 20130 - State_General            (APPLICATION_EXCEPTION)
       * 20131 - State_Not_Exist          (APPLICATION_EXCEPTION)
       * 20132 - State_Event_Not_Handled  (APPLICATION_EXCEPTION)
       * 20140 - Security_Checkpoint      (SECURITY_CHECKPOINT) aka re-authentication
       */
      if (errorCode == 1013) //ORA-01013
         return USER_ABORT;
      else if (errorCode >= 20120 && errorCode <= 20125)
         return VALIDATION_EXCEPTION;
      else if (errorCode == 20106)
         return SECURITY_EXCEPTION;
      else if (errorCode == 20100)
         return SYSTEM_EXCEPTION;
      else if (errorCode >= 20100) //Everything else above 20100 is an APPLICATION_EXCEPTION
         return APPLICATION_EXCEPTION;

      return UNKNOWN_EXCEPTION;
   }

   /** Check if an error code (from a SQLException) can be mapped to a
    *  <code>DatabaseException</code>.
    * @param errorCode Error thrown from driver.
    * @return An object indicating which DatabaseException type the error code
    *  maps to, or null if the error can not be mapped.
    */
   private DatabaseException.ErrorType toDatabaseErrorType(int errorCode) {
      switch (errorCode) {
         case 1 : //ORA-00001
            return DatabaseException.UNIQUE_CONSTRAINT;
         case 942 : //ORA-00942
            return DatabaseException.TABLE_DOES_NOT_EXIST;
         case 1400 : //ORA-01400
            return DatabaseException.NULL_VALUE_CONSTRAINT;
         default :
            return null;
      }
   }

   /**
    * Check if the original error should be rethrown as some other kind of error.
    * In case a ValidationException or a DatabaseException is thrown, application code
    * should add extra information to the thrown exception.
    * @param e Original exception thrown from database
    * @throws IfsException May re-throw the exception.
    */
   @Override
   protected void checkError(final SQLException e) throws IfsException {

      getFndConnection().checkFatalError(e);

      if (e instanceof SQLTimeoutException) {
          //We can't seem to detect the difference between timeout and cancel
          throw new UserAbortException(e, Texts.STMTABORTED);
      }

      int err = exceptionType(e);

      if (err == APPLICATION_EXCEPTION)
         throw new ApplicationException(e, getMessage(e));
      else if (err == SECURITY_EXCEPTION)
         throw new ifs.fnd.base.SecurityException(e, getMessage(e));
      else if (err == VALIDATION_EXCEPTION)
         throw new ValidationException(getMessage(e));
      else if (err == SYSTEM_EXCEPTION)
         throw new SystemException(e, getMessage(e));
      else if (err == USER_ABORT) {
         if(result != null) {
            try {
               result.close();
            }
            catch(SQLException ignore) {
               if (log.warning)
                  log.warning(ignore, "Ignored error when closing result set", ignore.getMessage());
            }
         }
         throw new UserAbortException(Texts.STMTABORTED);
      }
      else {
         DatabaseException.ErrorType errorType = toDatabaseErrorType(e.getErrorCode());
         if (errorType != null) {
            DatabaseException dbException = new DatabaseException(errorType, getMessage(e), null);
            dbException.setExtraInfo(sqlError); // Detailed error to be reported
            throw dbException;
         }
      }
   }

   /** Update a BLOB
    */
   @Override
   protected void updateBlob(int colNr, byte[] value) throws IfsException {
      InputStream in = new ByteArrayInputStream(value);
      Blob blob = getBlob(colNr);   
      
      try (OutputStream out = blob.setBinaryStream(1L)) {
         byte[] buf = new byte[getBufferSize(blob)];
         int len;
         while((len = in.read(buf)) != -1) {
            out.write(buf, 0, len);
         }
         out.flush();
      }
      catch (SQLException | IOException e) {
         if (e instanceof SQLException) {
            checkError((SQLException)e);
         }
         throw new SystemException(e, "STMTUPDBLOB:Could not update BLOB. Message is: &1", e.getMessage());
      }
      finally {
         if (blob != null) {
            try {
               blob.free();
            }
            catch (SQLException e) {
               log.error(e, "Failed freeing blob");
            }
         }
      }
   }

   /** Update a BLOB from an InputStream.
    */
   @Override
   public void updateBlob(int colNr, FndInputStreamManager inputMgr) throws IfsException {
      Blob blob = getBlob(colNr);
      try (OutputStream out = blob.setBinaryStream(1L)) {
         InputStream in = inputMgr.getInputStream();
         byte[] buf = new byte[getBufferSize(blob)];
         int len;
         while((len = in.read(buf)) != -1) {
            out.write(buf, 0, len);
         }
         out.flush();
      }
      catch (SQLException | IOException e) {
         if (e instanceof SQLException) {
            checkError((SQLException)e);
         }
         throw new SystemException(e, "STMTUPDBLOB:Could not update BLOB. Message is: &1", e.getMessage());
      }
      finally {
         if (blob != null) {
            try {
               blob.free();
            }
            catch (SQLException e) {
               log.error(e, "Failed freeing blob");
            }
         }
         inputMgr.close();
      }
   }

   /** Update a CLOB
    */
   @Override
   protected void updateClob(int colNr, String value) throws IfsException {
      Reader in = new StringReader(value);
      Clob clob = getClob(colNr);
      
      try (Writer w = clob.setCharacterStream(1L)) {
         char [] buf = new char[getBufferSize(clob)];
         int len;
         while((len = in.read(buf)) != -1){
            w.write(buf, 0, len);
         }
         w.flush();
      }
      catch (SQLException | IOException e) {
         if (e instanceof SQLException) {
            checkError((SQLException)e);
         }
         throw new SystemException(e, "STMTUPDCLOB:Could not update long text. Message is: &1", e.getMessage());
      }
      finally {
         if (clob != null) {
            try {
               clob.free();
            }
            catch (SQLException e) {
               log.error(e, "Failed freeing clob");
            }
         }
      }
   }

   /** Initialize a new empty LOB locator.
    */
   @Override
   void initializeLocator(FndAttribute attr, FndAutoString sql) {
      if (attr instanceof FndBinary) {
         sql.append("EMPTY_BLOB()");
      }
      else if (attr instanceof FndText && attr.isLong()) {
         sql.append("EMPTY_CLOB()");
      }
   }

   /**
    * Spool trace from PlSql call to the database logger.
    * This method will be called after every execute of a stored procedure
    * on a V3 instance. (Using Client_SYS.Get_Trace)
    */
   @Override
   protected final void spoolPlSqlTrace() throws IfsException {
      if (getFndConnection().isPlsqlConnection()) { //Do nothing if this is not a V3 instance

         /**
          * Update GetTrace time event if tagged connection pool is used.
          */
         FndConnectionManager mgr = FndServerContext.getCurrentServerContext().getConnectionManager();
         TimeEvent event = mgr.isTaggedConnectionMode() ? ((FndTaggedConnection) getFndConnection()).getGetTraceEvent() : null;
         if(event != null) {
            event.start();
         }

         try (FndStatement trcStmt = getFndConnection().createStatement()) {
            trcStmt.setRecursive();
            trcStmt.setSpoolPlsqlTrace(false); // avoid infinite loop

            FndSqlValue param = new FndSqlValue("TEXT", FndSqlType.LONG_TEXT);
            param.setDirection(FndSqlValue.DIRECTION_INOUT);
            param.setNull();
            trcStmt.defineInOutParameter(param);
            trcStmt.prepareCall("begin Client_SYS.Get_Trace(?); end;");
            trcStmt.execute();

            String trace = trcStmt.getLongText(1);
            if (trace != null) {
               log.log(FndPlsqlLogTagger.tagTrace(trace, false));
            }
         }

         if(event != null) {
            event.end();
         }
      }
   }

   /**
    * Spool information about an SQL error to debug or standard output.
    * @param error error to be reported
    */
   @Override
   protected final void reportSqlError(SQLException error) {
      String dbUser = FndServerContext.getCurrentServerContext().getConnectionManager().getCurrentDatabaseUser();
      FndAutoString buf = new FndAutoString();
      buf.append("ifs.fnd.sf.storage.FndStatement: SQL exception caught:\n");
      buf.append(" Current database user:\n    " + dbUser + "\n");
      buf.append(" Error message:\n    "+ error.getMessage() + "\n");
      buf.append(" SQL statement:\n    "+ getSqlText()+ "\n");
      buf.append(" Bind parameters:\n");
      for(FndSqlValue val : parameters) {
         String name = val.getName();
         Object value = val.getValue();
         String strValue = value == null ? "<null>" : value.toString();
         if(strValue.length() > 200) {
            strValue = strValue.substring(0, 200)+ "...";
         }
         buf.append("    " + name + " = " + strValue + "\n");
      }
      buf.append("\n");
      /**
       * Store detailed error to be reported.
       * Logging of security checkpoint exception is delayed until checkError() clears the checkpoint ID.
       */
      sqlError = buf.toString();
      int errorCode = FndStandardStatement.exceptionType(error);
      if (errorCode == SYSTEM_EXCEPTION || errorCode == UNKNOWN_EXCEPTION) {
         log.error(sqlError);
      }
   }
}
