/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.sta;

import ifs.fnd.service.IfsConstants;
import java.io.*;
import java.util.*;

/**
 * Command line argument parser. This class have three distinct stages in it's
 * life-cycle:
 * <UL>
 * <LI>Registering arguments.</LI>
 * <LI>Parsing and validating command line arguments against the registered ones.</LI>
 * <LI>Retrieving the values given at the command line</LI>
 * </UL>
 */
public class FndArguments {

   private static final String NL = IfsConstants.LINE_SEPARATOR;
   private Map<String, FndArgument> arguments;
   private int maxParamNameLength = 9; // Initial maxlength

   /**
    * Creates an <code>FndArguments</code> object.
    */
   public FndArguments() {
      arguments = new TreeMap<>();
   }

   /**
    * Constructs an <code>FndArguments</code> object with a set of arguments
    * as extra arguments. During validation, if a given argument is not found
    * in this object, then <code>extraArguments</code> is searched.
    */
   public FndArguments(FndArguments extraArguments) {
      this(new FndArguments[]{extraArguments});
   }

   /**
    * Constructs an <code>FndArguments</code> object with a set of arguments
    * as extra arguments. During validation, if a given argument is not found
    * in this object, then <code>extraArguments</code> are searched.
    */
   public FndArguments(FndArguments[] extraArguments) {
      this();
      if (extraArguments != null) {
         // Iterate through the array and merge all attributes.
         for (int i = 0; i < extraArguments.length; i++) {

            // Iterate through all the argument's keys.
            for (Iterator itr = extraArguments[i].arguments.keySet().iterator(); itr.hasNext();) {
               String key = (String) itr.next();

               // If an argument with the same name already exists.
               // Make the extra arguments point to the same object.
               if (arguments.containsKey(key)) {
                  extraArguments[i].arguments.put(key, arguments.get(key));
               }
               else {
                  arguments.put(key, extraArguments[i].arguments.get(key));
               }
            }

            if (extraArguments[i].maxParamNameLength > maxParamNameLength) {
               maxParamNameLength = extraArguments[i].maxParamNameLength;
            }
         }
      }
   }

   /**
    * Gets the value of an argument. The argument must have been added with
    * {@link #registerArgument(String, String) registerArgument}.
    * @param   name  the name of the arguement to get the value for.
    * @return  a String with the arguments value.
    * @throws  IllegalArgumentException   if the argument isn't registered.
    */
   public String getArgument(String name) {
      FndArgument arg = (FndArgument) arguments.get(name.toUpperCase());

      if (arg != null) {
         return arg.getStringValue();
      }

      throw new IllegalArgumentException("Argument '" + name + "' not found");
   }

   /**
    * Gets the value of a boolean argument. The argument must have been added with
    * {@link #registerBooleanArgument(String, String) registerBooleanArgument}.
    * @param   name  the name of the arguement to get the value for.
    * @return  the argument's boolean value.
    * @throws  IllegalArgumentException   if the argument isn't registered or is
    * not a boolean argument.
    */
   public boolean getBooleanArgument(String name) {
      FndArgument arg = (FndArgument) arguments.get(name.toUpperCase());

      if (arg instanceof FndBooleanArgument) {
         return ((FndBooleanArgument) arg).getBooleanValue();
      }

      throw new IllegalArgumentException("Boolean argument '" + name + "' not found");
   }

   /**
    * Checks if an enumerated value was set. The argument must have been added with
    * {@link #registerEnumArgument(String, String, String[]) registerEnumArgument}
    * or with
    * {@link #registerEnumSetArgument(String, String, String[]) registerEnumSetArgument}.
    * @param   name  the name of the arguement to check the value for.
    * @param   enumVal  the enumerated value to check if it was set.
    * @return  <code>true</code> if the enumerated value was set for the argument,
    * <code>false</code> otherwise.
    * @throws  IllegalArgumentException   if the argument isn't registered or
    * is not an enumerated argument.
    */
   public boolean getEnumArgument(String name, String enumVal) {
      FndArgument arg = (FndArgument) arguments.get(name.toUpperCase());

      if (arg instanceof FndEnumSetArgument) {
         return ((FndEnumSetArgument) arg).isSet(enumVal);
      }

      if (arg instanceof FndEnumArgument) {
         return ((FndEnumArgument) arg).isSet(enumVal);
      }

      throw new IllegalArgumentException("Enumerated argument '" + name + "' not found");
   }

   /**
    * Gets the value of an integer argument. The argument must have been added
    * with {@link #registerIntArgument(String, String)}.
    * @param   name  the name of the arguement to get the value for.
    * @return  the argument's integer value.
    * @throws  IllegalArgumentException   if the argument isn't registered or is
    * not an integer argument.
    */
   public int getIntArgument(String name) {
      FndArgument arg = (FndArgument) arguments.get(name.toUpperCase());

      if (arg instanceof FndIntegerArgument) {
         return ((FndIntegerArgument) arg).getIntegerValue();
      }

      throw new IllegalArgumentException("Integer argument '" + name + "' not found");
   }

   /**
    * Checks if an argument was set in the command line parameters.
    * @param   name  the name of the argument to check if set.
    * @return  <code>true</code> if the argument was set, <code>false</code>
    * otherwise.
    */
   public boolean isSet(String name) {
      FndArgument arg = (FndArgument) arguments.get(name.toUpperCase());

      if (arg != null) {
         return arg.isSet();
      }

      return false;
   }

   /**
    * Registers an argument.
    * @param   name  the argument's name.
    * @param   argument the argument to register.
    */
   private void registerArgument(String name, FndArgument argument) {
      arguments.put(name.toUpperCase(), argument);
      if (name.length() > maxParamNameLength) {
         maxParamNameLength = name.length();
      }
   }

   /**
    * Registers an argument without a default value.
    * @param   name  the argument's name.
    * @param   description the argument's description.
    */
   public void registerArgument(String name, String description) {
      registerArgument(name, description, null);
   }

   /**
    * Registers an argument with a default value.
    * @param   name  the argument's name.
    * @param   description the argument's description.
    * @param   defaultValue   the argument's default value.
    */
   public void registerArgument(String name, String description, String defaultValue) {
      registerArgument(name, new FndStringArgument(name, description, defaultValue));
   }

   /**
    * Registers a boolean argument without a default value.
    * @param   name  the argument's name.
    * @param   description the argument's description.
    */
   public void registerBooleanArgument(String name, String description) {
      registerBooleanArgument(name, description, false);
   }

   /**
    * Registers a boolean argument with a default value.
    * @param   name  the argument's name.
    * @param   description the argument's description.
    * @param   defaultValue   the argument's default value.
    */
   public void registerBooleanArgument(String name, String description, boolean defaultValue) {
      registerArgument(name, new FndBooleanArgument(name, description, defaultValue));
   }

   /**
    * Registers an enumerated argument without a default value. During validation
    * one of the <code>allowedValues</code> can be set.
    * @param   name  the argument's name.
    * @param   description the argument's description.
    * @param   allowedValues  the allowed enumeration values.
    */
   public void registerEnumArgument(String name, String description, String[] allowedValues) {
      registerEnumArgument(name, description, null, allowedValues);
   }

   /**
    * Registers an enumerated argument with a default value. During validation
    * one of the <code>allowedValues</code> can be set.
    * @param   name  the argument's name.
    * @param   description the argument's description.
    * @param   defaultValue   the argument's default value.
    * @param   allowedValues  the allowed enumeration values.
    */
   public void registerEnumArgument(String name, String description, String defaultValue, String[] allowedValues) {
      registerArgument(name, new FndEnumArgument(name, description, defaultValue, allowedValues));
   }

   /**
    * Registers an enumerated argument without a default value. During validation
    * a (comma-separated) list of the <code>allowedValues</code> can be set.
    * @param   name  the argument's name.
    * @param   description the argument's description.
    * @param   allowedValues  the allowed enumeration values.
    */
   public void registerEnumSetArgument(String name, String description, String[] allowedValues) {
      registerEnumSetArgument(name, description, null, allowedValues);
   }

   /**
    * Registers an enumerated argument with a default value. During validation
    * a (comma-separated) list of the <code>allowedValues</code> can be set.
    * @param   name  the argument's name.
    * @param   description the argument's description.
    * @param   defaultValue   the argument's default value.
    * @param   allowedValues  the allowed enumeration values.
    */
   public void registerEnumSetArgument(String name, String description, String defaultValue, String[] allowedValues) {
      registerArgument(name, new FndEnumSetArgument(name, description, defaultValue, allowedValues));
   }

   /**
    * Registers an integer argument without a default value.
    * @param   name  the argument's name.
    * @param   description the argument's description.
    */
   public void registerIntArgument(String name, String description) {
      registerArgument(name, new FndIntegerArgument(name, description));
   }

   /**
    * Registers an integer argument with a default value.
    * @param   name  the argument's name.
    * @param   description the argument's description.
    * @param   defaultValue   the argument's default value.
    */
   public void registerIntArgument(String name, String description, int defaultValue) {
      registerArgument(name, new FndIntegerArgument(name, description, defaultValue));
   }

   /**
    * Helper method for creating a String with a specified length.
    */
   private String createString(char c, int len) {
      StringBuilder res = new StringBuilder(len);

      while (res.length() < len) {
         res.append(c);
      }

      return res.toString();
   }

   /**
    * Helper method to ensure a String's minimum length.
    */
   private String rpad(String s, char c, int minLen) {
      StringBuilder res = new StringBuilder(s);
      while (res.length() < minLen) {
         res.append(c);
      }

      return res.toString();
   }

   /**
    * Returns a String with a list of the registered arguments (with extra
    * arguments, if they're used). The list contains the arguments' names,
    * type, default value & description.
    */
   @Override
   public String toString() {
      StringBuilder res = new StringBuilder();

      res.append(NL);
      res.append("Usage:  <applicationname> [param=value, ...]");
      res.append(NL);
      res.append(NL);
      res.append(rpad(" Parameter", ' ', maxParamNameLength + 2));
      res.append("Type Default Value        Description");
      res.append(NL);
      res.append(" ");
      res.append(rpad(createString('-', maxParamNameLength)
            + " ---- -------------------- --------------------", '-', 78));
      res.append(NL);

      FndArgument arg;
      for (Iterator itr = arguments.entrySet().iterator(); itr.hasNext();) {
         arg = (FndArgument) ((Map.Entry) itr.next()).getValue();

         res.append(" ");
         res.append(rpad(arg.getName(), ' ', maxParamNameLength));
         res.append(" ");
         res.append(rpad(arg.getTypeName(), ' ', 4));
         res.append(" ");
         res.append(rpad(arg.getDefaultValue(), ' ', 20));
         res.append(" ");
         res.append(arg.getDescription());
         res.append(NL);
      }

      return res.toString();
   }

   /**
    * Parses and validates the given arguments against the registered.
    * @param   args  a <code>String</code> array with the parameters to parse.
    * The <code>String</code>s here must have the format <code>param=value</code>.
    * If a <code>String</code> here starts with an <code>@</code> and is followed
    * by a filename, then the file is read and the arguments therein are parsed.
    * The arguments in the file must have the same format (cannot reference another
    * file tough). Empty lines, lines starting with <code>#</code> and <code>--</code>
    * are ignored.
    * @throws  IllegalArgumentException   if any of the arguments are given
    * an illegal value or a non-registered argument is given.
    */
   public void validateArguments(String[] args) {
      int pos;
      for (int i = 0; i < args.length; i++) {
         if (args[i].startsWith("@")) {
            validateParamsFromFile(args[i].substring(1));
         }
         else if ((pos = args[i].indexOf('=')) != -1) {
            validateParam(args[i], pos);
         }
         else {
            throw new IllegalArgumentException(
                  "Bad syntax in command line argument '" + args[i]
                  + "'. Should be 'param=value'.");
         }
      }
   }

   /**
    * Parses and validates parameters from a file.
    */
   private void validateParamsFromFile(String filename) {
      try {
         BufferedReader in = new BufferedReader(new FileReader(filename));
         String line;
         while ((line = in.readLine()) != null) {
            line = line.trim();
            if (!(line.startsWith("#") || line.startsWith("_")
                  || line.startsWith("--") || line.length() == 0)) {
               int pos = line.indexOf('=');
               if (pos != -1) {
                  validateParam(line, pos);
               }
               else {
                  throw new IllegalArgumentException(
                        "Bad syntax in command line argument '" + line
                        + "'. Should be 'param=value'.");
               }
            }
         }
         in.close();
      }
      catch (FileNotFoundException ex) {
         throw new IllegalArgumentException(ex.getMessage(), ex);
      }
      catch (IOException ex) {
         throw new IllegalArgumentException(ex.toString(), ex);
      }
   }

   /**
    * Validates a parameter.
    */
   private void validateParam(String inparam, int pos) {
      String name, value;
      name = inparam.substring(0, pos).toUpperCase();
      value = inparam.substring(pos + 1);

      FndArgument arg = (FndArgument) arguments.get(name);
      if (arg != null) {
         arg.validate(value);
      }
      else {
         throw new IllegalArgumentException("Invalid argument '" + inparam + "' on command line.");
      }
   }


   /* ************************************************************************
    *     HELPER CLASSES BELOW THAT HANDLES THE DIFFERENT ARGUMENT TYPES     *
    * ************************************************************************/
   /**
    * Abstract base class for all argument types.
    */
   private static abstract class FndArgument {

      protected String name, description, value, defaultValue;
      protected boolean isSet = false;

      protected FndArgument(String name, String description, String defaultValue) {
         this.name = name;
         this.description = description;
         this.defaultValue = defaultValue;
         this.value = defaultValue;
      }

      public String getStringValue() {
         return value;
      }

      public boolean isSet() {
         return isSet;
      }

      public void validate(String value) {
         this.value = value;
         isSet = true;
      }

      public String getName() {
         return name;
      }

      public String getTypeName() {
         return "";
      }

      public String getDefaultValue() {
         return (defaultValue != null) ? defaultValue : "";
      }

      public String getDescription() {
         return description;
      }
   }

   /**
    * Handles plain arguments.
    */
   private static class FndStringArgument extends FndArgument {

      public FndStringArgument(String name, String description, String defaultValue) {
         super(name, description, defaultValue);
      }

      @Override
      public String getTypeName() {
         return "STR";
      }
   }

   /**
    * Handles boolean arguments.
    */
   private static class FndBooleanArgument extends FndArgument {

      protected boolean boolValue;

      public FndBooleanArgument(String name, String description, boolean defaultValue) {
         super(name, description, toString(defaultValue));
         boolValue = defaultValue;
      }

      public boolean getBooleanValue() {
         return boolValue;
      }

      @Override
      public void validate(String value) {
         boolValue = getBooleanValue(value);
         isSet = true;
      }

      private static String toString(boolean b) {
         if (b) {
            return "Yes";
         }
         else {
            return "No";
         }
      }

      private boolean getBooleanValue(String value) {
         if ("Y".equalsIgnoreCase(value) || "YES".equalsIgnoreCase(value) || "TRUE".equalsIgnoreCase(value)) {
            return true;
         }

         if ("N".equalsIgnoreCase(value) || "NO".equalsIgnoreCase(value) || "FALSE".equalsIgnoreCase(value)) {
            return false;
         }

         throw new IllegalArgumentException("Illegal value for boolean argument: '" + value + "'");
      }

      @Override
      public String getTypeName() {
         return "BOOL";
      }
   }

   /**
    * Handles enum arguments.
    */
   private static class FndEnumArgument extends FndArgument {

      protected String[] allowedValues;
      protected String allowedValuesStr;

      public FndEnumArgument(String name, String description,
                             String defaultValue, String[] allowedValues) {
         super(name, description, defaultValue);
         this.allowedValues = allowedValues;

         for (int i = 0; i < this.allowedValues.length; i++) {
            this.allowedValues[i] = this.allowedValues[i].toUpperCase();
         }
         allowedValuesStr = toString(allowedValues);

         if (defaultValue != null) {
            checkIsAllowed(value);
         }
      }

      @Override
      public String getTypeName() {
         return "STR";
      }

      @Override
      public String getDescription() {
         return description + " [" + allowedValuesStr + "]";
      }

      protected void checkIsAllowed(String value) {
         for (int i = 0; i < allowedValues.length; i++) {
            if (value.equalsIgnoreCase(allowedValues[i])) {
               return;
            }
         }

         throw new IllegalArgumentException("Illegal value for argument '"
               + name + "'. Allowed values are one of [" + allowedValuesStr + "].");
      }

      @Override
      public void validate(String value) {
         checkIsAllowed(value);

         this.value = value;
         isSet = true;
      }

      public boolean isSet(String name) {
         if (isSet) {
            return value.equalsIgnoreCase(name);
         }

         return false;
      }

      protected String toString(String[] args) {
         StringBuilder res = new StringBuilder();
         for (int i = 0; i < allowedValues.length; i++) {
            res.append(allowedValues[i]);
            if (i < allowedValues.length - 1) {
               res.append(", ");
            }
         }
         return res.toString();
      }
   }

   /**
    * Handles enum set arguments.
    */
   private static class FndEnumSetArgument extends FndEnumArgument {

      private List<String> setValues;

      public FndEnumSetArgument(String name, String description, String defaultValue, String[] allowedValues) {
         super(name, description, null, allowedValues);
         setValues = new ArrayList<>();

         if (defaultValue != null) {
            validate(defaultValue, false);
            this.defaultValue = defaultValue;
            this.value = defaultValue;
         }
      }

      @Override
      public String getDescription() {
         return description + " List of [" + allowedValuesStr + "]";
      }

      @Override
      public void validate(String value) {
         validate(value, true);
      }

      private void validate(String value, boolean set) {
         StringTokenizer st = new StringTokenizer(value, ", ");
         String val;
         if (set) {
            setValues.clear();
         }
         while (st.hasMoreTokens()) {
            val = st.nextToken();

            checkIsAllowed(val);
            if (set) {
               setValues.add(val);
               isSet = true;
            }
         }

         if (set) {
            this.value = value;
         }
      }

      @Override
      public boolean isSet(String value) {
         String next = null;
         for (Iterator itr = setValues.iterator(); itr.hasNext();) {
            next = (String) itr.next();
            if (next.equalsIgnoreCase(value)) {
               return true;
            }
         }

         return false;
      }
   }

   /**
    * Handles integer arguments.
    */
   private static class FndIntegerArgument extends FndArgument {

      int intValue;

      public FndIntegerArgument(String name, String description) {
         super(name, description, null);
      }

      public FndIntegerArgument(String name, String description, int defaultValue) {
         super(name, description, null);
         this.defaultValue = String.valueOf(defaultValue);
         intValue = defaultValue;
         this.value = this.defaultValue;
      }

      public int getIntegerValue() {
         return intValue;
      }

      @Override
      public String getTypeName() {
         return "INT";
      }

      @Override
      public void validate(String value) {
         intValue = Integer.parseInt(value);
         this.value = value;
         isSet = true;
      }
   }
}
