/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.remote;

/**
 * Holder class for byte array. Used in various locations to pass byte array
 * as an (mutable) object.
 */
public class FndSkeletonParameter implements java.io.Serializable {

   /**
    * The internal byte array held by this object.
    */
   public byte[] value;

   /**
    * Creates an instance of this class with the internal byte array
    * <I>uninitialized</I>.
    */
   public FndSkeletonParameter() {
   }

   /**
    * Creates an instance of this class with the internal byte array
    * <I>initialized</I>.
    */
   public FndSkeletonParameter(byte[] val) {
      value = val;
   }
}