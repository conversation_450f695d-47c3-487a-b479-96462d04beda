/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.util;

import java.io.BufferedWriter;
import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;

/**
 * This class can be used instead of of java.io.PrintWriter.
 * It does not ignore exceptions (while java.io.PrintWriter does).
 */
public class StreamWriter {
   BufferedWriter writer;

   /**
    * Create a new StreamWriter from an existing OutputStream.
    * @param output an output stream
    */
   public StreamWriter(OutputStream output) throws IOException {
      try {
         writer = new BufferedWriter(new OutputStreamWriter(output, "UTF8"));
      }
      catch (UnsupportedEncodingException x) {
         throw new IOException("UnsupportedEncodingException while creating StreamWriter", x);
      }
   }

   /**
    * Close the stream.
    */
   public void close() throws IOException {
      writer.close();
   }

   /**
    * Flush the stream.
    */
   public void flush() throws IOException {
      writer.flush();
   }

   /**
    * Write a string.
    * @param str the String value to be written
    */
   public void print(String str) throws IOException {
      writer.write(str, 0, str.length());
   }

   /**
    * Write a string and the line separator.
    * @param str the String value to be written
    */
   public void println(String str) throws IOException {
      writer.write(str, 0, str.length());
      writer.newLine();
   }

   /**
    * Write the line separator.
    */
   public void println() throws IOException {
      writer.newLine();
   }
}
