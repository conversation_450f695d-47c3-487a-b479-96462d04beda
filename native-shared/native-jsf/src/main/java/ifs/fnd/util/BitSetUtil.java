package ifs.fnd.util;

import java.util.BitSet;

/**
 *
 * <AUTHOR>
 */
public class BitSetUtil {

   public static BitSet toBitSet(byte[] bytes) {
      BitSet bits = new BitSet(bytes.length * 8);
      for (int i = 0; i < bytes.length * 8; i++) {
         if ((bytes[i / 8] & (1 << (7 - (i % 8)))) > 0) {
            bits.set(i);
         }
      }
      return bits;
   }

   public static byte[] toByteArray(BitSet bits) {
      return toByteArray(bits, 0);
   }

   public static byte[] toByteArray(BitSet bits, int minLength) {
      int arraySize = (bits.length() - 1) / 8 + 1;

      //array size should at least be 3
      byte[] bytes = new byte[(arraySize < minLength) ? minLength : arraySize];
      for (int i = 0; i < bits.length(); i++) {
         if (bits.get(i)) {
            bytes[i / 8] |= 1 << (7 - (i % 8));
         }
      }
      return bytes;
   }
}
