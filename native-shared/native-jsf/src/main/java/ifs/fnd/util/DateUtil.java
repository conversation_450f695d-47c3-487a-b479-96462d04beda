/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.util;

import java.util.Calendar;

/**
 * Contains utility methods related to date manipulations.
 * <AUTHOR>
 */
public final class DateUtil {

   //avoid instantiation
   private DateUtil() {
   }

   /**
    * Converts a java.sql.Date object to a java.util.Calendar object.
    * @param sqlDate the java.sql.Date object.
    * @return a java.util.Calendar object corresponding to the sqlDate
    */
   public static Calendar toCalendar(final java.sql.Date sqlDate) {
      Calendar calendar = Calendar.getInstance();
      calendar.setTime(sqlDate);
      return calendar;
   }

   /**
    * Converts a java.util.Calendar object to a java.sql.Date object.
    * @param calendar the java.util.Calendar object.
    * @return a java.sql.Date object corresponding to the time represented by it.
    */
   public static java.sql.Date toSqlDate(final Calendar calendar) {
      java.sql.Date sqlDate = new java.sql.Date(calendar.getTimeInMillis());
      return sqlDate;
   }
}
