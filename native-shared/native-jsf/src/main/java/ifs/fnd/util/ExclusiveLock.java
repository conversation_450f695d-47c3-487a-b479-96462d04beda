/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.util;

import ifs.fnd.log.LogMgr;
import ifs.fnd.log.Logger;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.nio.channels.OverlappingFileLockException;

/**
 * Class used to acquire an exclusive lock on an empty file.
 * The lock will be seen by other processes and by other threads in this process (Java VM).
 */
public class ExclusiveLock {

   /**
    * The default number of milliseconds to sleep between tryLock invocations.
    */
   private static final int TRY_LOCK_INTERVAL = 100;

   /**
    * The default timeout period, in milliseconds, to wait for a lock.
    */
   private static final int LOCK_TIMEOUT = 60 * 1000;

   private final File file;

   private final int tryLockInterval;

   private final int lockTimeout;

   private RandomAccessFile access;

   /**
    * Constructs an instance of ExclusiveLock.
    * @throws FileNotFoundException if the specified file path is invalid.
    */
   public ExclusiveLock(File file) throws FileNotFoundException {
      this(file, TRY_LOCK_INTERVAL, LOCK_TIMEOUT);
   }

   /**
    * Constructs an instance of ExclusiveLock.
    * @throws FileNotFoundException if the specified file path is invalid.
    */
   public ExclusiveLock(File file, int tryLockInterval, int lockTimeout) throws FileNotFoundException {
      this.file = file;
      this.tryLockInterval = tryLockInterval;
      this.lockTimeout = lockTimeout;
      RandomAccessFile tmp = createRandomAccessFile(file); // verify that the file path is valid
      try {
         tmp.close();
      }
      catch(IOException e) {
      }
   }

   /**
    * Creates and locks en empty file.
    * @throws IOException when timeout occurs when trying to lock the file
    */
   public void lock() throws IOException {
      if(access != null) {
         return; // already locked
      }
      Logger log = LogMgr.getFrameworkLogger();
      long t1 = System.currentTimeMillis();
      while(true) {
         if(tryLock(log)) {
            return;
         }
         long t2 = System.currentTimeMillis();
         if(t2 - t1 > lockTimeout) {
            access = null;
            throw new IOException("Timeout occured when trying to lock file: " + file.getPath());
         }
         sleep();
      }
   }

   /**
    * Releases the lock and deletes the file.
    * If possible, should be called from a finally clause.
    * @throws IOException when I/O error occurs when releasing the lock
    */
   public void release() throws IOException {
      try {
         if(access != null) {
            access.close(); // closes the random access file, the channel and releases the file lock
         }
      }
      finally {
         access = null;
         file.delete();
      }
   }

   private RandomAccessFile createRandomAccessFile(File file) throws FileNotFoundException {
      return new RandomAccessFile(file, "rw");
   }

   private boolean tryLock(Logger log) {
      if(access == null) {
         try {
            access = createRandomAccessFile(file);
         }
         catch(FileNotFoundException e) {
            /**
             * This should never happen, because the constructor verifies that the file path is valid.
             */
            if(log.debug) {
               log.debug("Could not create RandomAccessFile [&1]: &2. Will retry in [&3] milliseconds.", file.getPath(), e.getMessage(), tryLockInterval);
            }
            return false;
         }
      }

      FileChannel channel = access.getChannel();

      try {
         FileLock fileLock = channel.tryLock();
         if(fileLock != null) {
            return true;
         }
         if(log.debug) {
            log.debug("File [&1] locked by another process. Will retry in [&2] milliseconds.", file.getPath(), tryLockInterval);
         }
      }
      catch(OverlappingFileLockException e) {
         if(log.debug) {
            log.debug("File [&1] locked by another thread in this Java VM. Will retry in [&2] milliseconds.", file.getPath(), tryLockInterval);
         }
      }
      catch(IOException e) {
         if(log.debug) {
            log.debug("I/O error when trying to lock file [&1]: &2. Will retry in [&3] milliseconds.", file.getPath(), e.getMessage(), tryLockInterval);
         }
      }
      return false;
   }

   private void sleep() throws IOException {
      try {
         Thread.sleep(tryLockInterval);
      }
      catch(InterruptedException e) {
         Logger log = LogMgr.getFrameworkLogger();
         if(log.debug) {
            log.debug("Interrpted while sleeping: &1", e.getMessage());
         }
      }
   }
}
