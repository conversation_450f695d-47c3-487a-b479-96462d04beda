/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.util;

import java.io.*;
import ifs.fnd.service.FndException;
import static ifs.fnd.service.IfsConstants.*;

/**
 * Utility class with general purpose IO functions
 * <AUTHOR>
 */
public class IoUtil {

   private static final int DEFAULT_BUFFER_SIZE = 10*1024;

   // Avoid instantiation.
   private IoUtil() {
   }

   /**
    * Returns the contents of a specified text file as one String using default encoding.
    * Should be deprecated to next release
    * @param filename a file name
    * @return the file contents
    * @throws IOException if an I/O error occurs
    */
   public static String readFile(String filename) throws IOException {
      return readStream(new FileInputStream(filename));
   }

   /**
    * Returns the contents of a specified text file as one String using default encoding.
    * Should be deprecated to next release
    * @param file the file
    * @return the file contents
    * @throws IOException if an I/O error occurs
    */
   public static String readFile(File file) throws IOException {
      return readStream(new FileInputStream(file));
   }

   /**
    * Returns the contents of a specified text file as one String using UTF-8 encoding.
    * @param filename a file name
    * @return the file contents
    * @throws IOException if an I/O error occurs
    */
   public static String readUtf8File(String filename) throws IOException {
      return readStream(new InputStreamReader(new FileInputStream(filename), UTF8));
   }

   /**
    * Returns the contents of a specified text file as one String using given encoding.
    * @param filename a file name
    * @param encoding character encoding
    * @return the file contents
    * @throws IOException if an I/O error occurs
    */
   public static String readFile(String filename, String encoding) throws IOException{
      return readStream(new InputStreamReader(new FileInputStream(filename), encoding));
   }

   /**
    * Transforms a specified InputStream to a String using default encoding.
    * Should be deprecated to next release
    * @param stream an open InputStream
    * @return the characters read from the stream
    * @throws IOException if an I/O error occurs
    */
   public static String readStream(InputStream stream) throws IOException {
      return readStream(new InputStreamReader(stream));
   }

   /**
    * Returns the contents of a specified Reader as one String.
    * @param stream an open Reader
    * @return the characters read from the stream
    * @throws IOException if an I/O error occurs
    */
   public static String readStream(Reader stream) throws IOException {
      BufferedReader r = new BufferedReader(stream);
      StringBuilder buf = new StringBuilder();
      String line;

      do {
         line = r.readLine();
         if (line != null)
            buf.append(line).append("\n");
      }
      while (line != null);

      r.close();
      return buf.toString();
   }

   /**
    * Transforms a specified InputStream into a String using default encoding.
    * Reads all lines, until end-of-stream or until the specified end-of-msg marker
    * (alone on a line). The stream will be NOT closed after consuming the message.
    * Should be deprecated to next release (or use UTF-8)
    * @param stream an open stream
    * @param endOfMsg end-of-msg marker
    * @return the message contents
    * @throws IOException if an I/O error occurs
    */
   public static String readMessage(InputStream stream, String endOfMsg) throws IOException {
      BufferedReader r = newBufferedReader(stream);
      StringBuilder buf = new StringBuilder();

      while (true) {
         String line = r.readLine();
         if (line == null || line.equals(endOfMsg))
            break;
         buf.append(line).append("\n");
      }
      return buf.toString();
   }

   /**
    * Transforms a specified InputStream into a String using default encoding.
    * Reads all lines, until end-of-stream or until a dot ('.') alone on a line.
    * The stream will be NOT closed after consuming the message.
    * Should be deprecated to next release (or use UTF-8)
    * @param stream an open stream
    * @return the message contents
    * @throws IOException if an I/O error occurs
    */
   public static String readMessage(InputStream stream) throws IOException {
      return readMessage(stream, ".");
   }

   /**
    * Read the file with the specified name using UTF-8 encoding,
    * remove trailing blanks from every line, and return the resulting
    * contents as one String.
    * @param filename a file name
    * @return the file contents
    * @throws ifs.fnd.service.FndException
    */
   public static String readAndTrimFile(String filename) throws FndException {
      try {
         InputStreamReader isr = new InputStreamReader(new FileInputStream(new File(filename)), UTF8);
         BufferedReader r = new BufferedReader(isr);

         StringBuilder buf = new StringBuilder();
         String line;

         do {
            line = r.readLine();
            if (line != null)
               buf.append(Str.trimLine(line)).append("\n");
         }
         while (line != null);

         r.close();
         return buf.toString();
      }
      catch (IOException ioex) {
         throw (new FndException("FNDOPENFERR: Cannot open file '&1': [&2]", filename, ioex.getClass().getName())).addCaughtException(ioex);
      }
   }

   /**
    * Writes the specified text to a file. Each line will have
    * a CR-LF sequence at the end.
    * @param filename file name
    * @param text file contents
    * @throws IOException if an I/O error occurs
    */
   public static void writeFileCrLf(String filename, String text) throws IOException {
      writeFileCrLf(filename, text, false);
   }

   /**
    * Writes the specified text to a file. Each line will have
    * a CR-LF sequence at the end.
    * @param filename file name
    * @param text file contents
    * @param createParentDirs if true creates parent directories if not exist
    * @throws IOException if an I/O error occurs
    */
   public static void writeFileCrLf(String filename, String text, boolean createParentDirs) throws IOException {
      File file = new File(filename);
      if(createParentDirs)
         createParentDirectory(file);

      if (text.indexOf('\r') < 0)
         text = Str.replace(text, "\n", "\r\n");

      StreamWriter f = new StreamWriter(new FileOutputStream(file));
      f.print(text);
      f.close();
   }

   /**
    * Writes an UTF-8 converted string to  a file.
    * @param filename file name
    * @param str file contents
    * @throws java.io.IOException
    */
   public static void writeFile(String filename, String str) throws IOException {
      writeFile(filename, str, false);
   }

   /**
    * Writes an UTF-8 converted string to  a file.
    * @param filename file name
    * @param str file contents
    * @param createParentDirs if true creates parent directories if not exist
    * @throws java.io.IOException
    */
   public static void writeFile(String filename, String str, boolean createParentDirs) throws IOException {
      File file = new File(filename);
      if(createParentDirs)
         createParentDirectory(file);
      OutputStreamWriter osw = new OutputStreamWriter(new FileOutputStream(file), UTF8);
      BufferedWriter w = new BufferedWriter(osw);
      w.write(str, 0, str.length());
      w.close();
   }

   /**
    * Writes byte array to a file.
    * @param file file to write
    * @param data byte array to be written
    * @throws java.io.IOException
    */
    public static void writeFile(File file, byte[] data) throws IOException {
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(data);
        fos.close();
    }

    /**
     * Creates parent directories, if necessary.
     * @param file
     * @throws IOException
     */
    public static void createParentDirectory(File file) throws IOException {
        File parent = file.getParentFile();
        if(parent!=null)
           mkdirs(parent);
    }

   /**
    * Creates a buffering character-input stream that uses an input buffer of the specified size
    * and the default character set.
    * Should be deprecated to next release.
    * @param fileName   File name
    * @param bufferSize Input-buffer size
    * @return a new BufferedReader
    * @throws FileNotFoundException
    */
   public static BufferedReader newBufferedReader(String fileName, int bufferSize) throws FileNotFoundException {
      return newBufferedReader(new FileInputStream(new File(fileName)), bufferSize);
   }

   /**
    * Creates a buffering character-input stream that uses default character set.
    * Should be deprecated to next release.
    * @param stream An input stream
    * @return a new BufferedReader
    * @throws FileNotFoundException
    */
   public static BufferedReader newBufferedReader(InputStream stream) throws FileNotFoundException {
      return newBufferedReader(stream, DEFAULT_BUFFER_SIZE);
   }

   /**
    * Creates a buffering character-input stream that uses an input buffer of the specified size
    * and the default character set.
    * Should be deprecated to next release.
    * @param stream An input stream
    * @param bufferSize Input-buffer size
    * @return a new BufferedReader
    * @throws FileNotFoundException
    */
   public static BufferedReader newBufferedReader(InputStream stream, int bufferSize) throws FileNotFoundException {
      return new BufferedReader(new InputStreamReader(stream), bufferSize);
   }

   /**
    * Creates a buffering character-input stream that uses UTF-8 character set.
    * @param fileName   File name of the file to be opened for reading
    * @return a new BufferedReader
    * @throws FileNotFoundException
    */
   public static BufferedReader newUtf8BufferedReader(String fileName) throws FileNotFoundException {
      return newUtf8BufferedReader(new FileInputStream(new File(fileName)));
   }

   /**
    * Creates a buffering character-input stream that uses UTF-8 character set.
    * @param file The file to be opened for reading
    * @return a new BufferedReader
    * @throws FileNotFoundException
    */
   public static BufferedReader newUtf8BufferedReader(File file) throws FileNotFoundException {
      return newUtf8BufferedReader(new FileInputStream(file));
   }

   /**
    * Creates a buffering character-input stream that uses UTF-8 character set.
    * @param stream An input stream
    * @return a new BufferedReader
    * @throws FileNotFoundException
    */
   public static BufferedReader newUtf8BufferedReader(InputStream stream) throws FileNotFoundException {
      try {
         return new BufferedReader(new InputStreamReader(stream, UTF8));
      } catch(UnsupportedEncodingException e) {
         throw new AssertionError("UTF-8 is not supported!");
      }
   }

   /**
    * Creates a buffered character-output stream that uses a default-sized
    * output buffer and default character encoding.
    * Should be deprecated to next release.
    * @param fileName File name of the file to be opened for writing
    * @return a new BufferedWriter
    * @throws FileNotFoundException
    */
   public static BufferedWriter newBufferedWriter(String fileName) throws FileNotFoundException {
      return newBufferedWriter(new File(fileName));
   }

   /**
    * Creates a buffered character-output stream that uses a default-sized
    * output buffer and default character encoding.
    * Should be deprecated to next release.
    * @param dir      The parent pathname string
    * @param fileName File name of the file to be opened for writing
    * @return a new BufferedWriter
    * @throws FileNotFoundException
    */
   public static BufferedWriter newBufferedWriter(String dir, String fileName) throws FileNotFoundException {
      return newBufferedWriter(new File(dir, fileName));
   }

   /**
    * Creates a buffered character-output stream that uses a default-sized
    * output buffer and default character encoding.
    * Should be deprecated to next release.
    * @param file   the file to be opened for writing
    * @return a new BufferedWriter
    * @throws FileNotFoundException
    */
   public static BufferedWriter newBufferedWriter(File file) throws FileNotFoundException {
      return new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file)));
   }

   /**
    * Creates a buffered character-output stream that uses a default-sized
    * output buffer and UTF-8 character encoding.
    * @param fileName File name of the file to be opened for writing
    * @return a new BufferedWriter
    * @throws FileNotFoundException
    */
   public static BufferedWriter newUtf8BufferedWriter(String fileName) throws FileNotFoundException {
      return newUtf8BufferedWriter(new File(fileName));
   }

   /**
    * Creates a buffered character-output stream that uses a default-sized
    * output buffer and UTF-8 character encoding.
    * @param fileName File name of the file to be opened for writing
    * @param append   If true, then bytes will be written to the end of the file rather than the beginning
    * @return a new BufferedWriter
    * @throws FileNotFoundException
    */
   public static BufferedWriter newUtf8BufferedWriter(String fileName, boolean append) throws FileNotFoundException {
      return newUtf8BufferedWriter(new File(fileName), append);
   }

   /**
    * Creates a buffered character-output stream that uses a default-sized
    * output buffer and UTF-8 character encoding.
    * @param file   the file to be opened for writing
    * @return a new BufferedWriter
    * @throws FileNotFoundException
    */
   public static BufferedWriter newUtf8BufferedWriter(File file) throws FileNotFoundException {
      return newUtf8BufferedWriter(file, false);
   }

   /**
    * Creates a buffered character-output stream that uses a default-sized
    * output buffer and UTF-8 character encoding.
    * @param file   the file to be opened for writing
    * @param append if true, then bytes will be written to the end of the file rather than the beginning
    * @return a new BufferedWriter
    * @throws FileNotFoundException
    */
   public static BufferedWriter newUtf8BufferedWriter(File file, boolean append) throws FileNotFoundException {
      try {
         return new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file, append), UTF8));
      } catch(UnsupportedEncodingException e) {
         throw new AssertionError("UTF-8 is not supported!");
      }
   }

   /**
    * Copies bytes from an InputStream to an OutputStream.
    * This method buffers the input internally.
    * Returns number of copied bytes.
    *
    * @param in   the <code>InputStream</code> to read from
    * @param out  the <code>OutputStream</code> to write to
    * @return     the number of bytes copied
    * @throws NullPointerException if the input or output is null
    * @throws IOException if an I/O error occurs
    */
   public static long copy( InputStream in, OutputStream out ) throws IOException {
      byte[] buf = new byte[DEFAULT_BUFFER_SIZE];
      long cnt = 0;

      while(true) {
         int n = in.read(buf);
         if( n<0 )
            break;
         out.write(buf, 0, n);
         cnt += n;
      }
      return cnt;
   }

   /**
    * Copy characters from a Reader to a Writer.
    * This method buffers the input internally.
    * Returns number of copied characters.
    *
    * @param in   the <code>Reader</code> to read from
    * @param out  the <code>Writer</code> to write to
    * @return the number of characters copied
    * @throws NullPointerException if the input or output is null
    * @throws IOException if an I/O error occurs
    */
   public static long copy( Reader in, Writer out) throws IOException {
      char[] buf = new char[DEFAULT_BUFFER_SIZE];
      long cnt = 0;

      while(true) {
         int n = in.read(buf);
         if( n<0 )
            break;
         out.write(buf, 0, n);
         cnt += n;
      }
      return cnt;
   }

   /**
    * Get the contents of an InputStream as a String
    * using the UTF-8 character encoding.
    * This method buffers the input internally.
    *
    * @param  in  the <code>InputStream</code> to read from
    * @return the requested String
    * @throws NullPointerException if the input is null
    * @throws IOException if an I/O error occurs
    */
   public static String toString( InputStream in ) throws IOException {
      StringWriter      writer = new StringWriter();
      InputStreamReader reader = new InputStreamReader(in, UTF8);

      copy(reader, writer);

      return writer.toString();
   }

   /**
    * Print out text to standard output
    * @param text
    */
   public static void stdoutprn(String text) {
      stdprn(text, false, true);
   }

   /**
    * Print out line feed to standard output
    */
   public static void stdoutln() {
      stdprn(null, true, true);
   }

   /**
    * Print out a line to standard output
    * @param line line to print
    */
   public static void stdoutln(String line) {
      stdprn(line, true, true);
   }

   /**
    * Print out a line to standard error
    * @param line line to print
    */
   public static void stderrln(String line) {
      stdprn(line, true, false);
   }

   /**
    * Print out text to standard output or error.
    * @param text    text to print
    * @param newLine if true print line feed after text
    * @param stdOut  if true print text to std out, otherwise to std err
    */
   private static void stdprn(String text, boolean newLine, boolean stdOut) {
      PrintStream out = stdOut ? System.out : System.err;
      if(text==null && newLine)
         out.println();
      else if(newLine)
         out.println(text);
      else
         out.print(text);
   }

   /**
    * Creates the directory named by this abstract pathname, including any necessary
    * but nonexistent parent directories.
    * @param dir File denoted directory to be created
    * @throws IOException
    */
   public static void mkdirs(File dir) throws IOException {
      if(dir.mkdirs())
         return;

      if(dir.exists() && dir.isDirectory())
         return;

      throw new IOException("Could not create directory or the file exists and is not a directory: "+dir);
   }

   /**
    * Deletes a file or directory.
    * @param file file or directory to delete
    * @throws IOException if the specified file/directory has not been successfully deleted
    */
   public static void delete(File file) throws IOException {
      if(!file.delete()) {
         throw new IOException("Could not delete file or directory: " + file);
      }
   }
}
