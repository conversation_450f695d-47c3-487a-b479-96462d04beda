package ifs.fnd.util;

import java.io.*;
import java.util.StringTokenizer;
import static ifs.fnd.service.IfsConstants.*;
import ifs.fnd.service.Util;

/**
 * This class encapsulates some common string functions.
 * It contains only static members and should never be instantiated.
 */
public final class Str {

   /**
    * Avoid instantiation.
    */
   private Str() {
   }

   /**
    * Returns the specified 'str' string with all occurrences of 'substr'
    * replaced with 'with'. The string is not copied if 'substr' is not found in 'str'.
    * @param str    a String to search and replace in
    * @param substr old substring
    * @param with   new substring
    * @return a string derived from 'str' string by replacing every occurrence of
    *         the 'substr' substring with the 'with' substring
    */
   public static String replace(String str, String substr, String with) {
      return replace(str, substr, with, false);
   }

   /**
    * Returns the specified 'str' string with all occurrences of 'substr'
    * replaced with 'with'. The string is not copied if 'substr' is not found in 'str'.
    * @param str        a String to search and replace in
    * @param substr     old substring
    * @param with       new substring
    * @param ignoreCase if true, ignore case when comparing characters
    * @return a string derived from 'str' string by replacing every occurrence of
    *         the 'substr' substring with the 'with' substring
    */
   public static String replace(String str, String substr, String with, boolean ignoreCase) {

      if (str == null || substr == null)
         return null;
      if (str.length() == 0 || substr.length() == 0)
         return str;

      StringBuilder buf = null;
      int i = 0, j, skip = substr.length();

      while (true) {
         j = Str.indexOf(str, substr, i, ignoreCase);
         if (j < 0) {
            if (buf == null)
               return str;
            else {
               buf.append(str.substring(i));
               return buf.toString();
            }
         }
         else {
            if (buf == null) {
               if (with == null)
                  return null;
               buf = new StringBuilder(10 + str.length());
            }
            buf.append(str.substring(i, j));
            buf.append(with);
            i = j + skip;
         }
      }
   }

   /**
    * Returns the specified 'str' string with the first occurrence of 'substr'
    * replaced with 'with'. The string is not copied if 'substr' is
    * not found in 'str'.
    * @param str a String to search and replace in
    * @param substr old substring
    * @param with   new substring
    * @return a string derived from 'str' string by replacing the first occurrence of
    *         the 'substr' substring with the 'with' substring
    */
   public static String replaceFirst(String str, String substr, String with) {
      if (str == null || substr == null)
         return null;
      if (str.length() == 0 || substr.length() == 0)
         return str;

      StringBuilder buf;
      int i = 0, j;

      j = str.indexOf(substr, i);
      if (j < 0)
         return str;
      else {
         if (with == null)
            return null;
         buf = new StringBuilder(10 + str.length());
         buf.append(str.substring(i, j));
         buf.append(with);
         buf.append(str.substring(j + substr.length()));
         return buf.toString();
      }
   }

   /**
    * Remove trailing whitespace from a string.
    * @param value a string to trim
    * @return a copy of the specified string with trailing whitespace omitted
    */
   public static String trimLine(String value) {
      if (value == null)
         return null;

      int len = value.length();
      int i = len;

      while (i > 0 && value.charAt(i - 1) <= ' ') {
         i--;
      }

      return i == len ? value : value.substring(0, i);
   }

   /**
    * Return given string left-padded with blanks to the specified length.
    */
   public static String lpad(String str, int length) {
      return lpad(str, length, ' ');
   }

   /**
    * Return given string left-padded to the specified length.
    */
   public static String lpad(String str, int length, char ch) {
      if (!Str.isEmpty(str) && str.length() >= length) {
         return str;
      }

      StringBuilder buf = new StringBuilder(length);
      while (buf.length() < length - str.length()) {
         buf.append(ch);
      }
      buf.append(str);
      return buf.toString();
   }

   /**
    * Return given string rigth-padded with blanks to the specified length.
    */
   public static String rpad(String str, int length) {
      return rpad(str, length, ' ');
   }

   /**
    * Return given string rigth-padded to the specified length.
    */
   public static String rpad(String str, int length, char ch) {
      if (!Str.isEmpty(str) && str.length() >= length)
         return str;

      StringBuilder buf = new StringBuilder(length);
      buf.append(str);
      while (buf.length() < length) {
         buf.append(ch);
      }
      return buf.toString();
   }

   /**
    * Replaces a null value of a string.
    * @param str a string which may have a null value
    * @param insteadOfNull default value returned if the first parameter is null
    * @return a not null string
    */
   public static String nvl(String str, String insteadOfNull) {
      return str == null ? insteadOfNull : str;
   }

   /**
    * Returns true if the specified string is null or is empty.
    * @param str a String to check
    * @return true if given string is null or has the length = 0, false otherwise
    */
   public static boolean isEmpty(String str) {
      return str == null || str.equals("");
   }

   /**
    * Returns the length of a string or 0 if the string is null.
    * @param str a String
    * @return the length of the string
    */
   public static int length(String str) {
      return str == null ? 0 : str.length();
   }

   /**
    * Counts occurrences of a character in a string.
    * @param str   string to search the specified character in
    * @param start the index to start the search from, inclusive
    * @param end   the index to end the search at, exclusive
    * @param ch    character to count occurrences of
    * @return number of occurrences, or 0 if not found
    */
   public static int countChar(String str, int start, int end, char ch) {
      int count = 0;
      for(int i = start; i < end; i++) {
         if(str.charAt(i) == ch) {
            count++;
         }
      }
      return count;
   }

   /**
    * Converts the specified string to an integer value.
    * @param str a String to parse
    * @return parsed int value
    */
   public static int toInt(String str) {
      return Integer.parseInt(str);
   }

   /**
    * Convert a specified integer into a String with the specified number of digits.
    * @param value an integer to convert
    * @param digits the number of digits to generate
    * @return String representation of the specified int value
    */
   public static String intToString(int value, int digits) {
      String s = Integer.toString(value);
      int len = s.length();
      return len >= digits ? s : "0000000000000".substring(0, digits - len) + s;
   }

   /**
    * Convert specified string to array of bytes using default character encoding.
    * Temporary method for delegation to minimize FindBugs warnings.
    * Should be deprecated to next release
    * @param str string to be converted
    * @return resulting array of bytes
    */
   public static byte[] getBytes(String str) {
      return str.getBytes();
   }

   /**
    * Convert specified string to array of bytes using UTF-8 character encoding.
    * @param str string to be converted
    * @return resulting array of bytes
    */
   public static byte[] getUtf8Bytes(String str) {
      try {
         return str.getBytes(UTF8);
      } catch(UnsupportedEncodingException e) {
         throw new AssertionError("UTF-8 is not supported!");
      }
   }

   /**
    * Convert specified array of bytes to String using default character encoding.
    * Temporary method for delegation to minimize FindBugs warnings.
    * Should be deprecated to next release
    * @param bytes the array to be converted
    * @return resulting string
    */
   public static String bytesToString(byte[] bytes) {
      return new String(bytes);
   }

   /**
    * Convert specified array of bytes to String using UTF-8 character encoding.
    * @param bytes the array to be converted
    * @return resulting string
    */
   public static String bytesUtf8ToString(byte[] bytes) {
      try {
         return new String(bytes, UTF8);
      } catch(UnsupportedEncodingException e) {
         throw new AssertionError("UTF-8 is not supported!");
      }
   }

   /**
    * Convert specified array of bytes to String using UTF-8 character encoding.
    * @param bytes   The bytes to be decoded into characters
    * @param offset  The index of the first byte to decode
    * @param length  The number of bytes to decode
    * @return resulting string
    */
   public static String bytesUtf8ToString(byte[] bytes, int offset, int length) {
      try {
         return new String(bytes, offset, length, UTF8);
      } catch(UnsupportedEncodingException e) {
         throw new AssertionError("UTF-8 is not supported!");
      }
   }

   /**
    * Return the first line from the specified text.
    * This method uses a StringTokenizer to find the first line of the text.
    * @param text a text
    * @return the first line from the specified text
    */
   public static String firstLine(String text) {
      if (Str.isEmpty(text))
         return text;

      StringTokenizer st = new StringTokenizer(text, "\r\n");
      return st.nextToken();
   }

   /**
    * Returns a boolean value of the SQL expression "a like b". The method
    * uses the following wild-characters:
    * <pre>
    *    "*" as match-any character (instead of "%")
    *    "?" as match-one character (instead of "_")
    * </pre>
    * @param a a string to match to the pattern
    * @param b a pattern with wildcards characters
    * @return true if the string matches the pattern, false otherwise
    */
   public static boolean like(String a, String b) {
      char matchAny = '*';
      char matchOne = '?';
      boolean wild = false;
      int i = 0, j = 0;
      int alen = a.length();
      int blen = b.length();

      while(i < alen && j < blen) {
         char ach = a.charAt(i);
         char bch = b.charAt(j);

         if(wild) {
            if ((ach == bch || bch == matchOne) && Str.like(a.substring(i + 1), b.substring(j + 1)))
               return true;
            else
               i++;
         }
         else if (bch == matchAny) {
            while(j < blen && b.charAt(j) == matchAny) {
               j++;
            }
            wild = true;
         }
         else if (bch == matchOne || ach == bch) {
            i++;
            j++;
         }
         else
            return false;
      }

      if (i == alen) {
         while (j < blen && b.charAt(j) == matchAny) {
            j++;
         }
         return j == blen;
      }
      else {
         return wild;
      }
   }

   /**
    * Formats message that can contain up to nine optional parameters referred as &1 to &9.
    * @param message
    * @param p
    * @return
    */
   public static String formatMessage( String message, Object... p ) {
      return formatMessage(message, false, p);
   }

   /**
    * Formats message that can contain up to nine optional parameters referred as &1 to &9.
    * @param message
    * @param removeUnused
    * @param p
    * @return
    */
   public static String formatMessage( String message, boolean removeUnused, Object... p ) {
      if( p == null || Str.isEmpty(message) )
         return message;

      StringBuilder buf = new StringBuilder();
      int len = message.length();

      for( int i=0; i<len; i++ ) {
         char ch = message.charAt(i);
         if( ch=='&' && i<len-1 && Character.isDigit(message.charAt(i+1)) ) {

            i++;
            ch = message.charAt(i);
            int ix;
            if( ch>'0' && ch<='9' && (ix = ch-'1')<p.length ) {
               Object obj = p[ix];
               if(obj instanceof byte[])
                  obj = new String((byte[])obj);
               buf.append(obj);
            }
            else if(!removeUnused) {
               buf.append('&');
               buf.append(ch);
            }
         }
         else
            buf.append(ch);
      }
      return buf.toString();
   }

   /**
    * Returns a token from a text.
    * @param text a text to tokenize
    * @param tokenNr the ordinal number of the token to return
    * @param delimiters delimiter characters used to tokenize the text
    * @return the string value of the specified token
    * @throws IOException if an I/O error occurs
    */
   public static String getStringToken(String text, int tokenNr, String delimiters) throws IOException {
      StringTokenizer tok = new StringTokenizer(text, delimiters);
      for (int i = 1; i <= tokenNr; i++) {
         if (!tok.hasMoreTokens())
            break;

         String atom = tok.nextToken();
         if (i == tokenNr)
            return atom;
      }
      throw new IOException("Missing token nr " + tokenNr + " in '" + text + "'");
   }

   /**
    * Returns a token from a text.
    * @param text a text to tokenize
    * @param tokenNr the ordinal number of the token to return
    * @param delimiters delimiter characters used to tokenize the text
    * @return the int value of the specified token
    * @throws IOException if an I/O error occurs
    */
   public static int getIntToken(String text, int tokenNr, String delimiters) throws IOException {
      return Str.toInt(getStringToken(text, tokenNr, delimiters));
   }

   /**
    * Split a string into tokens.
    * The method returns an array of strings created by separating the string
    * into substrings using the specified set of delimiter characters.
    * @param str a string to split
    * @param delimChars delimiter characters used to tokenize the string
    * @return array of tokens
    */
   public static String[] split(String str, String delimChars) {
      if (str == null || str.length() == 0)
         return null;

      StringTokenizer st = new StringTokenizer(str, delimChars, true);
      String[] arr = new String[st.countTokens() + 1];
      String tmp = null;
      int i = 0;

      while (st.hasMoreTokens()) {
         tmp = st.nextToken();
         if (!delimChars.contains(tmp)) {
            arr[i] = tmp;
            if (st.hasMoreTokens())
               tmp = st.nextToken();
         }
         else
            arr[i] = "";
         i++;
      }

      if (delimChars.contains(tmp)) {
         arr[i] = "";
         i++;
      }

      if (i < arr.length) {
         String[] newarr = new String[i];
         System.arraycopy(arr, 0, newarr, 0, i);
         return newarr;
      }

      return arr;
   }

   /**
     * Finds the first occurrence of a substring in a string.
     *
     * @param  str        string to search in
     * @param  substr     substring to search for
     * @param  fromIndex  index from which to start the search
     * @param  ignoreCase if true, ignore case when comparing characters
     * @return the index of the first occurrence of the specified substring,
     *         or {@code -1} if there is no such occurrence.
     */
   public static int indexOf(String str, String substr, int fromIndex, boolean ignoreCase) {
      if(!ignoreCase) {
         return str.indexOf(substr, fromIndex);
      }

      int len = str.length();
      int sublen = substr.length();
      for(int i = fromIndex; i < len; i++) {
         if(str.regionMatches(ignoreCase, i, substr, 0, sublen)) {
            return i;
         }
      }

      return -1;
   }

   //==========================================================================
   //  Obsolete functions moved to other classes
   //==========================================================================

   /**
    * Returns the current stack formatted as one String.
    * @param t a Throwable
    * @return the stack trace for the specified Throwable
    * @deprecated Moved to class ifs.fnd.service.Util
    * @see ifs.fnd.service.Util#getStackTrace(java.lang.Throwable)
    */
   public static String getStackTrace(Throwable t) {
      return Util.getStackTrace(t);
   }

   /**
    * Output the details of the specified exception to standard output.
    * @param ex an exception
    * @deprecated Moved to class ifs.fnd.service.Util
    * @see ifs.fnd.service.Util#showException(java.lang.Throwable)
    */
   public static void showException(Throwable ex) {
      Util.showException(ex);
   }

   /**
    * Returns the contents of a specified text file as one String.
    * @param filename a file name
    * @return the file contents
    * @throws IOException if an I/O error occurs
    * @deprecated Moved to the new utility class ifs.fnd.util.IoUtil
    * @see ifs.fnd.util.IoUtil#readFile(java.lang.String)
    */
   public static String readFile(String filename) throws IOException {
      return IoUtil.readFile(filename);
   }
   /**
    * Returns the contents of a specified text file as one String using given encoding.
    * @param filename a file name
    * @param encoding character encoding
    * @return the file contents
    * @throws IOException if an I/O error occurs
    * @deprecated Moved to the new utility class ifs.fnd.util.IoUtil
    * @see ifs.fnd.util.IoUtil#readFile(java.lang.String, java.lang.String)
    */
   public static String readFile(String filename, String encoding) throws IOException{
      return IoUtil.readFile(filename, encoding);
   }

   /**
    * Transforms a specified InputStream to a String.
    * @param stream an open InputStream
    * @return the characters read from the stream
    * @throws IOException if an I/O error occurs
    * @deprecated Moved to the new utility class ifs.fnd.util.IoUtil
    * @see ifs.fnd.util.IoUtil#readStream(java.io.InputStream)
    */
   public static String readStream(InputStream stream) throws IOException {
      return IoUtil.readStream(stream);
   }

   /**
    * Returns the contents of a specified Reader as one String.
    * @param stream an open Reader
    * @return the characters read from the stream
    * @throws IOException if an I/O error occurs
    * @deprecated Moved to the new utility class ifs.fnd.util.IoUtil
    * @see ifs.fnd.util.IoUtil#readStream(java.io.Reader)
    */
   public static String readStream(Reader stream) throws IOException {
      return IoUtil.readStream(stream);
   }

   /**
    * Transforms a specified InputStream into a String. Reads all lines,
    * until end-of-stream or until the specified end-of-msg marker
    * (alone on a line). The stream will be NOT closed after
    * consuming the message.
    * @param stream an open stream
    * @param endOfMsg end-of-msg marker
    * @return the message contents
    * @throws IOException if an I/O error occurs
    * @deprecated Moved to the new utility class ifs.fnd.util.IoUtil
    * @see ifs.fnd.util.IoUtil#readMessage(java.io.InputStream, java.lang.String)
    */
   public static String readMessage(InputStream stream, String endOfMsg) throws IOException {
      return IoUtil.readMessage(stream, endOfMsg);
   }

   /**
    * Transforms a specified InputStream into a String. Reads all lines,
    * until end-of-stream or until a dot ('.') alone on a line.
    * The stream will be NOT closed after consuming the message.
    * @param stream an open stream
    * @return the message contents
    * @throws IOException if an I/O error occurs
    * @deprecated Moved to the new utility class ifs.fnd.util.IoUtil
    * @see ifs.fnd.util.IoUtil#readStream(java.io.InputStream)
    */
   public static String readMessage(InputStream stream) throws IOException {
      return IoUtil.readMessage(stream, ".");
   }

   /**
    * Writes the specified text to a file. Each line will have
    * a CR-LF sequence at the end.
    * @param filename file name
    * @param text file contents
    * @throws IOException if an I/O error occurs
    * @deprecated Moved to the new utility class ifs.fnd.util.IoUtil
    * @see ifs.fnd.util.IoUtil#writeFileCrLf(java.lang.String, java.lang.String)
    */
   public static void writeFile(String filename, String text) throws IOException {
      IoUtil.writeFileCrLf(filename, text);
   }
}
