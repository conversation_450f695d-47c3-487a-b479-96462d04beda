/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.util;

/**
 * Holder class for <code>Object</code>. Useful for implementing out/in-out
 * parameters in Java.
 */
public class ObjectHolder {
   /**
    * The <code>Object</code> held.
    */
   public Object value;

   /**
    * Default constructor. <code>value</code> remains unset.
    */
   public ObjectHolder() {
   }

   /**
    * Constructor that sets <code>value</code>.
    */
   public ObjectHolder(Object obj) {
      value = obj;
   }

   /**
    * Returns a <code>String</code> representation of the object.
    */
   @Override
   public String toString() {
      if (value != null)
         return value.toString();

      return "";
   }
}