/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.util;

import ifs.fnd.log.LogMgr;
import ifs.fnd.log.Logger;
import ifs.fnd.service.Util;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Map that associates a list of values with a key and supports mark and reset operations.
 * @param <K> the type of keys maintained by this map
 * @param <V> the type of mapped values
 */
public class ResetableListMap<K, V> {

   /**
    * Name used in debug outputs.
    */
   private String name;

   /**
    * Map storing list of values per key.
    */
   private Map<K, List<V>> map;

   /**
    * Stack with un-do lists used by reset() operation.
    */
   private SimpleStack<State<V>> stack;

   /**
    * Constructs an empty ResetableListMap.
    */
   public ResetableListMap() {
      map = Util.newHashMap();
      stack = Util.newSimpleStack();
      stack.push(new State<>());
   }

   /**
    * Constructs an empty ResetableListMap.
    * @param name name used in debug outputs
    */
   public ResetableListMap(String name) {
      this();
      this.name = name;
   }

   /**
    * Static factory method for generic ResetableListMap.
    * @param <K> the type of map keys
    * @param <V> the type of map values
    * @param name a name used in debug outputs
    * @return new instance of parameterized ResetableListMap
    */
   public static <K, V> ResetableListMap<K, V> newMap(String name) {
      return new ResetableListMap<>(name);
   }

   /**
    * Adds a value to the list of values mapped to the specified key.
    * @param key key with which the specified value is to be associated
    * @param value value to be associated with the specified key
    * @return true if the value has been added to the map, false if the value had already been associated with the key
    */
   public boolean add(K key, V value) {

      // get or create the list of values mapped to the specified key
      List<V> list = map.get(key);
      if (list == null) {
         list = Util.newArrayList();
         map.put(key, list);
      }
      else if (list.contains(value)) {
         return false;
      }

      // add the value to the map
      list.add(value);

      // create a new entry in the current State list
      State<V> state = stack.peek();
      Entry<V> entry = new Entry<>(list, value);
      state.list.add(entry);
      return true;
   }

   /**
    * Class that identifies the state of a ResetableListMap.
    * A new instance of State is created by mark() operation.
    * The instance will contain an undo-list with all map entries created before the next
    * call to mark(). reset() operation uses this list to restore the state of the map.
    */
   public static class State<V> {

      private List<Entry<V>> list = Util.newArrayList();
   }

   /**
    * Class representing one value added to the map.
    * It is used to implement reset() operation.
    */
   private static class Entry<V> {

      private final List<V> list;
      private final V value;

      Entry(List<V> list, V value) {
         this.list = list;
         this.value = value;
      }
   }

   /**
    * Retrieves all values associated with given key.
    * @param key a kay to retrive valus for
    * @return an iterator over associated values
    */
   public Iterator<V> iterator(K key) {
      List<V> list = map.get(key);
      return list == null ? Collections.<V>emptyList().iterator() : list.iterator();
   }

   /**
    * Marks the current state of the filter map.
    * @return an instance of inner class State that identifies the current state of the map
    */
   public State<V> mark() {
      Logger log = LogMgr.getClassLogger(ResetableListMap.class);
      if(log.debug) {
         log.debug("Current state of [&1] has been marked on level: &2", name, String.valueOf(stack.size()));
         debug(log);
      }
      State<V> state = new State<>();
      stack.push(state);
      return state;
   }

   /**
    * Resets the state of this map.
    * @param state an instance of State returned by mark()
    * @throws IllegalStateException if the specified state does not match the state returned by the last call to mark()
    */
   public void reset(State<V> state) throws IllegalStateException {
      if (!state.equals(stack.peek())) {
         throw new IllegalStateException("Unmatched calls to mark/reset on ResetableMap.");
      }

      stack.pop();

      Iterator<Entry<V>> iter = state.list.iterator();
      while (iter.hasNext()) {
         Entry entry = iter.next();
         entry.list.remove(entry.value);
      }

      Logger log = LogMgr.getClassLogger(ResetableListMap.class);
      if(log.debug) {
         log.debug("Current state of [&1] has been reset to level: &2", name, String.valueOf(stack.size()));
         debug(log);
      }
   }

   /**
    * Print the contents of this map to debug output.
    * @param log the logger to write the debug information to
    */
   public void debug(Logger log) {
      log.debug("Contents of [" + name + "]");
      log.debug("Map:");
      Iterator<K> keys = map.keySet().iterator();
      while(keys.hasNext()) {
         K key = keys.next();
         List<V> list = map.get(key);
         int size = list.size();
         log.debug("   &1 (&2 values):", key.toString(), String.valueOf(size));
         for(V value : list) {
            log.debug("      &1", value.toString());
         }
      }

      log.debug("Stack:");
      int size = stack.size();
      for(int i = 0; i < size; i++) {
         State<V> state = stack.get(i);
         log.debug("   Level &1 (&2 values):", String.valueOf(i+1), String.valueOf(state.list.size()));
         for(Entry<V> entry : state.list) {
            log.debug("      &1", entry.value.toString());
         }
      }
   }

   @Override
   public String toString() {
      return map.toString();
   }
}
