/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.os;

import ifs.fnd.util.*;

/**
 * <B>Framework internal class:</B> The root class for OS-specific native utilities.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 * @deprecated 
 */
public class NativeUtilities {

   private static NativeUtilities nu = new NativeUtilities();

   //==========================================================================
   //   Public interface
   //==========================================================================

   /**
    *
    */
   public static void debug(String s) {
      int id = Thread.currentThread().hashCode();
      nu.doDebug(Integer.toHexString(id) + "\t" + s);
   }

   public static void enableNativeDebug(boolean enable) {
      nu.doEnableNativeDebug(enable);
   }

   public static boolean isNativeDebugEnabled() {
      return nu.doIsNativeDebugEnabled();
   }

   public static long getThreadTimes(int precision) {
      return nu.doGetThreadTimes(precision);
   }

   public static int authorizeNTUser(String user, String passwd, String domain) {
      return nu.doAuthorizeNTUser(user, passwd, domain);
   }

   public static int authorizeLDAPUser(String user, String passwd, String host) {
      return nu.doAuthorizeLDAPUser(user, passwd, host);
   }

   public static int compress(String src, StringBuffer dest) {
      return nu.doCompress(src, dest);
   }

   public static int uncompress(String src, StringBuffer dest) {
      return nu.doUncompress(src, dest);
   }

   public static String perform(String request) {
      return nu.doPerform(request);
   }

   //==========================================================================
   //  Protected abstarct functions
   //==========================================================================

   protected void doDebug(String s) {
      IoUtil.stdoutln(s);
   }

   protected void doEnableNativeDebug(boolean enable) {
   }

   protected boolean doIsNativeDebugEnabled() {
      return false;
   }

   protected long doGetThreadTimes(int precision) {
      throw new RuntimeException("This function is not implemented in the sub-class");
   }

   protected int doAuthorizeNTUser(String user, String passwd, String domain) {
      throw new RuntimeException("This function is not implemented in the sub-class");
   }

   protected int doAuthorizeLDAPUser(String user, String passwd, String host) {
      throw new RuntimeException("This function is not implemented in the sub-class");
   }

   protected int doCompress(String src, StringBuffer dest) {
      throw new RuntimeException("This function is not implemented in the sub-class");
   }

   protected int doUncompress(String src, StringBuffer dest) {
      throw new RuntimeException("This function is not implemented in the sub-class");
   }

   protected String doPerform(String request) {
      throw new RuntimeException("This function is not implemented in the sub-class");
   }
}
