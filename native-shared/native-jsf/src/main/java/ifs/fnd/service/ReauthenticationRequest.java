/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.service;

import ifs.fnd.buffer.Buffer;
import ifs.fnd.buffer.Bufferable;
import ifs.fnd.util.Message;

/**
 * Re-authentication request parameters sent from a server to a client.
 */
public class ReauthenticationRequest implements Bufferable {

   private String username;
   private String gateId;

   /**
    * Create new instance of ReauthenticationRequest with undefined contents.
    */
   public ReauthenticationRequest() {
   }

   /**
    * Create new instance of ReauthenticationRequest with the specified contents.
    * @param username value for USERNAME  parameter
    * @param gateId   value for GATE_ID  parameter
    */
   public ReauthenticationRequest(String username, String gateId) {
      this.username = username;
      this.gateId = gateId;
   }

   /**
    * Get the USERNAME parameter.
    * @return String value of the USERNAME parameter, or null if this value is undefined.
    */
   public String getUsername() {
      return username;
   }

   /**
    * Get the GATE_ID parameter.
    * @return String value of the GATE_ID parameter, or null if this value is undefined.
    */
   public String getGateId() {
      return gateId;
   }

   /**
    * Returns a String representation of this object.
    */
   @Override
   public String toString() {
      return "ReauthenticationRequest{" + "username=" + username + ", gateId=" + gateId + '}';
   }

   //==========================================================================
   // Conversion to/from Buffer
   //==========================================================================

   /**
    * Copy the internal state of this Bufferable object into a specified Buffer.
    * @param buf the buffer to store the internal state in
    */
   @Override
   public void save(Buffer buf) {
      if(username != null)
         buf.setItem("USERNAME",  username);
      if(gateId != null)
         buf.setItem("GATE_ID",  gateId);
   }

   /**
    * Retrieve the internal state of this Bufferable object from a specified Buffer.
    * @param buf the buffer to read the internal state from
    */
   @Override
   public void load(Buffer buf) {
      username  = buf.getString("USERNAME",  null);
      gateId  = buf.getString("GATE_ID",  null);
   }

   //==========================================================================
   // Conversion to/from IFS-Message
   //==========================================================================

   /**
    * Copy the internal state of this object into an IFS-Message.
    * @param msg the IFS-Message to store the internal state in
    */
   public void save(Message msg) {
      if(username != null)
         msg.setAttribute("USERNAME",  username);
      if(gateId != null)
         msg.setAttribute("GATE_ID",  gateId);
   }

   /**
    * Retrieve the internal state of this request from an IFS-Message.
    * @param msg the IFS-Message to retrieve the internal state from
    */
   public void load(Message msg) {
      username  = msg.findAttribute("USERNAME",  (String)null);
      gateId  = msg.findAttribute("GATE_ID",  (String)null);
   }
}
