/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.service;

/**
 * <B>Framework internal class:</B> This class defines some standard IFS naming conventions and constants.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public final class IfsNames {

   /**
    * @deprecated Use {@link IfsConstants#TEXT_SEPARATOR}
    */
   public static final char textSeparator = '^';
   /**
    * @deprecated Use {@link IfsConstants#TEXT_SEPARATOR}
    */
   public static final char text_separator = textSeparator;
   /**
    * @deprecated Use {@link IfsConstants#FIELD_SEPARATOR}
    */
   public static final char fieldSeparator = (char) 31;
   /**
    * @deprecated Use {@link IfsConstants#FIELD_SEPARATOR}
    */
   public static final char field_separator = fieldSeparator;
   /**
    * @deprecated Use {@link IfsConstants#RECORD_SEPARATOR}
    */
   public static final char recordSeparator = (char) 30;
   /**
    * @deprecated Use {@link IfsConstants#RECORD_SEPARATOR}
    */
   public static final char record_separator = recordSeparator;
   /**
    * @deprecated Use {@link IfsConstants#GROUP_SEPARATOR}
    */
   public static final char groupSeparator = (char) 29;
   /**
    * @deprecated Use {@link IfsConstants#GROUP_SEPARATOR}
    */
   public static final char group_separator = groupSeparator;
   /**
    * @deprecated Use {@link IfsConstants#FILE_SEPARATOR}
    */
   public static final char fileSeparator = (char) 28;
   /**
    * @deprecated Use {@link IfsConstants#FILE_SEPARATOR}
    */
   public static final char file_separator = fileSeparator;

   public static final String serverDateMask = "yyyy-MM-dd-HH.mm.ss";
   /**
    * @deprecated Use serverDateMask
    */
   public static final String server_date_mask = serverDateMask;


   //Disable instantiations
   private IfsNames() {
   }

   /**
    * Remove the package part from the specified full class name.
    */
   public static String getShortClassName(String className) {
      return className.substring(className.lastIndexOf('.') + 1);
   }

   /**
    * Return a standard table name for the specified class name.
    */
   public static String getTableName(String className) {
      return getViewName(className) + "_TAB";
   }

   /**
    * Return a standard view name for the specified class name.
    */
   public static String getViewName(String className) {
      return appToDbName(getShortClassName(className));
   }

   /**
    * Return a standard PL/SQL package name for the specified class name.
    */
   public static String getPlSqlPackageName(String className) {
      return appToDbName(getShortClassName(className)) + "_API";
   }

   /**
    * Convert an application to a database name.
    */
   public static String appToDbName(String name) {
      int len = name.length();
      StringBuilder buf = new StringBuilder(len + 5);
      for (int i = 0; i < len; i++) {
         char ch = name.charAt(i);

         if (Character.isUpperCase(ch)) {
            if (buf.length() > 0)
               buf.append('_');
            buf.append(ch);
         }
         else
            buf.append(Character.toUpperCase(ch));
      }
      return buf.toString();
   }

   /**
    * Convert a database name to an application name.
    */
   public static String dbToAppName(String name) {
      boolean first = true;
      int len = name.length();
      StringBuilder buf = new StringBuilder(len);
      for (int i = 0; i < len; i++) {
         char ch = name.charAt(i);

         if (ch == '_')
            first = true;
         else if (first) {
            buf.append(Character.toUpperCase(ch));
            first = false;
         }
         else
            buf.append(Character.toLowerCase(ch));
      }
      return buf.toString();
   }

   public static boolean isId(String str) {
      if (str == null)
         return false;
      if (!isIdStart(str.charAt(0)))
         return false;
      for (int i = 1; i < str.length(); i++)
         if (!isIdPart(str.charAt(i)))
            return false;
      return true;
   }

   public static boolean isIdStart(char ch) {
      return (ch >= 'A' && ch <= 'Z') || (ch >= 'a' && ch <= 'z') || ch == '_';
   }

   public static boolean isIdPart(char ch) {
      return (ch >= 'A' && ch <= 'Z') || (ch >= 'a' && ch <= 'z') || (ch >= '0' && ch <= '9') || ch == '_';
   }

   public static boolean isUpperCaseId(String str) {
      if (str == null)
         return false;
      if (!isUpperCaseIdStart(str.charAt(0)))
         return false;
      for (int i = 1; i < str.length(); i++)
         if (!isUpperCaseIdPart(str.charAt(i)))
            return false;
      return true;
   }

   public static boolean isUpperCaseIdStart(char ch) {
      return (ch >= 'A' && ch <= 'Z') || ch == '_';
   }

   public static boolean isUpperCaseIdPart(char ch) {
      return (ch >= 'A' && ch <= 'Z') || (ch >= '0' && ch <= '9') || ch == '_';
   }
}