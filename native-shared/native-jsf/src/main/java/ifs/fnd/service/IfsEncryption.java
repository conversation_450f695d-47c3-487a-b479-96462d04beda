/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.service;

import ifs.fnd.util.Base64;

import java.io.IOException;

import java.security.AlgorithmParameters;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.ShortBufferException;
import javax.crypto.spec.SecretKeySpec;

/**
 * Encryption and decryption routines.
 * THIS CLASS IS CURRENTLY NOT USED IN NATIVE MOBILE.CLEANUP
 */
public final class IfsEncryption {

   /**
    * The encryption key used. The actual encryption used is partly generated.
    * This is the static part.
    */
   private static final byte[] KEY_DATA = {
      0xffffffd0,   0x46,       0x3e, 0xfffffff5,
      0xffffffdd,   0x75,       0x60, 0x73,
      0x34,         0xfffffff2, 0x7e, 0x7,
      0xffffff8f,   0x4b,       0x16, 0xffffffb4};

   /**
    * The encryption algorithm to use.
    */
   private static final String ALGORITHM = "AES";

   /**
    * The cipher transformation mode used (=algorithm/mode/padding).
    */
   private static final String TRANSFORMATION = "AES/GCM/NoPadding";

   /**
    * Creates a new instance of FndEncryption. Private to disable instantiation.
    */
   private IfsEncryption() {
   }

   /**
    * Utility method for decoding a Base64 string.
    * @param   data  the Base64 encoded string to decode.
    * @return  a <code>byte</code>-array with the decoded data.
    * @throws  IOException if there is a problem decoding the data.
    */
   private static byte[] base64Decode(String data) throws IOException {
      return new Base64().decode(data);
   }

   /**
    * Utility method for Base64-encoding data.
    * @param   data  the data to encode.
    * @return  a <code>String</code> with the data encoded in Base64.
    * @throws  IOException if there is a problem encoding the data.
    */
   private static String base64Encode(byte[] data) throws IOException {
      return new Base64().encode(data, false);
   }

   /**
    * Decrypts a string previously encrypted with
    * {@link #encrypt(java.lang.String) encrypt(String}.
    * @param   cipherText  the encrypted text to decrypt.
    * @return  a <code>String</code> with the decrypted text. This must be
    * Base64 encoded data.
    * @throws  EncryptionException  if there is a problem decrypting the text.
    * With this exception there is a parent exception available (via
    * <code>getCause()</code>) that is the real problem that occurred.
    */
   public static String decrypt(String cipherText) throws EncryptionException {
      try {
         return new String(decrypt(base64Decode(cipherText)), "UTF-8");
      }
      catch(IOException ex) {
         throw new EncryptionException(ex, ex.getMessage());
      }
   }

   /**
    * Decrypts data previously encrypted with
    * {@link #encrypt(byte[]) encrypt(byte[]}.
    * @param   cipherText  the encrypted data to decrypt.
    * @return  a <code>byte</code>-array with the decrypted data.
    * @throws  EncryptionException  if there is a problem decrypting the data.
    * With this exception there is a parent exception available (via
    * <code>getCause()</code>) that is the real problem that occurred.
    */
   public static byte[] decrypt(byte[] cipherText) throws EncryptionException {
      try {
         //THIS CLASS IS CURRENTLY NOT USED IN NATIVE MOBILE.CLEANUP
         SecretKeySpec keySpec = new SecretKeySpec(KEY_DATA, ALGORITHM);
         Cipher cipher = Cipher.getInstance(TRANSFORMATION);

         byte[] algParamData = new byte[cipherText[0]];
         System.arraycopy(cipherText, 1, algParamData, 0, algParamData.length);
         AlgorithmParameters algParams = AlgorithmParameters.getInstance(ALGORITHM);
         algParams.init(algParamData);
         cipher.init(Cipher.DECRYPT_MODE, keySpec, algParams);

         return cipher.doFinal(cipherText, 1+algParamData.length, cipherText.length - (1 + algParamData.length));
      }
      catch (NoSuchAlgorithmException | NoSuchPaddingException | IOException | InvalidKeyException | InvalidAlgorithmParameterException | IllegalBlockSizeException | BadPaddingException ex) {
         throw new EncryptionException(ex, ex.getMessage());
      }
   }

   /**
    * Encrypts a <code>String</code> value.
    * @param   clearText   the string to encrypt.
    * @return  the encrypted string.
    * @throws  EncryptionException  if there is a problem decrypting the text.
    * With this exception there is a parent exception available (via
    * <code>getCause()</code>) that is the real problem that occurred.
    * @see  #decrypt(java.lang.String)
    */
   public static String encrypt(String clearText) throws EncryptionException {
      try {
         return base64Encode(encrypt(clearText.getBytes("UTF-8")));
      }
      catch(IOException ex) {
         throw new EncryptionException(ex, ex.getMessage());
      }
   }

   /**
    * Encrypts data.
    * @param   clearText   the data to encrypt.
    * @return  the encrypted data.
    * @throws  EncryptionException  if there is a problem decrypting the text.
    * With this exception there is a parent exception available (via
    * <code>getCause()</code>) that is the real problem that occurred.
    * @see  #decrypt(byte[])
    */
   public static byte[] encrypt(byte[] clearText) throws EncryptionException {
      /*
         The output from this method is a byte-array consting of:
         <length-of-parameter-data><parameter-data><cipher-text>

         The length-of-parameter-data is always one byte.

         The parameter-data is randomly generated data during the encryption
         operation and is supplied because it must be known when decrypting the
         data (i.e. it's the encryption key). According to javadoc, the format
         for these parameters is called ASN.1.

         The cipher-text is a Blowfish encryption of the clearText parameter to
         this method.

         Note! It is not known if encrypted values are portable to other
         [non-Java] environments. Most concerns is wheter the parameter data
         format is portable/supported by other environments.

         NOTE! This method does not offer real security for the encrypted data!
               With access to this class, any encrypted data can be decrypted by
               anyone who knows how to write a Java program.

         The primary usage of this method is for protecting password data in
         configuration files so that is not directly readable. Therefore the
         file system where such configuration files and this class exist must
         also be protected to avoid unauthorized access!

         A note on the output: the encrypted data returned by this method differs
         between two invokations even if the same intput data is used.
       */
      try {
         SecretKeySpec keySpec = new SecretKeySpec(KEY_DATA, ALGORITHM);
         Cipher cipher = Cipher.getInstance(TRANSFORMATION);
         cipher.init(Cipher.ENCRYPT_MODE, keySpec);

         byte[] algParamData = cipher.getParameters().getEncoded();
         int cipherTextLength = cipher.getOutputSize(clearText.length);
         byte[] cipherText = new byte[1 + algParamData.length + cipherTextLength];
         cipherText[0] = (byte)algParamData.length;
         System.arraycopy(algParamData, 0, cipherText, 1, algParamData.length);

         cipher.doFinal(clearText, 0, clearText.length, cipherText, 1 + algParamData.length);
         return cipherText;
      }
      catch (NoSuchAlgorithmException | NoSuchPaddingException | IOException | InvalidKeyException | IllegalBlockSizeException | BadPaddingException | ShortBufferException ex) {
         throw new EncryptionException(ex, ex.getMessage());
      }
   }


   @SuppressWarnings("PMD")
   public static void main(String[] args) throws EncryptionException {
      // utility for encrypting string values. Not really official, hence no javadoc.
      for (String arg : args) {
         System.out.println(encrypt(args[0]));
      }
   }

   public static class EncryptionException extends IOException {
      private EncryptionException(Exception e, String msg) {
         super(msg);
         this.initCause(e);
      }

   }
}
