/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.service;

import ifs.fnd.buffer.Buffer;
import ifs.fnd.buffer.BufferFormatter;

/**
 * <B>Framework internal class:</B> An interface for all Object factories.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public interface Factory {
   /**
    * Return an instance of Buffer interface.
    * @return an instance of Buffer interface
    */
   Buffer getBuffer();

   /**
    * Return an instance of BufferFormatter interface.
    * @return an instance of BufferFormatter interface
    */
   BufferFormatter getBufferFormatter();
}
