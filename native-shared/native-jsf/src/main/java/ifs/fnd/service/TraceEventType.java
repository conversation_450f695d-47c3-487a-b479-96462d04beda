/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.service;

/**
 * <B>Framework internal class:</B> Registration and reporting of trace events.
 *
 * <pre>
 *  precision       time to overflow
 *  =========  ===========================
 *     4            3.5min  (    214 sec)
 *     3             35min  (   2147 sec)
 *     2          5h 57min  (  21474 sec)
 *     1          2d 11.5h  ( 214748 sec)
 *     0         24d 20.5h  (2147483 sec)
 * </pre>
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class TraceEventType {

   public static final boolean DEBUG = false;
   public static int precision = 3;

   //==========================================================================
   // Construction
   //==========================================================================

   public TraceEventType(String name) {
   }

   //==========================================================================
   // Member methods
   //==========================================================================

   public void clear() {
   }

   public TraceEvent begin() {
      return TraceEvent.dummy;
   }

   //==========================================================================
   // Static functions
   //==========================================================================

   public static void spoolStatistics() {
   }

   public static void spoolStatistics(String mainEventName) throws FndException {
   }

   public static void clearStatistics() {
   }
}
