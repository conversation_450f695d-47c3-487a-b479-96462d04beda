/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.service;

import ifs.fnd.util.*;
import ifs.fnd.buffer.*;

/**
 * This exception is thrown when an application rule has been violated.
 */
public class FndException extends Exception implements Bufferable {
   public static final boolean DEBUG = Util.isDebugEnabled("ifs.fnd.service.FndException");

   // Types of error
   private static final String FNDERR = "FND_ERR";
   private static final String IFSPLERR = "IFSPL_ERR";
   private static final String ORAERR = "ORA_ERR";
   private static final String JAVAERR = "JAVA_ERR";

   // Common attributes
   private String type; // type of message
   private String classname; // Original class name
   private String message; // user defined translatable message or original message
   private String errstack; // Java error stack

   // PerRow command info
   private String cmdname;
   private String rowno;

   // Fnd attributes - sets only for construction with message text
   private String p1; // parameters
   private String p2;
   private String p3;

   // Oracle error
   private transient OraError oraerr;

   // Additional exception which can be set by addStackTrace()
   private Throwable any;

   // Additional message
   private String addmsg;

   //==========================================================================
   //  Construction
   //==========================================================================

   /**
    * Constructs a new instance of FndException.
    */
   public FndException() {
   }

   /**
    * Constructs a new instance of FndException.
    * @param e exception being the cause of this exception
    */
   public FndException(Throwable e) {
      this();

      if (e instanceof FndException) {
         FndException fex = (FndException) e;

         // set common attributes
         this.type = fex.type;
         this.classname = fex.classname;
         this.message = fex.message;
         this.errstack = fex.errstack;
         // set specific attributes
         this.cmdname = fex.cmdname;
         this.rowno = fex.rowno;
         this.p1 = fex.p1;
         this.p2 = fex.p2;
         this.p3 = fex.p3;
      }
      else if (e instanceof InterruptFndException) {
         throw (InterruptFndException) e;
      }
      else {
         // set common attributes
         this.type = JAVAERR;
         this.classname = "FndException(" + e.getClass().getName() + ")";
         this.message = e.getMessage();
         this.errstack = Util.getStackTrace(e);
         // eventually create an instance of inner class OraError
         this.oraerr = new OraError();
         if (JAVAERR.equals(this.type))
            this.oraerr = null;
      }
   }

   /**
    * Constructs a new instance of FndException.
    * @param msg error message
    */
   public FndException(String msg) {
      this(msg, null, null, null);
   }

   /**
    * Constructs a new instance of FndException.
    * @param msg error message
    * @param p1 first placeholder text
    */
   public FndException(String msg, String p1) {
      this(msg, p1, null, null);
   }

   /**
    * Constructs a new instance of FndException.
    * @param msg error message
    * @param p1 first placeholder text
    * @param p2 second placeholder text
    */
   public FndException(String msg, String p1, String p2) {
      this(msg, p1, p2, null);
   }

   /**
    * Constructs a new instance of FndException.
    * @param msg error message
    * @param p1 first placeholder text
    * @param p2 second placeholder text
    * @param p3 third placeholder text
    */
   public FndException(String msg, String p1, String p2, String p3) {
      this();

      // set common attributes
      this.type = FNDERR;
      this.classname = this.getClass().getName();
      this.message = msg;
      this.errstack = Util.getStackTrace(new Throwable());
      // set specific attributes
      this.p1 = p1;
      this.p2 = p2;
      this.p3 = p3;
   }

   //==========================================================================
   //  Public interface - methods for common attributes
   //==========================================================================

   /**
    * Sets the cause of this FndException.
    * @param any exception being the cause of this exception
    */
   public FndException addCaughtException(Throwable any) {
      this.any = any;
      return this;
   }

   /**
    * Gets the cause of this FndException.
    * @return an exception being the cause of this exception
    */
   public Throwable getCaughtException() {
      return this.any;
   }

   /**
    * This function overrides getMessage() in class Throwable. Replaces optional
    * parameters with their actual values.
    */
   @Override
   public String getMessage() {
      if (isFndError())
         return Str.replace(Str.replace(Str.replace(message, "&1", "" + p1), "&2", "" + p2), "&3", "" + p3);
      else
         return initOraError() ? oraerr.getMessage() : message;
   }

   /**
    * Returns value of the message string without replacing optional parameters.
    */
   public String getMsg() {
      return message;
   }

   /**
    * Returns the original error stack.
    */
   public String getErrorStack() {
      return errstack;
   }

   /**
    * Returns the original name of exception class.
    */
   public String getOriginalClassName() {
      return classname;
   }

   /**
    * Sets additional error message.
    */
   public void setAdditionalMessage(String msg) {
      addmsg = msg;
   }

   /**
    * Returns value of the additional erroe message.
    */
   public String getAdditionalMessage() {
      return addmsg;
   }

   //==========================================================================
   //  Public interface - methods for PerRow command info
   //==========================================================================

   /**
    * Sets the name of the command this FndException corresponds to.
    */
   public void setCommandName(String cmdname) {
      this.cmdname = cmdname;
   }

   /**
    * Sets the row number this FndException corresponds to.
    */
   public void setRowNo(String rowno) {
      this.rowno = rowno;
   }

   /**
    * Gets the name of the command this FndException corresponds to.
    */
   public String getCommandName() {
      return cmdname;
   }

   /**
    * Gets the row number this FndException corresponds to.
    */
   public String getRowNo() {
      return rowno;
   }

   //==========================================================================
   //  Public interface - methods for FND specific attributes
   //==========================================================================

   /**
    * Returns value of the first optional parameter.
    */
   public String getParam1() {
      return p1;
   }

   /**
    * Returns value of the second optional parameter.
    */
   public String getParam2() {
      return p2;
   }

   /**
    * Returns value of the third optional parameter.
    */
   public String getParam3() {
      return p3;
   }

   //==========================================================================
   //  Public interface - common methods for Oracle and IFS PL/SQL specific attributes
   //==========================================================================

   private boolean initOraError() {
      if (isOracleError() || isIfsPlError()) {
         if (oraerr == null)
            oraerr = new OraError();
         return true;
      }
      else
         return false;
   }

   /**
    * Returns the exception source.
    */
   public String getExceptionSource() {
      return initOraError() ? oraerr.getExceptionSource() : null;
   }

   /**
    * Returns the Oracle error code.
    */
   public String getOraErrorCode() {
      return initOraError() ? oraerr.getOraErrorCode() : null;
   }

   /**
    * Returns the Oracle or IFS error message.
    */
   public String getOraErrorMessage() {
      return initOraError() ? oraerr.getMessage() : null;
   }

   //==========================================================================
   //  Publik interface - methods for IFS PL/SQL specific attributes
   //==========================================================================

   /**
    * Returns the LU name.
    */
   public String getLuName() {
      return initOraError() ? oraerr.getLuName() : null;
   }

   /**
    * Returns the error code for IFS PL/SQL error message.
    */
   public String getIfsErrorCode() {
      return initOraError() ? oraerr.getIfsErrorCode() : null;
   }

   //==========================================================================
   //  Check type functions
   //==========================================================================

   /**
    * Return true for Oracle error but not IFS PL/SQL error.
    */
   public boolean isOracleError() {
      return ORAERR.equals(type);
   }

   /**
    * Return true for IFS PL/SQL error.
    */
   public boolean isIfsPlError() {
      return IFSPLERR.equals(type);
   }

   /**
    * Return true for user defined exceptions.
    */
   public boolean isFndError() {
      return FNDERR.equals(type);
   }

   /**
    * Return true for standard Java exceptions but not Oracle errors.
    */
   public boolean isJavaError() {
      return JAVAERR.equals(type);
   }

   //==========================================================================
   //  Bufferable interface
   //==========================================================================

   /**
    * Store the internal state of this FndException in a specified Buffer.
    */
   @Override
   public void save(Buffer into) {
      // common attributes
      Buffers.save(into, "CLASS", getClass().getName());
      Buffers.save(into, "TYPE", type);
      Buffers.save(into, "ORG_CLASS_NAME", classname);
      Buffers.save(into, "MESSAGE", message);
      Buffers.save(into, "ERROR_STACK", errstack);

      // PerRow command info
      Buffers.save(into, "CMDNAME", cmdname);
      Buffers.save(into, "ROWNO", rowno);

      // user defined attributes
      Buffers.save(into, "PARAM1", p1);
      Buffers.save(into, "PARAM2", p2);
      Buffers.save(into, "PARAM3", p3);
   }

   /**
    * Retrieve the internal state of this FndException from a specified Buffer.
    */
   @Override
   public void load(Buffer from) throws Exception {
      // common attributes
      type = Buffers.loadString(from, "TYPE");
      classname = Buffers.loadString(from, "ORG_CLASS_NAME");
      message = Buffers.loadString(from, "MESSAGE");
      errstack = Buffers.loadString(from, "ERROR_STACK");

      // PerRow command info
      cmdname = Buffers.loadString(from, "CMDNAME");
      rowno = Buffers.loadString(from, "ROWNO");

      // user defined attributes
      p1 = Buffers.loadString(from, "PARAM1");
      p2 = Buffers.loadString(from, "PARAM2");
      p3 = Buffers.loadString(from, "PARAM3");
   }

   /**
    * Replaced with loadAndParse().
    * @see ifs.fnd.service.FndException#loadAndParse
    * @deprecated
    */
   public void loadNParse(Buffer from) throws FndException {
      loadAndParse(from);
   }

   /**
    * This function is custom made to handle ORB error buffers. Probably fetched from
    * an APException.
    */
   public void loadAndParse(Buffer from) throws FndException {
      try {
         this.type = JAVAERR; //Just in case.
         this.classname = this.getClass().getName();
         this.message = from.getBuffer("ERROR").getString("MESSAGE", "");
         this.errstack = Util.getStackTrace(this);

         try {
            this.rowno = from.getBuffer("FND_CONTEXT").getBuffer("USER_INFO").getItem(1).getString();
            this.cmdname = from.getBuffer("FND_CONTEXT").getBuffer("USER_INFO").getItem(0).getString();
         }
         catch (ItemNotFoundException e) {
            assert true;
         }
      }
      catch (ItemNotFoundException x) {
         throw new FndException("FNDFNDEXPARSE: Item ERROR not found in buffer");
      }
      //This should be an OraError
      this.oraerr = new OraError();
      if (JAVAERR.equals(this.type))
         this.oraerr = null;
   }

   //==========================================================================
   // Private inner class for parsing of Oracle errors
   //==========================================================================

   private final class OraError {
      // common attributes
      private String excsource; // exception source
      private String oraerrcode; // Oracle error code in form ORA-nnnnn
      private String message; // Oracle or IFS PL/SQL error message
      // IFS specific attributes - only for errors 20...
      private String luname; // LU name or the star ('*') if can not find LU name
      private String luerrcode; // IFS PL/SQL error code

      /**
       * Construct the private instance of the OraError class.
       */
      private OraError() {
         parse();
      }

      /**
       *  Parse an error message caused by a COM exception in MTS.
       */
      private void parse() {
         String rest = FndException.this.message;

         int pos = rest == null ? -1 : rest.indexOf("ORA-");
         if (pos >= 0) {
            FndException.this.type = ORAERR;
            this.excsource = rest.substring(0, pos);
            rest = rest.substring(pos);

            pos = rest.indexOf(':');
            if (pos < 0)
               this.message = "Cannot obtain the Oracle Error Code";
            else {
               this.oraerrcode = rest.substring(0, pos);
               rest = rest.substring(pos + 2);

               if (this.oraerrcode.startsWith("ORA-20")) // PL FND Error
                  {
                  FndException.this.type = IFSPLERR;
                  this.luname = "*";
                  pos = rest.indexOf('.');
                  if (pos < 0)
                     this.message = "Cannot obtain LU name";
                  else {
                     this.luname = rest.substring(0, pos);
                     rest = rest.substring(pos + 1);

                     pos = rest.indexOf(':');
                     if (pos < 0)
                        this.message = "Cannot obtain Foundation Error Code";
                     else {
                        this.luerrcode = rest.substring(0, pos);
                        rest = rest.substring(pos + 2);
                        if (Str.isEmpty(rest))
                           this.message = "Cannot obtain Foundation Error Message";
                        else
                           this.message = rest;
                     }
                  }
               }
               else // Ora Error
                  //this.message = rest;
                  this.message = "[" + this.oraerrcode + "]: " + rest;
            }
         }
      }

      private String getExceptionSource() {
         return excsource;
      }

      private String getOraErrorCode() {
         return oraerrcode;
      }

      private String getMessage() {
         return message;
      }

      private String getLuName() {
         return luname;
      }

      private String getIfsErrorCode() {
         return luerrcode;
      }
   }
}
