/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.service;

import ifs.fnd.util.IoUtil;
import ifs.fnd.util.Base64;
import ifs.fnd.util.SimpleStack;
import ifs.fnd.util.Str;
import java.util.zip.*;
import java.util.*;
import java.io.*;
import java.text.*;

/**
 * Common class with diverse utility functions.
 * TODO:There are a lot of methods in this class which look very suspicious. Why do they still exist? Some are only used by fndweb. Better move them there.
 */
public final class Util {
   public static final boolean DEBUG = Util.isDebugEnabled("ifs.fnd.service.Util");
   public static final boolean JAVA_COMPRESS = true;

   //Disable instantiations
   private Util() {
   }

   /**
    * Show text inside the Windows debug console (DBMON.EXE).
    * @param s a text to print to the console
    * @deprecated
    */
   public static void debug(String s) {
   }

   //==========================================================================

   // old public functions - should be obsolete in the next release

   /**
    * Compress a string.
    * @param str a string to compress
    * @return the compressed string
    */
   public static String compress(String str) throws FndException, IOException, ZipException {
      StringBuffer temp = new StringBuffer(100);
      return compress(str, temp);
   }

   /**
    * Uncompress a string.
    * @param str a string to uncompress
    * @return the original (uncompressed) string
    */
   public static String uncompress(String str) throws FndException, IOException, ZipException {
      StringBuffer temp = new StringBuffer(100);
      return uncompress(str, temp);
   }

   // new public functions

   /**
    * Compress a string.
    * @param src a string to compress
    * @param dest destination buffer
    * @return the compressed string
    */
   public static String compress(String src, StringBuffer dest) throws FndException, IOException, ZipException {
      if (Str.isEmpty(src))
         return src;
      return compressJava(src, true); //,dest);
   }

   /**
    * Uncompress a string.
    * @param src a compressed string
    * @return the original (uncompressed) string
    */
   public static String uncompress(String src, StringBuffer dest) throws FndException, IOException, ZipException {
      if (Str.isEmpty(src))
         return src;

      // obtain header information, if exists
      String headVer;
      String protVer;
      //String fmterVer = "J0";
      //String strLen = "?";

      if (src.startsWith("#")) {
         String header = null;
         int pos = src.indexOf('#', 1);
         if (pos < 0)
            throw headerSyntaxError(src);
         else
            header = src.substring(0, pos + 1);

         // obtain header version
         int end = src.indexOf(';');
         if (end < 0)
            throw headerSyntaxError(header);
         headVer = src.substring(1, end);
         if (!"1".equals(headVer))
            throw new FndException("FNDUTLHEADV: Not supported header version: &1", headVer);

         // obtain protocol version
         int start = end;
         end = src.indexOf(';', start + 1);
         if (end < 0)
            throw headerSyntaxError(header);
         protVer = src.substring(start + 1, end);

         // obtain formatter version
         //fmterVer = src.substring(end + 1, pos);

         // obtain length if protocol ver = 2
         switch(protVer) {
            case "1":
               src = src.substring(pos + 1);
               break;
            case "2":
               end = src.indexOf(';', pos);
               if (end < 0)
                  throw headerSyntaxError(src);
               //strLen = src.substring(pos + 1, end);
               break;
            default:
               throw new FndException("FNDUTLPROTVER: Not supported protocol version: &1", protVer);
         }
      }
      return uncompressJava(src); //,dest);
   }

   // functions for (un)compress in Java

   /**
    * Compress a string.
    * @param str a string to compress
    * @return the compressed string
    */
   public static String compressJava(String str) throws IOException, ZipException {
      return compressJava(str, false);
   }

   /**
    * Compress a string.
    * @param str a string to compress
    * @return the compressed string
    */
   public static String compressJava(String str, boolean addHeader) throws IOException, ZipException {

      byte[] bytes = str.getBytes("UTF-8");
      int size = bytes.length;
      ByteArrayOutputStream out = new ByteArrayOutputStream();
      try (GZIPOutputStream zip = new GZIPOutputStream(out)) {
         zip.write(bytes, 0, size);
      }

      bytes = out.toByteArray();

      //String outstr = toSingleByteString(bytes);
      String outstr = toBase64Text(bytes);
      //String outstr = toHexText(bytes);

      if (addHeader)
         // add header according to the new standard
         return "#1;1;J2#" + outstr;
      else
         return outstr;
   }

   /**
    * Uncompress a string.
    * @param str a compressed string
    * @return the original (uncompressed) string
    */
   public static String uncompressJava(String str) throws IOException, ZipException {

      //byte[] bytes = fromSingleByteString(str);
      byte[] bytes = fromBase64Text(str);
      //byte[] bytes = fromHexText(str);

      ByteArrayInputStream in = new ByteArrayInputStream(bytes);
      ByteArrayOutputStream out;
      try (GZIPInputStream zip = new GZIPInputStream(in)) {
         byte buf[] = new byte[1024];
         out = new ByteArrayOutputStream();
         while (true) {
            int len = zip.read(buf);
            if (len < 0)
               break;
            out.write(buf, 0, len);
         }
      }

      String outstr = out.toString("UTF-8");
      return outstr;
   }

   private static FndException headerSyntaxError(String header) {
      return new FndException("FNDUTLHEADSYNTAXE: Syntax error in the header: &1", header);
   }

   /**
    * Perform the BASE64 encoding of the specified array of bytes.
    */
   public static String toBase64Text(byte[] data) throws IOException {
      return new Base64().encode(data, true);
   }

   /**
    * Perform the BASE64 decoding of the specified string.
    */
   public static byte[] fromBase64Text(String text) throws IOException {
      return new Base64().decode(text);
   }

   private static char intToHex(int i) {
      switch (i) {
         case  0 :  return '0';
         case  1 :  return '1';
         case  2 :  return '2';
         case  3 :  return '3';
         case  4 :  return '4';
         case  5 :  return '5';
         case  6 :  return '6';
         case  7 :  return '7';
         case  8 :  return '8';
         case  9 :  return '9';
         case 10 :  return 'A';
         case 11 :  return 'B';
         case 12 :  return 'C';
         case 13 :  return 'D';
         case 14 :  return 'E';
         case 15 :  return 'F';
         default :  return (char)0;
      }
   }

   private static int hexToInt(char ch) {
      switch (ch) {
         case '0' :  return  0;
         case '1' :  return  1;
         case '2' :  return  2;
         case '3' :  return  3;
         case '4' :  return  4;
         case '5' :  return  5;
         case '6' :  return  6;
         case '7' :  return  7;
         case '8' :  return  8;
         case '9' :  return  9;
         case 'A' :  return 10;
         case 'B' :  return 11;
         case 'C' :  return 12;
         case 'D' :  return 13;
         case 'E' :  return 14;
         case 'F' :  return 15;
         default  :  return -1;
      }
   }

   /**
    * Transform an array of bytes into its hexadecimal representation.
    */
   public static String toHexText(byte[] data) {
      int size = data.length;
      StringBuilder buf = new StringBuilder(2 * size);
      for (int i = 0; i < size; i++) {
         int b = data[i];
         buf.append(intToHex(b >> 4 & 0xf));
         buf.append(intToHex(b & 0xf));
      }
      return buf.toString();
   }

   /**
    * Transform a hexadecimal string into an array of bytes.
    */
   public static byte[] fromHexText(String text) {
      int size = text.length();
      byte[] data = new byte[size / 2];

      for (int j = 0, i = 0; i < size; i += 2) {
         int hi = hexToInt(text.charAt(i)) << 4;
         int lo = hexToInt(text.charAt(i + 1));
         data[j++] = (byte) (hi | lo);
      }
      return data;
   }

   /**
    * Check if debugging of a specific class is enabled.
    */
   public static boolean isDebugEnabled(String className) {
      return DebugInfo.isDebugEnabled(className);
   }

   /**
    * Check if debugging is enabled.
    */
   public static boolean isDebugEnabled() {
      return isDebugEnabled("All");
   }

   /**
    * Check if debug text should be written to the trace file.
    */
   public static boolean isTraceFileDebugEnabled() {
      return isDebugEnabled("TraceFile");
   }

   /**
    * Forces the initialization of a class.
    * @param cls the class to initialize
    */
   public static void initClass(Class cls)
   {
      try {
         Class.forName(cls.getName(), true, cls.getClassLoader());
      }
      catch (ClassNotFoundException e) {
         throw new AssertionError(e);  // Can't happen
      }
   }

   /**
    * Format time showing separately hours, minutes and seconds.
    * @param milliseconds the number of milliseconds to format
    * @return a String in the format: "... hours ... minutes ... seconds"
    */
   public static String formatElapsedTime(long milliseconds) {
      DecimalFormat fmt = (DecimalFormat) NumberFormat.getInstance(Locale.US);
      fmt.applyPattern("#.###");

      double seconds = (double) milliseconds / 1000.0;

      int minutes = (int) seconds / 60;
      seconds = seconds - minutes * 60;

      int hours = minutes / 60;
      minutes = minutes % 60;

      StringBuilder buf = new StringBuilder();
      if(hours > 0) {
         buf.append(String.valueOf(hours)).append(" hour");
         if(hours != 1)
            buf.append("s");
      }
      if(hours > 0 || minutes > 0) {
         buf.append(" ").append(String.valueOf(minutes)).append(" minute");
         if(minutes != 1)
            buf.append("s");
         seconds = Math.round(seconds);
      }

      String ss = fmt.format(seconds);
      buf.append(" ").append(ss).append(" second");
      if(!"1".equals(ss))
         buf.append("s");

      return buf.toString().trim();
   }

   private static final long MILLIS_PER_SECOND = 1000;
   private static final long MILLIS_PER_MINUTE = 60 * MILLIS_PER_SECOND;
   private static final long MILLIS_PER_HOUR   = 60 * MILLIS_PER_MINUTE;
   private static final long MILLIS_PER_DAY    = 24 * MILLIS_PER_HOUR;

  /**
   * Format time duration as a string using format "[days:]HH:mm:ss.SSS".
   * The first part representing days is omitted if the number of days is equal to 0.
   * @param durationMillis the duration to format
   * @return the time as a String
   */
   public static String formatDuration(long durationMillis) {
      int days = (int) (durationMillis / MILLIS_PER_DAY);
      durationMillis = durationMillis - (days * MILLIS_PER_DAY);

      int hours = (int) (durationMillis / MILLIS_PER_HOUR);
      durationMillis = durationMillis - (hours * MILLIS_PER_HOUR);

      int minutes = (int) (durationMillis / MILLIS_PER_MINUTE);
      durationMillis = durationMillis - (minutes * MILLIS_PER_MINUTE);

      int seconds = (int) (durationMillis / MILLIS_PER_SECOND);
      durationMillis = durationMillis - (seconds * MILLIS_PER_SECOND);

      int milliseconds = (int) durationMillis;

      char pad = ' ';
      StringBuilder buf = new StringBuilder();
      if(days > 0) {
         buf.append(String.valueOf(days)).append(':');
         pad = '0';
      }

      if(hours == 0) {
         buf.append("   ");
      }
      else {
         buf.append(Str.lpad(String.valueOf(hours), 2, pad)).append(":");
         pad = '0';
      }

      if(minutes == 0 && hours == 0) {
         buf.append("   ");
      }
      else {
         buf.append(Str.lpad(String.valueOf(minutes), 2, pad)).append(":");
         pad = '0';
      }

      if(seconds == 0 && minutes == 0 && hours == 0) {
         buf.append("  0.");
      }
      else {
         buf.append(Str.lpad(String.valueOf(seconds), 2, pad)).append(".");
      }

      buf.append(Str.lpad(String.valueOf(milliseconds), 3, '0'));
      return buf.toString();
   }

   /**
    * Get a new number formatter suitable for big integer numbers.
    * @return a new instance of DecimalFormat that uses space as grouping separator.
    */
   public static NumberFormat getIntegerFormat() {
      DecimalFormat fmt = (DecimalFormat) NumberFormat.getInstance(Locale.US);
      DecimalFormatSymbols ss = fmt.getDecimalFormatSymbols();
      ss.setGroupingSeparator(' ');
      fmt.setDecimalFormatSymbols(ss);
      fmt.setGroupingUsed(true);
      return fmt;
   }

   /**
    * Computes a hash key on a canonical (OS independent) form of a text.
    * @param text a text to compute the hash key on
    * @return hash key
    */
   public static int canonicalHashKey(String text) {
      int h = 0;
      int len = text.length();
      if (len > 0) {
         for (int i = 0; i < len; i++) {
            char ch = text.charAt(i);
            if(ch != '\r') {
               h = 31 * h + ch;
            }
         }
      }
      return h;
   }

   /**
    * Factory method for generic HashSet.
    * @param <E> the type of set elements
    * @return new instance of parameterized HashSet
    */
   @SuppressWarnings("PMD.LooseCoupling")
   public static <E> HashSet<E> newHashSet() {
      return new HashSet<>();
   }

   /**
    * Factory method for generic TreeSet.
    * @param <E> the type of set elements
    * @return new instance of parameterized TreeSet
    */
   @SuppressWarnings("PMD.LooseCoupling")
   public static <E> TreeSet<E> newTreeSet() {
      return new TreeSet<>();
   }

   /**
    * Factory method for generic LinkedHashSet.
    * @param <E> the type of set elements
    * @return new instance of parameterized LinkedHashSet
    */
   @SuppressWarnings("PMD.LooseCoupling")
   public static <E> LinkedHashSet<E> newLinkedHashSet() {
      return new LinkedHashSet<>();
   }

   /**
    * Factory method for generic HashMap.
    * @param <K> the type of map keys
    * @param <V> the type of map values
    * @return new instance of parameterized HashMap
    */
   @SuppressWarnings("PMD.LooseCoupling")
   public static <K, V> HashMap<K, V> newHashMap() {
      return new HashMap<>();
   }

   /**
    * Factory method for generic HashMap.
    * @param <K> the type of map keys
    * @param <V> the type of map values
    * @param  initialCapacity the initial capacity of the list
    * @return new instance of parameterized HashMap
    */
   @SuppressWarnings("PMD.LooseCoupling")
   public static <K, V> HashMap<K, V> newHashMap(int initialCapacity) {
      return new HashMap<>(initialCapacity);
   }

   /**
    * Factory method for generic TreeMap.
    * @param <K> the type of map keys
    * @param <V> the type of map values
    * @return new instance of parameterized TreeMap
    */
   @SuppressWarnings("PMD.LooseCoupling")
   public static <K, V> TreeMap<K, V> newTreeMap() {
      return new TreeMap<>();
   }

   /**
    * Factory method for generic LinkedHashMap.
    * @param <K> the type of map keys
    * @param <V> the type of map values
    * @return new instance of parameterized LinkedHashMap
    */
   @SuppressWarnings("PMD.LooseCoupling")
   public static <K, V> LinkedHashMap<K, V> newLinkedHashMap() {
      return new LinkedHashMap<>();
   }

   /**
    * Factory method for generic ArrayList.
    * @param <E> the type of array elements
    * @return new instance of parameterized ArrayList
    */
   @SuppressWarnings("PMD.LooseCoupling")
   public static <E> ArrayList<E> newArrayList() {
      return new ArrayList<>();
   }

   /**
    * Factory method for generic ArrayList.
    * @param <E> the type of array elements
    * @param  initialCapacity the initial capacity of the list
    * @return new instance of parameterized ArrayList
    */
   @SuppressWarnings("PMD.LooseCoupling")
   public static <E> ArrayList<E> newArrayList(int initialCapacity) {
      return new ArrayList<>(initialCapacity);
   }

   /**
    * Factory method for generic SimpleStack.
    * @param <E> the type of stack elements
    * @return new instance of parameterized SimpleStack
    */
   public static <E> SimpleStack<E> newSimpleStack() {
      return new SimpleStack<>();
   }

   /**
    * Returns the current stack formatted as one String.
    * @param t a Throwable
    * @return the stack trace for the specified Throwable
    */
   public static String getStackTrace(Throwable t) {
      StringWriter writer = new StringWriter();
      try (PrintWriter printWriter = new PrintWriter(writer)) {
         t.printStackTrace(printWriter);
      }
      return writer.toString();
   }

   /**
    * Returns short description of a Throwable. Works similar to
    * Throwable.toString() with the exception that the message
    * is limited to 512 characters and/or one line.
    * Message shorter then 512 characters will remain unchanged.
    * Is the message longer, only the first line will be taken.
    * Is the line longer then 512 characters, it will be truncated.
    * @param t Throwable to extract description from
    * @return short description
    */
   public static String getShortDescription(Throwable t) {
      String cls = t.getClass().getName();
      String msg = getShortMessage(t);
      return (msg!=null) ? cls+": "+msg : cls;
   }

   private static final int ERRMSG_MAXLEN = 512;
   /**
    * Returns short message from a Throwable. Works similar to
    * Throwable.getLocalizedMessage() with the exception that the message
    * is limited to 512 characters and/or one line.
    * Message shorter then 512 characters will remain unchanged.
    * Is the message longer, only the first line will be taken.
    * Is the line longer then 512 characters, it will be truncated.
    * @param t Throwable to extract message from
    * @return short message
    */
   public static String getShortMessage(Throwable t) {
      String msg = t.getLocalizedMessage();
      if(msg!=null) {
         msg = msg.trim();
         if(msg.length()>ERRMSG_MAXLEN) {
            int ix = msg.indexOf('\n');
            if(ix>0)
               msg = msg.substring(0, ix);
            if(msg.length()>ERRMSG_MAXLEN)
               msg = msg.substring(0, ERRMSG_MAXLEN)+"...";
         }
      }
      return msg;
   }

   /**
    * Output the details of the specified exception to standard output.
    * @param t an exception
    */
   public static void showException(Throwable t) {
      t.printStackTrace(System.err);
   }

   //==========================================================================
   //  Obsolete functions (moved to other classes)
   //==========================================================================

   /**
    * Read the file with the specified name, remove trailing blanks from every
    * line, and return the resulting contents as one String.
    * @param filename a file name
    * @return the file contents
    * @deprecated Moved to the new utility class ifs.fnd.util.IoUtil
    * @see ifs.fnd.util.IoUtil#readAndTrimFile(java.lang.String)
    */
   public static String readAndTrimFile(String filename) throws FndException {
      return IoUtil.readAndTrimFile(filename);
   }

   /**
    * Writes an UTF-8 converted string to  a file.
    * @param filename file name
    * @param str file contents
    * @deprecated Moved to the new utility class ifs.fnd.util.IoUtil
    * @see ifs.fnd.util.IoUtil#writeFile(java.lang.String, java.lang.String)
    */
   public static void writeFile(String filename, String str) throws IOException {
      IoUtil.writeFile(filename, str);
   }

   /**
    * Formats message that can contain up to nine optional parameters referred as &1 to &9.
    * @deprecated Moved to class ifs.fnd.util.Str
    * @see ifs.fnd.util.Str#formatMessage(java.lang.String, java.lang.Object[])
    */
   public static String formatMessage( String message, Object... p ) {
      return Str.formatMessage(message, p);
   }

   /**
    * Return given string left-padded with blanks to the specified length.
    * @deprecated Moved to class ifs.fnd.util.Str
    * @see ifs.fnd.util.Str#lpad(java.lang.String, int)
    */
   public static String lpad(String str, int length) {
      return Str.lpad(str, length);
   }

   /**
    * Return given string left-padded to the specified length.
    * @deprecated Moved to class ifs.fnd.util.Str
    * @see ifs.fnd.util.Str#lpad(java.lang.String, int, char)
    */
   public static String lpad(String str, int length, char ch) {
      return Str.lpad(str, length, ch);
   }

   /**
    * Return given string rigth-padded with blanks to the specified length.
    * @deprecated Moved to class ifs.fnd.util.Str
    * @see ifs.fnd.util.Str#rpad(java.lang.String, int)
    */
   public static String rpad(String str, int length) {
      return Str.rpad(str, length);
   }

   /**
    * Return given string rigth-padded to the specified length.
    * @deprecated Moved to class ifs.fnd.util.Str
    * @see ifs.fnd.util.Str#rpad(java.lang.String, int, char)
    */
   public static String rpad(String str, int length, char ch) {
      return Str.rpad(str, length, ch);
   }

   /**
    * Remove trailing whitespace from a string.
    * @param value a string to trim
    * @return a copy of the specified string with trailing whitespace omitted
    * @deprecated Moved to class ifs.fnd.util.Str
    * @see ifs.fnd.util.Str#trimLine(java.lang.String)
    */
   public static String trimLine(String value) {
      return Str.trimLine(value);
   }

   /**
    * Return the first line from the specified text.
    * This method uses a StringTokenizer to find the first line of the text.
    * @param text a text
    * @return the first line from the specified text
    * @deprecated Moved to class ifs.fnd.util.Str
    * @see ifs.fnd.util.Str#firstLine(java.lang.String)
    */
   public static String firstLine(String text) {
      return Str.firstLine(text);
   }
}
