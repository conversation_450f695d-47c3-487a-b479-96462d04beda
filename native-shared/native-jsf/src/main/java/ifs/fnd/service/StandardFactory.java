/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.service;

import ifs.fnd.buffer.Buffer;
import ifs.fnd.buffer.BufferFormatter;
import ifs.fnd.buffer.StandardBuffer;
import ifs.fnd.buffer.StandardBufferFormatter;

/**
 * <B>Framework internal class:</B> Standard implementation of Factory interface.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class StandardFactory implements Factory {

   public static boolean DEBUG = Util.isDebugEnabled("ifs.fnd.service.StandardFactory");

   public StandardFactory() {
   }

   /**
    * Return a new instance of StandardBuffer class.
    */
   @Override
   public Buffer getBuffer() {
      return new StandardBuffer();
   }

   /**
    * Return a new instance of StandardBufferFormatter class.
    */
   @Override
   public BufferFormatter getBufferFormatter() {
      return new StandardBufferFormatter();
   }
}
