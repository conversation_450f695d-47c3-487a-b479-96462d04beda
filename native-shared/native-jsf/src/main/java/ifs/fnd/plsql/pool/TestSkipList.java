/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package ifs.fnd.plsql.pool;

import ifs.fnd.service.Util;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ScheduledThreadPoolExecutor;

/**
 * <AUTHOR>
 */
public class TestSkipList {

   private final TaggedConnectionPoolManager mgr;

   private final FreeConnectionPool pool;

   private static final int loopCount = 10000;

   private static volatile int nextNr = 0;

   private final class Task implements Runnable {

      private final int nr = nextNr++;

      private final TaggedConnection x = new TaggedConnection(null, "DbUser" + nr, nr, nr, mgr);

      @Override
      public void run() {
         for(int i = 0; i < loopCount; i++) {
            pool.add(x);
            if(!pool.remove(x)) {
               log("Failed to remove connection [" + nr + "] from pool");
               Thread.yield();
               if(!pool.remove(x)) {
                  log("ERROR: Failed to remove connection (2) [" + nr + "] from pool");
               }
               else {
                  log("Second remove of connection [" + nr + "] succeeded");
               }
            }
            x.setState(TaggedConnection.State.INITIALIZED); // mutate key removed from pool
         }
         taskDone(this);
      }
   }

   private ScheduledThreadPoolExecutor executor;
   private CountDownLatch latch;
   private final List<Task> tasks = new ArrayList<>();

   private final int taskCount = 100;

   TestSkipList() throws Exception {
      TaggedConnectionPoolManager.start();
      mgr = TaggedConnectionPoolManager.getInstance();
      pool = mgr.getFreePool();
   }

   private void runTest() throws Exception {


      executor = new ScheduledThreadPoolExecutor(taskCount);
      latch = new CountDownLatch(taskCount);

      for(int i = 0; i < taskCount; i++) {
         Task t = new Task();
         tasks.add(t);
      }

      for(Task t : tasks) {
         executor.execute(t);
      }

      log("Started " + taskCount + " tasks");

      latch.await();
      executor.shutdown();

      log("Done");
      System.exit(0);
   }

   synchronized void taskDone(Task task)
   {
      latch.countDown();
   }

   private void log(String msg) {
      System.out.println(msg);
   }

   public static void main(String[] arg) throws Exception {
      try {
         new TestSkipList().runTest();
      }
      catch(Exception any) {
         System.out.println(Util.getStackTrace(any));
      }
   }
}
