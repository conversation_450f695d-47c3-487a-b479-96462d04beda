/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.plsql.pool;

import ifs.fnd.log.LogMgr;
import ifs.fnd.log.Logger;
import java.util.Iterator;
import java.util.NavigableSet;
import java.util.NoSuchElementException;
import java.util.concurrent.ConcurrentSkipListSet;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Queue with idle resources waiting for timeout.
 * The queue manager is notified about timeout events by a background thread.
 * @param E the type of elements held in the queue
 */
class TimeoutQueue<E> {

   /**
    * Manager of the timeout queue.
    */
   interface Manager<E> {

      /**
       * Gets a unique identifier for for the specified element.
       * @param e element to get the unique identifier for
       * @return unique ID
       */
      String getUniqueId(E e);

      /**
       * Gets the time when a timeout period for the specified element elapses.
       * @param e element to get the timeout timestamp for
       * @return computer time in milliseconds
       */
      long getTimeoutTimestamp(E e);

      /**
       * Gets a dummy queue element having timeout timestamp set to the current time.
       * @return element that passed to getTimeoutTimestamp() will return the current time
       */
      E now();

      /**
       * Notifies the manager that a timeout has occurred.
       * The manager is responsible for removing the specified element from the queue.
       * @param e element that has timed out
       * @param log current thread logger
       */
      void timeout(E e, Logger log);

      /**
       * Gets the name for this queue to be used in logging messages.
       * @return name of the queue
       */
      String getName();
   }

   /**
    * Sorted set of queue elements. ConcurrentSkipListSet gives log(n) time cost for add() and remove() operations.
    */
   private final ConcurrentSkipListSet<E> set;

   /**
    * An AtomicInteger is used to maintain the size of the queue. ConcurrentSkipListSet.size() runs in linear time.
    */
   private final AtomicInteger size = new AtomicInteger();

   /**
    * The queue manager.
    */
   private final Manager<E> mgr;

   /**
    * Lock used to block the background task waiting for new elements.
    */
   private final Lock taskLock = new ReentrantLock();

   /**
    * Condition signaled when a new element, that should be checked by the background task, is added to the queue.
    */
   private final Condition newElementAdded = taskLock.newCondition();

   /**
    * Maximum time to sleep when waiting for timeout events.
    */
   private final long maxWaitTime = 1000;

   /**
    * The next wake up time for the background thread.
    */
   private volatile long nextWakeUp;

   /**
    * Constructs an empty queue managed by the specified manager.
    * @param mgr manager of the queue
    */
   TimeoutQueue(Manager<E> mgr) {
      this.mgr = mgr;
      set = new ConcurrentSkipListSet<>();
   }

   /**
    * Gets the size of this queue.
    * @return the current number of elements in the queue.
    */
   int size() {
      return size.intValue();
   }

   Iterator<E> iterator() {
      return set.iterator();
   }

   /**
    * Gets the first (lowest) element.
    * @return the first element, or null if the queue is empty
    */
   private E peekFirst() {
      try {
         return set.first();
      }
      catch(NoSuchElementException e) {
         return null;
      }
   }

   /**
    * Adds the specified element to this queue if it is not already present.
    * @param e element to be added to this queue
    * @return true if this queue did not already contain the specified element
    */
   boolean add(E e) {
      Logger clsLog = LogMgr.getClassLogger(TimeoutQueue.class);
      if(clsLog.debug) {
         clsLog.debug("Adding element [&1] to timeout queue [&2] of size [&3]", e, mgr.getName(), size());
      }
      if(set.add(e)) {
         size.incrementAndGet();
         if(mgr.getTimeoutTimestamp(e) <= nextWakeUp) {
            //
            // Notify the backgroud thread about a new element that will time out before the next wake up time.
            //
            Logger log = PoolUtil.getLogger();
            if(log.trace) {
               log.trace("Signal new element that will time out shortly: &1", e);
            }
            taskLock.lock();
            try {
               newElementAdded.signal();
            }
            finally {
               taskLock.unlock();
            }
         }
         return true;
      }
      else {
         if(clsLog.debug) {
            clsLog.debug("Failed to add element [&1] to timeout queue [&2] of size [&3]", e, mgr.getName(), size());
         }
         return false;
      }
   }

   /**
    * Removes the specified element from this queue if it is present.
    * @param e element to be removed
    * @return true if this queue contained the specified element
    */
   boolean remove(E e) {
      Logger clsLog = LogMgr.getClassLogger(TimeoutQueue.class);
      if(clsLog.debug) {
         clsLog.debug("Removing element [&1] from timeout queue [&2] of size [&3]", e, mgr.getName(), size());
      }
      if(set.remove(e)) {
         size.decrementAndGet();
         return true;
      }
      else {
         if(clsLog.debug) {
            clsLog.debug("Failed to remove element [&1] from timeout queue [&2] of size [&3].", e, mgr.getName(), size());
         }
         return false;
      }
   }

   /**
    * Creates the background task that will generate timeout notifications.
    * The task must be started, otherwise the queue manager will be not notified about timeout events.
    */
   PoolTask createTimeoutTask() {
      return new PoolTask() {

         /**
          * Number of times this thread was signaled about a new element added to the queue.
          */
         private int signalCount;

         private int sleepCount;

         @Override
         String getName() {
            return mgr.getName();
         }

         @Override
         void refreshConfig() {
         }

         @Override
         void doRun() {
            while(true) {
               //
               // Notify the queue manager about new timeouts until there is nothing to do
               //
               long waitTime;
               while(true) {
                  waitTime = tryNotify(clsLog);
                  if(waitTime > 0) {
                     break;
                  }
               }
               //
               // Nothing to do. Wait until a new element is added to the queue or the maximum waiting time elapses.
               //
               if(clsLog.debug) {
                  clsDebug("Nothing to do. Sleep &1 ms. (Sleeps: &2 Signals: &3)", waitTime, sleepCount, signalCount);
               }

               checkAction();

               nextWakeUp = PoolUtil.now() + waitTime;

               taskLock.lock();
               try {
                  sleepCount++;
                  if(newElementAdded.await(waitTime, TimeUnit.MILLISECONDS)) {
                     signalCount++;
                  }
               }
               catch(InterruptedException e) {
               }
               finally {
                  taskLock.unlock();
               }
               checkAction();
            }
         }

         /**
          * Notify the queue manager about new timeouts.
          * @return time to wait before next call to this method, 0 to skip waiting
          */
         private long tryNotify(Logger log) {
            //
            // Peek the first element from the queue.
            //
            if(size() == 0) {
               return maxWaitTime;
            }
            E first = peekFirst();
            if(first == null) {
               return maxWaitTime;
            }
            //
            // Check if the first element has already timed out
            //
            E now = mgr.now();
            long delta = mgr.getTimeoutTimestamp(first) - mgr.getTimeoutTimestamp(now);

            if(log.debug) {
               clsDebug("First: &1", first);
            }


            if(delta > 0) {
               return Math.min(delta + 1, maxWaitTime);
            }
            //
            // There are some elements that have timed out. Notify the manager.
            //
            NavigableSet<E> head = set.headSet(now);
            if(head.size() == 0) {
               return Math.min(delta + 1, maxWaitTime);
            }

            if(log.debug) {
               clsDebug("Notifying timeout for &1 elements", head.size());
            }

            for(E e : head) {
               checkAction();
               try {
                  mgr.timeout(e, log);
               }
               catch(RuntimeException err) {
                  log.warning(err, "Runtime exception occured in backgroud thread [&1]: &2", getName(), err.getMessage());
                  return maxWaitTime;
               }
            }
            return 0;
         }
      };
   }
}
