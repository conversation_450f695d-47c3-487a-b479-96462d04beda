/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.plsql.pool;

import ifs.fnd.log.Logger;
import ifs.fnd.service.IfsConstants;
import ifs.fnd.service.IfsProperties;

import java.util.ArrayList;
import java.util.List;

/**
 * Configuration parameters for tagged connection pool.
 * A new instance of this class may be created, modified and passed to restart or reconfigure.
 * An instance currently used by the pool is frozen and cannot be modified any more.
 * @see TaggedConnectionPoolManager#restart(TaggedConnectionPoolConfig)
 * @see TaggedConnectionPoolManager#reconfigure(TaggedConnectionPoolConfig)
 */
public final class TaggedConnectionPoolConfig {

   /**
    * List with all parameters created at construction time.
    */
   private final List<Parameter> params = new ArrayList<>();

   /**
    * Optional label that identifies this configuration in debug outputs.
    */
   private String configLabel;

   /**
    * True if this instance is frozen (read-only).
    */
   private boolean frozen;

   //=========================================================================================
   // Pool size
   //=========================================================================================

   /**
    * Maximum number of all existing tagged connections.
    */
   public final Parameter connectionPoolMaxSize      = new Parameter("ConnectionPoolMaxSize"     , false);

   /**
    * Desired minimum number of idle connections in the free connection pool.
    */
   public final Parameter freeConnectionPoolMinSize  = new Parameter("FreeConnectionPoolMinSize" , false);

   /**
    * Core number of idle connections in the free connection pool.
    */
   public final Parameter freeConnectionPoolCoreSize = new Parameter("FreeConnectionPoolCoreSize", false);

   //=========================================================================================
   // Timeout. Value in seconds, internal value in milliseconds.
   //=========================================================================================

   /**
    * Timeout for connection request.
    */
   public final Parameter connectionRequestTimeout   = new Parameter("ConnectionRequestTimeout"  , true);

   /**
    * MinTimeout for cursors.
    */
   public final Parameter cursorMinTimeout           = new Parameter("CursorMinTimeout"          , true);

   /**
    * MaxTimeout for cursors.
    */
   public final Parameter cursorMaxTimeout           = new Parameter("CursorMaxTimeout"          , true);

   /**
    * MinTimeout for transactions.
    */
   public final Parameter transactionMinTimeout      = new Parameter("TransactionMinTimeout"     , true);

   /**
    * MaxTimeout for transactions.
    */
   public final Parameter transactionMaxTimeout      = new Parameter("TransactionMaxTimeout"     , true);

   /**
    * Timeout for an initialized session.
    */
   public final Parameter initializedSessionTimeout  = new Parameter("InitializedSessionTimeout" , true);

   /**
    * Timeout for a security checkpoint.
    */
   public final Parameter securityCheckpointTimeout  = new Parameter("SecurityCheckpointTimeout" , true);

   /**
    * Timeout for an idle connection. After this period the connection will be closed by a background thread.
    */
   public final Parameter idleConnectionTimeout      = new Parameter("IdleConnectionTimeout"     , true);

   /**
    * Timeout for a valid connection. After this period the connection will be validated by a background thread.
    */
   public final Parameter validConnectionTimeout     = new Parameter("ValidConnectionTimeout"    , true);

   /**
    * Timeout for empty index entries.
    * The same value is used for {@link DedicatedSessionIndex} and {@link FndUserIndex}.
    */
   public final Parameter indexEntryTimeout          = new Parameter("IndexEntryTimeout"         , true);

   //=========================================================================================
   // Background thread interval. Value in seconds, internal value in milliseconds.
   //=========================================================================================

   /**
    * Interval between executions of a background thread removing old index entries.
    * The same value is used for {@link DedicatedSessionIndex} and {@link FndUserIndex}.
    */
   public final Parameter indexCleanerInterval        = new Parameter("IndexCleanerInterval"        , true);

   /**
    * Interval between executions of a background thread closing idle connections.
    */
   public final Parameter connectionCloserInterval    = new Parameter("ConnectionCloserInterval"    , true);

   /**
    * Interval between executions of a background thread validating idle connections.
    */
   public final Parameter connectionValidatorInterval = new Parameter("ConnectionValidatorInterval" , true);

   //=========================================================================================
   // Config Label
   //=========================================================================================

   /**
    * Sets optional label that will identify this configuration in debug outputs.
    * @param configLabel label text
    */
   public void setConfigLabel(String configLabel) {
      this.configLabel = configLabel;
   }

   /**
    * Gets optional label used to identify this configuration in debug outputs.
    */
   public String getConfigLabel() {
      return configLabel;
   }

   /**
    * Integer configuration parameter.
    */
   public final class Parameter {

      private final String name;

      private final String propertyName;

      private boolean initialized;

      private int value;

      private int internalValue;

      /**
       * True if value is in seconds and internal value in milliseconds.
       */
      private final boolean millis;

      /**
       * Constructs and adds to the internal list a new parameter.
       * @param name camel-case parameter name
       * @param millis true if parameter value is in seconds and internal value in milliseconds, false otherwise
       */
      private Parameter(String name, boolean millis) {
         this.name = name;
         this.millis = millis;
         propertyName = "ifs." + name.substring(0,1).toLowerCase() + name.substring(1);
         Parameter me = this; // skip "Leaking this in constructor" warning
         add(me);
      }

      /**
       * Gets the name of this configuration parameter.
       * @return camel-case name
       */
      public String getName() {
         return name;
      }

      /**
       * Gets the name of IFS property corresponding to this configuration parameter.
       * @return IFS property name
       */
      public String getPropertyName() {
         return propertyName;
      }

      /**
       * Checks if this parameter has been initialized by calling setValue.
       * @return boolean flag
       */
      public boolean isInitialized() {
         return initialized;
      }

      /**
       * Gets the value of this configuration parameter.
       * @return value of an initialized parameter
       * @throws IllegalStateException if this parameter has not been initialized
       */
      public int getValue() {
         if(!initialized) {
            throw new IllegalStateException("Cannot get value from an uninitilalized parameter " + getName());
         }
         return value;
      }

      /**
       * Gets the internal value of this configuration parameter.
       */
      int getInternalValue() {
         return internalValue;
      }

      private void initialize(int defaultValue) {
         Logger log = PoolUtil.getLogger();
         String val = System.getProperty(propertyName);
         if(val != null) {
            if(log.info) {
               log.info("Setting parameter [&1] to value [&2] from System property [&3]", name, val, propertyName);
            }
            try {
               setValue(Integer.parseInt(val));
            }
            catch (NumberFormatException e) {
               throw new IllegalArgumentException("Invalid integer value '" + val + "' for System property " + propertyName, e);
            }
            val = IfsProperties.getSnapshot().getProperty(propertyName);
            if(val != null) {
               log.error("WARNING! Ignored obsolete IFS property [&1]", propertyName);
            }
         }
         else {
            val = IfsProperties.getSnapshot().getProperty(propertyName);
            if(val != null) {
               log.error("WARNING! Setting parameter [&1] to value [&2] from obsolete IFS property [&3]", name, val, propertyName);
               try {
                  setValue(Integer.parseInt(val));
               }
               catch (NumberFormatException e) {
                  throw new IllegalArgumentException("Invalid integer value '" + val + "' for IFS property " + propertyName, e);
               }
            }
            else {
               if(log.info) {
                  log.info("Setting parameter [&1] to default value [&2]", name, defaultValue);
               }
               setValue(defaultValue);
            }
         }
      }

      /**
       * Sets the value of this configuration parameter.
       * After a call to this method the parameter becomes initialized.
       * @param value integer value
       * @throws IllegalStateException if this parameter has been frozen
       */
      public void setValue(int value) {
         if(frozen) {
            throw new IllegalStateException("Cannot modify frozen instance of TaggedConnectionPoolConfig");
         }
         this.value = value;
         this.internalValue = millis ? 1000 * value : value;
         initialized = true;
      }

      private void assign(Parameter p) {
         value = p.value;
         internalValue = p.internalValue;
         initialized = true;
      }

      private String formatValue() {
         return PoolUtil.formatCount(getValue());
      }
   }

   /**
    * Constructs a new instance of pool configuration with uninitialized parameters.
    */
   public TaggedConnectionPoolConfig() {
   }

   /**
    * Constructs a new instance of pool configuration with default values for all parameters.
    * @return an instance initialized from IfsProperties or else from java code.
    */
   static TaggedConnectionPoolConfig getDefaultInstance() {
      TaggedConnectionPoolConfig cfg = new TaggedConnectionPoolConfig();
      /**
       * Pool size parameters.
       */
      cfg.connectionPoolMaxSize     .initialize(50);
      cfg.freeConnectionPoolMinSize .initialize(cfg.connectionPoolMaxSize.getValue() / 8);
      cfg.freeConnectionPoolCoreSize.initialize(cfg.connectionPoolMaxSize.getValue() / 4);
      /**
       * Time interval parameters: value in seconds, internal value in milliseconds.
       */
      cfg.connectionRequestTimeout   .initialize(10);
      cfg.cursorMinTimeout           .initialize(60);
      cfg.cursorMaxTimeout           .initialize(3 * 60);
      cfg.transactionMinTimeout      .initialize(3 * 60);
      cfg.transactionMaxTimeout      .initialize(5 * 60);
      cfg.initializedSessionTimeout  .initialize(24 * 60 * 60);
      cfg.securityCheckpointTimeout  .initialize(2 * 60);
      cfg.idleConnectionTimeout      .initialize(10 * 60);
      cfg.validConnectionTimeout     .initialize(2 * 60);
      cfg.indexEntryTimeout          .initialize(10 * 60);
      cfg.indexCleanerInterval       .initialize(2 * 60);
      cfg.connectionCloserInterval   .initialize(1 * 60);
      cfg.connectionValidatorInterval.initialize(1 * 60);
      return cfg;
   }

   private void add(Parameter param) {
      params.add(param);
   }

   /**
    * Gets a parameter by name.
    * @param name parameter name
    * @return found parameter
    * @throws IllegalArgumentException if a non existing parameter has been specified
    */
   public Parameter get(String name) {
      for(Parameter p : params) {
         if(name.equals(p.getName())) {
            return p;
         }
      }
      throw new IllegalArgumentException(name);
   }

   /**
    * Freezes this instance.
    * IllegalStateException is thrown when setValue is called on a frozen instance.
    */
   void freeze() {
      frozen = true;
   }

   /**
    * Checks if this instance is frozen (read-only).
    */
   boolean isFrozen() {
      return frozen;
   }

   /**
    * Clone this configuration.
    * @return new unfrozen instance of TaggedConnectionPoolConfig
    */
   TaggedConnectionPoolConfig cloneConfig() {
      TaggedConnectionPoolConfig tmp = new TaggedConnectionPoolConfig();
      for(Parameter p : params) {
         tmp.get(p.getName()).assign(p);
      }
      return tmp;
   }

   /**
    * Merge this configuration with initialized parameters from another configuration.
    * @param cfg parameters to apply on this configuration
    */
   void merge(TaggedConnectionPoolConfig cfg) {
      /**
       * Copy only initialized parameters.
       */
      for(Parameter p : cfg.initializedParameters()) {
         get(p.getName()).assign(p);
      }
   }

   /**
    * Creates a list of all initialized parameters in this configuration.
    */
   List<Parameter> initializedParameters() {
      List<Parameter> tmp = new ArrayList<>();
      for(Parameter p : params) {
         if(p.isInitialized()) {
            tmp.add(p);
         }
      }
      return tmp;
   }

   /**
    * Sets default values for all uninitialized parameters from the default configuration.
    * @see #getDefaultInstance()
    */
   void setDefaultValues() {
      TaggedConnectionPoolConfig def = null;
      for(Parameter p : params) {
         if(!p.isInitialized()) {
            if(def == null) {
               def = TaggedConnectionPoolConfig.getDefaultInstance();
            }
            p.assign(def.get(p.getName()));
         }
      }
   }

   //=========================================================================================
   // Current state
   //=========================================================================================

   void getState(StringBuilder buf) {
      String nl = IfsConstants.LINE_SEPARATOR;
      buf.append("Connection Pool Parameters:").append(nl);
      buf.append(" Size:").append(nl);
      buf.append("   Min Pool Size:  ").append(freeConnectionPoolMinSize .formatValue()).append(nl);
      buf.append("   Max Pool Size:  ").append(connectionPoolMaxSize     .formatValue()).append(nl);
      buf.append("   Core Pool Size: ").append(freeConnectionPoolCoreSize.formatValue()).append(nl);
      buf.append(" Timeout [sec]:").append(nl);
      buf.append("   Cursor Min Timeout:         ").append(cursorMinTimeout         .formatValue()).append(nl);
      buf.append("   Cursor Max Timeout:         ").append(cursorMaxTimeout         .formatValue()).append(nl);
      buf.append("   Transaction Min Timeout:    ").append(transactionMinTimeout    .formatValue()).append(nl);
      buf.append("   Transaction Max Timeout:    ").append(transactionMaxTimeout    .formatValue()).append(nl);
      buf.append("   Initialized Session Timeout:").append(initializedSessionTimeout.formatValue()).append(nl);
      buf.append("   Security Checkpoint Timeout:").append(securityCheckpointTimeout.formatValue()).append(nl);
      buf.append("   Idle Connection Timeout:    ").append(idleConnectionTimeout    .formatValue()).append(nl);
      buf.append("   Valid Connection Timeout:   ").append(validConnectionTimeout   .formatValue()).append(nl);
      buf.append("   Index Entry Timeout:        ").append(indexEntryTimeout        .formatValue()).append(nl);
      buf.append("   Connection Request Timeout: ").append(connectionRequestTimeout .formatValue()).append(nl);
      buf.append(" Interval [sec]:").append(nl);
      buf.append("   Index Cleaner Interval:        ").append(indexCleanerInterval       .formatValue()).append(nl);
      buf.append("   Connection Closer Interval   : ").append(connectionCloserInterval   .formatValue()).append(nl);
      buf.append("   Connection Validator Interval: ").append(connectionValidatorInterval.formatValue()).append(nl);
   }
}