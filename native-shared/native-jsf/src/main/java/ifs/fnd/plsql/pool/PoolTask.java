/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private reuse), and all other reuse of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.plsql.pool;

import ifs.fnd.log.Logger;
import java.text.DecimalFormat;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Super class for background threads maintaining the connection pool.
 */
abstract class PoolTask implements Runnable {

   private enum Action {STOP, REFRESH, NONE};

   private static class TaskStoppedException extends RuntimeException {
      TaskStoppedException(String msg) {
         super(msg);
      }
   }

   private DecimalFormat timeFmt;

   private int execCount, successCount, failureCount;

   private long sumExecTime, minExecTime, maxExecTime;

   private AtomicReference<Action> actionFlag = new AtomicReference<>(Action.NONE);

   protected Logger log;
   protected Logger clsLog;

   ScheduledThreadPoolExecutor executor;

   PoolTask() {
      executor = new ScheduledThreadPoolExecutor(1,
         new ThreadFactory() {
         @Override
         public Thread newThread(Runnable r) {
            Thread thread = Executors.defaultThreadFactory().newThread(r);
            thread.setName("IFS PlsqlGateway:" + getName());
            return thread;
         }
      });
   }

   /**
    * Schedules this task for repeated executions.
    * @param initialDelay the time to delay first execution
    * @param interval the delay between the termination of one execution and the commencement of the next
    */
   void schedule(int initialDelay, int interval) {
      executor.scheduleWithFixedDelay(this, initialDelay, interval, TimeUnit.MILLISECONDS);
   }

   /**
    * Starts this task.
    * @param initialDelay the time to delay the execution
    */
   void start(int initialDelay) {
      executor.schedule(this, initialDelay, TimeUnit.MILLISECONDS);
   }

   /**
    * Gets the name of the task.
    */
   abstract String getName();

   /**
    * Refreshes variables dependent on pool configuration.
    */
   abstract void refreshConfig();

   /**
    * Does actual job of this task.
    */
   abstract void doRun();

   @Override
   public void run() {
      long t0 = PoolUtil.now();
      execCount++;
      refreshLoggers();
      if(execCount == 1) {
         if(log.info) {
            clsInfo("Initializing config parameters");
         }
         refreshConfig();
      }

      if(clsLog.info) {
         clsLog.info("Task [&1] started execution [&2]", getName(), execCount);
      }
      try {
         checkAction();
         doRun();
         successCount++;
         long time = computeExecTime(t0);
         if(clsLog.info) {
            clsLog.info("Task [&1] finished execution [&2] in [&3] ms", getName(), execCount, time);
            //clsLog.info(getPoolManager().getPoolSizes());
         }
      }
      catch(TaskStoppedException e) {

      }
      catch(RuntimeException e) {
         if(e instanceof TaskStoppedException) {
            log.warning(e.getMessage());
         }
         else {
            log.warning(e, "Runtime exception occured in backgroud thread [&1]: &2", getName(), e.getMessage());
         }
         failureCount++;
         long time = computeExecTime(t0);
         if(clsLog.info) {
            clsLog.info("Task [&1] aborted execution [&2] in [&3] ms", getName(), execCount, time);
            //clsLog.info(getPoolManager().getPoolSizes());
         }
      }
   }

   void refreshLoggers() {
      PoolUtil.initLoggers();
      log = PoolUtil.getLogger();
      clsLog = PoolUtil.getClassLogger();
   }

   protected void warning(Exception e, String msg, Object... params) {
      log.warning(e, "Task [" + getName() + "]: " + msg, params);
   }

   protected void info(String msg, Object... params) {
      log.info("Task [" + getName() + "]: " + msg, params);
   }

   protected void trace(String msg, Object... params) {
      log.trace("Task [" + getName() + "]: " + msg, params);
   }

   protected void debug(String msg, Object... params) {
      log.debug("Task [" + getName() + "]: " + msg, params);
   }

   protected void clsInfo(String msg, Object... params) {
      clsLog.info("Task [" + getName() + "]: " + msg, params);
   }

   protected void clsTrace(String msg, Object... params) {
      clsLog.trace("Task [" + getName() + "]: " + msg, params);
   }

   protected void clsDebug(String msg, Object... params) {
      clsLog.debug("Task [" + getName() + "]: " + msg, params);
   }

   /**
    * Notifies this task that it should stop running.
    */
   void stop() {
      actionFlag.set(Action.STOP);
      executor.shutdownNow();
   }

   /**
    * Notifies this task that the pool configuration has been changed.
    */
   void configChanged() {
      actionFlag.compareAndSet(Action.NONE, Action.REFRESH);
   }

   void checkAction() {
      switch(actionFlag.get()) {
         case STOP:
            throw new TaskStoppedException("Task [" + getName() + "] has been stopped");
         case REFRESH:
            if(log.info) {
               clsInfo("Refreshing config parameters");
            }
            refreshConfig();
            actionFlag.compareAndSet(Action.REFRESH, Action.NONE);
            break;
         default:
            break;
      }
   }

   private long computeExecTime(long start) {
      long time = PoolUtil.now() - start;
      minExecTime = Math.min(minExecTime, time);
      maxExecTime = Math.max(maxExecTime, time);
      sumExecTime += time;
      return time;
   }

   String formatTime(long time) {
      return timeFmt.format(time / 1000.0);
   }
}
