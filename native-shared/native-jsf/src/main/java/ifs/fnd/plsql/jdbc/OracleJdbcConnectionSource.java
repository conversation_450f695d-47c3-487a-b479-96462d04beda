/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.plsql.jdbc;

import ifs.fnd.log.LogMgr;
import ifs.fnd.log.Logger;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Properties;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.pool.OracleDataSource;

/**
 * Class that wraps Oracle JDBC driver.
 */
public class OracleJdbcConnectionSource extends JdbcConnectionSource {

   /**
    * The datasource used to create new connections.
    */
   private final OracleDataSource ods;

   /**
    * Size of statement cache, 0 means no statement caching.
    */
   private final int maxStatementsLimit;

   /**
    * Constructs an instance of OracleJdbcConnectionSource for specified configuration parameters.
    * @param user       database user name
    * @param password   database user password
    * @param url        database connection url
    * @param maxStatementsLimit the size of statement cache, 0 means no statement caching
    * @throws SQLException if a JDBC error occurs
    */
   OracleJdbcConnectionSource(String user, String password, String url, int maxStatementsLimit) throws SQLException {
      this.maxStatementsLimit = maxStatementsLimit;
      ods = new OracleDataSource();
      ods.setUser(user);
      ods.setPassword(password);
      ods.setURL(url);
      if(maxStatementsLimit > 0) {
         ods.setImplicitCachingEnabled(true);
      }

      Properties prop = new Properties();
      prop.setProperty("v$session.program", "IFS PLSQL Gateway");
      ods.setConnectionProperties(prop);
   }

   @Override
   Connection getConnection() throws SQLException {

      Logger log = LogMgr.getDatabaseLogger();
      if(log.debug)
         log.debug("Getting connection ...");

      OracleConnection c = (OracleConnection) ods.getConnection();

      if(maxStatementsLimit > 0)
         c.setStatementCacheSize(maxStatementsLimit);

      if(log.debug)
         log.debug("Got connection &1 wrapped=&2", c.toString(), c.unwrap());

      return c;
   }

   @Override
   void returnConnection(Connection c) throws SQLException {
      OracleConnection oraConn = (OracleConnection) c;
      oraConn.close();
      Logger log = LogMgr.getDatabaseLogger();
      if(log.debug)
         log.debug("Closed connection &1 wrapped=&2", oraConn.toString(), oraConn.unwrap());
   }
}

