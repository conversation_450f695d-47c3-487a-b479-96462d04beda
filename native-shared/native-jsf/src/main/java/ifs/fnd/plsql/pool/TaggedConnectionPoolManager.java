/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.plsql.pool;

import ifs.fnd.log.LogMgr;
import static ifs.fnd.plsql.pool.TaggedConnection.State.*;

import ifs.fnd.log.Logger;
import ifs.fnd.plsql.jdbc.JdbcConnectionSource;
import ifs.fnd.service.IfsEncryption;
import ifs.fnd.service.IfsProperties;
import ifs.fnd.util.Str;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Public API for access to the tagged connection pool.
 */
public final class TaggedConnectionPoolManager {

   //Use proxy authentication by default
   private final static boolean USE_PROXY_SESSIONS = IfsProperties.getSnapshot().getProperty("ifs.proxyUserAuthentication", true);

   /**
    * The singleton instance of the connection pool manager.
    * Null value means that the pool is not initialized.
    */
   private static final AtomicReference<TaggedConnectionPoolManager> MGR = new AtomicReference<>();

   /**
    * Gets the singleton instance of the connection pool manager.
    * @return the only instance of TaggedConnectionPoolManager
    * @throws IllegalStateException if the connection pool has not been initialized
    */
   public static TaggedConnectionPoolManager getInstance() throws IllegalStateException {
      TaggedConnectionPoolManager mgr = MGR.get();
      if(mgr == null) {
         throw new IllegalStateException("Tagged Connection Pool has not been initialized");
      }
      return mgr;
   }

   /**
    * In test mode no database connections are created.
    */
   private boolean testMode;

   /**
    * Configuration parameters for the connection pool.
    * The variable is initialized with default values fetched from IfsProperties or from java code and validated by start().
    * After initialization the parameters can be modified only by a synchronized method restart() or reconfigure().
    */
   private final AtomicReference<TaggedConnectionPoolConfig> config = new AtomicReference<>(TaggedConnectionPoolConfig.getDefaultInstance());

   /**
    * Collection of all existing instances of TaggedConnection.
    */
   private final ConcurrentMap<TaggedConnection, Boolean> register = new ConcurrentHashMap<>(getConfig().connectionPoolMaxSize.getInternalValue());

   /**
    * Sup-pool for connections in state RESERVED.
    */
   private final ReservedConnectionPool reservedPool;

   /**
    * Sup-pool for connections in state UNRESERVED, INITIALIZED and ANONYMOUS.
    */
   private final FreeConnectionPool freePool;

   /**
    * Dedicated session index giving access to a dedicated connection owned by a specific client session.
    */
   private final DedicatedSessionIndex sessionIndex;

   /**
    * Index giving access to free connections initialized for a specific FND user.
    */
   private final FndUserIndex fndUserIndex;

   /**
    * Queue with resources (transactions, cursors, sessions) that must be closed when MaxTimeout occurs.
    */
   private final CloseableResourceQueue resourceQueue;

   /**
    * Number of destroyed connections after the last call to shutdown.
    */
   private final AtomicInteger destroyedCount = new AtomicInteger();

   private final TaggedConnectionStatistics destroyedConnectionStatistics = new TaggedConnectionStatistics();

   private final AtomicInteger requestFailureCount = new AtomicInteger();

   private final AtomicInteger connectionRequestTimeoutCount = new AtomicInteger();

   /**
    * The number of connections created by client requests.
    */
   private final AtomicInteger connectionsCreatedByRequest = new AtomicInteger();

   /**
    * The number of connections created by a background thread.
    */
   private final AtomicInteger connectionsCreatedByTask = new AtomicInteger();

   /**
    * List with all background tasks.
    */
   private final List<PoolTask> tasks = new ArrayList<>();

   private TaggedConnectionPoolManager() {
      reservedPool  = new ReservedConnectionPool();
      freePool      = new FreeConnectionPool(this);
      fndUserIndex  = new FndUserIndex(this);
      sessionIndex  = new DedicatedSessionIndex(this, freePool, fndUserIndex);
      resourceQueue = new CloseableResourceQueue();
   }

   /**
    * Turns the test mode on.
    * In test mode no database connections are created.
    */
   public void setTestMode() {
      testMode = true;
   }

   /**
    * Checks if the test mode is on.
    * In test mode no database connections are created.
    * @return true if in test mode, false otherwise
    */
   public boolean isTestMode() {
      return testMode;
   }

   CloseableResourceQueue getResourceQueue() {
      return resourceQueue;
   }

   FndUserIndex getFndUserIndex() {
      return fndUserIndex;
   }

   ReservedConnectionPool getReservedPool() {
      return reservedPool;
   }

   FreeConnectionPool getFreePool() {
      return freePool;
   }

   DedicatedSessionIndex getSessionIndex() {
      return sessionIndex;
   }

   ArrayList<TaggedConnection> getRegisterSnapshot() {
      return new ArrayList<>(register.keySet());
   }

   /**
    * Gets the currently used pool configuration.
    * @return a frozen (read-only) instance
    */
   public TaggedConnectionPoolConfig getConfig() {
      return config.get();
   }

   /**
    * Creates a copy of the currently used pool configuration.
    * @return an unfrozen (read-write) instance
    */
   TaggedConnectionPoolConfig cloneConfig() {
      return config.get().cloneConfig();
   }

   String getPoolSizes() {
      return getPoolSize() + " (Reserved: " + getReservedPoolSize() + " Free: " + getFreePoolSize() + ")";
   }

   int getDestroyedCount() {
      return destroyedCount.get();
   }

   int getRequestFailureCount() {
      return requestFailureCount.get();
   }

   int getConnectionRequestTimeoutCount() {
      return connectionRequestTimeoutCount.get();
   }

   int getConnectionsCreatedByRequest() {
      return connectionsCreatedByRequest.get();
   }

   int getConnectionsCreatedByTask() {
      return connectionsCreatedByTask.get();
   }

   //============================================================================================
   // Startup & Shutdown
   //============================================================================================

   /**
    * Starts up the connection pool and the background threads used for maintenance of the pool.
    * The method does nothing if the pool has been already started.
    */
   public static synchronized void start() throws SQLException, ConfigurationException {
      startImpl(null);
   }

   private static void startImpl(TaggedConnectionPoolConfig newConfig) throws SQLException, ConfigurationException {
      TaggedConnectionPoolManager mgr = MGR.get();
      if(mgr == null) {
         mgr = new TaggedConnectionPoolManager();
         if(newConfig != null) {
            mgr.config.set(newConfig);
         }
         mgr.init();
         MGR.set(mgr);
         mgr.spoolPoolStatistics();
      }
   }

   private void init() throws SQLException, ConfigurationException {
      PoolInfo info = new PoolInfo();
      info.log("Starting tagged connection pool");

      TaggedConnectionPoolConfig cfg = getConfig();
      if(!cfg.isFrozen()) {
         /**
          * Validate default configuration fetched from IFS properties and/or java code.
          */
         validateAndFreezeConfigurationParameters(cfg);
      }

      /*
       * Initialize JDBC data source.
       */
      String jdbcUrl = IfsProperties.getSnapshot().getJdbcUrl();
      String dbUser = IfsProperties.getSnapshot().getDbUser();
      String dbPassword = IfsProperties.getSnapshot().getDbPassword();

      try {
         dbPassword = IfsEncryption.decrypt(dbPassword);
      }
      catch(IfsEncryption.EncryptionException e) {
         throw new SQLException(e);
      }
      JdbcConnectionSource.init(dbUser, dbPassword, jdbcUrl, IfsProperties.getSnapshot().getStatementCacheSize());

      /**
       * (1) Start background thread removing old empty entries from the dedicated session index.
       */
      scheduleTask(sessionIndex.createCleanerTask(), 100, cfg.indexCleanerInterval.getInternalValue());
      /**
       * (2) Start background thread removing old empty entries from the FND user index.
       */
      scheduleTask(fndUserIndex.createCleanerTask(), 300, cfg.indexCleanerInterval.getInternalValue());
      /**
       * (3) Start background thread moving dedicated connections from the reserved pool to the free pool.
       */
      startTask(reservedPool.createTimeoutTask(), 400);
      /**
       * (4) Start background thread creating new database connections.
       */
      startTask(freePool.createCreatorTask(), 500);
      /**
       * (5) Start background thread closing idle database connections.
       */
      scheduleTask(freePool.createCloserTask(), 700, cfg.connectionCloserInterval.getInternalValue());
      /**
       * (6) Start background thread validating idle database connections.
       */
      scheduleTask(createValidatorTask(), 800, cfg.connectionValidatorInterval.getInternalValue());
      /**
       * (7) Start background thread closing resources (transactions, cursors, sessions) that have timed out.
       */
      startTask(resourceQueue.createTimeoutTask(), 900);

      info.log("Finished startup of tagged connection pool");
   }

   private void scheduleTask(PoolTask task, int initialDelay, int interval) {
      tasks.add(task);
      task.schedule(initialDelay, interval);
   }

   private void startTask(PoolTask task, int initialDelay) {
      tasks.add(task);
      task.start(initialDelay);
   }

   /**
    * Shuts down the connection pool and its background threads.
    * The method does nothing if the pool is not running.
    */
   public static synchronized void shutdown() {
      TaggedConnectionPoolManager mgr = MGR.get();
      if(mgr != null) {
         mgr.shutdownImpl(new PoolInfo());
         MGR.set(null);
      }
   }

   private synchronized void shutdownImpl(PoolInfo info) {
      info.log("Started shutdown of tagged connection pool (register size = " + register.size() + ")");

      info.log("Stopping background threads");
      for(PoolTask task : tasks) {
         task.stop();
      }

      info.log("Closing database connections");
      for(TaggedConnection x : register.keySet()) {
         info.log("Destroying tagged connection " + x.label());
         x.destroy(info.getLogger());
      }

      JdbcConnectionSource.clear();

      info.log("Finished shutdown of tagged connection pool (register size = " + register.size() + ")");
   }

   /**
    * Restarts the connection pool using new configuration parameters.
    * The method will close all existing connections in the pool and restart the background threads.
    * Pool statistics are cleared.
    * Note! This method is not 100% safe and should be used only during development.
    * @param newConfig an instance representing new configuration parameters
    * @throws ConfigurationException if validation of the new configuration parameters fails
    * @throws SQLException if restart of the pool fails
    */
   public synchronized void restart(TaggedConnectionPoolConfig newConfig) throws SQLException, ConfigurationException {
      validateAndFreezeConfigurationParameters(newConfig);
      shutdown();
      startImpl(newConfig);
   }

   /**
    * Reconfigures some connection pool parameters without restarting the pool itself.
    * Pool statistics are not cleared.
    * @param cfg an instance containing parameters to be modified
    * @throws ReconfigurationException if an invalid parameter, or a parameter requiring restart of the pool, has been specified
    * @throws ConfigurationException if validation of the new configuration parameters fails
    */
   public synchronized void reconfigure(TaggedConnectionPoolConfig cfg) throws ConfigurationException, ReconfigurationException {
      /**
       * Validate reconfigured parameters.
       */
      validateReconfigurationParameters(cfg);
      /**
       * Validate, freeze and set the new configuration.
       */
      TaggedConnectionPoolConfig newConfig = cloneConfig();
      newConfig.merge(cfg);
      validateAndFreezeConfigurationParameters(newConfig);
      config.set(newConfig);
      /**
       * Refresh configuration parameters in free pool.
       */
      freePool.refreshConfig();
      /**
       * Notify background threads about changed configuration.
       */
      for(PoolTask task : tasks) {
         task.configChanged();
      }
   }

   /**
    * Close all database connections without aborting ongoing transactions and cursors.
    * Connections, currently used or reserved by clients, will be closed as soon as they are returned/moved to the free pool.
    * New connections will be created, in background, to satisfy the current pool configuration.
    * The method does nothing if the connection pool is not running in the current application.
    */
   public static void flushPool() {
      TaggedConnectionPoolManager mgr = MGR.get();
      if(mgr != null) {
         mgr.flushPoolImpl();
      }
   }

   private synchronized void flushPoolImpl() {
      /*
       * Create a snapshot of all existing connections. New connections will be not affected.
       */
      ArrayList<TaggedConnection> all = getRegisterSnapshot();
      Logger log = new PoolInfo().getLogger();
      if(log.info) {
         log.info("Trying to close &1 currently existing connections", all.size());
      }
      int closed = 0;
      int marked = 0;
      for(TaggedConnection x : all) {
         synchronized(x) {
            if(log.trace) {
               log.trace("Got connection &1", x.label());
            }
            TaggedConnection.State state = x.getState();
            if(state == USED || state == RESERVED) {
               x.destroyWhenFree();
               marked++;
               if(log.trace) {
                  log.trace("Marked connection &1", x.shortLabel());
               }
            }
            else {
               if(log.trace) {
                  log.trace("Destroying connection &1", x.shortLabel());
               }
               x.destroy(log);
               closed++;
            }
         }
      }
      if(log.info) {
         log.info("Processed &1 connections:\n   Closed: &2\n   Marked: &3",
                  closed + marked, PoolUtil.formatCount(closed), PoolUtil.formatCount(marked));
      }
   }

   /**
    * Mark all connections in the pool for refreshing of active roles.
    * The roles for each marked connection will be refreshed next time it is used by a client.
    */
   public static void refreshSessionRoles() {
      TaggedConnectionPoolManager mgr = MGR.get();
      if(mgr != null) {
         mgr.refreshSessionRolesImpl();
      }
   }

   private synchronized void refreshSessionRolesImpl() {
      /*
       * Create a snapshot of all existing connections. New connections will be not affected.
       */
      ArrayList<TaggedConnection> all = getRegisterSnapshot();
      Logger log = new PoolInfo().getLogger();
      if(log.info) {
         log.info("Marking &1 currently existing connections", all.size());
      }
      for(TaggedConnection x : all) {
         synchronized(x) {
            if(log.trace) {
               log.trace("Marking connection &1", x.label());
            }
            /*
             * If the connection is USED and already initialized, then the roles will be refreshed by next request.
             */
            x.setRefreshSessionRoles();
         }
      }
   }

   /**
    * Exception thrown when validation of pool configuration parameters fails.
    */
   public static class ConfigurationException extends Exception {
      ConfigurationException(String message, Object... params) {
         super(Str.formatMessage(message, params));
      }
   }

   /**
    * Exception thrown when an invalid parameter has been passed to reconfigure.
    */
   public static class ReconfigurationException extends Exception {
      ReconfigurationException(String message, Object... params) {
         super(Str.formatMessage(message, params));
      }
   }

   private void validateAndFreezeConfigurationParameters(TaggedConnectionPoolConfig cfg) throws ConfigurationException {
      cfg.setDefaultValues();
      int min = cfg.freeConnectionPoolMinSize.getInternalValue();
      int core = cfg.freeConnectionPoolCoreSize.getInternalValue();
      int max = cfg.connectionPoolMaxSize.getInternalValue();
      if((min >= 0 && min <= core && core <= max) == false) {
         throw new ConfigurationException("Broken pool size constraint: 0 <= [Min:&1] <= [Core:&2] <= [Max:&3]", min, core, max);
      }
      cfg.freeze();
   }

   private void validateReconfigurationParameters(TaggedConnectionPoolConfig cfg) throws ReconfigurationException {
      for(TaggedConnectionPoolConfig.Parameter p : cfg.initializedParameters()) {
         if(p == cfg.connectionPoolMaxSize) {
            int existing = getRegisterSize();
            int newValue = p.getInternalValue();
            if(newValue < existing) {
               throw new ReconfigurationException("Pool configuration parameter [&1] cannot be set to value [&2] lower than the number of existing connections [&3]", p.getName(), newValue, existing);
            }
         }
         else if(p != cfg.indexCleanerInterval && p != cfg.connectionCloserInterval && p != cfg.connectionValidatorInterval) {
            /**
             * IndexCleanerInterval is used when starting index cleaner tasks SessionIndex.Cleaner and FndUserIndex.Cleaner,
             * ConnectionCloserInterval is used when starting Connection.Closer task.
             * ConnectionValidatorInterval is used when starting Connection.Validator task.
             */
         }
         else {
            throw new ReconfigurationException("Parameter [&1] cannot be reconfigured without restarting the connection pool.", p.getName());
         }
      }
   }

   public static class PoolInfo {

      private final Logger log;

      PoolInfo() {
         Logger tmp = PoolUtil.getPoolClassLogger();
         if(!tmp.info) {
            tmp = PoolUtil.getLogger();
         }
         PoolUtil.initLogger(tmp);
         log = tmp;
      }

      Logger getLogger() {
         return log;
      }

      void log(String msg) {
         if(log.info) {
            log.info(msg);
         }
      }
   }

   //============================================================================================
   // Public API
   //============================================================================================

   /**
    * Gets a connection from the connection pool.
    * The method blocks until a connection is available.
    * An invalid connection found in the pool is destroyed.
    * @param clientSessionId client session ID or null if an anonymous connection is required
    * @param fndUserId FND user identity, or null if not known
    * @param initKey current value of serialized in-parameters to PLSQL initialization procedure (Proxy_Login_SYS.Init_Session_)
    * @param clientId client identifier, or null if not known
    * @return valid connection in state USED
    */
   public TaggedConnection getConnection(String clientSessionId, String fndUserId, String initKey, String clientId) throws SQLException {
      Logger log = PoolUtil.getAndInitRequestLogger();
      while(true) {
         TaggedConnection x = sessionIndex.get(clientSessionId, fndUserId, initKey, clientId, log);
         TimeEvent event = x.getIsValidEvent();
         event.start();
         boolean valid = true; //JdbcConnectionSource.isConnectionValid(x.getJdbcConnection());
         event.end();
         if(valid) {
            if(log.info) {
               log.info("Got connection from pool in &1 ms: &2", x.getGetConnectionEvent().getLastTime(), x.shortLabel());
            }
            return x;
         }
         if(log.trace) {
            log.trace("Destroying invalid connection fetched from pool: &1", x.toString());
         }
         x.destroy(log);
      }
   }

   /**
    * Returns a connection to the pool after a request.
    * @param x tagged connection
    */
   public void returnConnection(TaggedConnection x) {
      Logger log = PoolUtil.getLogger();
      if(log.info) {
         log.info(x.isDestroyed() ? "Taking out connection of pool: &1" : "Returning connection to pool: &1", x.shortLabel());
      }
      x.returnToPool(log);
   }

   /**
    * Gets the current number of connections in the connection pool.
    * @return number of connections waiting in the pool for client requests
    */
   public int getPoolSize() {
      return getFreePoolSize() + getReservedPoolSize();
   }

   /**
    * Gets the total number of existing tagged connections.
    * @return number of connections waiting in the pool plus those currently used by client requests
    */
   public int getRegisterSize() {
      return register.size();
   }

   /**
    * Gets the current number of connections in the free connection pool.
    * @return number of connections in state UNRESERVED, INITIALIZED or ANONYMOUS waiting in the free pool for client requests
    */
   public int getFreePoolSize() {
      return freePool.size();
   }

   /**
    * Gets the current number of connections in the reserved connection pool.
    * @return number of connections in state RESERVED
    */
   public int getReservedPoolSize() {
      return reservedPool.size();
   }

   /**
    * Gets the number of tagged connections currently used by client requests.
    * @return number of connections in state USED
    */
   public int getUsedPoolSize() {
      return getRegisterSize() - getPoolSize();
   }

   /**
    * Checks if Oracle Proxy Authentication mode is turned on.
    * @return true to use Proxy Authentication, false otherwise
    */
   public boolean useProxySessions() {
      return USE_PROXY_SESSIONS;
   }

   //============================================================================================
   // State transition
   //============================================================================================

   /**
    * Field used to reserve place for new pool connections. After reservation, which
    * is synchronized, new JDBC connections can be created in parallel.
    */
   private final AtomicInteger reservedRegisterSize = new AtomicInteger();

   enum ConnectionCreator {REQUEST, TASK};

   /**
    * Creates and registers a new tagged connection.
    * @return tagged connection in state NEW
    *          or null if the maximum number of tagged connections already exists
    * @throws SQLException if a SQL error occurs
    */
   TaggedConnection createNewConnection(ConnectionCreator creator) throws SQLException {
      int maxSize = getConfig().connectionPoolMaxSize.getInternalValue();
      if(register.size() >= maxSize) {
         return null;
      }
      /*
       * Reserve the place for a new tagged connection. Normally, this code is executed only by
       * the background thread maintaining the size of the free connection pool, but it may be
       * also executed by request threads.
       */
      synchronized(this) {
         if(reservedRegisterSize.get() >= maxSize) {
            return null;
         }
         reservedRegisterSize.incrementAndGet();
      }
      //
      // Create new JDBC connection and register new tagged connection.
      // This code may be executed by many threads in parallel.
      //
      try {
         TaggedConnection x;
         if(isTestMode()) {
            x = new StaTestConnection(null, this);
         }
         else {
            Connection c = JdbcConnectionSource.createConnection();
            try (Statement stmt = c.createStatement(); ResultSet rs = stmt.executeQuery("select sys_context('USERENV', 'SID'), sys_context('USERENV', 'SESSIONID') from dual")) {
               rs.next();
               x = new TaggedConnection(c, JdbcConnectionSource.getDatabaseUser(), rs.getLong(1), rs.getLong(2), this);
            }
         }
         register.put(x, Boolean.TRUE);
         if(creator == ConnectionCreator.REQUEST) {
            connectionsCreatedByRequest.incrementAndGet();
         }
         else {
            connectionsCreatedByTask.incrementAndGet();
         }
         return x;
      }
      catch(SQLException e) {
         //
         // Unreserve reserved slot in the register
         //
         synchronized(this) { //ToDo: is synchronized necessary here?
            reservedRegisterSize.decrementAndGet();
         }
         throw e;
      }
   }

   /**
    * General state transition method for tagged connections.
    * The method validates the state transition and accordingly updates the connection sub-pools.
    * @param x connection to set state on
    * @param target target state
    */
   void setConnectionState(TaggedConnection x, TaggedConnection.State target) {
      TaggedConnection.State current = x.getState();
      /**
       * Validate state transition
       */
      validateStateTransition(current, target);
      /**
       * Remove connection from the current sub-pool
       */
      if(target == DESTROYED) {
         reservedPool.remove(x);
         freePool.remove(x);
         sessionIndex.remove(x);
         fndUserIndex.remove(x);
         register.remove(x);
         reservedRegisterSize.decrementAndGet();
         destroyedCount.incrementAndGet();
         destroyedConnectionStatistics.synchronizedCombine(x);
      }
      else if(current == RESERVED) {
         reservedPool.remove(x);
      }
      else if(current == UNRESERVED || current == ANONYMOUS) {
         freePool.remove(x);
      }
      else if(current == INITIALIZED) {
         freePool.remove(x);
         fndUserIndex.remove(x);
         /**
          * Connection could have already been removed from FndUserIndex by take() or uninitialize(),
          * Connection already removed from CloseableResourceQueue by reuseSession() or timeout().
          */
      }
      /**
       * Change the connection state and compute the new sort key.
       */
      x.setState(target);
      /**
       * Add connection to a new sub-pool.
       */
      if(target == RESERVED) {
         reservedPool.add(x);
      }
      else if(target == UNRESERVED || target == ANONYMOUS) {
         freePool.add(x);
      }
      else if(target == INITIALIZED) {
         freePool.add(x);
         fndUserIndex.add(x);
         x.addInitializedSessionToResourceQueue();
      }
      Logger log = LogMgr.getClassLogger(TaggedConnection.State.class);
      if(log.info) {
         logStateTransition(log, x.getNr(), current, target);
      }
   }

   private void logStateTransition(Logger log, int nr, TaggedConnection.State current, TaggedConnection.State target) {
      String poolSize = poolSizeLogImage(freePool.getQueueLength());
      String transition = Str.rpad("{nr=" + nr + "} " + current + " -> " + target, 32);
      log.info("&1 &2", transition, poolSize);
   }

   String poolSizeLogImage(int waiting) {
      int all      = getRegisterSize();
      int free     = getFreePoolSize();
      int reserved = getReservedPoolSize();
      int used     = all - free - reserved;
      return Str.formatMessage("Pool[&1]: Waiting=&2 Used=&3 Free=&4 Reserved=&5 &6 &7 &8 &9",
                                all, waiting, used, free, reserved,
                                PoolUtil.replicate('*', waiting), PoolUtil.replicate('U', used), PoolUtil.replicate('F', free), PoolUtil.replicate('R', reserved));
   }

   private void validateStateTransition(TaggedConnection.State current, TaggedConnection.State target) {
      if(current == NEW && (target == ANONYMOUS || target == USED)) {
      }

      else if(current == ANONYMOUS && target == USED) {
      }

      else if(current == USED && (target == ANONYMOUS || target == INITIALIZED || target == RESERVED || target == UNRESERVED)) {
      }

      else if(current == RESERVED && (target == UNRESERVED || target == INITIALIZED || target == USED)) {
      }

      else if(current == UNRESERVED && (target == INITIALIZED || target == USED)) {
      }

      else if(current == INITIALIZED && (target == ANONYMOUS || target == USED)) {
      }

      else if(target == DESTROYED) {
      }

      else {
        throw new IllegalStateException("Illegal state transition for TaggedConnection: current=" + current + " target=" + target);
      }
   }

   //============================================================================================
   // Background validation
   //============================================================================================

   /**
    * Creates the background task that validates database connections.
    */
   private PoolTask createValidatorTask() {
      return new PoolTask() {

         private long totalValidationCount;
         private long totalValidationTime;

         private int validConnectionTimeout;

         @Override
         String getName() {
            return "Connection.Validator";
         }

         @Override
         void refreshConfig() {
            TaggedConnectionPoolConfig cfg = getConfig();
            validConnectionTimeout = cfg.validConnectionTimeout.getInternalValue();
            if(clsLog.info) {
               clsInfo("   ValidConnectionTimeout: &1", validConnectionTimeout);
            }
         }

         @Override
         void doRun() {
            if(clsLog.debug) {
               clsDebug("Started scan of &1 registered connections", getRegisterSize());
            }
            int validationCount = 0;
            for(TaggedConnection x : register.keySet()) {
               synchronized(x) {
                  if(clsLog.debug) {
                     clsDebug("Got connection &1", x.label());
                  }
                  switch(x.getState()) {
                     case NEW:
                     case USED:
                     case DESTROYED:
                        break;

                     default:
                        boolean passedTimeout = x.getTimestamp() + validConnectionTimeout < PoolUtil.now();
                        if(passedTimeout) {
                           validationCount++;
                           if(clsLog.debug) {
                              clsDebug("   ValidConnectionTimeout has passed. Validating connection.");
                           }
                           validateConnection(x);
                        }
                        else {
                           if(clsLog.debug) {
                              clsDebug("   ValidConnectionTimeout has not passed. Skipping validation.");
                           }
                        }
                        break;
                  }
               }
               checkAction();
            }
            totalValidationCount += validationCount;
            if(clsLog.debug) {
               clsDebug("Finished scan of registered connections. Done &1 validations.", validationCount);
               clsDebug("   Total Validation Count: &1", totalValidationCount);
               clsDebug("   Total Validation Time:  &1 ms", totalValidationTime);
            }
         }

         private void validateConnection(TaggedConnection x) {
            long t1 = PoolUtil.now();
            boolean valid = JdbcConnectionSource.isConnectionValid(x.getJdbcConnection());
            long time = PoolUtil.now() - t1;
            totalValidationTime += time;
            if(clsLog.debug) {
               clsDebug("   Validation done in &1 ms. Result: &2", time, valid);
            }
            if(!valid) {
               if(clsLog.info) {
                  clsInfo("   Destroying invalid connection &1", x.shortLabel());
               }
               x.destroy(clsLog);
            }
         }
      };
   }

   //============================================================================================
   // Connection Pool Statistics
   //============================================================================================

   public void incRequestFailureCount() {
      requestFailureCount.incrementAndGet();
   }

   void incConnectionRequestTimeoutCount() {
      connectionRequestTimeoutCount.incrementAndGet();
   }

   /**
    * Gets a snapshot of the connection pool.
    * @return an array list of all existing database connections in the pool
    */
   public ArrayList<DatabaseSessionSnapshot> listDatabaseSessions() {
      ArrayList<DatabaseSessionSnapshot> list = new ArrayList<>();
      for(TaggedConnection x : getRegisterSnapshot()) {
         list.add(new DatabaseSessionSnapshot(x));
      }
      return list;
   }

   /**
    * Gets the current state of the pool.
    * @return text describing the current state of the pool inclusive statistic information
    */
   public String getPoolState() {
      return new TaggedConnectionPoolStatistics(this).getState();
   }

   /**
    * Spools the current state of the pool and statistic data to log output.
    */
   public void spoolPoolStatistics() {
      Logger log = new PoolInfo().getLogger();
      if(log.info) {
         log.info(getPoolState());
      }
   }

   /**
    * Gets statistics gathered from existing and non-existing (destroyed) tagged connections.
    */
   public TaggedConnectionStatistics getTaggedConnectionStatistics() {
      TaggedConnectionStatistics current = getCurrentConnectionStatistics();
      TaggedConnectionStatistics destroyed = getDetsroyedConnectionStatistics();
      current.combine(destroyed);
      return current;
   }

   /**
    * Gets statistics gathered from existing tagged connections.
    */
   TaggedConnectionStatistics getCurrentConnectionStatistics() {
      TaggedConnectionStatistics current = new TaggedConnectionStatistics();
      getRegisterSnapshot().stream().forEach((x) -> {
         synchronized(x) {
            current.combine(x);
         }
      });
      return current;
   }

   /**
    * Gets statistics gathered from destroyed tagged connections.
    */
   TaggedConnectionStatistics getDetsroyedConnectionStatistics() {
      TaggedConnectionStatistics destroyed = new TaggedConnectionStatistics();
      destroyedConnectionStatistics.synchronizedCombineTo(destroyed);
      return destroyed;
   }
}
