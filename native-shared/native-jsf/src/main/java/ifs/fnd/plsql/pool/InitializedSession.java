/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.plsql.pool;

import ifs.fnd.log.Logger;
import java.util.UUID;

/**
 * Class representing an initialized FND session.
 */
final class InitializedSession extends CloseableResource {

   /**
    * Unique identifier required for a closeable resource.
    */
   private final String uuid;

   /**
    * The FND user returned from the initialization procedure.
    */
   private final String fndUser;

   /**
    * The directory ID passed as input to the initialization procedure.
    */
   private final String directoryId;

   /**
    * The language code passed as input to the initialization procedure.
    */
   private final String language;

   /**
    * Serialized in-parameters to PLSQL initialization procedure that have been used to initialize this session.
    */
   private final String initKey;

   /**
    * Constructs an instance representing an initialized FND session.
    * @param fndUser identity of FND user returned from PLSQL initialization procedure
    * @param directoryId directory ID of the end user running this session
    * @param language language code passed to the PLSQL initialization procedure
    * @param initKey serialized in-parameters to PLSQL initialization procedure
    */
   InitializedSession(TaggedConnection owner, String fndUser, String directoryId, String language, String initKey) {
      super(owner);
      uuid = UUID.randomUUID().toString();
      this.fndUser = fndUser;
      this.directoryId = directoryId;
      this.language = language;
      this.initKey = initKey;
   }

   /**
    * Gets unique identifier for this initialized session.
    * @return UUID generated at construction time
    */
   @Override
   String getUniqueId() {
      return uuid;
   }

   String getFndUser() {
      return fndUser;
   }

   String getDirectoryId() {
      return directoryId;
   }

   String getLanguage() {
      return language;
   }

   String getInitKey() {
      return initKey;
   }

   /**
    * Gets the timestamp when this session should be closed, if idle.
    * @return "computer time" measured in milliseconds
    */
   @Override
   long getCloseTimestamp() {
      return getTimestamp() + getConfig().initializedSessionTimeout.getInternalValue();
   }

   /**
    * Closes this session without throwing an exception.
    */
   @Override
   void safeClose(Logger log) {
      getTaggedConnection().uninitialize(log);
   }

   @Override
   public String toString() {
      return super.toString("InitializedSession", "");
   }
}
