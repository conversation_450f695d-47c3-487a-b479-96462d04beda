/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.plsql.pool;

import ifs.fnd.log.Logger;
import ifs.fnd.service.IfsConstants;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Index with dedicated connections mapped to client sessions.
 * An entry in this index contains a connection owned by (dedicated to) a client session.
 * A connection reused by another client session, or another client, is moved to a new entry in the index.
 * A dedicated session that has timed out is not owned by a client session any more,
 * and as such is removed from the index.
 * Old empty index entries are removed from the index by a background thread.
 */
final class DedicatedSessionIndex {

   /**
    * An entry in the dedicated session index.
    * It contains a connection owned by a specific client session.
    * The class is synchronized to guarantee consistence of the index, despite the fact that normally it is accessed only by one client session.
    */
   final static class Entry {

      private final String clientSessionId;

      private TaggedConnection x;

      private long timestamp;

      Entry(String clientSessionId) {
         this.clientSessionId = clientSessionId;
         touch();
      }

      String getClientSessionId() {
         return clientSessionId;
      }

      long getTimestamp() {
         return timestamp;
      }

      private void touch() {
         timestamp = PoolUtil.now();
      }

      synchronized TaggedConnection getConnection() {
         touch();
         return x;
      }

      synchronized boolean isEmpty() {
         return x == null;
      }

      synchronized void setConnection(TaggedConnection x) {
         this.x = x;
         touch();
      }

      /**
       * Clears this entry if the specified connection is currently stored in the entry.
       */
      synchronized void clearConnection(TaggedConnection x) {
         if(this.x == x) {
            this.x = null;
         }
         touch();
      }

      @Override
      public synchronized String toString() {
         return clientSessionId + " -> " + (x == null ? "" : x.label());
      }
   }

   /**
    * Index with dedicated connections currently used by clients.
    */
   private final ConcurrentMap<String, Entry> index = new ConcurrentHashMap<>();

   private final TaggedConnectionPoolManager poolMgr;

   private final FreeConnectionPool freePool;

   private final FndUserIndex fndUserIndex;

   DedicatedSessionIndex(TaggedConnectionPoolManager poolMgr, FreeConnectionPool freePool, FndUserIndex fndUserIndex) {
      this.poolMgr = poolMgr;
      this.freePool = freePool;
      this.fndUserIndex = fndUserIndex;
   }

   /**
    * Gets the current number of client entries in the index.
    */
   int size() {
      return index.size();
   }

   /**
    * Gets a connection from the connection pool.
    * The method blocks until a connection is available.
    * @param clientSessionId client session ID or null if an anonymous connection is required
    * @param fndUserId FND user identity, or null if not known
    * @param initKey current value of serialized in-parameters to PLSQL initialization procedure (Proxy_Login_SYS.Init_Session_)
    * @param clientId client identifier, or null if not known
    * @param log current thread logger
    * @return tagged connection in state USED
    * @throws SQLException if timeout occurs while waiting for a connection
    */
   TaggedConnection get(String clientSessionId, String fndUserId, String initKey, String clientId, Logger log) throws SQLException {
      long start = PoolUtil.now();
      TaggedConnection x = getImpl(clientSessionId, fndUserId, initKey, clientId, log);
      x.exitGet();
      x.getGetConnectionEvent().end(start);
      return x;
   }

   private TaggedConnection getImpl(String clientSessionId, String fndUserId, String initKey, String clientId, Logger log) throws SQLException {
      TaggedConnection x;
      Entry entry = null;

      /**
       * (1) Try to get the dedicated connection from dedicated session index.
       */
      if(clientSessionId != null) {
         entry = index.get(clientSessionId);
         if(entry == null) {
            /**
             * Create and store a new entry, putIfAbsent is used to guarantee that no duplicates are created.
             */
            Entry newEntry = new Entry(clientSessionId);
            entry = index.putIfAbsent(clientSessionId, newEntry);
            if(entry == null) {
               entry = newEntry; // put succeeded, use new value
            }
         }
         else {
            /**
             * Try to use the dedicated session, which may fail if the connection is already used by another thread.
             */
            x = entry.getConnection();
            if(x != null && x.useOwnSession(log)) {
               return x;
            }
         }
      }

      /**
       * (2) Try to get an initialized connection from the FND user index.
       */
      if(fndUserId != null) {
         while(true) {
            x = fndUserIndex.get(fndUserId, initKey, clientId);
            if(x == null)
               break;
            /**
             * Try to reuse the connection, which may fail if the connection is already used by another thread.
             */
            if(x.reuseOwnSession(entry, clientSessionId, log)) {
               return x;
            }
         }
      }

      /**
       * (3) Get the eldest connection from the free pool.
       */
      while(true) {
         x = freePool.take(); // blocks until a connection is avaiable
         /**
          * Try to reuse the connection, which may fail if the same connection has been found by another thread in step (1) or (2).
          */
         if(x.reuseSession(entry, clientSessionId, log)) {
            return x;
         }
      }
   }

   /**
    * Removes a connection from the dedicated session index.
    * @param x tagged connection to remove
    */
   void remove(TaggedConnection x) {
      Entry e = x.getOwner();
      if(e != null) {
         e.clearConnection(x);
      }
   }

   /**
    * Removes specified entry from the index.
    * @param clientSessionId client session ID identifying the entry to remove
    */
   void removeEntry(String clientSessionId) {
      index.remove(clientSessionId);
   }

   /**
    * Creates the background task that removes old empty entries from the index.
    */
   PoolTask createCleanerTask() {
      return new PoolTask() {

         private int indexEntryTimeout;

         @Override
         String getName() {
            return "SessionIndex.Cleaner";
         }

         @Override
         void refreshConfig() {
            indexEntryTimeout = poolMgr.getConfig().indexEntryTimeout.getInternalValue();
            if(clsLog.info) {
               clsInfo("   IndexEntryTimeout:  &1", indexEntryTimeout);
            }
         }

         @Override
         void doRun() {
            Iterator<Entry> iter = index.values().iterator();
            while(iter.hasNext()) {
               Entry e = iter.next();
               if(e.isEmpty() && e.getTimestamp() + indexEntryTimeout < PoolUtil.now()) {
                  if(log.debug) {
                     clsDebug("Removing empty index entry for client session &1", e.getClientSessionId());
                  }
                  iter.remove();
               }
               checkAction();
            }
         }
      };
   }

   void getState(StringBuilder buf) {
      String nl = IfsConstants.LINE_SEPARATOR;
      buf.append("Dedicated Session Index Entries (").append(index.size()).append("):").append(nl);

      ArrayList<Entry> arr = new ArrayList<>(index.values());
      Comparator<Entry> c = (Entry e1, Entry e2) -> e1.getClientSessionId().compareTo(e2.getClientSessionId());
      Collections.sort(arr, c);

      for(Entry e : arr) {
         buf.append("   ").append(e).append(nl);
      }
   }
}
