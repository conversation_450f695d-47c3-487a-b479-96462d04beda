/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 *
 */

package ifs.fnd.plsql.pool;

/**
 * Authentication parameters entered at a security checkpoint.
 * The parameters are used to skip re-authentication at other checkpoints during the same transaction.
 */
public class SecurityCheckpointAuthentication {

   private final String username;

   private final String userComment;

   SecurityCheckpointAuthentication(String username, String userComment) {
      this.username = username;
      this.userComment = userComment;
   }

   /**
    * Gets re-authenticated user name.
    *
    * @return user name
    */
   public String getUsername() {
      return username;
   }

   /**
    * Gets user comment entered at re-authentication.
    *
    * @return user comment
    */
   public String getUserComment() {
      return userComment;
   }

   @Override
   public String toString() {
      return "SecurityCheckpointAuthentication{hash=" + System.identityHashCode(this) + " username=" + username + " userComment=" + userComment + '}';
   }
}
