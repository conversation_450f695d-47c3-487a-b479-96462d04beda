/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.plsql.pool;

import ifs.fnd.log.Logger;
import ifs.fnd.service.IfsConstants;
import ifs.fnd.util.Str;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Class that spools pool statistics to debug output.
 */
final class TaggedConnectionPoolStatistics {

   private final static String NL = IfsConstants.LINE_SEPARATOR;

   private final TaggedConnectionPoolManager poolMgr;

   TaggedConnectionPoolStatistics(TaggedConnectionPoolManager mgr) {
      poolMgr = mgr;
   }

   String getState() {
      Logger log = new TaggedConnectionPoolManager.PoolInfo().getLogger();

      TaggedConnectionPoolConfig cfg = poolMgr.getConfig();
      StringBuilder buf = new StringBuilder();
      buf.append(NL);
      buf.append(NL);
      buf.append("Tagged Connection Pool Statistics {").append(cfg.getConfigLabel()).append("}").append(NL);
      buf.append(NL);
      cfg.getState(buf);
      buf.append(NL);

      buf.append("Connection Pool Components:").append(NL);
      buf.append("   Reserved Connection Pool:").append(PoolUtil.formatCount(poolMgr.getReservedPool().size())).append(NL);
      buf.append("   Free Connection Pool:    ").append(PoolUtil.formatCount(poolMgr.getFreePool().size())).append(NL);
      buf.append("   Dedicated Session Index: ").append(PoolUtil.formatCount(poolMgr.getSessionIndex().size())).append(NL);
      buf.append("   Fnd User Index:          ").append(PoolUtil.formatCount(poolMgr.getFndUserIndex().size())).append(NL);
      buf.append("   Closeable Resource Queue:").append(PoolUtil.formatCount(poolMgr.getResourceQueue().size())).append(NL);
      buf.append(NL);

      ArrayList<TaggedConnection> register = poolMgr.getRegisterSnapshot();
      int[] size = new int[TaggedConnection.State.values().length];
      for(TaggedConnection x : register) {
         TaggedConnection.State s = x.getState();
         size[s.ordinal()]++;
      }
      Collections.sort(register);

      buf.append("Tagged Connections:").append(NL);
      buf.append("   New:        ").append(PoolUtil.formatCount(size[TaggedConnection.State.NEW        .ordinal()])).append(NL);
      buf.append("   Anonymous:  ").append(PoolUtil.formatCount(size[TaggedConnection.State.ANONYMOUS  .ordinal()])).append(NL);
      buf.append("   Initialized:").append(PoolUtil.formatCount(size[TaggedConnection.State.INITIALIZED.ordinal()])).append(NL);
      buf.append("   Reserved:   ").append(PoolUtil.formatCount(size[TaggedConnection.State.RESERVED   .ordinal()])).append(NL);
      buf.append("   Unreserved: ").append(PoolUtil.formatCount(size[TaggedConnection.State.UNRESERVED .ordinal()])).append(NL);
      buf.append("   Used:       ").append(PoolUtil.formatCount(size[TaggedConnection.State.USED       .ordinal()])).append(NL);
      buf.append("   Destroyed:  ").append(PoolUtil.formatCount(poolMgr.getDestroyedCount())).append(NL);
      buf.append("   Existing:   ").append(PoolUtil.formatCount(register.size())).append(NL);
      buf.append(NL);

      if(log.trace) {
         soolConnectionPoolTable(register, buf);
      }

      if(log.debug) {
         poolMgr.getSessionIndex().getState(buf);
         buf.append(NL);
         poolMgr.getFndUserIndex().getState(buf);
         buf.append(NL);
         poolMgr.getResourceQueue().getState(buf);
         buf.append(NL);
         buf.append("Connection pool snapshot:").append(NL).append(poolMgr.listDatabaseSessions().toString()).append(NL);
         buf.append(NL);
      }

      if(log.trace) {
         SizeEventDist waitEvent = poolMgr.getFreePool().getTakeQueueLengthEvent();
         buf.append("Get Connection Queue Length Distribution:").append(NL);
         buf.append("   Min:  ").append(PoolUtil.formatCount(waitEvent.getMin())).append(NL);
         buf.append("   Max:  ").append(PoolUtil.formatCount(waitEvent.getMax())).append(NL);
         buf.append("   Avg:  ").append(PoolUtil.formatCount(waitEvent.getAvg())).append(NL);
         buf.append("   Count:").append(PoolUtil.formatCount(waitEvent.getCount())).append(NL);
         buf.append(NL);
         buf.append("  Length     Count").append(NL);
         buf.append("  ====== =========").append(NL);
         for(Map.Entry<Integer, Integer> e : waitEvent.getDistribution()) {
            buf.append(PoolUtil.formatCount(e.getKey())).append(PoolUtil.formatCount(e.getValue(), 10)).append(NL);
         }
         buf.append(NL);
         buf.append("   Connection Queue Max Length: ").append(waitEvent.getMax()).append(NL);
         buf.append(NL);
      }

      buf.append("Connection Creators:").append(NL);
      buf.append("   Connections Created By Request:").append(PoolUtil.formatCount(poolMgr.getConnectionsCreatedByRequest())).append(NL);
      buf.append("   Connections Created By Task:   ").append(PoolUtil.formatCount(poolMgr.getConnectionsCreatedByTask())).append(NL);
      buf.append(NL);
      buf.append("Request Failures:").append(NL);
      buf.append("   Total Request Failures:     ").append(PoolUtil.formatCount(poolMgr.getRequestFailureCount())).append(NL);
      buf.append("   Connection Request Timeouts:").append(PoolUtil.formatCount(poolMgr.getConnectionRequestTimeoutCount())).append(NL);
      buf.append(NL);

      TaggedConnectionStatistics destroyedStat = poolMgr.getDetsroyedConnectionStatistics();
      TaggedConnectionStatistics currentStat = poolMgr.getCurrentConnectionStatistics();
      TaggedConnectionStatistics allStat = new TaggedConnectionStatistics();
      allStat.combine(destroyedStat);
      allStat.combine(currentStat);

      TaggedConnection.Counters counters = allStat.getCounters();
      buf.append("Tagged Connection Counters:").append(NL);
      String margin = "   ";
      int pad = 11;
      buf.append(margin).append("Get          ").append(PoolUtil.formatCount(counters.getGetCount(),          pad)).append(NL);
      buf.append(margin).append("Return       ").append(PoolUtil.formatCount(counters.getReturnCount(),       pad)).append(NL);
      buf.append(margin).append("UseOwn       ").append(PoolUtil.formatCount(counters.getUseOwnCount(),       pad)).append(NL);
      buf.append(margin).append("ReuseOwn     ").append(PoolUtil.formatCount(counters.getReuseOwnCount(),     pad)).append(NL);
      buf.append(margin).append("Reuse        ").append(PoolUtil.formatCount(counters.getReuseCount(),        pad)).append(NL);
      buf.append(margin).append("Fail         ").append(PoolUtil.formatCount(counters.getFailCount(),         pad)).append(NL);
      buf.append(margin).append("Unreserve    ").append(PoolUtil.formatCount(counters.getUnreserveCount(),    pad)).append(NL);
      buf.append(margin).append("Undedicate   ").append(PoolUtil.formatCount(counters.getUndedicateCount(),   pad)).append(NL);
      buf.append(margin).append("Uninitialize ").append(PoolUtil.formatCount(counters.getUninitializeCount(), pad)).append(NL);
      buf.append(margin).append("SetAnonymous ").append(PoolUtil.formatCount(counters.getSetAnonymousCount(), pad)).append(NL);
      buf.append(NL);

      spoolTaggedConnectionTimeEvents(destroyedStat, "Destroyed", false, buf);
      spoolTaggedConnectionTimeEvents(currentStat,   "Current",   false, buf);
      spoolTaggedConnectionTimeEvents(allStat,       "Tagged",    true,  buf);

      PoolServiceDataProvider data = new PoolServiceDataProvider();
      buf.append("Pool Service Data Provider:").append(NL);
      buf.append(margin).append("Max Wait Time  ").append(PoolUtil.formatCount(data.getMaxWaitTime(), pad)).append(" ms").append(NL);
      buf.append(margin).append("Sum Wait Time  ").append(PoolUtil.formatCount(data.getSumWaitTime(), pad)).append(" ms").append(NL);
      buf.append(margin).append("Avg Wait Time  ").append(Str.lpad(PoolUtil.formatPct(data.getAverageWaitTime()),          pad)).append(" ms").append(NL);
      buf.append(margin).append("Switch User    ").append(Str.lpad(PoolUtil.formatPct(data.getSwitchUserPercentage()),     pad)).append(" %").append(NL);
      buf.append(margin).append("Waited Requests").append(Str.lpad(PoolUtil.formatPct(data.getWaitedRequestsPercentage()), pad)).append(" %").append(NL);
      buf.append(margin).append("Request Count  ").append(PoolUtil.formatCount(data.getRequestCount(), pad)).append(NL);
      buf.append(NL);

      spoolElapsedTimeIndicators(allStat.getTimeEvents(), "Request", buf);
      return buf.toString();
   }

   private void spoolTaggedConnectionTimeEvents(TaggedConnectionStatistics stats, String titlePrefix, boolean addWaitForConnectionEvent, StringBuilder buf) {
      buf.append(titlePrefix).append(" Connection Time Events [ms] collected from ");
      buf.append(stats.getConnectionCount()).append(" connections:").append(NL).append(NL);
      int labelPad = 30;
      String lineMargin = "   ";
      String titleMargin = PoolUtil.replicate(' ', labelPad + 3);
      buf.append(titleMargin).append("      Count        Min        Max        Avg        Sum").append(NL);
      buf.append(titleMargin).append(" ========== ========== ========== ========== ==========").append(NL);
      for(TaggedConnection.TimeEvents.Event e : stats.getTimeEvents().listEvents()) {
         buf.append(lineMargin);
         addTimeEventLine(e, Str.rpad(e.getLabel(), labelPad), buf);
      }
      if(addWaitForConnectionEvent) {
         TimeEvent sumWaitTime = poolMgr.getFreePool().getWaitForConnectionEvent();
         addTimeEventLine(sumWaitTime, lineMargin + Str.rpad("Wait For Connection", labelPad), buf);
         buf.append(NL);
      }
      buf.append(NL);
   }

   private static void addTimeEventLine(TimeEvent e, String label, StringBuilder buf) {
      final int lpad = 11;
      buf.append(label)
         .append(PoolUtil.formatCount(e.getCount(),   lpad))
         .append(PoolUtil.formatCount(e.getMinTime(), lpad))
         .append(PoolUtil.formatCount(e.getMaxTime(), lpad))
         .append(PoolUtil.formatCount(e.getAvgTime(), lpad))
         .append(PoolUtil.formatCount(e.getSumTime(), lpad))
         .append(NL);
   }

   private static void spoolElapsedTimeIndicators(TaggedConnection.TimeEvents timeEvents, String requestEventLabel, StringBuilder buf) {
      ArrayList<TaggedConnection.TimeEvents.Event> arr = new ArrayList<>();
      for(TaggedConnection.TimeEvents.Event e : timeEvents.listEvents()) {
         arr.add(e);
      }
      Collections.sort(arr, (TaggedConnection.TimeEvents.Event e1, TaggedConnection.TimeEvents.Event e2) -> Long.compare(e1.getSumTime(), e2.getSumTime()));
      buf.append("Elapsed Time [%]:").append(NL);
      TaggedConnection.TimeEvents.Event request = timeEvents.findEvent(requestEventLabel);
      long sumRequest = request.getSumTime();
      long sumOverhead = 0;
      for(TaggedConnection.TimeEvents.Event e : arr) {
         if(e != request) {
            double avgPct = pct(e.getSumTime(), sumRequest);
            buf.append("   ").append(Str.rpad(e.getLabel() + ":", 17)).append(Str.lpad(PoolUtil.formatPct(avgPct), 7)).append(NL);
            sumOverhead += e.getSumTime();
         }
      }
      buf.append(Str.lpad("=====", 27)).append(NL);
      double overheadPct = pct(sumOverhead, sumRequest);
      double requestPct = pct(sumRequest, sumRequest);
      buf.append("   ").append(Str.rpad("Request Overhead:", 17)).append(Str.lpad(PoolUtil.formatPct(overheadPct), 7)).append(NL);
      buf.append("   ").append(Str.rpad(request.getLabel() + ":", 17)).append(Str.lpad(PoolUtil.formatPct(requestPct), 7)).append(NL);

      long sumDbRequest = sumRequest == 0 ? 0 : sumRequest - sumOverhead;
      buf.append(NL).append("   Db Request Time:").append(PoolUtil.formatCount(sumDbRequest)).append(" ms").append(NL);
   }

   private static double pct(long time, long requestTime) {
      return requestTime == 0 ? Double.NaN : 100.0 * time / requestTime;
   }

   private void soolConnectionPoolTable(ArrayList<TaggedConnection> register, StringBuilder buf) {
      final TimeStatistics  timeStats  = new TimeStatistics();
      final CountStatistics countStats = new CountStatistics();
      final SizeStatistics  sizeStats  = new SizeStatistics();
      PoolTable tbl = new PoolTable();
      tbl.addLongColumn  ("Nr"               ,  6, (TaggedConnection x) -> x.getNr());
      tbl.addLongColumn  ("SID"              ,  7, (TaggedConnection x) -> x.getDbSid());
      tbl.addStringColumn("State"            , 16, (TaggedConnection x) -> x.stateLabel());
      tbl.addLongColumn  ("Key[ms]"          , 10, (TaggedConnection x) -> x.getRelativeKeyTimestamp());
      tbl.addLongColumn  ("Timestamp"        , 10, (TaggedConnection x) -> x.getRelativeTimestamp());
      tbl.addStringColumn("Client Session ID", 70, (TaggedConnection x) -> x.getClientSessionId());
      countStats.addColumn(tbl);
      timeStats.addColumn(tbl);
      sizeStats.addColumn(tbl);
      buf.append("Connection Pool Entries (").append(register.size()).append("):").append(NL);
      tbl.spool(register, buf);
      buf.append(NL);
   }

   private static final class TimeStatistics {

      private final TaggedConnection.TimeEvents sumEvents = new TaggedConnection.TimeEvents();

      private static final String leftMargin  = "| ";
      private static final String midMargin   = " | ";
      private static final String rightMargin = " |";

      private String leftMargin(TaggedConnection.TimeEvents.Event e) {
         return e.getIndex() == 0 ? leftMargin : midMargin;
      }

      private String headline1() {
         StringBuilder buf = new StringBuilder();
         for(TaggedConnection.TimeEvents.Event e : sumEvents.listEvents()) {
            String head = " " + e.getLabel() + " [ms] ";
            int width = 5 * 9 - 1;
            int len = head.length();
            int left = (width - len) / 2;
            head = Str.lpad(head, left + len, '-');
            head = Str.rpad(head, width, '-');
            buf.append(leftMargin(e)).append(head);
         }
         buf.append(rightMargin);
         return buf.toString();
      }

      private String headline2() {
         StringBuilder buf = new StringBuilder();
         for(TaggedConnection.TimeEvents.Event e : sumEvents.listEvents()) {
            buf.append(leftMargin(e));
            buf.append("   Count      Min      Max      Avg      Sum");
         }
         buf.append(rightMargin);
         return buf.toString();
      }

      private String underline() {
         StringBuilder buf = new StringBuilder();
         for(TaggedConnection.TimeEvents.Event e : sumEvents.listEvents()) {
            buf.append(leftMargin(e));
            buf.append("======== ======== ======== ======== ========");
         }
         buf.append(rightMargin);
         return buf.toString();
      }

      /**
       * @param x tagged connection, or null for a summation line
       */
      void appendLine(TaggedConnection x, StringBuilder buf) {
         for(TaggedConnection.TimeEvents.Event sumEvent : sumEvents.listEvents()) {
            TaggedConnection.TimeEvents.Event event = sumEvent;
            if(x != null) {
               event = x.getTimeEvents().getEventAt(sumEvent.getIndex());
               sumEvent.combine(event);
            }
            buf.append(leftMargin(event));
            buf.append(PoolUtil.lpad(event.getCount(), 8));
            buf.append(PoolUtil.lpad(event.getMinTime(), 9));
            buf.append(PoolUtil.lpad(event.getMaxTime(), 9));
            buf.append(PoolUtil.lpad(event.getAvgTime(), 9));
            buf.append(PoolUtil.lpad(event.getSumTime(), 9));
         }
         buf.append(rightMargin);
      }

      void addColumn(PoolTable tbl) {
         tbl.addColumn(headline1(), headline2(), underline(), true, (TaggedConnection x, StringBuilder buf) -> {
            appendLine(x, buf);
         });
      }
   }

   private static final class CountStatistics {

      private final TaggedConnection.Counters sumCounters = new TaggedConnection.Counters();

      private static final String leftMargin  = "| ";
      private static final String rightMargin = "";

      private static final String headline1 = "--------------------------------------- Counters ----------------------------------------";
      private static final String headline2 = "     Get   Return   UseOwn ReuseOwn    Reuse     Fail  SetAnon Unreserv Undedica Uninitia";
      private static final String underline = "======== ======== ======== ======== ======== ======== ======== ======== ======== ========";

      private String headline1() {
         return leftMargin + headline1 + rightMargin;
      }

      private String headline2() {
         return leftMargin + headline2 + rightMargin;
      }

      private String underline() {
         return leftMargin + underline + rightMargin;
      }

      /**
       * @param x tagged connection, or null for a summation line
       */
      void appendLine(TaggedConnection x, StringBuilder buf) {
         TaggedConnection.Counters counters = sumCounters;
         if(x != null) {
            counters = x.getCounters();
            sumCounters.combine(counters);
         }
         buf.append(leftMargin);
         buf.append(PoolUtil.lpad(counters.getGetCount(), 8));
         buf.append(PoolUtil.lpad(counters.getReturnCount(), 9));
         buf.append(PoolUtil.lpad(counters.getUseOwnCount(), 9));
         buf.append(PoolUtil.lpad(counters.getReuseOwnCount(), 9));
         buf.append(PoolUtil.lpad(counters.getReuseCount(), 9));
         buf.append(PoolUtil.lpad(counters.getFailCount(), 9));
         buf.append(PoolUtil.lpad(counters.getSetAnonymousCount(), 9));
         buf.append(PoolUtil.lpad(counters.getUnreserveCount(), 9));
         buf.append(PoolUtil.lpad(counters.getUndedicateCount(), 9));
         buf.append(PoolUtil.lpad(counters.getUninitializeCount(), 9));
         buf.append(rightMargin);
      }

      void addColumn(PoolTable tbl) {
         tbl.addColumn(headline1(), headline2(), underline(), true, (TaggedConnection x, StringBuilder buf) -> {
            appendLine(x, buf);
         });
      }
   }

   private static final class SizeStatistics {

      private static final String leftMargin  = "";
      private static final String rightMargin = " |";

      private static final String headline1 = "------- Get Free Size ---------  ----- Return Free Size --------";
      private static final String headline2 = "  Count     Min     Max     Avg    Count     Min     Max     Avg";
      private static final String underline = "======= ======= ======= =======  ======= ======= ======= =======";

      private final SizeEvent sumGetPoolSize    = new SizeEvent();
      private final SizeEvent sumReturnPoolSize = new SizeEvent();

      private String headline1() {
         return leftMargin + headline1 + rightMargin;
      }

      private String headline2() {
         return leftMargin + headline2 + rightMargin;
      }

      private String underline() {
         return leftMargin + underline + rightMargin;
      }

      /**
       * @param x tagged connection, or null for a summation line
       */
      void appendLine(TaggedConnection x, StringBuilder buf) {
         SizeEvent getSize = sumGetPoolSize;
         SizeEvent returnSize = sumReturnPoolSize;
         if(x != null) {
            getSize = x.getGetSizeEvent();
            sumGetPoolSize.combine(getSize);
            returnSize = x.getReturnSizeEvent();
            sumReturnPoolSize.combine(returnSize);
         }
         buf.append(leftMargin);
         buf.append(PoolUtil.lpad(getSize.getCount(), 7));
         buf.append(PoolUtil.lpad(getSize.getMin(), 8));
         buf.append(PoolUtil.lpad(getSize.getMax(), 8));
         buf.append(PoolUtil.lpad(getSize.getAvg(), 8));
         buf.append(' ');
         buf.append(PoolUtil.lpad(returnSize.getCount(), 8));
         buf.append(PoolUtil.lpad(returnSize.getMin(), 8));
         buf.append(PoolUtil.lpad(returnSize.getMax(), 8));
         buf.append(PoolUtil.lpad(returnSize.getAvg(), 8));
         buf.append(rightMargin);
      }

      void addColumn(PoolTable tbl) {
         tbl.addColumn(headline1(), headline2(), underline(), true, (TaggedConnection x, StringBuilder buf) -> {
            appendLine(x, buf);
         });
      }
   }

   interface Data {

      /**
       * Appends a field to table describing connection pool entries.
       * @param x tagged connection or null for summation line
       */
      void addField(TaggedConnection x, StringBuilder buf);

   }

   interface LongData {
      long getValue(TaggedConnection x);
   }

   interface StringData {
      String getValue(TaggedConnection x);
   }

   private static class PoolTable {

      private class Column {

         private final Data data;
         private final String headline1;
         private final String headline2;
         private final String underline;
         private final boolean sumLine;

         Column(Data data, String headline1, String headline2, String underline, boolean sumLine) {
            this.data = data;
            this.headline1 = headline1;
            this.headline2 = headline2;
            this.underline = underline;
            this.sumLine = sumLine;
         }
      }

      private final List<Column> columns = new ArrayList<>();

      void addLongColumn(String label, final int width, final LongData data) {
         final String h1 = Str.lpad(" ", width);
         String h2 = Str.lpad(label, width);
         String u = Str.lpad("=", width, '=');
         addColumn(h1, h2, u, false, (TaggedConnection x, StringBuilder buf) -> {
            buf.append(x == null ? h1 : PoolUtil.lpad(data.getValue(x), width));
         });
      }

      void addStringColumn(String label, final int width, final StringData data) {
         final String h1 = Str.rpad(" ", width);
         String h2 = Str.rpad(label, width);
         String u = Str.lpad("=", width, '=');
         addColumn(h1, h2, u, false, (TaggedConnection x, StringBuilder buf) -> {
            buf.append(x == null ? h1 : Str.rpad(data.getValue(x), width));
         });
      }

      void addColumn(String headline1, String headline2, String underline, boolean sumLine, Data data) {
         columns.add(new Column(data, headline1, headline2, underline, sumLine));
      }

      void spool(List<TaggedConnection> arr, StringBuilder buf) {
         for(Column c : columns) {
            buf.append(c.headline1).append(' ');
         }
         buf.append(NL);
         for(Column c : columns) {
            buf.append(c.headline2).append(' ');
         }
         buf.append(NL);
         for(Column c : columns) {
            buf.append(c.underline).append(' ');
         }
         buf.append(NL);
         for(TaggedConnection x : arr) {
            for(Column c : columns) {
               c.data.addField(x, buf);
               buf.append(' ');
            }
            buf.append(NL);
         }
         for(Column c : columns) {
            buf.append(c.sumLine ? c.underline : c.headline1).append(' ');
         }
         buf.append(NL);
         for(Column c : columns) {
            c.data.addField(null, buf);
            buf.append(' ');
         }
         buf.append(NL);
      }
   }
}
