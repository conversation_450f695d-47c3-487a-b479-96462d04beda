/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.plsql.pool;

import ifs.fnd.log.Logger;

/**
 * Class representing an active transaction in the database.
 */
final class DedicatedTransaction extends CloseableResource {

   /**
    * ID for this transaction.
    */
   private final String transactionId;

   DedicatedTransaction(TaggedConnection owner, String transactionId) {
      super(owner);
      this.transactionId = transactionId;
   }

   /**
    * Gets unique identifier for this transaction.
    * @return transaction ID
    */
   @Override
   String getUniqueId() {
      return transactionId;
   }

   /**
    * Gets the timestamp when this transaction should be closed.
    * @return "computer time" measured in milliseconds
    */
   @Override
   long getCloseTimestamp() {
      return getTimestamp() + getConfig().transactionMaxTimeout.getInternalValue();
   }

   /**
    * Closes this transaction without throwing an exception.
    */
   @Override
   void safeClose(Logger log) {
      TaggedConnection x = getTaggedConnection();
      x.safeCloseTransaction(log);
      x.tryUndedicate(log);
   }

   /**
    * Gets the transaction ID.
    * @return string identifying this transaction
    */
   String getTransactionId() {
      return transactionId;
   }

   @Override
   public String toString() {
      return super.toString("DedicatedTransaction", transactionId);
   }
}
