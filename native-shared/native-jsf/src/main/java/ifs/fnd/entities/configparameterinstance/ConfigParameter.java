/*
 * ==================================================================================
 * File:             ConfigParameter.java
 * Software Package: ConfigParameterInstance
 * Template Version: 8.0
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package ifs.fnd.entities.configparameterinstance;

import ifs.fnd.base.*;
import ifs.fnd.record.*;

/**
 * This view represents the <code>ConfigParameter</code> entity.
 */
public class ConfigParameter extends FndLUEntityView {

   public static final FndRecordMeta viewMeta = new FndRecordMeta("FNDEXT", "CONFIGPARAMETER", "CONFIG_PARAMETER", "CONFIG_PARAMETER", "ConfigParameter", "CONFIG_PARAMETER_API", "FNDCN_CONFIG_PARAM_TAB").setViewClassName("ifs.fnd.entities.configparameter.ConfigParameter");

   /**
    * AreaName
    */
   public final FndText      areaName      = new FndText(Meta.areaName);
   /**
    * GroupName
    */
   public final FndText      groupName     = new FndText(Meta.groupName);
   /**
    * InstanceName
    */
   public final FndText      instanceName  = new FndText(Meta.instanceName);
   /**
    * ParameterName
    */
   public final FndText      parameterName  = new FndText(Meta.parameterName);
   /**
    * Ordinal
    */
   public final FndNumber    ordinal        = new FndNumber(Meta.ordinal);
   /**
    * ParameterValue
    */
   public final FndText      parameterValue = new FndText(Meta.parameterValue);
   /**
    * BinaryValue
    */
   public final FndBinary    binaryValue    = new FndBinary(Meta.binaryValue);
   /**
    * ParameterType
    */
   public final FndText      parameterType  = new FndText(Meta.parameterType);
   /**
    * ValueList
    */
   public final FndText      valueList      = new FndText(Meta.valueList);
   /**
    * HelpText
    */
   public final FndText      helpText       = new FndText(Meta.helpText);
   /**
    * WriteProtected
    */
   public final FndBoolean   writeProtected = new FndBooleanString(Meta.writeProtected, "1", "0");

   public final Reference    primaryKey     = new Reference(Meta.primaryKey, areaName, groupName, instanceName, parameterName);

   public final ParentReferenceConfigParameterInstance parentKeyConfigParameterInstance = new ParentReferenceConfigParameterInstance(Meta.parentKeyConfigParameterInstance, areaName, groupName, instanceName);

   public ConfigParameter() {
      this(viewMeta);
   }

   protected ConfigParameter(FndRecordMeta meta) {
      super(meta);
      add(areaName);
      add(groupName);
      add(instanceName);
      add(parameterName);
      add(primaryKey);
      add(parentKeyConfigParameterInstance);
      add(ordinal);
      add(parameterValue);
      add(binaryValue);
      add(parameterType);
      add(valueList);
      add(helpText);
      add(writeProtected);
   }

   @Override
   public FndAbstractRecord newInstance() {
      return new ConfigParameter();
   }

   @Override
   public FndAbstractArray newArrayInstance() {
      return new ConfigParameterArray();
   }

   public static class Reference extends FndCompoundReference {

      public Reference(FndCompoundReferenceMeta ref, FndText areaName, FndText groupName, FndText instanceName, FndText parameterName) {
         super(ref);
         add(areaName);
         add(groupName);
         add(instanceName);
         add(parameterName);
      }

      public void assign(Reference ref) throws IfsException {
         protectedAssign(ref);
      }

      public boolean isEqualTo(Reference ref) {
         return protectedIsEqualTo(ref);
      }
   }

   public static class ParentReferenceConfigParameterInstance extends FndCompoundReference {

      public ParentReferenceConfigParameterInstance(FndCompoundReferenceMeta ref, FndText areaName, FndText groupName, FndText instanceName) {
         super(ref);
         add(areaName);
         add(groupName);
         add(instanceName);
      }

      public void assign(ParentReferenceConfigParameterInstance ref) throws IfsException {
         protectedAssign(ref);
      }

      public boolean isEqualTo(ParentReferenceConfigParameterInstance ref) {
         return protectedIsEqualTo(ref);
      }
   }

   public static class Meta {

      public static final FndAttributeMeta areaName = new FndAttributeMeta(viewMeta, "AREA_NAME", "AREA_NAME", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 50).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta groupName = new FndAttributeMeta(viewMeta, "GROUP_NAME", "GROUP_NAME", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 50).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta instanceName = new FndAttributeMeta(viewMeta, "INSTANCE_NAME", "INSTANCE_NAME", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 50).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta parameterName = new FndAttributeMeta(viewMeta, "PARAMETER_NAME", "PARAMETER_NAME", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 50).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta ordinal = new FndAttributeMeta(viewMeta, "ORDINAL", "ORDINAL", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta parameterValue = new FndAttributeMeta(viewMeta, "PARAMETER_VALUE", "PARAMETER_VALUE", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 4000);
      public static final FndAttributeMeta binaryValue = new FndAttributeMeta(viewMeta, "BINARY_VALUE", "BINARY_VALUE", FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta parameterType = new FndAttributeMeta(viewMeta, "PARAMETER_TYPE", "PARAMETER_TYPE", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 100);
      public static final FndAttributeMeta valueList = new FndAttributeMeta(viewMeta, "VALUE_LIST", "VALUE_LIST", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 1000);
      public static final FndAttributeMeta helpText = new FndAttributeMeta(viewMeta, "HELP_TEXT", "HELP_TEXT", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 1000);
      public static final FndAttributeMeta writeProtected = new FndAttributeMeta(viewMeta, "WRITE_PROTECTED", "WRITE_PROTECTED", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndCompoundReferenceMeta primaryKey = new FndCompoundReferenceMeta(viewMeta, "ABSTRACT_CONFIG_PARAMETER_KEY", new FndAttributeMeta[]{areaName, groupName, instanceName, parameterName}, viewMeta, true);
      public static final FndCompoundReferenceMeta parentKeyConfigParameterInstance = new FndCompoundReferenceMeta(viewMeta, "PARENT_KEY_CONFIG_PARAMETER_INSTANCE", new FndAttributeMeta[] {areaName, groupName, instanceName}, ConfigParameterInstance.viewMeta);
   }
}
