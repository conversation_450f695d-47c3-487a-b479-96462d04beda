/*
* ==================================================================================
* File:             AbstractLanguageCode.java
* Software Package: LanguageCode
*
* DO NOT EDIT this file. It was generated and will be overwritten at next generation
*
* ==================================================================================
*/

package ifs.fnd.entities.languagecode;

import ifs.fnd.base.IfsException;
import ifs.fnd.base.SystemException;
import ifs.fnd.record.FndAttributeMeta;
import ifs.fnd.record.FndCompoundReference;
import ifs.fnd.record.FndCompoundReferenceMeta;
import ifs.fnd.record.FndFilter;
import ifs.fnd.record.FndLUEntityView;
import ifs.fnd.record.FndRecordMeta;
import ifs.fnd.record.FndText;

/**
 * The abstract class for the <code>LanguageCode</code> entity.
 */
public abstract class AbstractLanguageCode extends FndLUEntityView {

   public static final FndRecordMeta viewMeta  = new FndRecordMeta("", "LANGUAGECODE", "LANGUAGE_CODE", "LANGUAGE_CODE", "LanguageCode", "LANGUAGE_CODE_API");

   public final FndText      langCode        = new FndText(Meta.langCode);
   public final FndText      langCodeRfc3066 = new FndText(Meta.langCodeRfc3066);

   public final Reference  primaryKey = new Reference(Meta.primaryKey, langCode);

   public final AltReference  alternateKey = new AltReference(Meta.alternateKey, langCodeRfc3066);

   protected AbstractLanguageCode(FndRecordMeta meta) {
      super(meta);
      add(langCode);
      add(langCodeRfc3066);
      add(primaryKey);
      add(alternateKey);
   }

   public void assign(AbstractLanguageCode from) throws SystemException {
      super.assign(from);
   }

   public void transformTo(final AbstractLanguageCode to) throws SystemException {
      transformView(to);
   }

   /**
    * Class representing the primary key for <code>LanguageCode</code> entity.
    */
   public static class Reference extends FndCompoundReference {
      public Reference(FndCompoundReferenceMeta ref, FndText langCode) {
         super(ref);
         add(langCode);
      }

      public void assign(Reference ref) throws IfsException {
         protectedAssign(ref);
      }

      public boolean isEqualTo(Reference ref) {
         return protectedIsEqualTo(ref);
      }
   }

   /**
    * Class representing the alternate key for <code>LanguageCode</code> entity.
    */
   public static class AltReference extends FndCompoundReference {
      public AltReference(FndCompoundReferenceMeta ref, FndText langCodeRfc3066) {
         super(ref);
         add(langCodeRfc3066);
      }

      public void assign(AltReference ref) throws IfsException {
         protectedAssign(ref);
      }

      public boolean isEqualTo(AltReference ref) {
         return protectedIsEqualTo(ref);
      }
   }

   /**
    * Meta class (contains meta attributes) for <code>LanguageCode</code entity.
    */
   public static class Meta {
      public static final FndAttributeMeta langCode        = new FndAttributeMeta(viewMeta, "LANG_CODE", "LANG_CODE", FndAttributeMeta.QUERYABLE|FndAttributeMeta.PERSISTENT, 20).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta langCodeRfc3066 = new FndAttributeMeta(viewMeta, "LANG_CODE_RFC_3066", "LANG_CODE_RFC3066", FndAttributeMeta.QUERYABLE|FndAttributeMeta.PERSISTENT, 24);
      public static final FndAttributeMeta description     = new FndAttributeMeta(viewMeta, "DESCRIPTION", "DESCRIPTION", FndAttributeMeta.QUERYABLE|FndAttributeMeta.PERSISTENT, 120);
      public static final FndAttributeMeta status          = new FndAttributeMeta(viewMeta, "STATUS", "STATUS_DB", FndAttributeMeta.QUERYABLE|FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta nlsLanguage     = new FndAttributeMeta(viewMeta, "NLS_LANGUAGE", "NLS_LANGUAGE", FndAttributeMeta.QUERYABLE|FndAttributeMeta.PERSISTENT, 90);
      public static final FndAttributeMeta nlsTerritory    = new FndAttributeMeta(viewMeta, "NLS_TERRITORY", "NLS_TERRITORY", FndAttributeMeta.QUERYABLE|FndAttributeMeta.PERSISTENT, 90);
      public static final FndAttributeMeta derivedFromLangCode = new FndAttributeMeta(viewMeta, "DERIVED_FROM_LANG_CODE", "DERIVED_FROM_LANG_CODE", FndAttributeMeta.PERSISTENT, 15);

      public static final FndCompoundReferenceMeta primaryKey = new FndCompoundReferenceMeta(viewMeta, "ABSTRACT_LANGUAGE_CODE_KEY", new FndAttributeMeta[] {langCode}, viewMeta, true);
      public static final FndCompoundReferenceMeta alternateKey = new FndCompoundReferenceMeta(viewMeta, "ABSTRACT_LANGUAGE_CODE_ALTERNATE_KEY", new FndAttributeMeta[] {langCodeRfc3066}, viewMeta, false);

      public static final FndFilter active = new FndFilter(
         "LANGUAGECODE",
         "ACTIVE",
         "",
         "&0.status_db = 'A'");
   }
}
