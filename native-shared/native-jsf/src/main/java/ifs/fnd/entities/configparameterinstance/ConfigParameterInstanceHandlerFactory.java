/*
 * ==================================================================================
 * File:             ConfigParameterInstanceHandlerFactory.java
 * Software Package: ConfigParameterInstance
 * Template Version: 8.0
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package ifs.fnd.entities.configparameterinstance;

public class ConfigParameterInstanceHandlerFactory {

   public static ConfigParameterInstanceHandler getHandler() {
      return new ConfigParameterInstanceHandler();
   }
}
