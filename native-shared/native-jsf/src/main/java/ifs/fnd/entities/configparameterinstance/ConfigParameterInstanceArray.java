/*
 * ==================================================================================
 * File:             ConfigParameterInstanceArray.java
 * Software Package: ConfigParameterInstance
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package ifs.fnd.entities.configparameterinstance;

import ifs.fnd.base.*;
import ifs.fnd.record.*;
import ifs.fnd.record.serialization.FndTokenReader;

/**
 * Array of <code>ConfigParameterInstance</code>.
 */
public class ConfigParameterInstanceArray extends FndAbstractArray {

   public ConfigParameterInstanceArray() {
      super();
   }

   public ConfigParameterInstanceArray(FndAttributeMeta meta) {
      super(meta);
   }

   public ConfigParameterInstanceArray(FndAttributeMeta meta, FndCompoundReference parentKeyInParent) {
      super(meta, parentKeyInParent);
   }

   public boolean add(ConfigParameterInstance record) {
      return internalAdd(record);
   }

   public void add(int index, ConfigParameterInstance record) {
      internalAdd(index, record);
   }

   public void add(ConfigParameterInstanceArray array) {
      internalAdd(array);
   }

   public void assign(ConfigParameterInstanceArray from) throws SystemException {
      assign(from, true);
   }

   public boolean contains(ConfigParameterInstance record) {
      return internalContains(record);
   }

   public ConfigParameterInstance firstElement() {
      return (ConfigParameterInstance)internalFirstElement();
   }

   public ConfigParameterInstance get(int index) {
      return (ConfigParameterInstance)internalGet(index);
   }

   public int indexOf(ConfigParameterInstance record) {
      return internalIndexOf(record);
   }

   public ConfigParameterInstance lastElement() {
      return (ConfigParameterInstance)internalLastElement();
   }

   public int lastIndexOf(ConfigParameterInstance record) {
      return internalLastIndexOf(record);
   }

   public ConfigParameterInstance remove(int index) {
      return (ConfigParameterInstance)internalRemove(index);
   }

   public ConfigParameterInstance set(int index, ConfigParameterInstance record) {
      return (ConfigParameterInstance)internalSet(index, record);
   }

   public FndDetailCondition createDetailCondition(ConfigParameterInstance record, FndQueryReferenceCategory category) {
      return createCondition(record, category);
   }

   @Override
   public FndAbstractRecord newRecord() {
      return new ConfigParameterInstance();
   }

   @Override
   protected FndAbstractRecord newRecord(FndTokenReader stream) throws ParseException {
      ConfigParameterInstance record = new ConfigParameterInstance();
      record.parse(stream);
      return record;
   }

   @Override
   protected FndAttribute newAttribute(FndAttributeMeta meta) {
       return new ConfigParameterInstanceArray(meta);
   }
}
