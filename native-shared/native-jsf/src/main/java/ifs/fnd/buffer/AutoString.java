/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.buffer;

/**
 * This class can be used instead of java.lang.StringBuffer.
 * It has better performance but is not safe for use by multiple threads.
 * 
 * Consider using StringBuilder instead.
 */
public final class AutoString implements Cloneable {

   private char[] value;
   private int length;

   /**
    * Construct a new AutoString allocating a buffer of the specified size.
    * @param initialSize initial size of AutoString
    */
   public AutoString(int initialSize) {
      value = new char[initialSize];
      length = 0;
   }

   /**
    * Construct a new AutoString allocating a buffer of the default size.
    */
   public AutoString() {
      this(256);
   }

   /**
    * Append a string to this string buffer.
    * @param str string to append
    */
   public void append(String str) {
      if (str == null)
         return;
      int len = str.length();
      if (length + len > value.length)
         reallocate(length + len);

      str.getChars(0, len, value, length);
      length += len;
   }

   /**
    * Append a sequence of two strings to this string buffer.
    * @param str1 first string
    * @param str2 seconds string
    */
   public void append(String str1, String str2) {
      append(str1);
      append(str2);
   }

   /**
    * Append a sequence of three strings to this string buffer.
    * @param str1 first string
    * @param str2 seconds string
    * @param str3 third string
    */
   public void append(String str1, String str2, String str3) {
      append(str1);
      append(str2);
      append(str3);
   }

   /**
    * Append a sequence of four strings to this string buffer.
    * @param str1 first string
    * @param str2 seconds string
    * @param str3 third string
    * @param str4 fourth string
    */
   public void append(String str1, String str2, String str3, String str4) {
      append(str1);
      append(str2);
      append(str3);
      append(str4);
   }

   /**
    * Append a sequence of five strings to this string buffer.
    * @param str1 first string
    * @param str2 seconds string
    * @param str3 third string
    * @param str4 fourth string
    * @param str5 fifth string
    */
   public void append(String str1, String str2, String str3, String str4, String str5) {
      append(str1);
      append(str2);
      append(str3);
      append(str4);
      append(str5);
   }

   /**
    * Append a portion of an array of characters to this string buffer.
    * @param cbuf array of characters to append
    * @param off start position
    * @param len number of characters from start (off)
    */
   public void append(char[] cbuf, int off, int len) {
      if (len == 0)
         return;
      if (length + len > value.length)
         reallocate(length + len);

      System.arraycopy(cbuf, off, value, length, len);
      length += len;
   }

   /**
    * Append to this buffer the contents of another AutoString.
    * @param buf auto string to append
    */
   public void append(AutoString buf) {
      append(buf.value, 0, buf.length);
   }

   /**
    * Append one character to this string buffer.
    * @param ch character to append
    */
   public void append(char ch) {
      if (length + 1 > value.length)
         reallocate(length + 1);

      value[length++] = ch;
   }

   /**
    * Append to this buffer the string representation of an integer.
    * @param i integer to append
    */
   public void appendInt(int i) {
      if (length + 12 > value.length)
         reallocate(length + 12);

      if (i < 0) {
         value[length++] = '-';
         i = -i;
      }

      int first = length;

      do {
         value[length++] = (char) ('0' + i % 10);
         i = i / 10;
      }
      while (i > 0);

      int last = length - 1;

      while (first < last) {
         char tmp = value[first];
         value[first] = value[last];
         value[last] = tmp;
         first++;
         last--;
      }
   }

   /**
    * Append to this buffer the string representation of boolean.
    * @param b boolean to append
    */
   public void appendBoolean(boolean b) {
      append(b ? "true" : "false");
   }

   /**
    * Clear the characters from the buffer but keep the allocated space.
    */
   public void clear() {
      length = 0;
   }

   /**
    * Return the length (character count) of this string buffer.
    * @return length of the buffer
    */
   public int length() {
      return length;
   }

   private void reallocate(int needSize) {
      int extsize = 2 * value.length;
      int newsize = needSize > extsize ? needSize : extsize;

      char[] newvalue = new char[newsize];

      System.arraycopy(value, 0, newvalue, 0, value.length);
      value = newvalue;
   }

   /**
    * Convert to a string representing the data in this string buffer.
    * @return string representation of the buffer
    */
   @Override
   public String toString() {
      return new String(value, 0, length);
   }

   /**
    * Return a hashcode for this AutoString.
    * @return hashcode
    */
   @Override
   public int hashCode() {
      int h = 0;
      int off = 0;
      char val[] = value;
      int len = length;

      if (len < 16) {
         for (int i = len; i > 0; i--)
            h = (h * 37) + val[off++];
      }
      else {
         // only sample some characters
         int skip = len / 8;
         for (int i = len; i > 0; i -= skip, off += skip)
            h = (h * 39) + val[off];
      }
      return h;
   }

   /**
    * Return true if a specified Object is an AutoString and has exactly the same
    * contents as this AutoString.
    * @param obj object to compare to
    * @return <code>true</code> if the objects are equal
    */
   @Override
   public boolean equals(Object obj) {
      if (obj != null && obj instanceof AutoString) {
         AutoString another = (AutoString) obj;
         int n = length;
         if (n == another.length) {
            char v1[] = value;
            char v2[] = another.value;
            int i = 0;
            int j = 0;
            while (n-- != 0) {
               if (v1[i++] != v2[j++])
                  return false;
            }
            return true;
         }
      }
      return false;
   }

   /**
    * Clones this AutoString.
    * @return clone of the object
    */
   @Override
   public Object clone() {
      // PMD generates a warning: "Object clone() should be implemented with super.clone()",
      // but according to Checkstyle documentation: "Final classes can use a constructor to create a clone"
      AutoString buf = new AutoString(length);
      buf.append(value, 0, length);
      return buf;
   }
}
