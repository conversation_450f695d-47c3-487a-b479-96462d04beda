/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.buffer;

import java.io.*;

/**
 * <B>Framework internal class:</B> A class that reads tokens and delimiters from an underlying stream.
 * The stream may contains bytes or characters depending on the used constructor.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class StandardTokenReader {

   private static final char NULL_CHAR = '\u0000';

   private Reader input;

   /**
    * Buffer reused by getToken.
    */
   StringBuilder sb = new StringBuilder(1024);

   /**
    * Number of characters in the token buffer.
    */
   //private int count;

   /**
    * A delimiter found in the input stream.
    */
   private char delimiter;

   /**
    * Construct an instance that will read bytes from the specified byte stream,
    * and then convert them to tokens and delimiters.
    */
   public StandardTokenReader(InputStream input) {
      this.input = new BufferedReader(new InputStreamReader(input));
   }

   /**
    * Construct an instance that will read tokens directly from the specified
    * character stream,
    */
   public StandardTokenReader(Reader input) {
      this.input = input;
   }

   /**
    * Force a delimiter from the input stream.
    * Throw BufferFormatException if not found.
    */
   public char getDelimiter() throws BufferFormatException, IOException {
      if (delimiter == NULL_CHAR) {
         int ch = input.read();

         if(isDelimiter(ch)) {
            return (char) ch;
         } else if(ch==-1) {
            throw new BufferFormatException("Expecting delimiter but found end-of-stream");
         } else {
            throw new BufferFormatException("Expecting delimiter but found character code " + ch);
         }
      }

      char d = delimiter;
      delimiter = NULL_CHAR;
      return d;
   }

   /**
    * Force next token from the input stream.
    * Throw BufferFormatException if not found.
    */
   public String getToken() throws BufferFormatException, IOException {
      if (delimiter != NULL_CHAR)
         throw new BufferFormatException("Expecting token but found delimiter code " + (int) delimiter);
      
      /* Note: it is important to set string buffer length to zero here. 
         Otherwise we would be concatanating this token with the previous one.*/
      sb.setLength(0);

      while (true) {
         int ch = input.read();

         if(ch==-1) {
            if (sb.length() == 0) {
               throw new BufferFormatException("Unexpected end of stream");
            }
            return sb.toString();
         } else if(isDelimiter(ch)) {
            delimiter = (char) ch;
            return sb.toString(); // empty String OK.
         } else {
            sb.append((char)ch);
         }
      }
   }

   /**
    * Force a next delimiter from the input stream and throw BufferFormatException
    * if it does not match the specified delimiter.
    */
   public void matchDelimiter(char ch) throws BufferFormatException, IOException {
      char c = getDelimiter();
      if (c != ch)
         throw new BufferFormatException("Expecting delimiter code " + (int) ch + " but found code " + (int) c);
   }
   
   public String getPrefixedToken() throws IOException, BufferFormatException {
      int len = BufferUtil.readLengthPrefix(input);
      return getToken(len); 
   }
   
   /* returns next token which is a UTF-8 string of the specified length */
   private String getToken(int byteLength) throws IOException, BufferFormatException {
      /* Note: it is important to set string buffer length to zero here.
         Otherwise we would be concatanating this token with the previous one.*/
      sb.setLength(0);
      UTF8ByteCounter counter = new UTF8ByteCounter(UTF8ByteCounter.Type.INPUT_STREAM);

      while (counter.getCount() < byteLength) {
         int ch = input.read();
         if (ch < 0) throw new BufferFormatException("Unexpected end of buffer");

         counter.consume((char)ch);
         
         sb.append((char)ch);
      }
      return sb.toString();
   }
   
   /**
    * Match the specified byte against delimiters defined in StandardBufferFormatter.
    */
   @SuppressWarnings("PMD.MissingBreakInSwitch")
   protected boolean isDelimiter(int ch) {
      switch((char)ch) {
         case StandardBufferFormatter.BEGIN_BUFFER_MARKER :
         case StandardBufferFormatter.END_BUFFER_MARKER :
         case StandardBufferFormatter.HEAD_MARKER :
         case StandardBufferFormatter.NAME_MARKER :
         case StandardBufferFormatter.TYPE_MARKER :
         case StandardBufferFormatter.STATUS_MARKER :
         case StandardBufferFormatter.VALUE_MARKER :
         case StandardBufferFormatter.NULL_VALUE_MARKER :
         case StandardBufferFormatter.NO_VALUE_MARKER :
         case StandardBufferFormatter.IDENTITY_MARKER :
         case StandardBufferFormatter.CHANGED_VALUE_MARKER :
         case StandardBufferFormatter.UNCHANGED_VALUE_MARKER :
            return true;
      }
      return false;
   }
}
