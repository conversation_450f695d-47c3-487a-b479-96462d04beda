/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.buffer;

import ifs.fnd.service.FndException;

/**
 * An instance of this class can format and parse String values.
 * The only supported Type ID is {@link DataFormatter#STRING}.
 */
public final class StringFormatter extends DataFormatter {

   /**
    * Constructs a new instance of StringFormatter with the specified parameters.
    * @param typeId must be {@link DataFormatter#STRING}
    * @param mask format mask
    */
   public StringFormatter(int typeId, String mask) throws FndException {
      init(typeId);
   }

   /**
    * Constructs a new instance of StringFormatter.
    * @param params this parameter is ignored
    */
   public StringFormatter(Buffer params) throws FndException {
      init(DataFormatter.STRING);
   }

   /**
    * Constructs a new instance of StringFormatter.
    */
   public StringFormatter() throws FndException {
      init(DataFormatter.STRING);
   }

   private void init(int typeId) throws FndException {
      switch (typeId) {
         case DataFormatter.STRING :
            break;

         default :
            throw new FndException("FNDSTRTYP: Invalid Type Id '&1' for StringFormatter.", DataFormatter.getTypeName(typeId));
      }
   }

   @Override
   public int getTypeId() {
      return STRING;
   }

   @Override
   public String format(Object value) {
      return value == null ? null : (String) value;
   }

   @Override
   public Object parse(String text) throws FndException {
      return text;
   }

   //==========================================================================
   //  Cloning
   //==========================================================================

   private StringFormatter(boolean dummy) {
   }

   /**
    * Clone this StringFormatter.
    */
   @Override
   public Object clone() {
      return new StringFormatter(true);
   }
}
