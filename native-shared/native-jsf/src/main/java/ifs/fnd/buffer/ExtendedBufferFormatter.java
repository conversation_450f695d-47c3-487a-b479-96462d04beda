/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.buffer;

import java.io.IOException;

/**
 * Framework internal class:</B> Extended implementation of BinaryBufferFormatter class
 * that introduces three additional markers: action, invalid value and count.
 * <p>
 * The additional markers are encoded within the status (invalid value and count) and
 * type (action) fields using denoting characters:
 * <pre>
 *   &#64;   action
 *   &   invalid value
 *   #   count
 * </pre>
 */
public class ExtendedBufferFormatter extends BinaryBufferFormatter {

   static final char ACTION_MARKER          = (char) 28;
   static final char INVALID_VALUE_MARKER   = (char) 18;
   static final char COUNT_MARKER           = (char) 17;

   public ExtendedBufferFormatter() {
   }

   /**
    * De-serialize one Buffer object from the specified stream into the specified Buffer.
    * "(" must have been consumed. This method will consume the matching ")".
    */
   @Override
   protected void parse(ByteArrayTokenReader reader, Buffer into) throws BufferFormatException, IOException {
      Item item;
      String type = null;

      char ch = reader.getDelimiter();
      if (ch == StandardBufferFormatter.HEAD_MARKER) { // header
         into.setHeader(reader.getToken());
         ch = reader.getDelimiter();
      }

      while (true) {
         if (ch == StandardBufferFormatter.END_BUFFER_MARKER) { // end of buffer
            into.freeze();
            return;
         }

         if (ch == StandardBufferFormatter.NAME_MARKER) { // name
            item = into.newItem();
            item.setName(reader.getToken());
            ch = reader.getDelimiter();
         } else {
            item = into.newItem();
         }

         if (ch == StandardBufferFormatter.TYPE_MARKER) { // type
            setTypeField(item, type = reader.getToken(), true);
            ch = reader.getDelimiter();
         }

         if (ch == StandardBufferFormatter.STATUS_MARKER) { // status
            setStatusField(item, reader.getToken(), StateParts.STATUS);
            ch = reader.getDelimiter();
         }

         if (ch == ACTION_MARKER) { // action
            setTypeField(item, reader.getToken(), false);
            ch = reader.getDelimiter();
         }

         if (ch == StandardBufferFormatter.IDENTITY_MARKER) { // identity
            item.setIdentity(reader.getToken());
            ch = reader.getDelimiter();
         }

         if (ch == StandardBufferFormatter.CHANGED_VALUE_MARKER) { // ignore changedValueMarker
            ch = reader.getDelimiter();
         }

         switch (ch) {
            case StandardBufferFormatter.NO_VALUE_MARKER : // no value
               into.addItem(item);
               break;

            case StandardBufferFormatter.NULL_VALUE_MARKER : // null value
               item.setValue(null);
               into.addItem(item);
               break;

            case StandardBufferFormatter.VALUE_MARKER :
               if (BufferUtil.LENGTH_PREFIXED_TEXT.equals(item.getType())
                   || BufferUtil.LENGTH_PREFIXED_ALPHA.equals(item.getType())) {
                  String token = reader.getPrefixedToken();
                  item.setValue(token);
               } else if (type != null && type.length() > 0 && type.charAt(0) == 'R') {
                   String b64 = reader.getBinaryToken();
                   item.setValue("<Binary data: " + b64.length() + " Base64 characters>");
               } else {
                   item.setValue(reader.getToken());
               }
               into.addItem(item);
               break;

            case INVALID_VALUE_MARKER :
               setStatusField(item, reader.getToken(), StateParts.INV_VALUE);
               into.addItem(item);
               break;

            case COUNT_MARKER :
               setStatusField(item, reader.getToken(), StateParts.COUNT);
               into.addItem(item);
               break;

            case StandardBufferFormatter.UNCHANGED_VALUE_MARKER :
               item.setUnchangedValueFlag(true);
               into.addItem(item);
               break;

            case StandardBufferFormatter.BEGIN_BUFFER_MARKER : // buffer
               Buffer sub = into.newInstance();
               item.setValue(sub);
               into.addItem(item);
               parse(reader, sub);
               break;

            default :
               throw new BufferFormatException("Expecting '*', '=' or '(' but found character code " + (int) ch);
         }

         ch = reader.getDelimiter();
      }
   }

   // Field format: "[<type>][@<action>]"
   private void setTypeField( Item item, String value, boolean isType) {

      String oldType   = null;
      String oldAction = null;

      String oldValue  = item.getType();
      if( oldValue!=null ) {
         int ix = oldValue.indexOf('@');
         oldType   = ix>0 ? oldValue.substring(0, ix) : (ix<0 ? oldValue : null);
         oldAction = ix>0 && ix<oldValue.length()-1 ? oldValue.substring(ix+1) : null;
      }

      if(isType)
         item.setType( value + (oldAction!=null ? "@"+oldAction : "") );
      else //action
         item.setType( (oldType!=null ? oldType : "") + "@"+value );
   }

   private enum StateParts { STATUS, INV_VALUE, COUNT };

   // Field format: "[<status>][&<invalid_value>][#<count>]"
   private void setStatusField( Item item, String value, StateParts part) {

      String status = null;
      String invVal = null;
      String count  = null;

      String oldValue  = item.getStatus();
      if( oldValue!=null ) {
         int ix1 = oldValue.indexOf('&');
         int ix2 = oldValue.indexOf('#');

         count    = ix2>0 && ix2<oldValue.length()-1 ? oldValue.substring(ix2+1) : null;
         oldValue = ix2>0 ? oldValue.substring(0, ix2) : oldValue;
         invVal   = ix1>0 && ix1<oldValue.length()-1 ? oldValue.substring(ix1+1) : null;
         status   = ix1>0 ? oldValue.substring(0, ix1) : (ix1<0 ? oldValue : null);
      }
      switch(part) {
         case STATUS:    status = value; break;
         case INV_VALUE: invVal = value; break;
         case COUNT:     count  = value; break;
      }

      item.setStatus( (status!=null ? status : "") + (invVal!=null ? "&"+invVal : "") + (count!=null ? "#"+count : "") );
   }
}

