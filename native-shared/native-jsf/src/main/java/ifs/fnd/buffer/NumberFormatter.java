/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.buffer;

import ifs.fnd.service.FndException;
import ifs.fnd.service.TraceEvent;
import ifs.fnd.service.TraceEventType;
import ifs.fnd.util.Str;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.util.Locale;

/**
 * An instance of this class can format and parse Java simple numeric values,
 * like Double, Long ...etc.
 * The type ID specified during construction must be one of:
 * {@link DataFormatter#NUMBER}, {@link DataFormatter#INTEGER}, {@link DataFormatter#MONEY}.
 */
public final class NumberFormatter extends DataFormatter {

   private static TraceEventType formatEventType = new TraceEventType("NumberFormatter.format");
   private static TraceEventType parseEventType = new TraceEventType("NumberFormatter.parse");

   private static final int MAX_DIGITS = 17;
   private static final int MAX_INT_DIGITS = 17;

   private int typeId;
   private DecimalFormat numberFormatter;
   private char decimalSeparator;

   private double roundDelta;
   private double roundFactor;
   private double maxDouble;
   private int fractionDigits;

   private static boolean performRounding;

   /**
    * Constructs a new instance of NumberFormatter.
    * @param params buffer containing TYPE, MASK and LANGUAGE items
    */
   public NumberFormatter(Buffer params) throws FndException {
      String typeName = params.getString("TYPE", "NUMBER");
      typeId = DataFormatter.getTypeId(typeName);
      String mask = params.getString("MASK", null);
      String language = params.getString("LANGUAGE", null);
      init(language, typeId, mask);
   }

   /**
    * Constructs a new instance of NumberFormatter with the specified parameters.
    * @param typeId one of {@link DataFormatter#NUMBER}, {@link DataFormatter#INTEGER}, {@link DataFormatter#MONEY}
    * @param mask format mask
    */
   public NumberFormatter(int typeId, String mask) throws FndException {
      init(null, typeId, mask);
   }

   /**
    * Constructs a new instance of NumberFormatter with the specified parameters.
    * @param language language
    * @param typeId one of {@link DataFormatter#NUMBER}, {@link DataFormatter#INTEGER}, {@link DataFormatter#MONEY}
    * @param mask format mask
    */
   public NumberFormatter(String language, int typeId, String mask) throws FndException {
      init(language, typeId, mask);
   }

   /**
    * Constructs a new instance of NumberFormatter.
    */
   public NumberFormatter() throws FndException {
      init(null, DataFormatter.NUMBER, null);
   }

   private void init(String language, int typeId, String mask) throws FndException {
      switch (typeId) {
         case DataFormatter.NUMBER :
         case DataFormatter.INTEGER :
         case DataFormatter.MONEY :
            break;

         default :
            throw new FndException("FNDNUMTYP: Invalid Type Id '&1' for NumberFormatter.", DataFormatter.getTypeName(typeId));
      }
      this.typeId = typeId;

      if (language == null)
         numberFormatter = new DecimalFormat();
      else
         numberFormatter = (DecimalFormat) NumberFormat.getNumberInstance(new Locale(language, "", ""));

      if (!Str.isEmpty(mask)){
         numberFormatter.applyPattern(mask);
      }

      numberFormatter.setGroupingUsed(true);

      if (typeId == DataFormatter.INTEGER)
         numberFormatter.setParseIntegerOnly(true);

      decimalSeparator = numberFormatter.getDecimalFormatSymbols().getDecimalSeparator();

      String pattern = numberFormatter.toPattern();
      fractionDigits = countFractionDigits(pattern);
      roundFactor = Math.pow(10.0, fractionDigits);
      roundDelta = 0.5 / roundFactor;
      maxDouble = Math.pow(10.0, MAX_DIGITS - fractionDigits);

      if (DEBUG)
         debug(
            "NumberFormatter: pattern="
               + pattern
               + " sep="
               + decimalSeparator
               + " delta="
               + roundDelta
               + "\n\t"
               + " factor="
               + roundFactor
               + " max_double="
               + maxDouble
               + " fraction_digits="
               + fractionDigits
               + " perform_rounding="
               + performRounding);
   }

   /**
    * Return the delta that defines the rounding interval for this formatter.
    */
   public double getRoundingDelta() {
      return roundDelta;
   }

   /**
    * Count all occurences of '0' and '#' after the decimal point '.'.
    */
   @SuppressWarnings("PMD.MissingBreakInSwitch")
   private int countFractionDigits(String pattern) {
      int i = pattern.indexOf('.');
      if (i < 0)
         return 0;

      int len = pattern.length();
      int count = 0;
      for (i++; i < len; i++) {
         switch (pattern.charAt(i)) {
            case '0' :
            case '#' :
               count++;
               break;
            default  :
               assert(true);
         }
      }
      return count;
   }

   /**
    * Remove all fraction digits if there is an overflow of integer+fraction digits.
    */
   @SuppressWarnings("PMD.MissingBreakInSwitch")
   private String fixOverflow(String text) throws FndException {
      int sep = text.indexOf(decimalSeparator);
      if (sep < 0)
         sep = text.length();

      int intDigits = 0;
      for (int i = 0; i < sep; i++) {
         switch (text.charAt(i)) {
            case '0' :
            case '1' :
            case '2' :
            case '3' :
            case '4' :
            case '5' :
            case '6' :
            case '7' :
            case '8' :
            case '9' :
               intDigits++;
               break;
            default  :
               assert(true);
         }
      }

      if (intDigits > MAX_INT_DIGITS)
         throw new FndException("FNDBIGNUM: &1 is a too big number. There can only be &2 integer digits.", text, ""+MAX_INT_DIGITS);

      if (intDigits + fractionDigits > MAX_DIGITS)
         return text.substring(0, sep);

      return text;
   }

   /**
    * Set the decimal separator for this formatter.
    * @param separator separator character
    */
   public void setDecimalSeparator(char separator) {
      DecimalFormatSymbols s = numberFormatter.getDecimalFormatSymbols();
      s.setDecimalSeparator(separator);
      numberFormatter.setDecimalFormatSymbols(s);
      decimalSeparator = separator;
      if (DEBUG) {
         debug(
            "NumberFormatter.setDecimalSeparator(): DecimalSeparator=" + numberFormatter.getDecimalFormatSymbols().getDecimalSeparator());
      }
   }

   @Override
   public int getTypeId() {
      return typeId;
   }

   @Override
   public String format(Object value) {
      TraceEvent formatEvent = formatEventType.begin();
      try {
         return value == null ? null : numberFormatter.format(value);
      }
      finally {
         formatEvent.end();
      }
   }

   @Override
   public Object parse(String text) throws FndException {
      if (Str.isEmpty(text))
         return null;
      TraceEvent parseEvent = parseEventType.begin();

      try {
         text = fixOverflow(text);

         ParsePosition pos = new ParsePosition(0);
         Object obj = numberFormatter.parse(text, pos);
         if (pos.getIndex() < text.length())
            throw new ParseException("Unconsumed data in '" + text + "'", pos.getIndex());

         if (obj instanceof Double) { // perform rounding
            BigDecimal bd = new BigDecimal(obj.toString());
            bd = bd.setScale(fractionDigits,RoundingMode.HALF_UP);
            obj = bd;
         }

         //text = format(obj);
         //pos = new ParsePosition(0);
         //obj = number_formatter.parse(text,pos);

         return obj;
      }
      catch (ParseException x) {
         throw (new FndException("FNDPARSESTR: Invalid value for number '&1'", text)).addCaughtException(x);
      }
      finally {
         parseEvent.end();
      }
   }

   /**
    * Sets the Group Separator of the number formatter.
    * @param c character to use as the Group Separator.
    */
   public void setGroupingSeparator(char c) {
      DecimalFormatSymbols s = numberFormatter.getDecimalFormatSymbols();
      s.setGroupingSeparator(c);
      numberFormatter.setDecimalFormatSymbols(s);
   }

   //==========================================================================
   //  Cloning
   //==========================================================================

   private NumberFormatter(boolean dummy) {
   }

   /**
    * Clone this NumberFormatter.
    */
   @Override
   public Object clone() {
      NumberFormatter f = new NumberFormatter(true);
      f.numberFormatter = (DecimalFormat) numberFormatter.clone();
      f.typeId = typeId;
      f.decimalSeparator = decimalSeparator;
      f.roundDelta = roundDelta;
      f.roundFactor = roundFactor;
      f.maxDouble = maxDouble;
      f.fractionDigits = fractionDigits;
      return f;
   }

   //==========================================================================
   //  Ststic initialization
   //==========================================================================

   static {
      try {
         NumberFormatter fmt = new NumberFormatter(DataFormatter.NUMBER, "#0.000");
         String txt = fmt.format(0.0009);
         performRounding = txt.endsWith("0");
      }
      catch (FndException any) {
         throw new RuntimeException("Cannot initialize NumberFormatter: " + any.fillInStackTrace());
      }
   }
}
