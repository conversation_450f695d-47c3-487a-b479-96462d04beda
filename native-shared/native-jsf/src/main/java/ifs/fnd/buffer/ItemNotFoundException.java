/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.buffer;

import ifs.fnd.service.FndException;

/**
 *  This exception is trhown when an Item could not be found in a Buffer.
 */
public class ItemNotFoundException extends FndException {

   /**
    * Create a new instance.
    * @param itemName name of the item not found
    */
   public ItemNotFoundException(String itemName) {
      super("FNDBUFNOITEM: Item '&1' not found in Buffer.", itemName);
   }

}
