/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.buffer;

import ifs.fnd.service.FndException;
import ifs.fnd.service.TraceEvent;
import ifs.fnd.service.TraceEventType;
import ifs.fnd.util.Str;
import java.util.Locale;

/**
 * Transforms simple Java Object
 * (like Date, Boolean, Double ...etc) into a String and vice versa.
 */
public final class ServerFormatter implements Cloneable {
   
   private static TraceEventType formatEventType = new TraceEventType("ServerFormatter.format");
   private static TraceEventType parseEventType = new TraceEventType("ServerFormatter.parse");

   private DataFormatter stringFormatter;
   //private DataFormatter numberFormatter;
   private DataFormatter booleanFormatter;
   private DataFormatter dateFormatter;

   /**
    * Create a new ServerFormatter based on the specified arguments.
    * @param timeZone time zone
    * @param dateMask format mask for dates
    */
   public ServerFormatter(String timeZone, String dateMask) throws FndException {
      dateFormatter = new DateFormatter(DataFormatter.DATETIME, dateMask, timeZone, Locale.US);

      booleanFormatter = new BooleanFormatter();
      stringFormatter = new StringFormatter();
      //number_formatter  = new NumberFormatter("en",
      //                                        DataFormatter.NUMBER,
      //                                        "#0.#############");
   }

   /**
    * Transform a specified simple Java Object (like Date, String ...etc)
    * into a String using one of the private DataFormatters.
    * The specified type_id must be one of following Base Types:
    *<pre>
    *    DataFormatter.STRING
    *    DataFormatter.BOOLEAN
    *    DataFormatter.NUMBER
    *    DataFormatter.DATETIME
    *</pre>
    */
   public String format(Object value, int typeId) throws FndException {
      if (value == null)
         return null;
      TraceEvent formatEvent = formatEventType.begin();

      try {
         switch (typeId) {
            case DataFormatter.STRING :
               return stringFormatter.format(value);

            case DataFormatter.BOOLEAN :
               return booleanFormatter.format(value);

            case DataFormatter.NUMBER :
               //return number_formatter.format(value);
               return value.toString();

            case DataFormatter.DATETIME :
               return dateFormatter.format(value);

            default :
               throw new FndException("FNDFMTOBJ: ServerFormatter cannot format object of type '&1'", DataFormatter.getTypeName(typeId));
         }
      }
      finally {
         formatEvent.end();
      }
   }

   /**
    * Transform a specified String into a simple Java Object
    * (like Date, Boolean ...etc) using one of the private DataFormatters.
    * The specified type_id must be one of following Base Types:
    *<pre>
    *    DataFormatter.STRING
    *    DataFormatter.BOOLEAN
    *    DataFormatter.NUMBER
    *    DataFormatter.DATETIME
    *</pre>
    */
   public Object parse(String text, int typeId) throws FndException {
      TraceEvent parseEvent = parseEventType.begin();

      try {
         if (DataFormatter.DEBUG)
            stringFormatter.debug("ServerFormatter: text=" + text + " type_id=" + typeId);

         switch (typeId) {
            case DataFormatter.STRING :
               return stringFormatter.parse(text);

            case DataFormatter.BOOLEAN :
               return booleanFormatter.parse(text);

            case DataFormatter.NUMBER :
               if (Str.isEmpty(text))
                  return null;
               if (text.indexOf('.') < 0)
                  return Long.valueOf(text);
               else
                  return Double.valueOf(text);

            case DataFormatter.DATETIME :
               return dateFormatter.parse(text);

            default :
               throw new FndException("FNDPARSEOBJ: ServerFormatter cannot parse object of type '&1'", DataFormatter.getTypeName(typeId));
         }
      }
      finally {
         parseEvent.end();
      }
   }

   //==========================================================================
   //  Cloning
   //==========================================================================

   private ServerFormatter() {
   }

   /**
    * Clone this ServerFormatter.
    */
   @Override
   public Object clone() {
      ServerFormatter f = new ServerFormatter();
      f.stringFormatter = (DataFormatter) stringFormatter.clone();
      f.booleanFormatter = (DataFormatter) booleanFormatter.clone();
      f.dateFormatter = (DataFormatter) dateFormatter.clone();
      return f;
   }

}