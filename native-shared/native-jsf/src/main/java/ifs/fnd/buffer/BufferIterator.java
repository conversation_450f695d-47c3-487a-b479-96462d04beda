/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.buffer;

/**
 * An interface for iterators over Items in a Buffer.
 */
public interface BufferIterator {
   /**
    * Return true if the iteration has more Items.
    * @return true if there is next item in this iteration, false otherwise
    */
   boolean hasNext();

   /**
    * Return the next Item in the interation.
    * @return next Item in the interation.
    */
   Item next();
}
