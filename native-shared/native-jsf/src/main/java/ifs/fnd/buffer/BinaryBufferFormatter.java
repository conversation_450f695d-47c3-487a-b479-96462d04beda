/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.buffer;

import java.io.IOException;

/**
 * <B>Framework internal class:</B> Binary implementation of BufferFormatter interface.
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class BinaryBufferFormatter {

   public static final char BINARY_INDICATOR = '!';

   public BinaryBufferFormatter() {
   }

   /**
    * Parse array of bytes to a Buffer. The array may contain length prefixed binary tokens,
    * which will be converted to Base64-encoded String tokens.
    * @param data a serialized buffer
    * @param buffer Buffer instance to fill with parsed data
    */
   public void parse(byte[] data, Buffer buffer) throws B<PERSON>er<PERSON><PERSON>atException, IOException {
      ByteArrayTokenReader reader = new ByteArrayTokenReader(data);
      reader.matchDelimiter(StandardBufferFormatter.BEGIN_BUFFER_MARKER);
      parse(reader, buffer);

      int rest = reader.getUnconsumedByteCount();
      if (rest > 0) {
         throw new BufferFormatException("Unconsumed " + rest + " bytes in serialized buffer");
      }
   }

   /**
    * De-serialize one Buffer object from the specified stream into the specified Buffer.
    * "(" must have been consumed. This method will consume the matching ")".
    */
   protected void parse(ByteArrayTokenReader reader, Buffer into) throws BufferFormatException, IOException {
      Item item = null;
      String type = null;

      char ch = reader.getDelimiter();
      if (ch == StandardBufferFormatter.HEAD_MARKER) { // header
         into.setHeader(reader.getToken());
         ch = reader.getDelimiter();
      }

      while (true) {
         if (ch == StandardBufferFormatter.END_BUFFER_MARKER) { // end of buffer
            into.freeze();
            return;
         }

         if (ch == StandardBufferFormatter.NAME_MARKER) { // name
            item = into.newItem();
            item.setName(reader.getToken());
            ch = reader.getDelimiter();
         } else {
            item = into.newItem();
         }

         if (ch == StandardBufferFormatter.TYPE_MARKER) { // type
            item.setType(type = reader.getToken());
            ch = reader.getDelimiter();
         }

         if (ch == StandardBufferFormatter.STATUS_MARKER) { // status
            item.setStatus(reader.getToken());
            ch = reader.getDelimiter();
         }

         if (ch == StandardBufferFormatter.IDENTITY_MARKER) { // identity
            item.setIdentity(reader.getToken());
            ch = reader.getDelimiter();
         }

         if (ch == StandardBufferFormatter.CHANGED_VALUE_MARKER) { // ignore changedValueMarker
            ch = reader.getDelimiter();
         }

         switch (ch) {
            case StandardBufferFormatter.NO_VALUE_MARKER : // no value
               into.addItem(item);
               break;

            case StandardBufferFormatter.NULL_VALUE_MARKER : // null value
               item.setValue(null);
               into.addItem(item);
               break;

            case StandardBufferFormatter.VALUE_MARKER :
               if (BufferUtil.LENGTH_PREFIXED_TEXT.equals(item.getType())
                   || BufferUtil.LENGTH_PREFIXED_ALPHA.equals(item.getType())) {
                  String token = reader.getPrefixedToken();
                  item.setValue(token);
               } else if (type != null && type.length() > 0 && type.charAt(0) == 'R') {
                   item.setValue(reader.getBinaryToken());
               } else {
                   item.setValue(reader.getToken());
               }
//               item.setValue(type != null && type.length() > 0 && type.charAt(0) == 'R' ? reader.getBinaryToken() : reader.getToken());
               into.addItem(item);

               break;

            case StandardBufferFormatter.UNCHANGED_VALUE_MARKER :
               item.setUnchangedValueFlag(true);
               into.addItem(item);
               break;

            case StandardBufferFormatter.BEGIN_BUFFER_MARKER : // buffer
               Buffer sub = into.newInstance();
               item.setValue(sub);
               into.addItem(item);
               parse(reader, sub);
               break;

            default :
               throw new BufferFormatException("Expecting '*', '=' or '(' but found character code " + (int) ch);
         }

         ch = reader.getDelimiter();
      }
   }
}
