/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */


package ifs.fnd.log;

import ifs.fnd.service.Util;
import ifs.fnd.service.IfsShutdownHook;
import ifs.fnd.service.FndRuntimeException;

/**
 * Logger Manager (factory) class that creates and manages instances of Loggers.
 * A set of loggers is created per thread basis. The default log level can be set globally for
 * the entire VM, but can be then overridden per thread and logger category (using one of setLevel() methods)
 * The manager handles predefined number of loggers: one for each category.
 * This is also possible to create new, named loggers, but is not recommended due to performance reasons.
 * Loggers are created on first call to one of getLogger() methods within a thread and are reused during
 * subsequent calls to this method, however loggers have to be recreated on log level changes.
 *
 * <pre>
 * A possible work flow:
 *
 *  - Default log level is defined globally for the entire VM through the configuration
 *    (can be different for each category)
 *
 *  - Due to different debug settings performed by a user framework can optionally call
 *    some of the setLevel() methods to set log levels for categories; the setting will
 *    be valid for the current thread only; e.g.:
 *       LogMgr.setLevel(LogMgr.INFO);
 *       LogMgr.setDatabaseLevel(LogMgr.DEBUG);
 *    For better performance framework can instead initiate all levels at the same time, e.g.:
 *       ThreadLoggers loggers = LogMgr.getThreadLoggers();
 *       loggers.setLevel(LogMgr.INFO);
 *       loggers.setDatabaseLevel(LogMgr.DEBUG);
 *       loggers.setFrameworkLevel(LogMgr.TRACE);
 *
 *  - Application flows will start with obtaining proper logger instances (one or more), e.g.:
 *       Logger log   = LogMgr.getApplicationLogger();
 *       Logger dblog = LogMgr.getDatabaseLogger();
 *    If possible loggers should be obtained as seldom as possible and stored locally
 *
 *  - Now applications can call the logger to log their messages, e.g.:
 *       log.error("Error message here...");
 *       if(log.warning) log.warning("Warning message here...");
 *       if(log.info)    log.info("Info message here...");
 *       if(log.trace)   log.trace("Trace message here...");
 *       if(log.debug)   log.debug("Debug message here...");
 *    With exception of error() methods, calls to all other logging methods HAVE TO be
 *    preceded by corresponding flag check.
 *
 *  - Once a request is finalized, the framework will typically call (e.g. from FndContext):
 *       LogMgr.reset();
 *    This method will reset log levels to the default ones and throw away all the pooled instances of loggers
 *    if the log level per logger has changed.
 *
 *  - During development developers can also create and use own instances of loggers and
 *    set wished log level for them, e.g.:
 *       Logger log   = LogMgr.getApplicationLogger(LogMgr.DEBUG);
 *    Such loggers are NOT managed by the manager.
 * </pre>
 *
 * Logging levels can be defined in different scope. On VM startup the framework will set up
 * a set of default levels: one per category, but also one global default level in case
 * the corresponding category level is not defined. All these levels are read on start up from
 * property files, if exists, but have also their hard coded values in the code. All the default
 * levels can also be set in the code by calling methods in this class.
 * <p>
 * This is possible to have different property files defining behaviour of the logging framework.
 * Following files are recognized by the framework: TODO...
 * <p>
 * Independently of the global levels each thread can define each own set of levels; one global
 * and one per category. Thread specific levels can be set by calling appropriate method as shown
 * in the example above.
 * <p>
 * On creation an instance of the Logger object is set up with level defined according
 * to the following order:
 * <ul>
 * <li>Thread level for the corresponding category
 * <li>Thread global level
 * <li>Default level for the category
 * <li>Default global level
 * </ul>
 * It is not possible to override with a less sensitive level, e.g. if a global default level
 * is set to WARNING and default database level is set to INFO, a thread can not set level
 * ERROR for any thread specific level and not even WARNING for database thread level.
 * <p>
 * Once created a Logger instance can not change its running level. But obtaining an instance
 * by calling one of getXLevel() method will automatically recreate the logger if necessary.
 *
 */
public final class LogMgr implements LogMgrMBean
{
   // Must be able to instantiate for JMX support
   public LogMgr() {}

   //===============================================================================
   //  Variable definitions
   //===============================================================================

   //  Constants defining logging levels (ERROR is always enabled)
   public final static int UNDEFINED = -1;
   public final static int ERROR     =  0; // Error is always on
   public final static int WARNING   =  1;
   public final static int INFO      =  2;
   public final static int TRACE     =  3;
   public final static int DEBUG     =  4;

   // Constants defining predefined logger categories
   public final static int PARENT_LOGGER         = 0;
   public final static int DATABASE_LOGGER       = 1;
   public final static int APPLICATION_LOGGER    = 2;
   public final static int FRAMEWORK_LOGGER      = 3;
   public final static int GATEWAY_LOGGER        = 4;
   public final static int CALLSEQUENCE_LOGGER   = 5;
   public final static int REQUEST_LOGGER        = 6;
   public final static int RESPONSE_LOGGER       = 7;
   public final static int SECURITY_LOGGER       = 8;
   public final static int INTEGRATION_LOGGER    = 9;
   public final static int BATCHPROCESSOR_LOGGER = 10;
   public final static int AUTHENTICATION_LOGGER = 11;
   public final static int DBIDPROVIDER_LOGGER = 12;

   /**
    * An array with names for all predefined loggers.
    */
   final static String[] LOGGER_NAMES = new String[]
       { "ifs",
         "ifs.Database",
         "ifs.Application",
         "ifs.Framework",
         "ifs.Gateway",
         "ifs.CallSequence",
         "ifs.Request",
         "ifs.Response",
         "ifs.Security",
         "ifs.Integration",
         "ifs.BatchProcessor",
         "ifs.Authentication",
         "ifs.DbIdProvider"};

   /**
    * Initial default debugging levels for all logger categories.
    */
   static final int[] defaultLevels = new int[]
       { ERROR, UNDEFINED, UNDEFINED, UNDEFINED, UNDEFINED, UNDEFINED, UNDEFINED, UNDEFINED, UNDEFINED, UNDEFINED, UNDEFINED, UNDEFINED, UNDEFINED };

   /**
    * ThreadLocal variable encapsulating all loggers categories for a thread.
    */
   private static ThreadLocal<ThreadLoggers> threadLoggers = new ThreadLocal<>();

   /**
    * Immutable dummy logger that ignores every call to it.
    */
   final static Logger DUMMY_LOGGER = new DummyLogger();

   /**
    * Current application name used to tag all log messages.
    */
   private static volatile String applicationName;

   //===============================================================================
   //  Application name
   //===============================================================================

   /**
    * Sets the name of the current application.
    * @param appName string that will be used to tag all log messages
    */
   public static void setApplicationName(String appName) {
      applicationName = appName;
   }

   /**
    * Gets the name of the current application.
    * @return string used as tag for all log messages.
    * @see #setApplicationName(java.lang.String)
    */
   public static String getApplicationName() {
      return applicationName;
   }

   //===============================================================================
   //  Defining and resetting default logging levels.
   //===============================================================================

   /**
    * Sets default global level for all loggers. Can be overridden per thread and/or category.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setDefaultLevel( int level )
   {
      return setDefaultLevel( PARENT_LOGGER, level );
   }

   /**
    * Sets default level for Database loggers. Can be overridden per thread.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setDefaultDatabaseLevel( int level )
   {
      return setDefaultLevel( DATABASE_LOGGER, level );
   }

   /**
    * Sets default level for Application loggers. Can be overridden per thread.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setDefaultApplicationLevel( int level )
   {
      return setDefaultLevel( APPLICATION_LOGGER, level );
   }

   /**
    * Sets default level for Framework loggers. Can be overridden per thread.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setDefaultFrameworkLevel( int level )
   {
      return setDefaultLevel( FRAMEWORK_LOGGER, level );
   }

   /**
    * Sets default level for Gateway loggers. Can be overridden per thread.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setDefaultGatewayLevel( int level )
   {
      return setDefaultLevel( GATEWAY_LOGGER, level );
   }

   /**
    * Sets default level for Call Sequence loggers. Can be overridden per thread.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setDefaultCallSequenceLevel( int level )
   {
      return setDefaultLevel( CALLSEQUENCE_LOGGER, level );
   }

   /**
    * Sets default level for Request loggers. Can be overridden per thread.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setDefaultRequestLevel( int level )
   {
      return setDefaultLevel( REQUEST_LOGGER, level );
   }

   /**
    * Sets default level for Response loggers. Can be overridden per thread.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setDefaultResponseLevel( int level )
   {
      return setDefaultLevel( RESPONSE_LOGGER, level );
   }

   /**
    * Sets default level for Security loggers. Can be overridden per thread.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setDefaultSecurityLevel( int level )
   {
      return setDefaultLevel( SECURITY_LOGGER, level );
   }

   /**
    * Sets default level for Integration loggers. Can be overridden per thread.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setDefaultIntegrationLevel( int level )
   {
      return setDefaultLevel( INTEGRATION_LOGGER, level );
   }

   /**
    * Sets default level for Batch Processor loggers. Can be overridden per thread.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setDefaultBatchProcessorLevel( int level )
   {
      return setDefaultLevel( BATCHPROCESSOR_LOGGER, level );
   }

   /**
    * Sets default level for Authentication loggers. Can be overridden per thread.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setDefaultAuthenticationLevel( int level )
   {
      return setDefaultLevel( AUTHENTICATION_LOGGER, level );
   }

   /**
    * Sets default level for DB_ID_Provider loggers. Can be overridden per thread.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setDefaultDbIdProviderLevel( int level )
   {
      return setDefaultLevel( DBIDPROVIDER_LOGGER, level );
   }
   /**
    *
    *
   public static int setDefaultLevel( String loggerName, int level )
   {
      return 0; // ToDo
   }*/

   //===============================================================================
   //  Defining and resetting thread logging levels.
   //===============================================================================

   /**
    * Sets thread level for all loggers. Can be overridden per category.
    * Returns value of the previous l
    * @param level.
    * @return
    */
   public static int setLevel( int level )
   {
      return getThreadLoggers().setLevel(level);
   }

   /**
    * Sets thread level for Database logger.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setDatabaseLevel( int level )
   {
      return getThreadLoggers().setDatabaseLevel(level);
   }

   /**
    * Sets thread level for Application logger.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setApplicationLevel( int level )
   {
      return getThreadLoggers().setApplicationLevel(level);
   }

   /**
    * Sets thread level for Framework logger.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setFrameworkLevel( int level )
   {
      return getThreadLoggers().setFrameworkLevel(level);
   }

   /**
    * Sets thread level for Gateway logger.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setGatewayLevel( int level )
   {
      return getThreadLoggers().setGatewayLevel(level);
   }

   /**
    * Sets thread level for Call Sequence logger.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setCallSequenceLevel( int level )
   {
      return getThreadLoggers().setCallSequenceLevel(level);
   }

   /**
    * Sets thread level for Request logger.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setRequestLevel( int level )
   {
      return getThreadLoggers().setRequestLevel(level);
   }

   /**
    * Sets thread level for Response logger.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setResponseLevel( int level )
   {
      return getThreadLoggers().setResponseLevel(level);
   }

   /**
    * Sets thread level for Security logger.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setSecurityLevel( int level )
   {
      return getThreadLoggers().setSecurityLevel(level);
   }

   /**
    * Sets thread level for Integration logger.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setIntegrationLevel( int level )
   {
      return getThreadLoggers().setIntegrationLevel(level);
   }

   /**
    * Sets thread level for Batch Processor logger.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setBatchProcessorLevel( int level )
   {
      return getThreadLoggers().setBatchProcessorLevel(level);
   }

   /**
    * Sets thread level for Authentication logger.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setAuthenticationLevel( int level )
   {
      return getThreadLoggers().setAuthenticationLevel(level);
   }

    /**
    * Sets thread level for DbIdProvider logger.
    * Returns value of the previous level.
    * @param level
    * @return
    */
   public static int setDbIdProviderLevel( int level )
   {
      return getThreadLoggers().setDbIdProviderLevel(level);
   }
   /**
    *
    *
   public static int setLevel( String loggerName, int level )
   {
      return 0; // ToDo
   }*/

   /**
    * Resets all thread loggers to their default levels.
    */
   public static void reset()
   {
      getThreadLoggers().reset();
   }

   /**
    * Clears all logger instances for the current thread.
    */
   public static void clearThreadLoggers()
   {
      setThreadLoggers(null);
   }

   //===============================================================================
   //  Methods for obtaining logger instances
   //===============================================================================

   /**
    * Returns an instance of the Database Logger. A new instance is created if necessary.
    * @return
    */
   public static Logger getDatabaseLogger()
   {
      return getThreadLoggers().getDatabaseLogger();
   }

   /**
    * Returns an instance of the Application Logger. A new instance is created if necessary.
    * @return
    */
   public static Logger getApplicationLogger()
   {
      return getThreadLoggers().getApplicationLogger();
   }

   /**
    * Returns an instance of the Framework Logger. A new instance is created if necessary.
    * @return
    */
   public static Logger getFrameworkLogger()
   {
      return getThreadLoggers().getFrameworkLogger();
   }

   /**
    * Returns an instance of the Gateway Logger. A new instance is created if necessary.
    * @return
    */
   public static Logger getGatewayLogger()
   {
      return getThreadLoggers().getGatewayLogger();
   }

   /**
    * Returns an instance of the Call Sequence Logger. A new instance is created if necessary.
    * @return
    */
   public static Logger getCallSequenceLogger()
   {
      return getThreadLoggers().getCallSequenceLogger();
   }

   /**
    * Returns an instance of the Request Logger. A new instance is created if necessary.
    * @return
    */
   public static Logger getRequestLogger()
   {
      return getThreadLoggers().getRequestLogger();
   }

   /**
    * Returns an instance of the Response Logger. A new instance is created if necessary.
    * @return
    */
   public static Logger getResponseLogger()
   {
      return getThreadLoggers().getResponseLogger();
   }

   /**
    * Returns an instance of the Security Logger. A new instance is created if necessary.
    * @return
    */
   public static Logger getSecurityLogger()
   {
      return getThreadLoggers().getSecurityLogger();
   }

   /**
    * Returns an instance of the Integration Logger. A new instance is created if necessary.
    * @return
    */
   public static Logger getIntegrationLogger()
   {
      return getThreadLoggers().getIntegrationLogger();
   }

   /**
    * Returns an instance of the Batch Processor Logger. A new instance is created if necessary.
    * @return
    */
   public static Logger getBatchProcessorLogger()
   {
      return getThreadLoggers().getBatchProcessorLogger();
   }

   /**
    * Returns an instance of the Authentication Logger. A new instance is created if necessary.
    * @return
    */
   public static Logger getAuthenticationLogger()
   {
      return getThreadLoggers().getAuthenticationLogger();
   }

   /**
    * Returns an instance of the DbIdProvider Logger. A new instance is created if necessary.
    * @return
    */
   public static Logger getDbIdProviderLogger()
   {
      return getThreadLoggers().getDbIdProviderLogger();
   }
   /**
    * Returns a logger for the specified class. A new instance is created if necessary.
    * Class loggers are used to output class specific debug information that is not
    * interesting during debugging of the system flow.
    * @param cls
    * @return
    */
   public static Logger getClassLogger(Class cls)
   {
      return getThreadLoggers().getClassLogger(cls);
   }

   /**
    * Returns an instance of a named Logger. A new instance is created if necessary.
    * Using of named loggers is not recommended due to performance reasons.
    *
   public static Logger getLogger( String name )
   {
      return null; // ToDo
   }

   public static Logger getLogger( String name, int level  )
   {
      return null; // ToDo
   }*/

   //===============================================================================
   //  Management methods
   //===============================================================================

   /**
    * Reads the default global level of all loggers.
    * This is a management interface method.
    * @return   Current default global level of all loggers
    */
   @Override
   public String getDefaultLevel()
   {
       int defLevel = defaultLevels[PARENT_LOGGER];
       return translateLogLevel(defLevel);
   }

   /**
    * Sets the default global level for all loggers.
    * This is a management interface method.
    * @param   level   New default global level. If invalid, level will be set to UNDEFINED.
    */
   @Override
   public void setDefaultLevel( String level )
   {
       int defLevel = translateLogLevel(level);
       setDefaultLevel(defLevel);
   }

   /**
    * Reads the default level of framework loggers.
    * This is a management interface method.
    * @return   Current default level of framework loggers
    */
   @Override
   public String getDefaultFrameworkLevel()
   {
      int defLevel = defaultLevels[FRAMEWORK_LOGGER];
      //defLevel = (defLevel==UNDEFINED) ? defaultLevels[PARENT_LOGGER] : defLevel;
      return translateLogLevel(defLevel);
   }

   /**
    * Sets the default level for framework loggers.
    * This is a management interface method.
    * @param   level   New default framework level. If invalid, level will be set to UNDEFINED.
    */
   @Override
   public void setDefaultFrameworkLevel( String level )
   {
      int defLevel = translateLogLevel(level);
      setDefaultFrameworkLevel(defLevel);
   }

   /**
    * Reads the default level of gateway loggers.
    * This is a management interface method.
    * @return   Current default level of gateway loggers
    */
   @Override
   public String getDefaultGatewayLevel()
   {
      int defLevel = defaultLevels[GATEWAY_LOGGER];
      return translateLogLevel(defLevel);
   }

   /**
    * Sets the default level for gateway loggers.
    * This is a management interface method.
    * @param   level   New default gateway level. If invalid, level will be set to UNDEFINED.
    */
   @Override
   public void setDefaultGatewayLevel( String level )
   {
      int defLevel = translateLogLevel(level);
      setDefaultGatewayLevel(defLevel);
   }

   /**
    * Reads the default level of integration loggers.
    * This is a management interface method.
    * @return   Current default level of integration loggers
    */
   @Override
   public String getDefaultIntegrationLevel()
   {
      int defLevel = defaultLevels[INTEGRATION_LOGGER];
      return translateLogLevel(defLevel);
   }

   /**
    * Sets the default level for integration loggers.
    * This is a management interface method.
    * @param   level   New default integration level. If invalid, level will be set to UNDEFINED.
    */
   @Override
   public void setDefaultIntegrationLevel( String level )
   {
      int defLevel = translateLogLevel(level);
      setDefaultIntegrationLevel(defLevel);
   }

   /**
    * Reads the default level of Batch Processor loggers.
    * This is a management interface method.
    * @return   Current default level of Batch Processor loggers
    */
   @Override
   public String getDefaultBatchProcessorLevel()
   {
      int defLevel = defaultLevels[BATCHPROCESSOR_LOGGER];
      return translateLogLevel(defLevel);
   }

   /**
    * Sets the default level for Batch Processor loggers.
    * This is a management interface method.
    * @param   level   New default Batch Processor level. If invalid, level will be set to UNDEFINED.
    */
   @Override
   public void setDefaultBatchProcessorLevel( String level )
   {
      int defLevel = translateLogLevel(level);
      setDefaultBatchProcessorLevel(defLevel);
   }

   /**
    * Reads the default level of call sequence loggers.
    * This is a management interface method.
    * @return   Current default level of call sequence loggers
    */
   @Override
   public String getDefaultCallSequenceLevel()
   {
      int defLevel = defaultLevels[CALLSEQUENCE_LOGGER];
      return translateLogLevel(defLevel);
   }

   /**
    * Sets the default level for call sequence loggers.
    * This is a management interface method.
    * @param   level   New default call sequence level. If invalid, level will be set to UNDEFINED.
    */
   @Override
   public void setDefaultCallSequenceLevel( String level )
   {
      int defLevel = translateLogLevel(level);
      setDefaultCallSequenceLevel(defLevel);
   }

   /**
    * Reads the default level of application loggers.
    * This is a management interface method.
    * @return   Current default level of application loggers
    */
   @Override
   public String getDefaultApplicationLevel()
   {
      int defLevel = defaultLevels[APPLICATION_LOGGER];
      return translateLogLevel(defLevel);
   }

   /**
    * Sets the default level for application loggers.
    * This is a management interface method.
    * @param   level   New default application level. If invalid, level will be set to UNDEFINED.
    */
   @Override
   public void setDefaultApplicationLevel( String level )
   {
      int defLevel = translateLogLevel(level);
      setDefaultApplicationLevel(defLevel);
   }

   /**
    * Reads the default level of database loggers.
    * This is a management interface method.
    * @return   Current default level of database loggers
    */
   @Override
   public String getDefaultDatabaseLevel()
   {
      int defLevel = defaultLevels[DATABASE_LOGGER];
      return translateLogLevel(defLevel);
   }

   /**
    * Sets the default level for database loggers.
    * This is a management interface method.
    * @param   level   New default database level. If invalid, level will be set to UNDEFINED.
    */
   @Override
   public void setDefaultDatabaseLevel( String level )
   {
      int defLevel = translateLogLevel(level);
      setDefaultDatabaseLevel(defLevel);
   }

    /**
    * Reads the default level of request loggers.
    * This is a management interface method.
    * @return   Current default level of request loggers
    */
   @Override
   public String getDefaultRequestLevel()
   {
      int defLevel = defaultLevels[REQUEST_LOGGER];
      return translateLogLevel(defLevel);
   }

   /**
    * Sets the default level for request loggers.
    * This is a management interface method.
    * @param   level   New default request level. If invalid, level will be set to UNDEFINED.
    */
   @Override
   public void setDefaultRequestLevel( String level )
   {
      int defLevel = translateLogLevel(level);
      setDefaultRequestLevel(defLevel);
   }
   /**
    * Reads the default level of response loggers.
    * This is a management interface method.
    * @return   Current default level of response loggers
    */
   @Override
   public String getDefaultResponseLevel()
   {
      int defLevel = defaultLevels[RESPONSE_LOGGER];
      return translateLogLevel(defLevel);
   }

   /**
    * Sets the default level for response loggers.
    * This is a management interface method.
    * @param   level   New default response level. If invalid, level will be set to UNDEFINED.
    */
   @Override
   public void setDefaultResponseLevel( String level )
   {
      int defLevel = translateLogLevel(level);
      setDefaultResponseLevel(defLevel);
   }

   /**
    * Reads the default level of security loggers.
    * This is a management interface method.
    * @return   Current default level of security loggers
    */
   @Override
   public String getDefaultSecurityLevel()
   {
      int defLevel = defaultLevels[SECURITY_LOGGER];
      return translateLogLevel(defLevel);
   }

   /**
    * Sets the default level for security loggers.
    * This is a management interface method.
    * @param   level   New default security level. If invalid, level will be set to UNDEFINED.
    */
   @Override
   public void setDefaultSecurityLevel( String level )
   {
      int defLevel = translateLogLevel(level);
      setDefaultSecurityLevel(defLevel);
   }

   /**
    * Reads the default level of authentication loggers.
    * This is a management interface method.
    * @return   Current default level of authentication loggers
    */
   @Override
   public String getDefaultAuthenticationLevel()
   {
      int defLevel = defaultLevels[AUTHENTICATION_LOGGER];
      return translateLogLevel(defLevel);
   }

   /**
    * Sets the default level for authenticationr loggers.
    * This is a management interface method.
    * @param   level   New default security level. If invalid, level will be set to UNDEFINED.
    */
   @Override
   public void setDefaultAuthenticationLevel( String level )
   {
      int defLevel = translateLogLevel(level);
      setDefaultAuthenticationLevel(defLevel);
   }

    /**
    * Reads the default level of authentication loggers.
    * This is a management interface method.
    * @return   Current default level of DB id provider loggers
    */
   @Override
   public String getDefaultDbIdProviderLevel()
   {
      int defLevel = defaultLevels[DBIDPROVIDER_LOGGER];
      return translateLogLevel(defLevel);
   }

   /**
    * Sets the default level for DbIdProvider loggers.
    * This is a management interface method.
    * @param   level   New default security level. If invalid, level will be set to UNDEFINED.
    */
   @Override
   public void setDefaultDbIdProviderLevel( String level )
   {
      int defLevel = translateLogLevel(level);
      setDefaultDbIdProviderLevel(defLevel);
   }
   /**
    * Adds class Logger definition on form 'class name=level'.
    * @param definition Class Logger definition to add
    * @return true if the definition has been added
    */
   @Override
   public boolean addClassLogger(String definition)
   {
      return LoggerConfig.addClassLogger(definition);
   }

   /**
    * Removes class Logger definition.
    * @param name class name for Logger that should be removed
    * @return true if the Logger has been removed
    */
   @Override
   public boolean removeClassLogger(String name)
   {
      return LoggerConfig.removeClassLogger(name);
   }

   /**
    * Parses the content of supplied mapping file.
    * @param file XML version of mapping file
    * @return true if read and parsed correctly
    */
   /*
   @Override
   public boolean readMappingFile(String file)
   {
      return LoggerConfig.readMappingFile(file);
   }
   */

   /**
    * Removes the currently defined mapping file
    * @return true if successfully removed
    */
   /*
   @Override
   public boolean removeMappingFile()
   {
      return LoggerConfig.removeMappingFile();
   }
   */

   //===============================================================================
   //  Help routines
   //===============================================================================

   /**
    * Checks the value of level.
    * @param level logging level to be checked
    * @param allowUndef true if the value of UNDEFINED level is allowed here, false otherwise
    */
   static void checkLevel( int level, boolean allowUndef )
   {
      int lowestLevel = allowUndef ? UNDEFINED : ERROR;
      if( level < lowestLevel || level > DEBUG )
         throw new FndRuntimeException("Undefined log level");
   }

   /**
    * Sets a new value of a specified default level. Returns the previous value.
    */
   private static int setDefaultLevel( int defLevel, int newLevel )
   {
      if( defaultLevels[defLevel] == newLevel )
         return newLevel;

      synchronized(defaultLevels)
      {
         checkLevel(newLevel, defLevel!=PARENT_LOGGER);

         int oldLevel = defaultLevels[defLevel];
         defaultLevels[defLevel] = newLevel;

         return oldLevel==UNDEFINED ? defaultLevels[PARENT_LOGGER] : oldLevel;
      }
   }

   private String translateLogLevel(int level)
   {
       switch (level)
       {
           case UNDEFINED:  return "UNDEFINED";
           case ERROR:      return "ERROR";
           case WARNING:    return "WARNING";
           case INFO:       return "INFO";
           case TRACE:      return "TRACE";
           case DEBUG:      return "DEBUG";
           default:         return "UNKNOWN";
       }
   }

   /**
    * Converts a string representation of a log level into an integer identifier.
    * @param level string representation of a log level
    * @return integer constant identifying the specified log level
    */
   public static int translateLogLevel(String level)
   {
       level = level.trim();

       if("ERROR".equalsIgnoreCase(level))
           return ERROR;
       else if("WARNING".equalsIgnoreCase(level))
           return WARNING;
       else if("INFO".equalsIgnoreCase(level))
           return INFO;
       else if("TRACE".equalsIgnoreCase(level))
           return TRACE;
       else if("DEBUG".equalsIgnoreCase(level))
           return DEBUG;
       else
           return UNDEFINED;
   }

   /**
    * Returns an instance of a ThreadLoggers class that encapsulates all thread loggers.
    * @see
    * @return  ifs.fnd.log.ThreadLoggers
    */
   public static ThreadLoggers getThreadLoggers()
   {
      ThreadLoggers loggers = threadLoggers.get();
      if( loggers==null )
      {
         loggers = new ThreadLoggers();
         loggers.globalLevel = defaultLevels[PARENT_LOGGER];

         loggers.databaseLogger      .defLevel = defaultLevels[DATABASE_LOGGER];
         loggers.applicationLogger   .defLevel = defaultLevels[APPLICATION_LOGGER];
         loggers.frameworkLogger     .defLevel = defaultLevels[FRAMEWORK_LOGGER];
         loggers.gatewayLogger       .defLevel = defaultLevels[GATEWAY_LOGGER];
         loggers.callSequenceLogger  .defLevel = defaultLevels[CALLSEQUENCE_LOGGER];
         loggers.requestLogger       .defLevel = defaultLevels[REQUEST_LOGGER];
         loggers.responseLogger      .defLevel = defaultLevels[RESPONSE_LOGGER];
         loggers.securityLogger      .defLevel = defaultLevels[SECURITY_LOGGER];
         loggers.integrationLogger   .defLevel = defaultLevels[INTEGRATION_LOGGER];
         loggers.batchProcessorLogger.defLevel = defaultLevels[BATCHPROCESSOR_LOGGER];
         loggers.authenticationLogger.defLevel = defaultLevels[AUTHENTICATION_LOGGER];
         loggers.dbIdProviderLogger.defLevel   = defaultLevels[DBIDPROVIDER_LOGGER];

         setThreadLoggers(loggers);
      }
      return loggers;
   }

   /**
    * Sets new value of thread loggers.
    */
   private static void setThreadLoggers( ThreadLoggers loggers )
   {
      threadLoggers.set(loggers);
   }

   //===============================================================================
   //  Debugging
   //===============================================================================

   /**
    * Prints out contents of an integer array.
    * TODO: Should be moved to an utility class.
    */
   static String intArrayToString( int[] arr )
   {
      StringBuilder buf = new StringBuilder();
      buf.append('[');
      for( int i=0; i<arr.length; i++ )
      {
         if(i>0)
            buf.append(',');
         buf.append(arr[i]);
      }
      buf.append(']');
      return buf.toString();
   }

   //===============================================================================
   //  Static initialization
   //===============================================================================

   /**
    * Global initialization of the logging framework
    */
   //All exceptions are logged but not thrown since logging framework errors shouldn't affect the running application server
   static
   {
      try
      {
         Util.initClass(IfsShutdownHook.class);
         LoggerConfig.init();
      }
      catch( Throwable t )// NOPMD
      {
         System.err.println(Util.getStackTrace(t));// NOPMD

         Throwable cause = t.getCause();
         if( cause!=null )
            System.err.println(Util.getStackTrace(cause));// NOPMD
      }
   }
}

