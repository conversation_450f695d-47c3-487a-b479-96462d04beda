/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */


package ifs.fnd.log;

import ifs.fnd.service.IfsConstants;
import ifs.fnd.service.Util;
import ifs.fnd.util.Str;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.logging.Formatter;
import java.util.logging.LogRecord;

/**
 * Base class for IFS logging formatters.
 */
abstract class IfsFormatter extends Formatter
{
   /**
    * The platform's line separator sequence.
    */
   protected static final String NL = IfsConstants.LINE_SEPARATOR;

   private final SimpleDateFormat shortTimestampFormat = new SimpleDateFormat("HH:mm:ss,SSS", Locale.US);

   private final SimpleDateFormat longTimestampFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss,SSS", Locale.US);

   /**
    * Format a short form of the timestamp associated with a log record.
    * @param buf the buffer to append formatted text to
    * @param record the log record being formatted
    */
   protected void formatShortTimestamp(StringBuilder buf, LogRecord record)
   {
      buf.append(shortTimestampFormat.format(new Date(record.getMillis())));
   }

   /**
    * Format a long form of the timestamp associated with a log record.
    * @param buf the buffer to append formatted text to
    * @param record the log record being formatted
    */
   protected void formatLongTimestamp(StringBuilder buf, LogRecord record)
   {
      buf.append(longTimestampFormat.format(new Date(record.getMillis())));
   }

   /**
    * Format the java source class and method name associated with a log record.
    * @param buf the buffer to append formatted text to
    * @param record the log record being formatted
    */
   protected void formatSourceMethod(StringBuilder buf, LogRecord record)
   {
      formatSourceMethod(buf, record, true);
   }

   /**
    * Format the java source class and method name associated with a log record.
    * @param buf the buffer to append formatted text to
    * @param record the log record being formatted
    * @param showPkg if 'true' the fully qualified class name is shown
    */
   protected void formatSourceMethod(StringBuilder buf, LogRecord record, boolean showPkg)
   {
      String name   = record.getLoggerName();
      String cls    = record.getSourceClassName();
      String meth   = record.getSourceMethodName();
      if(meth == null)
         return;
      int lineNo = -1;
      int pos = meth.indexOf(':');
      if(pos>0)
      {
         try
         {
            lineNo = Integer.parseInt(meth.substring(pos+1));
         }
         catch(NumberFormatException e)
         {
            lineNo = -1;
         }
         meth = meth.substring(0, pos);
      }

      String orgCls = cls;
      if( cls!=null )
      {
         cls = LoggerConfig.getMappedClass(cls);
         if( !showPkg )
         {
            int ix = cls.lastIndexOf('.');
            if( ix>=0 && ix<cls.length()-1 )
               cls = cls.substring(ix+1);
         }
         buf.append(cls);
      }
      else
      {
         buf.append('{');
         buf.append(name);
         buf.append('}');
      }
      if( meth!=null )
      {
         meth = LoggerConfig.getMappedMethod(orgCls, meth, lineNo);
         buf.append( cls!=null ? '.' : ' ');
         buf.append(meth).append("()");
      }
      buf.append(": ");
   }

   /**
    * Format the throwable associated with a log record.
    * @param buf the buffer to append formatted text to
    * @param record the log record being formatted
    */
   protected void formatThrowable(StringBuilder buf, LogRecord record)
   {
      Throwable th = record.getThrown();
      if(th != null)
      {
         String stackTrace = Util.getStackTrace(th);
         try
         {
            stackTrace = LoggerConfig.decodeStackTrace(stackTrace);
         }
         catch(IOException e)
         {
            stackTrace = "Got IOException:\n"+Util.getStackTrace(e)+"\nwhile decoding stack trace:\n"+stackTrace;// NOPMD
         }
         buf.append(stackTrace);
      }
   }

   /**
    * Format the thread ID associated with a log record.
    * @param buf the buffer to append formatted text to
    * @param record the log record being formatted
    */
   protected void formatThreadId(StringBuilder buf, LogRecord record)
   {
      buf.append( Str.lpad(String.valueOf(record.getThreadID()), 3) );
   }

   /**
    * Format the logger name associated with a log record.
    * @param buf the buffer to append formatted text to
    * @param record the log record being formatted
    */
   protected void formatLoggerName(StringBuilder buf, LogRecord record)
   {
      String name = record.getLoggerName();

      if( name.length() > 4 )
      {
         if( name.startsWith("ifs.Classdebug.") )
            name = "Classdebug";
         else if( name.startsWith("ifs.") )
            name = name.substring(4);
      }

      buf.append('[');
      buf.append(name);
      buf.append(']');
   }

   /**
    * Format the tag associated with a log record.
    * @param buf the buffer to append formatted text to
    * @param record the log record being formatted
    */
   protected void formatLoggerTag(StringBuilder buf, LogRecord record)
   {
      Object[] params = record.getParameters();
      if( params==null || params[0]==null || !(params[0] instanceof String) )
         return;

      Tags tag = new Tags();
      if( !tag.parse((String)params[0]) )
         return;

      String name = tag.getName();

      buf.append('{');
      buf.append(name);
      buf.append('}');
   }

   /**
    * Format the logging message level associated with a log record.
    * @param buf the buffer to append formatted text to
    * @param record the log record being formatted
    */
   protected void formatLevel(StringBuilder buf, LogRecord record)
   {
      //buf.append( Util.rpad( LoggerImpl.translateLevelStr(record.getLevel()), 8) );
      buf.append( LoggerImpl.translateLevelStr(record.getLevel()) );
   }

   /**
    * Format message. Fix new line characters depending on OS.
    * @param  record  the log record containing the message
    * @return   a formatted message
    */
   @Override
   public String formatMessage(LogRecord record)
   {
      String msg = record.getMessage();
      if( msg==null )
         return null;

      StringBuilder buf = new StringBuilder();
      int len = msg.length();
      for( int i=0; i<len; i++ )
      {
         char ch = msg.charAt(i);
         switch(ch)
         {
            case '\r':
               break;
            case '\n':
               buf.append(NL);
               break;
            default:
               buf.append(ch);
         }
      }
      return buf.toString();
   }
}
