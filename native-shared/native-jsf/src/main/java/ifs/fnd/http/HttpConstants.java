/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.http;

/**
 * Definitions of HTTP related constants.
 */
public interface HttpConstants
{
   String HTTP_GET  = "GET";
   String HTTP_POST = "POST";

   String HEADER_HOST            = "Host";
   String HEADER_CONTENT_TYPE    = "Content-Type";
   String HEADER_CONTENT_LENGTH  = "Content-Length";
   String HEADER_ACCEPT_LNG      = "Accept-Language";

   String HEADER_SOAP_ACTION          = "SOAPAction";
   String HEADER_AUTHORIZATION        = "Authorization";
   String HEADER_RUN_AS               = "Run-As-Identity";
   String HEADER_INTERACTIVE          = "InteractiveMode";
   String HEADER_DYNAMIC_OP           = "Dynamic-Operation-Call";
   String HEADER_APP_CONTEXT_LEN      = "ApplicationContextLength";
   String HEADER_CONNECT_DEBUG_LENGTH = "ConnectDebugLength";

   String BASE64_START = "B64{";
   String ENCODE_END = "}";
}
