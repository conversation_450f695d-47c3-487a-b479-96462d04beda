/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.http;

import java.net.*;
import java.io.*;
import jakarta.servlet.http.HttpServletRequest;

import ifs.fnd.log.*;
import ifs.fnd.base.*;
import ifs.fnd.service.IfsProperties;
import ifs.fnd.util.Str;

/**
 * Static class containing a collection of HTTP utility functions.
 */

public class HttpUtilities implements HttpConstants
{
   private static final int MAX_REQUEST_HEADER_LENGTH = IfsProperties.getSnapshot().getProperty("fnd.maxRequestHeaderLength", 10240);

   /**
    * Avoid instantiation
    */
   private HttpUtilities()
   {
   }

   /**
    * Reads Application Context of length given as a HTTP header "ApplicationContextLength"
    * from the given Input Stream.
    *
    * @param httpHeaders HTTP header container object; currently supported classes are:
    *                     jakarta.servlet.http.HttpServletRequest and java.net.URLConnection
    * @param is          Input Stream to read the context from
    * @param log         Logger used for logging and debugging
    * @return the read Application Context
    */
   public static byte[] readApplicationContext( Object httpHeaders, InputStream is, Logger log ) throws SystemException, IOException
   {
      byte[] appctx    = null;
      int    appctxlen = 0;
      String header    = null;
      boolean request = false;

      if( httpHeaders instanceof HttpServletRequest ) {
         header = ((HttpServletRequest)httpHeaders).getHeader(HEADER_APP_CONTEXT_LEN);
         request = true;
      }
      else if( httpHeaders instanceof URLConnection )
         header = ((URLConnection)httpHeaders).getHeaderField(HEADER_APP_CONTEXT_LEN);
      else
         throw new SystemException("FNDCNAPPCTXLENDEF: Not supported HTTP header container class '&1'", httpHeaders.getClass().getName() );

      if( header!=null )
      {
         try
         {
            appctxlen = Integer.parseInt(header);
            if(log!=null && log.debug) log.debug("Application Context found with length: &1", header);
         }
         catch( NumberFormatException ex )
         {
            throw new SystemException(ex, "FNDCNAPPCTXLENDEF: Invalid value of HTTP header attribute 'ApplicationContextLength': &1", header);
         }

         if( appctxlen>0 )
         {
            if(log!=null && log.debug) log.debug("Reading Application Context");
            if (request && appctxlen > MAX_REQUEST_HEADER_LENGTH) {
               throw new RefusedRequestException("Unreasonable request header length (&1)", String.valueOf(appctxlen));
            }
            appctx = new byte[appctxlen];
            DataInputStream dis = new DataInputStream(is);
            dis.readFully(appctx);
            if(log!=null && log.debug) log.debug("Read Application Context with length &1:\n&2\n.", appctxlen, Str.bytesUtf8ToString(appctx) );
         }
      }
      return appctx;
   }
}
