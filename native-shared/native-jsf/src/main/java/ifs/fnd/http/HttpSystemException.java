/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.http;

import ifs.fnd.base.SystemException;

/**
 * Subclass of SystemException that encapsulates HTTP response code
 * 
 * <AUTHOR>
 */
public class HttpSystemException extends SystemException
{
   private int    responseCode;
   private String responseMessage;

   public HttpSystemException( int respCode, final String msg, final String... p1 )
   {
      super(msg, p1);
      this.responseCode = respCode;
   }

   public HttpSystemException( String respMsg, int respCode, final String msg, final String... p1 )
   {
      super(msg, p1);
      this.responseCode    = respCode;
      this.responseMessage = respMsg;
   }

   public int getResponseCode()
   {
      return this.responseCode;
   }

   public String getResponseMessage()
   {
      return this.responseMessage;
   }
}
