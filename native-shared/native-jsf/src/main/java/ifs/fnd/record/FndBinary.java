/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.--
 */

package ifs.fnd.record;

import java.io.*;
import ifs.fnd.base.*;
import ifs.fnd.record.serialization.*;

/** FndBinary data type
 */
public class FndBinary extends FndAttribute implements Cloneable {

   protected int defaultEncoding = FndSerializeConstants.ENCODING_B64;
   private boolean isLongRaw = false;

   private transient FndInputStreamManager inputMgr;
   private transient FndOutputStreamManager outputMgr;

   /**
    * Create a new instance.
    */
   public FndBinary() {
      super(FndAttributeType.BINARY);
   }

   /**
    * Create a new instance, also specifying attribute name.
    * @param name attribute name
    */
   public FndBinary(String name) {
      super(FndAttributeType.BINARY, name);
   }

   /**
    * Create a new instance with specified name and type.
    * @param name attribute name
    * @param type attribute type
    */
   protected FndBinary(String name, FndAttributeType type) {
      super(type, name);
   }

   /**
    * Create a new instance based on specified meta data.
    * @param meta attribute meta data
    */
   public FndBinary(FndAttributeMeta meta) {
      super(meta);
      setType(FndAttributeType.BINARY);
   }

   protected FndBinary(FndAttributeMeta meta, FndAttributeType type) {
      super(meta);
      setType(type);
   }

   /**
    * Create a new instance with the specidied name.
    * @param name attribute name
    * @param isLongRaw true for storage type {@link ifs.fnd.record.FndSqlType#LONGRAW},
    *                  false for {@link ifs.fnd.record.FndSqlType#BINARY}
    */
   public FndBinary(String name, boolean isLongRaw) {
      super(FndAttributeType.BINARY, name);
      this.isLongRaw = isLongRaw;
   }

   /**
    * Create a new instance based on specified meta data.
    * @param meta attribute meta data
    * @param isLongRaw true for storage type {@link ifs.fnd.record.FndSqlType#LONGRAW},
    *                  false for {@link ifs.fnd.record.FndSqlType#BINARY}
    */
   public FndBinary(FndAttributeMeta meta, boolean isLongRaw) {
      super(meta);
      setType(FndAttributeType.BINARY);
      this.isLongRaw = isLongRaw;
   }

   /**
    * Creates a new attribute based on meta-data.
    * @param meta Meta-data to base the attribute on
    * @return The created attribute
    */
   @Override
   protected FndAttribute newAttribute(FndAttributeMeta meta) {
      return new FndBinary(meta, isLongRaw);
   }

   /**
    * Assigns an input stream manager to this attribute.
    * The input stream will act as source of data to be saved in the database.
    * The value of this attribute will be ignored during save operation.
    * @param inputMgr an instance of FndInputStreamManager cooperating with this attribute
    */
   public void setInputStreamManager(FndInputStreamManager inputMgr) {
      this.inputMgr = inputMgr;
      setNull(); // make the attribute dirty
   }

   /**
    * Assigns an output stream manager to this attribute.
    * The output stream will act as destination for data to be fetched from the database.
    * The value of this attribute will be ignored during query and get operations.
    * @param outputMgr an instance of FndOutputStreamManager cooperating with this attribute
    */
   public void setOutputStreamManager(FndOutputStreamManager outputMgr) {
      this.outputMgr = outputMgr;
   }

   /**
    * Gets an input stream manager for this attribute.
    * @return an instance of FndInputStreamManager or null if no input stream manager has been assigned to this attribute
    */
   public FndInputStreamManager getInputStreamManager() {
      return inputMgr;
   }

   /**
    * Gets an output stream manager for this attribute.
    * @return an instance of FndOutputStreamManager or null if no output stream manager has been assigned to this attribute
    */
   public FndOutputStreamManager getOutputStreamManager() {
      return outputMgr;
   }

   /** Sets the value of the attribute to a byte array.
    * @param data Value
    * @throws ApplicationException Out of range
    */
   public void setValue(byte[] data) throws ApplicationException {
      internalSetValue(data);
   }

   /** Copies the value from another FndBinary
    * @param attribute Attribute to copy value from
    * @throws ApplicationException Value out of range
    */
   public void setValue(FndBinary attribute) throws ApplicationException {
      setValue(attribute.getValue());
   }

   /** Sets the value of the attribute from another attribute.
    *  The main difference between assign and setValue is that the value will be cloned
    *  in assign (if necessary). The attribute value will never be cloned in setValue.
    * @param from Attribute to copy value from
    * @throws SystemException Thrown if cloning fails
    * @throws ApplicationException Thrown if value if out of range for this attribute
    */
   public void assign(FndBinary from) throws ApplicationException, SystemException {
      super.assign(from);
   }

   /**
    * Checks if attribute is null.
    * @return true if null
    */
   @Override
   public boolean isNull() {
      return (super.isNull() || ((byte[]) internalGetValue()).length < 1);
   }

   /** Returns string representation of the byte array
    * @return Value
    */
   @Override
   public String toString() {
      if (value != null)
         return getDataAsString(defaultEncoding);
      else
         return null;
   }

   /** Parses in the value from a string
    * @param value String from which to get the value
    */
   @Override
   public void parseString(String value) throws ParseException {
      if (value == null || value.length() == 0) {
         this.value = null;
         setExistent();
         set();
      }
      else
         setData(value, defaultEncoding);
   }

   /**
    * Sets the value of this attribute.
    * The method is called during parsing of a serialized stream.
    * @param value the binary value to set
    */
   void setBinaryValue(byte[] value) throws ParseException {
      this.value = value;
      setExistent();
      set();
   }

   /** Returns value of the attribute
    * @return Value
    */
   public byte[] getValue() {
      return (byte[]) internalGetValue();
   }

   /** Return the value of the attribute. If value is null, return defaultValue.
    *  @param defaultValue Value to return if attribute value is null.
    *  @return Attribute value.
    */
   public byte[] getValue(byte[] defaultValue) {
      return isNull() ? defaultValue : getValue();
   }

   /**
    * Returns the data as a String.
    * @param encoding one of the ENC_* constants of this class indicating how
    * the data should be encoded.
    * @return Value as String
    */
   protected String getDataAsString(int encoding) {
      try {
         if (encoding == FndSerializeConstants.ENCODING_B64)
            return FndUtil.toBase64Text(getValue());
         else
            return FndUtil.toHexText(getValue());
      }
      catch (IOException e) {
         return "";
      }
   }
   /**
    * Returns the size of the data in bytes. Overrides the parent method.
    * @return Length
    */
   @Override
   public int getLength() {
      if (!isNull())
         return getValue().length;
      else
         return -1;
   }
   /**
    * Returns the size of the data in bytes.
    * @return Size
    */
   public int getSize() {
      return getLength();
   }

   /**
    * Sets the data to be held.
    * @param encData a String with the encoded data.
    * @param encoding  any of the ENC_* constants of this class indicating how the
    * data is encoded.
    */
   private void setData(String encData, int encoding) throws ParseException {
      try {
         if (encoding == FndSerializeConstants.ENCODING_B64) {
            this.value = FndUtil.fromBase64Text(encData);
         }
         else {
            this.value = FndUtil.fromHexText(encData);
         }
         setExistent();
         set();
      }
      catch (IOException ex) {
         throw new ParseException(
            ex,
            "IOEX: Error while parsing data '&1' for attribute &2.&3",
            ex.getMessage(),
            getParentRecord().getName(),
            getName());
      }
   }

   /**
    * Formats this attribute's value to the xml stream.
    * @param s XML serializer to which to add the value
    */
   @Override
   protected void formatValueToXml(FndXmlSerializer s)  throws ParseException{
      if (defaultEncoding == FndSerializeConstants.ENCODING_B64)
         s.appendBase64(getValue());
      else
         s.append(FndUtil.toHexText(getValue()));
      if(s.destroyFormattedRecords())
         value = null;
   }

   @Override
   protected FndSqlValue toFndSqlValue() {
      if (isNull()) {
         FndSqlValue val = new FndSqlValue(getName(), getSqlType());
         val.setNull();
         return val;
      }
      else
         return new FndSqlValue(getName(), getValue(), isLongRaw);
   }

   /**
    * Get value from database and set on this attribute.
    * If an output stream manager is assigned to this attribute then the binary data
    * will be spooled to the stream instead of setting the attribute value.
    * @param data an instance of FndSqlData interface to get value from
    * @param colNr column/parameter index number
    */
   @Override
   protected void setSqlValue(FndSqlData data, int colNr) throws IfsException {
      if(outputMgr != null) {
         if(outputMgr.getOutputStream() == null)
            throw new SystemException("OUTMGRNULL: Null stream returned from FndOutputStreamManager for attribute &1",
                                      getMeta().getFullName());
         if(!data.getBinary(colNr, outputMgr))
            setNull();
         return;
      }

      byte[] val;

      if (isLongRaw)
         val = data.getLongRaw(colNr);
      else
         val = data.getBinary(colNr);

      if (val != null)
         setValue(val);
      else
         setNull();
   }

   /** Get SQL type.
    */
   @Override
   protected FndSqlType getSqlType() {
      if (isLongRaw)
         return FndSqlType.LONGRAW;
      else
         return FndSqlType.BINARY;
   }

   /** FndBinary has long storage (BLOB).
    *  @return true if isLongRaw = false
    */
   @Override
   public boolean isLong() {
      return !isLongRaw;
   }

   /**
    * Create a "attribute is null" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @return Created condition.
    */
   public FndSimpleCondition createIsNullCondition() {
      return new FndSimpleCondition(this, FndQueryOperator.EQUAL, null);
   }

   /**
    * Create a "attribute is not null" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @return Created condition.
    */
   public FndSimpleCondition createIsNotNullCondition() {
      return new FndSimpleCondition(this, FndQueryOperator.NOT_EQUAL, null);
   }

   /**
    * Copy the attribute flags and value.
    * Depending on the implementation of cloneValue() the attribute value may
    * be copied or cloned.
    * @param target Attribute to copy to.
    * @param cloning true - clone value if necessary, false - never clone value
    */
   @Override
   protected void copy(FndAttribute target, boolean cloning) throws CloneNotSupportedException, SystemException {
      super.copy(target, cloning);
      FndBinary attr = (FndBinary) target;
      attr.inputMgr  = inputMgr;
      attr.outputMgr = outputMgr;
   }

   /**
    * Copies the attribute.
    * The internal value of the attribute will be cloned if necessary.
    * @return A copy of the attribute.
    * @throws CloneNotSupportedException if cloning is not supported
    */
   @Override
   public Object clone() throws CloneNotSupportedException {
      FndBinary attr = (FndBinary) super.clone();
      attr.inputMgr  = inputMgr;
      attr.outputMgr = outputMgr;
      return attr;
   }

   /**
    * Clone the attribute value.
    * @return A copy of the value of the attribute
    */
   @Override
   protected Object cloneValue() throws CloneNotSupportedException {
      Object val = internalGetValue();
      return val == null ? null : ((byte[]) val).clone();
   }

   /**
    * Get the result from a "select count(attr) from ..." query.
    * <P>See class {@link ifs.fnd.record.FndQueryResultCategory FndQueryResultCategory} for example of usage.
    * @return The returned integer value from the storage operation.
    */
   @Override
   public int getCount() {
      return super.getCount();
   }

   /**
    * Returns an FndBinary attribute holding the value represented by the specified String.
    * @param value the string to be parsed
    * @return a new instance of FndAlpha with the specified value
    * @throws ParseException if the specified String has invalid format
    */
   public static FndBinary valueOf(String value) throws ParseException {
      FndBinary attr = new FndBinary();
      attr.parseString(value);
      return attr;
   }

   /** Format the value of the attribute to an Fnd Buffer
    * @param stream Stream to which to write the buffer
    * @param releaseMemory true if after formatting the attribute value should be cleared releasing the memory, false otherwise
    * @throws ParseException Attribute's value cannot be serialized into a buffer
    */
   @Override
   protected void formatValue(FndTokenWriter stream, boolean releaseMemory) throws ParseException {
      if(meta.isEncrypted()) {
         super.formatValue(stream, releaseMemory);
      }
      else {
         stream.write(FndSerializeConstants.VALUE_MARKER);
         stream.writeBinaryToken(getValue());
         if(releaseMemory)
            value = null;
      }
   }
}
