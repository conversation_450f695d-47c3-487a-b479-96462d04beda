/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */


package ifs.fnd.record.serialization;

import ifs.fnd.base.ParseException;
import ifs.fnd.buffer.BufferUtil;
import java.io.IOException;
import java.io.UnsupportedEncodingException;

/**
 * <B>Framework internal class:</B> Extension of FndTokenReader that reads serialized stream from an array of bytes.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public final class FndByteArrayTokenReader extends FndTokenReader
{
   // the buffer with complete serialized stream
   private byte[] buf;

   // current (next to read) position in the buffer
   private int pos;

   // the length of the buffer
   private int len;

   /**
    * Construct a token reader that will read bytes from the specified byte array.
    * @param buf a buffer with complete serialized stream
    */
   public FndByteArrayTokenReader(byte[] buf) {
      //System.out.println("FndByteArrayTokenReader[" + buf.length+"]");
      this.buf = buf;
      len = buf.length;
      pos = 0;
   }

   /**
    * Force a delimiter from the input stream.
    * @return the consumed delimiter character
    * @throws ParseException if no delimiter has been found at the current position in the input stream
    */
   @Override
   public char getDelimiter() throws ParseException {
      if(pos >= len)
         throw new ParseException("EOSDELIM: Expecting delimiter but found end-of-stream");

      byte ch = buf[pos++];

      if(isDelimiter(ch))
         return (char)ch;
      else
         throw new ParseException("NODELIM: Expecting delimiter but found character code " + ((int)ch));
   }

   /**
    * Force next token from the input stream.
    * @return the next String token from the stream
    * @throws ParseException if no delimiter has been found or the consumed bytes could not have been converted to a String
    */
   @Override
   public String getToken() throws ParseException {
      for(int i = pos; i < len; i++) {
         if(isDelimiter(buf[i])) {
            try {
               String token = new String(buf, pos, i - pos, FndSerializeConstants.BUFFER_CHARSET);
               pos = i;
               return token;
            }
            catch (UnsupportedEncodingException e) {
               throw new ParseException(e, e.toString());
            }
         }
      }
      throw new ParseException("EOSTOKEN: Expecting token but found end-of-stream");
   }

   /**
    * Force next binary token from the input stream.
    * The token may be BASE64 encoded or not (if BINARY_INDICATOR is the first character of the token).
    * A serialized non-encoded binary token consists of the data length (4 bytes, high byte first) followed by the data bytes.
    * @return binary token data consumed from the input stream
    * @throws ParseException if the input stream does not contain a properly serialized binary token
    */
   @Override
   public byte[] getBinaryToken() throws ParseException {
      if (pos >= len)
         throw new ParseException("EOSBINTOKEN: Expecting binary token but found end-of-stream");

      if (buf[pos] == FndSerializeConstants.BINARY_INDICATOR) {
         pos++; // byte for binary indicator

         int tokenLen = getBinaryLengthPrefix();
         if (len - pos < tokenLen)
            throw new ParseException("EOSBINDATA: Expecting length prefixed token data but found end-of-stream");

         byte[] tokenBytes = new byte[tokenLen];
         System.arraycopy(buf, pos, tokenBytes, 0, tokenLen);
         pos += tokenLen;
         return tokenBytes;
      } else {
         try {
            String encData = getToken();
            return FndUtil.fromBase64Text(encData);
         }
         catch (IOException ex) {
            throw new ParseException(ex, "BASE64ERROR: Error while decoding BASE64 data: &1", ex.getMessage());
         }
      }
   }

   /**
    * Force next length prefixed string token form the input stream.
    * @return Length prefixed string token read from the input stream.
    * @throws ParseException if the input stream does not contain a properly 
    *         serialized length prefixed text token.
    */
   @Override
   public String getLPStringToken() throws ParseException {
      if (pos >= len)
         throw new ParseException("EOSBINTOKEN: Expecting binary token but found end-of-stream");
      
      // Since we added a new token types for length-prefixed strings, we do not
      // need to check if the client is capable of handling them. Client will
      // only send length-prefixed tokens if it is capable of handling them.
      try {
         int tokenLen = getStringLengthPrefix();

         if (len - pos < tokenLen)
            throw new ParseException("EOSLPSTRDATA: Expecting length prefixed token data but found end-of-stream");

         String token = new String(buf, pos, tokenLen,
                 FndSerializeConstants.BUFFER_CHARSET);
         pos += tokenLen;
         return token;
      } catch (UnsupportedEncodingException ex) {
         throw new ParseException(ex, ex.getMessage());
      }
   }
   
   /**
    * Converts length prefix bytes from the buffer into a length value. This
    * method deals with new variable length length prefixes.
    */
   private int getStringLengthPrefix() {
      /* not required to check if the input bytes has enough room for a length
         prefix. this check is performed while reading the prefix. */
      int prefixVal = BufferUtil.readLengthPrefix(buf, pos);
      pos += BufferUtil.getPrefixSize(prefixVal);
      return prefixVal;
   }

   /**
    * Converts length prefix bytes from the buffer into a length value. This
    * method deals with length prefixes used in length prefixed FndBinary.
    * (converts 4 consecutive bytes to an int)
    */
   private int getBinaryLengthPrefix() throws ParseException {
      if(len - pos < 4) {
         throw new ParseException("EOSLPLEN: Expecting length prefixed token length but found end-of-stream");
      }
      int retVal = toInt(buf, pos);
      pos += 4;
      return retVal;
   }

   /**
    * Match the specified byte against delimiters defined in FndSerializeConstants.
    */
   @SuppressWarnings("PMD.MissingBreakInSwitch")
   private boolean isDelimiter(byte ch) {
      switch(ch) {
         case FndSerializeConstants.BEGIN_BUFFER_MARKER :
         case FndSerializeConstants.END_BUFFER_MARKER :
         case FndSerializeConstants.HEAD_MARKER :
         case FndSerializeConstants.NAME_MARKER :
         case FndSerializeConstants.TYPE_MARKER :
         case FndSerializeConstants.STATUS_MARKER :
         case FndSerializeConstants.VALUE_MARKER :
         case FndSerializeConstants.NULL_VALUE_MARKER :
         case FndSerializeConstants.NO_VALUE_MARKER :
         case FndSerializeConstants.INVALID_VALUE_MARKER :
         case FndSerializeConstants.ACTION_MARKER :
         case FndSerializeConstants.IDENTITY_MARKER :
         case FndSerializeConstants.COUNT_MARKER :
         case FndSerializeConstants.CHANGED_VALUE_MARKER :
         case FndSerializeConstants.UNCHANGED_VALUE_MARKER :
            return true;
      }
      return false;
   }

   /**
    * Convert four bytes (high byte first) to an int.
    */
   private static int toInt(byte[] b, int index) {
      return ((b[index + 0] & 0xFF) << 24) +
             ((b[index + 1] & 0xFF) << 16) +
             ((b[index + 2] & 0xFF) <<  8) +
             ((b[index + 3] & 0xFF));
   }

//   private static String dump(byte[] data) {
//      return dump(data, 0, data.length, true);
//   }
//
//   private static String dump(byte[] data, int start, int end, boolean truncate) {
//      FndAutoString buf = new FndAutoString();
//      buf.append('{');
//      for(int i = start; i < end; i++) {
//         if(i > start)
//            buf.append(',');
//         buf.append(Integer.toHexString(data[i]));
//         if(truncate && buf.length() > 200) {
//            buf.append("...");
//            break;
//         }
//      }
//      buf.append('}');
//      return buf.toString();
//   }
}
