/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.record;

import java.lang.reflect.*;
import java.util.*;
import ifs.fnd.base.*;
import ifs.fnd.util.Str;

/**
 * Utility class with methods for extraction of Meta data.
 * 
 * <AUTHOR>
 * @deprecated 
 */
public class FndMetaUtil {
   
   private FndMetaUtil() {
      // avoid instantiation
   }

   /**
    * Returns a Class instance representing an entity given by its name.
    * @param entityName name of the entity
    * @return Class instance for the entity
    * @throws IfsException 
    */
   private static Class getEntityClass(String entityName) throws IfsException {
      
      entityName = "ifs.entity."+entityName.toLowerCase()+"."+entityName+"Entity";
      
      try {
         Class<?> cls = Class.forName(entityName);
         Class<?> fndLUEntityViewClass = FndLUEntityView.class;
         
         if( !fndLUEntityViewClass.isAssignableFrom(cls) )
            throw new SystemException("Class '&1' is not an instance of FndLUEntityViewClass", cls.getName());
         
         int modifiers = cls.getModifiers();
         if( !Modifier.isAbstract(modifiers) )
            throw new SystemException("Class '&1' is not an abstract class", cls.getName());
         if( Modifier.isInterface(modifiers) )
            throw new SystemException("Class '&1' is an interface", cls.getName());
         if( !Modifier.isPublic(modifiers) )
            throw new SystemException("Class '&1' is not public", cls.getName());
         
         return cls;
         
      } catch(ClassNotFoundException ex) {
         throw new SystemException(ex, "Class '&1' not found", entityName);
      }
   }
   
   /**
    * Returns record Meta for an entity given by its name.
    * @param entityName name of the entity
    * @return instance of record Meta
    * @throws IfsException 
    * @deprecated 
    */
   public static FndRecordMeta getEntityRecordMeta(String entityName) throws IfsException {
      try {
         Class<?> cls = getEntityClass(entityName);
         
         Field[] fields = cls.getDeclaredFields();
         for(Field field:fields) {
            int modifiers = field.getModifiers();
            if( !Modifier.isPublic(modifiers) )        continue;
            if( !Modifier.isStatic(modifiers) )        continue;
            if( !Modifier.isFinal (modifiers) )        continue;
            if( FndRecordMeta.class!=field.getType() ) continue;
            if( !"viewMeta".equals(field.getName()) )  continue;
            
            return (FndRecordMeta)field.get(null);
         }
         
         throw new SystemException("Field 'viewMeta' not found in class '&1'", cls.getName());
         
      } catch (IllegalAccessException ex) {
         throw new SystemException(ex, "Cannot fetch field 'vieMeta'");
      }
   }
   
   /**
    * Returns name of database base table or view for an entity given by its name.
    * @param entityName name of the entity
    * @return name of the database table or view the entity is based on.
    * @throws IfsException 
    * @deprecated 
    */
   public static String getEntityDbViewName(String entityName) throws IfsException {
      return getEntityRecordMeta(entityName).getTable();
   }
   
   /**
    * Returns an array of attribute Meta instances for all simple attributes
    * contained within an entity given by its name.
    * @param entityName name of the entity
    * @return array of attribute meta instances
    * @throws IfsException 
    * @deprecated 
    */
   public static FndAttributeMeta[] getEntityAttributesMeta(String entityName) throws IfsException {
      
      Class  cls     = getEntityClass(entityName);
      String clsName = cls.getName()+"$Meta";
      String fldName = null;
      try {
         
         cls = Class.forName(clsName);
         
         int modifiers = cls.getModifiers();
         if( Modifier.isAbstract(modifiers) )
            throw new SystemException("Class '&1' is not an abstract class", cls.getName());
         if( Modifier.isInterface(modifiers) )
            throw new SystemException("Class '&1' is an interface", cls.getName());
         if( !Modifier.isPublic(modifiers) )
            throw new SystemException("Class '&1' is not public", cls.getName());
         if( !Modifier.isStatic(modifiers) )
            throw new SystemException("Class '&1' is not static", cls.getName());
         
         List<FndAttributeMeta> attribs = new ArrayList<>();
         Field[] fields = cls.getDeclaredFields();
         for(Field field:fields) {
            modifiers = field.getModifiers();
            if( !Modifier.isPublic(modifiers) )           continue;
            if( !Modifier.isStatic(modifiers) )           continue;
            if( !Modifier.isFinal (modifiers) )           continue;
            if( FndAttributeMeta.class!=field.getType() ) continue;
            
            fldName = field.getName();
            attribs.add( (FndAttributeMeta)field.get(null) );
         }
         return attribs.toArray(new FndAttributeMeta[0]);
      
      } catch (ClassNotFoundException ex) {
         throw new SystemException(ex, "Class '&1' not found", clsName);
      } catch (IllegalAccessException ex) {
         throw new SystemException(ex, "Cannot fetch field '&1' from class '&2'", fldName, clsName);
      }
   }
   
   /**
    * Returns an array of database column names in the base table or database view
    * for an entity given by its name.
    * @param entityName the name of the entity
    * @return array of database column names
    * @throws IfsException 
    * @deprecated 
    */
   public static String[] getEntityDbAttributes(String entityName) throws IfsException {
      FndAttributeMeta[] attribs = getEntityAttributesMeta(entityName);
      List<String> dbAttrs = new ArrayList<>(attribs.length);
      for(FndAttributeMeta meta : attribs) {
         String colName = meta.getColumn();
         if( !Str.isEmpty(colName) )
            dbAttrs.add(colName);
      }
      return dbAttrs.toArray(new String[0]);
   }
}
