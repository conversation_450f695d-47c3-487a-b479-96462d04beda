/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;

import ifs.fnd.base.SystemException;

/**
 * Implementation of FndAbstractAggregate for use with generic records.
 */
public class FndAggregate extends FndAbstractAggregate {
   /**
    * Create a new instance
    */
   public FndAggregate() {
      super();
   }

   /**
    * Create a new instance with a specified name
    * @param name name of the aggregate
    */
   public FndAggregate(String name) {
      super(name);
   }

   /**
    * Create a new instance with a specified meta attribute
    * @param meta attribute meta
    */
   public FndAggregate(FndAttributeMeta meta) {
      super(meta);
   }

   /**
    * Create a new instance with a specified meta attribute
    * @param meta attribute meta
    */
   @Override
   protected FndAttribute newAttribute(FndAttributeMeta meta) {
      return new FndAggregate(meta);
   }

   /** 
    * Returns the record contained within the aggregate.
    * @return The aggregated record
    */
   public FndAbstractRecord getRecord() {
      return internalGetRecord();
   }

   /** 
    * Sets the aggregated record.
    * @param rec Record to aggregate
    */
   public void setRecord(FndAbstractRecord rec) {
      internalSetRecord(rec);
   }

   /**
    * Copy the value of another aggregate to this attribute.
    * @param from Aggregate to copy record from.
    * @throws SystemException
    */
   public final void assign(FndAggregate from) throws SystemException {
      try {
         if (from.exist() && !from.isNull())
            internalSetRecord((FndAbstractRecord)from.getRecord().clone());
      }
      catch (CloneNotSupportedException e) {
         throw new SystemException(e, "AGGASSIGNCLONE:Could not assign &1 to &2.", from.getName(), getName());
      }
   }
   
   /* 
    * Since this is a non-typed aggregate, there is no record associated with this aggregate.
    * @return FndRecord if the aggregate is empty and a new record instance of the same type as
    * the present record (if available).
    * @see ifs.fnd.record.FndAbstractAggregate#getTemplateRecord()
    */
   @Override
   protected FndAbstractRecord getTemplateRecord() {
      //don't use internalGetRecord since that will cause recursion...
      FndAbstractRecord r = (FndAbstractRecord) internalGetValue();
      if (r != null) {
         return r.newInstance();
      }
      return new FndRecord();
   }
}
