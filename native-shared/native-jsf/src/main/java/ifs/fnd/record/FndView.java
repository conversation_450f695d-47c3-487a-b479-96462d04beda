/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;

import ifs.fnd.base.SystemException;
import ifs.fnd.internal.FndAttributeInternals;

/**
 * A view class. While record is a general datatype, a view corresponds to a view in the model.
 */
public class FndView extends FndAbstractRecord {

   /**
    * Framework internal constructor required by Externalizable interface.
    */
   public FndView() {
      assert(false);
   }

   public FndView(String name) {
      super(new FndRecordMeta(name));
   }

   public FndView(FndRecordMeta meta) {
      super(meta);
   }

   @Override
   public FndAbstractRecord newInstance() {
      return new FndView(meta);
   }

   @Override
   public FndAbstractArray newArrayInstance() {
      return new FndArray();
   }

   /**
    * Transform this view to some other view. Both views should have a common ancestor.
    * The view will be invalid after this call.
    * @param to view to transform to
    */
   protected void transformView(final FndView to) throws SystemException {
      if (to == null)
         return;

      try {
         //Copy attributes
         int attrCount = getAttributeCount();
         for(int i=0; i<attrCount; i++) {
            FndAttribute fromAttr = getAttribute(i);
            //Find an attribute with the same name
            FndAttribute toAttr = to.getAttribute(fromAttr);
            if (toAttr != null) { //Same attribute
               fromAttr.copy(toAttr, false); //No cloning
            }
            else {
               //Attribute not found, add it
               FndAttribute attr = (FndAttribute)fromAttr.clone();
               to.add(attr);
               if (attr instanceof FndCompoundAttribute)
                  ((FndCompoundAttribute)attr).reconnect(to);
            }
         }

         //Copy flags
         to.setFlags(this);
         to.setState(getState()); // copies state and propagates it recursively (is it necessary?)
         to.setIdentity(getIdentity());
      }
      catch (CloneNotSupportedException e) {
         throw new SystemException(e, "TRANSFORMVIEW:Failed transforming view &1 to &2", this.getName(), to.getName());
      }
   }

   /**
    * Transform this view to a dynamic FndRecord.
    * The view will be invalid after this call.
    * @return The newly created FndRecord
    */
   public FndRecord transformToRecord() throws SystemException {

      FndRecord to = new FndRecord(getName());
      //Copy attributes
      int attrCount = getAttributeCount();
      for(int i=0; i<attrCount; i++) {
         FndAttribute attr = getAttribute(i);
         //add the attribute to the record
         if (attr instanceof FndAbstractArray) {
            attr = ((FndAbstractArray)attr).transformToRecordArray();
         } else if (attr instanceof FndAbstractAggregate) {
            FndAbstractRecord record = FndAttributeInternals.internalGetRecord((FndAbstractAggregate)attr);
            attr = new FndAggregate(attr.getName());
            if(record instanceof FndView)
               record = ((FndView)record).transformToRecord();
            FndAttributeInternals.internalSetRecord((FndAbstractAggregate)attr, record);
         }
         to.add(attr);
      }

      //Copy flags
      to.setFlags(this);
      to.setState(getState()); // copies state and propagates it recursively (is it necessary?)
      to.setIdentity(getIdentity());
      return to;
   }
}
