/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record.serialization;

import ifs.fnd.base.*;

/**
 * <B>Framework internal class:</B> Parser for arrays of simple java types stored by FndSimpleArray.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class FndSimpleArrayParser {

   /**
    * <B>Framework internal class:</B> An interface used by the parser during format operation.
    *
    * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
    */
   public interface SourceArray {

      /**
       * Returns the size of the array.
       * @return the number of elements in the array.
       */
      int getSize();

      /**
       * Returns the type of array elements.
       * @return a String representing the type of array elements.
       */
      String getElementType();

      /**
       * Formats an element in the array.
       * @param index 0-based array index
       * @return the string representation of the array element at specified index.
       */
      String formatValue(int index);
   }

   /**
    * <B>Framework internal class:</B> An interface used by the parser during parse operation.
    *
    * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
    */
   public interface DestArray {

      /**
       * Sets the number of elements in the array.
       */
      void setSize(int size);

      /**
       * Sets the type of array elements.
       * @param type the String representing the type of array elements.
       */
      void setElementType(String type) throws ParseException;

      /**
       * Parses and sets the value of the array element at specified index.
       * @param index 0-based index of the array element
       * @param value String representing the value of the array element
       */
      void parseValue(int index, String value) throws ParseException;
   }

   /**
    * Formats an array of simple java types.
    * @param srcArray an implementation of interface SourceArray encapsulating an array of simple types
    * @return byte array representing the serialized form of the specified array
    */
   public byte[] format(SourceArray srcArray) throws ParseException {
      int size = srcArray.getSize();

      FndTokenWriter writer = new FndTokenWriter();
      writer.write(FndSerializeConstants.BEGIN_BUFFER_MARKER);

      writer.write(FndSerializeConstants.NAME_MARKER);
      writer.write("TYPE");
      writer.write(FndSerializeConstants.VALUE_MARKER);
      writer.write(srcArray.getElementType());

      writer.write(FndSerializeConstants.NAME_MARKER);
      writer.write("COUNT");
      writer.write(FndSerializeConstants.VALUE_MARKER);
      writer.write(String.valueOf(size));

      writer.write(FndSerializeConstants.NAME_MARKER);
      writer.write("VALUES");
      writer.write(FndSerializeConstants.BEGIN_BUFFER_MARKER);

      for(int i=0; i<size; i++) {
         String value = srcArray.formatValue(i);
         if(value == null) {
            writer.write(FndSerializeConstants.NULL_VALUE_MARKER);
         }
         else {
            writer.write(FndSerializeConstants.VALUE_MARKER);
            writer.write(value);
         }
      }

      writer.write(FndSerializeConstants.END_BUFFER_MARKER);

      writer.write(FndSerializeConstants.END_BUFFER_MARKER);

      return writer.getByteBuffer();
   }


   /**
    * Parses a byte array into an array of simple java types.
    * @param data byte array representing the serialized form of an array of simple java types
    * @param destArray an implementation of interface DestArray encapsulating an array of simple types
    */
   public void parse(byte[] data, DestArray destArray) throws ParseException {
      if(data == null)
         return;

      FndTokenReader reader = new FndByteArrayTokenReader(data);

      reader.matchDelimiter(FndSerializeConstants.BEGIN_BUFFER_MARKER);

      char ch = reader.getDelimiter();

      while(ch != FndSerializeConstants.END_BUFFER_MARKER) {

         if(ch != FndSerializeConstants.NAME_MARKER)
            throw new ParseException("Error in FndSimpleArrayParser.parse(): Expecting NAME_MARKER but found character code " + (int) ch);

         String name = reader.getToken();

         if("TYPE".equals(name)) {
            reader.matchDelimiter(FndSerializeConstants.VALUE_MARKER);
            destArray.setElementType(reader.getToken());
         }

         if("COUNT".equals(name)) {
            reader.matchDelimiter(FndSerializeConstants.VALUE_MARKER);
            destArray.setSize(Integer.parseInt(reader.getToken()));
         }

         if("VALUES".equals(name)) {
            reader.matchDelimiter(FndSerializeConstants.BEGIN_BUFFER_MARKER);
            int index = 0;
            ch = reader.getDelimiter();
            while(ch != FndSerializeConstants.END_BUFFER_MARKER) {

               if(ch == FndSerializeConstants.VALUE_MARKER)
                  destArray.parseValue(index++, reader.getToken());
               else if(ch == FndSerializeConstants.NULL_VALUE_MARKER)
                  destArray.parseValue(index++, null);
               else
                  throw new ParseException("Error in FndSimpleArrayParser.parse(): Expecting END_BUFFER_MARKER, VALUE_MARKER or NULL_VALUE_MARKER but found character code " + (int) ch);

               ch = reader.getDelimiter();
            }
         }

         ch = reader.getDelimiter();
      }
   }


   /**
    * Formats an array of simple java types into an XML stream.
    * @param xml XML serializer to which to format the contents of this array
    * @param srcArray an implementation of interface SourceArray encapsulating an array of simple types
    * @param name the name of the formatted FndSimpleArray attribute
    * @param isDirty flag indicating if the formatted attribute is dirty or not
    */
   public void formatXml(FndXmlSerializer xml, SourceArray srcArray, String name, boolean isDirty) throws ParseException {

      FndXmlElementAttributeList nilAttr = null;
      FndXmlElementAttributeList attributes = new FndXmlElementAttributeList();
      if(isDirty && xml.isFlagSet(FndXmlSerializer.FLAG_DIRTY_INFO))
         attributes.add("ifsrecord:dirty", "true");

      int size = srcArray.getSize();

      attributes.add("elementType", srcArray.getElementType());
      attributes.add("elementCount", String.valueOf(size));

      xml.startElement(name, attributes);
      xml.newLine();

      for(int i=0; i<size; i++) {
         String value = srcArray.formatValue(i);
         if(value == null) {
            if(nilAttr == null) {
               nilAttr = new FndXmlElementAttributeList();
               nilAttr.add("xsi:nil", "1");
            }
            xml.addElement("VALUE", nilAttr);
            xml.newLine();
         }
         else {
            xml.startElement("VALUE");
            xml.append(xml.encode(value));
            xml.endElement("VALUE");
            xml.newLine();
         }
      }

      xml.endElement(name);
      xml.newLine();
   }

}