/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.record;

import ifs.fnd.base.SystemException;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Time;
import java.sql.Timestamp;

/**
 * Class that can contain one typed simple value used in communication with SQL database.
 */
public final class FndSqlValue {

   private final FndSqlType type;
   private final String name;
   private int direction = 0;
   private Object value;
   public static final int DIRECTION_IN = 0;
   public static final int DIRECTION_OUT = 1;
   public static final int DIRECTION_INOUT = 2;

   public FndSqlValue(String name, FndSqlType type) {
      this.name = name;
      this.type = type;
   }

   /**
    * Used for TEXT or LONG types (LONG only used by LU wrappers)
    */
   public FndSqlValue(String name, String value, boolean isLong) {
      this.name = name;
      if (isLong) {
         this.type = FndSqlType.LONG;
      }
      else {
         this.type = FndSqlType.TEXT;
      }
      this.value = value;
   }

   public FndSqlValue(String name, String value, boolean isText, boolean isLong) {
      this.name = name;
      if (isText && isLong) {
         this.type = FndSqlType.LONG_TEXT;
      }
      else if (isText) {
         this.type = FndSqlType.TEXT;
      }
      else {
         this.type = FndSqlType.STRING;
      }
      this.value = value;
   }

   public FndSqlValue(String name, String value) {
      this(name, value, true, false);
   }

   public FndSqlValue(String name, boolean value) {
      this.name = name;
      this.type = FndSqlType.BOOLEAN;
      this.value = value;
   }

   public FndSqlValue(String name, Date value) {
      this.name = name;
      this.type = FndSqlType.DATE;
      this.value = value;
   }

   public FndSqlValue(String name, Time value) {
      this.name = name;
      this.type = FndSqlType.TIME;
      this.value = value;
   }

   public FndSqlValue(String name, Timestamp value) {
      this.name = name;
      this.type = FndSqlType.TIMESTAMP;
      this.value = value;
   }

   public FndSqlValue(String name, BigDecimal value) {
      this.name = name;
      this.type = FndSqlType.DECIMAL;
      this.value = value;
   }

   public FndSqlValue(String name, long value) {
      this.name = name;
      this.type = FndSqlType.INTEGER;
      this.value = value;
   }

   public FndSqlValue(String name, double value) {
      this.name = name;
      this.type = FndSqlType.NUMBER;
      this.value = value;
   }

   public FndSqlValue(String name, byte[] value) {
      this.name = name;
      this.type = FndSqlType.BINARY;
      this.value = value;
   }

   public FndSqlValue(String name, byte[] value, boolean isLongRaw) {
      if (isLongRaw) {
         this.type = FndSqlType.LONGRAW;
      } else {
         this.type = FndSqlType.BINARY;
      }

      this.name = name;
      this.value = value;
   }

   public boolean isNull() {
      return this.value == null;
   }

   public void setNull() {
      this.value = null;
   }

   public FndSqlType getType() {
      return type;
   }

   public String getName() {
      return name;
   }

   public String getStringValue() {
      return (String) value;
   }

   public long getLongValue() {
      return (Long) value;
   }

   public Date getDateValue() {
      return (Date) value;
   }

   public Time getTimeValue() {
      return (Time) value;
   }

   public Timestamp getTimestampValue() {
      return (Timestamp) value;
   }

   public BigDecimal getDecimalValue() {
      return (BigDecimal) value;
   }

   public double getDoubleValue() {
      return (Double) value;
   }

   public byte[] getBinaryValue() {
      return (byte[]) value;
   }

   public boolean getBooleanValue() {
      return (Boolean) value;
   }

   public Object getValue() {
      return value;
   }

   public static FndSqlValue objectToFndSqlValue(Object value, String name, FndSqlType type) throws SystemException {
      if (type == FndSqlType.BOOLEAN) {
         return new FndSqlValue(name, (Boolean) value);
      }
      else if (type == FndSqlType.DATE) {
         return new FndSqlValue(name, (Date) value);
      }
      else if (type == FndSqlType.DECIMAL) {
         return new FndSqlValue(name, (BigDecimal) value);
      }
      else if (type == FndSqlType.INTEGER) {
         return new FndSqlValue(name, (Long) value);
      }
      else if (type == FndSqlType.LONG_TEXT) {
         return new FndSqlValue(name, (String) value, true, true);
      }
      else if (type == FndSqlType.LONG) {
         return new FndSqlValue(name, (String) value, true);
      }
      else if (type == FndSqlType.NUMBER) {
         return new FndSqlValue(name, (Double) value);
      }
      else if (type == FndSqlType.STRING) {
         return new FndSqlValue(name, (String) value, false, false);
      }
      else if (type == FndSqlType.TEXT) {
         return new FndSqlValue(name, (String) value, true, false);
      }
      else if (type == FndSqlType.TIME) {
         return new FndSqlValue(name, (Time) value);
      }
      else if (type == FndSqlType.TIMESTAMP) {
         return new FndSqlValue(name, (Timestamp) value);
      }
      else {
         throw new SystemException("OBJTOSQL:Unable to convert object to FndSqlValue (type = &1", type.getName());
      }
   }

   public void setDirection(int direction) throws SystemException {
      if (direction != DIRECTION_IN && direction != DIRECTION_OUT && direction != DIRECTION_INOUT) {
         throw new SystemException("SQLVALDIR:Invalid parameter direction");
      }
      this.direction = direction;
   }

   public int getDirection() {
      return this.direction;
   }

   public String getDirectionName() {
      switch(direction) {
         case DIRECTION_OUT:
            return "OUT";
         case DIRECTION_INOUT:
            return "IN_OUT";
         default:
            return "IN";
      }
   }
}
