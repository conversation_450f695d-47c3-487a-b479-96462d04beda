/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;

import java.io.Serializable;
import java.io.ObjectStreamException;

/**
 * Enumeration class for query result categories for attributes.
 * Query result categories are used to include or exclude attributes in a query result.
 * The {@link ifs.fnd.record.FndAttribute#include include} method marks an
 * attribute with {@link #INCLUDE} category.
 * The {@link ifs.fnd.record.FndAttribute#exclude exclude} method marks an attribute
 * with {@link #EXCLUDE} category. Other query result categories correspond
 * to set functions {@link #MIN}, {@link #MAX}, {@link #AVG}, {@link #SUM} and {@link #COUNT}.
 * The method {@link ifs.fnd.record.FndAttribute#include(FndQueryResultCategory) include(category)}
 * is used to assign any query result category to an attribute.
 *
 * <p>
 * The following example demonstrates a query that counts persons per country. Also, the query
 * retrieves the oldest creation date per country by using MIN query result category.
 * <pre>
 *    PersonInfo person = new PersonInfo();
 *    person.excludeQueryResults();
 *    person.country.include();
 *    person.personId.include(FndQueryResultCategory.<B>COUNT</B>);
 *    person.creationDate.include(FndQueryResultCategory.<B>MIN</B>);
 *    person.partyType.setValue(PartyTypeEnumeration.PERSON);
 *
 *    PersonInfoHandler handler = PersonInfoHandlerFactory.getHandler();
 *    FndQueryRecord qry = new FndQueryRecord(person);
 *    PersonInfoArray arr = (PersonInfoArray) handler.query(qry); </pre>
 * The query will result in the following server debug output. Note that GROUP BY
 * clause is automatically generated on every included attribute that has not been
 * assigned a set function.
 * <pre>
 *    SELECT <B>COUNT</B>(A.PERSON_ID), <B>MIN</B>(A.CREATION_DATE), A.COUNTRY_DB
 *    FROM ifsapp.PERSON_INFO_PUBLIC A
 *    WHERE A.PARTY_TYPE_DB = :1
 *    <B>GROUP BY</B> A.COUNTRY_DB
 *    FndStatement: Binding parameters:
 *       1: (STRING) = PERSON </pre>
 * The result value from set functions MIN, MAX, SUM and AVG will be set on the attribute
 * just as any other value fetched from the database (use getValue() to get the result).
 * COUNT, on the other hand, is a bit different; you need to use
 * {@link ifs.fnd.record.FndQueryableAttribute#getCount getCount} to retrieve the value,
 * for example:
 * <pre>
 *    for(int i = 0; i < arr.size(); i++) {
 *       person = arr.get(i);
 *       String country = person.country.getValue();
 *       int count = person.personId.<B>getCount</B>();
 *       java.util.Date date = person.creationDate.<B>getValue</B>();
 *       System.out.println(country+": "+count+", "+date);
 *    }
 * </pre>
 *
 * @see ifs.fnd.record.FndAttribute#include(FndQueryResultCategory) FndAttribute.include(FndQueryResultCategory)
 * @see ifs.fnd.record.FndAttribute#include FndAttribute.include
 * @see ifs.fnd.record.FndCompoundReference#include FndCompoundReference.include
 * @see ifs.fnd.record.FndAttribute#exclude FndAttribute.exclude
 * @see ifs.fnd.record.FndAbstractRecord#includeQueryResults FndAbstractRecord.includeQueryResults
 * @see ifs.fnd.record.FndAbstractRecord#excludeQueryResults FndAbstractRecord.excludeQueryResults
 * @see ifs.fnd.record.FndBoolean#getCount FndBoolean.getCount
 * @see ifs.fnd.record.FndBinary#getCount FndBinary.getCount
 * @see ifs.fnd.record.FndQueryableAttribute#getCount FndQueryableAttribute.getCount
 */
public final class FndQueryResultCategory implements Serializable {

   /**
    * Sequence number for instances.
    */
   private static int nextOrdinal = 0;

   /**
    * The buffer format for the category.
    */
   private  String   bufFormat;

   /**
    * An internal number for keeping track of the instances.
    */
   private int ordinal = nextOrdinal++;

   /**
    * Constructor setting the format. Private disables instantiation.
    */
   private FndQueryResultCategory(String format) {
      bufFormat = format;
   }

   /**
    * readResolve() for correct de-serialization.
    */
   private Object readResolve() throws ObjectStreamException {
      return VALUES[ordinal];
   }


   /**
    * Returns a <code>String</code> representation of this object.
    */
   @Override
   public String toString() {
      return bufFormat;
   }

   /**
    * Returns the ordinal number identifying this result category.
    * @return an integer in range 0 .. 6
    */
   int getOrdinal() {
      return ordinal;
   }

   /**
    * Gets an instance of this class for the given value.
    * @param   s  the value to get the instance for.
    * @return  an instance of this class matching <code>s</code>, or
    * INCLUDE if it doesn't match any instance.
    */
   static FndQueryResultCategory getInstance(String s) {
      if(s == null || "".equals(s))
         return INCLUDE;

      if(s.indexOf("MAX") != -1)
         return MAX;

      if(s.indexOf('X') != -1)
         return EXCLUDE;

      if(s.indexOf("MIN") != -1)
         return MIN;

      if(s.indexOf("SUM") != -1)
         return SUM;

      if(s.indexOf("AVG") != -1)
         return AVG;

      if(s.indexOf("COUNT") != -1)
         return COUNT;

      return INCLUDE;
   }

   /**
    * Gets an instance of this class identified by given ordinal number.
    * @param   ordinal an integer in range 0 .. 6
    * @return  an instance of this class corresponding to the ordinal number
    */
   static FndQueryResultCategory getInstance(int ordinal) {
      return VALUES[ordinal];
   }

   /**
    * Category for including attribute in query result.
    */
   public static final FndQueryResultCategory INCLUDE = new FndQueryResultCategory("");

   /**
    * Category for excluding attribute from query result.
    */
   public static final FndQueryResultCategory EXCLUDE = new FndQueryResultCategory("X");

   /**
    * Category for group function MIN.
    */
   public static final FndQueryResultCategory MIN = new FndQueryResultCategory("MIN");

   /**
    * Category for group function MAX.
    */
   public static final FndQueryResultCategory MAX = new FndQueryResultCategory("MAX");

   /**
    * Category for group function SUM.
    */
   public static final FndQueryResultCategory SUM = new FndQueryResultCategory("SUM");

   /**
    * Category for group function AVG.
    */
   public static final FndQueryResultCategory AVG = new FndQueryResultCategory("AVG");

   /**
    * Category for group function COUNT.
    */
   public static final FndQueryResultCategory COUNT = new FndQueryResultCategory("COUNT");

   /**
    * An array with all instances (used by readResolve()).
    */
   private static final FndQueryResultCategory[] VALUES = {INCLUDE, EXCLUDE, MIN,
                                                     MAX, SUM, AVG, COUNT};
}
