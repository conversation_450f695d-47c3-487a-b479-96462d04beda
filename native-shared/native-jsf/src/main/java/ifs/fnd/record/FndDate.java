/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.record;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import ifs.fnd.base.*;

/** FndDate data type for attributes.
 */
public final class FndDate extends FndAbstractDate {

   /**
    * Create a new instance.
    */
   public FndDate() {
      super(FndAttributeType.DATE);
   }

   /**
    * Create a new instance, also specifying attribute name.
    * @param name attribute name
    */
   public FndDate(String name) {
      super(FndAttributeType.DATE, name);
   }

   /**
    * Create a new instance, also specifying attribute name and initial value.
    * @param name attribute name
    * @param value initial attribute value
    */
   public FndDate(String name, Date value) {
      super(FndAttributeType.DATE, name, value);
   }

   /**
    * Create a new instance based on specified meta data.
    * @param meta attribute meta data
    */
   public FndDate(FndAttributeMeta meta) {
      super(meta);
      setType(FndAttributeType.DATE);
   }

   /**
    * Create a new instance based on specified meta data. Also set initial attribute value.
    * @param meta meta data
    * @param value initial value
    */
   public FndDate(FndAttributeMeta meta, Date value) {
      super(meta, value);
      setType(FndAttributeType.DATE);
   }

   /**
    * Creates a new attribute based on meta-data.
    * @param meta Meta-data to base the attribute on
    * @return The created attribute
    */
   @Override
   protected FndAttribute newAttribute(FndAttributeMeta meta) {
      return new FndDate(meta);
   }

   /** Returns value of the attribute
    * @return Date
    */
   public Date getValue() {
      return (Date)internalGetValue();
   }

   /** Return the value of the attribute. If value is null, return defaultValue.
    *  @param defaultValue Value to return if attribute value is null.
    *  @return Attribute value.
    */
   public Date getValue(Date defaultValue) {
      return isNull() ? defaultValue : getValue();
   }

   /** Sets value of the attribute
    * @param value Date
    */
   public void setValue(Date value) {
      internalSetValue(value);
   }

   /** Copies the value from another FndDate
    * @param field Attribute to copy value from
    */
   public void setValue(FndDate field) {
      setValue(field.getValue());
   }

   /** Sets the value of the attribute from another attribute.
    *  The main difference between assign and setValue is that the value will be cloned
    *  in assign (if necessary). The attribute value will never be cloned in setValue.
    * @param from Attribute to copy value from
    * @throws SystemException Thrown if cloning fails
    */
   public void assign(FndDate from) throws SystemException {
      super.assign(from);
   }

   /** Returns string representation (as used in Fnd buffers) of the date.
    * @return Date in string format yyyy-MM-dd
    */
   @Override
   public String toString() {
      if (value != null) {
         SimpleDateFormat dateFormat = FndContext.getCurrentDateFormat();
         return dateFormat.format(getValue());
      }

      return null;
   }

   /** Parses a string containing a date and sets the value of the attribute to that date.
    * @param value date value to parse.
    * throws ParseException  if <code>value</code> is of illegal format.
    */
   @Override
   public void parseString(String value) throws ifs.fnd.base.ParseException {
      parseString(value, FndContext.getCurrentDateFormat());
   }

   /** Parses a string containing a date and sets the value of the attribute to that date.
    * @param value date value to parse.
    * @param dateformat Object used to parse date value
    * throws ParseException  if <code>value</code> is of illegal format.
    */
   private void parseString(String value, SimpleDateFormat dateFormat) throws ifs.fnd.base.ParseException {
      try {
         if (value == null || value.length() == 0) {
            this.value = null;
         }
         else {
            this.value = dateFormat.parse(value);
         }
         set();
         setExistent();
      }
      catch (ParseException ex) {
         throw new ifs.fnd.base.ParseException(ex, "ILLEGALDATEVALUE: Illegal date value (&1) for attribute &2.&3", value, getParentRecord().getName(), getName());
      }
   }

   /** Compares the value of this attribute to the value of the specified attribute.
    *  (Used for sorting)
    *  @param attr Attribute to compare to.
    *  @return 0 if values are equal, a value less than 0 if the value of this attribute
    *  is a Date before the value of the specified attribute and a value greater han 0
    *  if the value of this attribute is a Date after the value of the specified attribute.
    */
   @Override
   public int compareTo(FndAttribute attr) {
      if (this == attr)
         return 0;

      if (attr instanceof FndDate) {
         if (this.isNull() && attr.isNull())
            return 0;
         else if (!this.isNull())
            return this.getValue().compareTo(((FndDate)attr).getValue());
      }
      return 1;
   }

   /** Return a FndSqlValue based on the value of this attribute
    */
   @Override
   protected FndSqlValue toFndSqlValue() {
      if (this.isNull()) {
         FndSqlValue val = new FndSqlValue(this.getName(), this.getSqlType());
         val.setNull();
         return val;
      }
      else {
         return new FndSqlValue(this.getName(), new java.sql.Date(this.getValue().getTime()));
      }
   }

   /**
    * Get value from database and set on this attribute
    * @param data an instance of FndSqlData interface to get value from
    * @param colNr column/parameter index number
    */
   @Override
   protected void setSqlValue(FndSqlData data, int colNr) throws IfsException {
      this.setValue(data.getDate(colNr));
   }

   /** Get SQL type
    */
   @Override
   protected FndSqlType getSqlType() {
      return FndSqlType.DATE;
   }

   /**
    * See {@link FndAttribute#createBetweenCondition(Object,Object) createBetweenCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createBetweenCondition(Date value1, Date value2) {
      return super.createBetweenCondition(value1, value2);
   }

   /**
    * See {@link FndAttribute#createEqualCondition(Object) createEqualCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createEqualCondition(Date value) {
      return super.createEqualCondition(value);
   }

   /**
    * See {@link FndAttribute#createGreaterThanCondition(Object) createGreaterThanCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createGreaterThanCondition(Date value) {
      return super.createGreaterThanCondition(value);
   }

   /**
    * See {@link FndAttribute#createGreaterThanOrEqualCondition(Object) createGreaterThanOrEqualCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createGreaterThanOrEqualCondition(Date value) {
      return super.createGreaterThanOrEqualCondition(value);
   }

   /**
    * See {@link FndAttribute#createLessThanCondition(Object) createLessThanCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createLessThanCondition(Date value) {
      return super.createLessThanCondition(value);
   }

   /**
    * See {@link FndAttribute#createLessThanOrEqualCondition(Object) createLessThanOrEqualCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createLessThanOrEqualCondition(Date value) {
      return super.createLessThanOrEqualCondition(value);
   }

   /**
    * See {@link FndAttribute#createNotEqualCondition(Object) createNotEqualCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createNotEqualCondition(Date value) {
      return super.createNotEqualCondition(value);
   }

   /**
    * Create a "attribute between attribute1 and attribute2" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr1 the first attribute to compare this attribute to
    * @param attr2 the second attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createBetweenCondition(FndDate attr1, FndDate attr2) {
      return new FndSimpleCondition(this, FndQueryOperator.BETWEEN, attr1, attr2);
   }

   /**
    * Create a 'attribute = attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createEqualCondition(FndDate attr) {
      return new FndSimpleCondition(this, FndQueryOperator.EQUAL, attr, null);
   }

   /**
    * Create a 'attribute > attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createGreaterThanCondition(FndDate attr) {
      return new FndSimpleCondition(this, FndQueryOperator.GREATER_THAN, attr, null);
   }

   /**
    * Create a 'attribute >= attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createGreaterThanOrEqualCondition(FndDate attr) {
      return new FndSimpleCondition(this, FndQueryOperator.GREATER_THAN_OR_EQUAL, attr, null);
   }

   /**
    * Create a 'attribute < attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createLessThanCondition(FndDate attr) {
      return new FndSimpleCondition(this, FndQueryOperator.LESS_THAN, attr, null);
   }

   /**
    * Create a 'attribute <= attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createLessThanOrEqualCondition(FndDate attr) {
      return new FndSimpleCondition(this, FndQueryOperator.LESS_THAN_OR_EQUAL, attr, null);
   }

   /**
    * Create a 'attribute <> attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createNotEqualCondition(FndDate attr) {
      return new FndSimpleCondition(this, FndQueryOperator.NOT_EQUAL, attr, null);
   }

   /** Clone attribute value.
    *  @return A clone of the internal Date value.
    */
   @Override
   protected Object cloneValue() throws CloneNotSupportedException {
      if (!this.isNull())
         return this.getValue().clone();
      else
         return null;
   }

   /**
    * Returns an FndDate attribute holding the value represented by the specified String.
    * @param value the string to be parsed
    * @return a new instance of FndDate with the specified value
    * @throws ParseException if the specified String has invalid format
    */
   public static FndDate valueOf(String value) throws ifs.fnd.base.ParseException {
      FndDate attr = new FndDate();
      attr.parseString(value);
      return attr;
   }
}
