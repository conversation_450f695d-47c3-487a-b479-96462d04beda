/*
 * IFS Research & Development
 *
 * This program is protected by copyright law and by international conventions.
 * All licensing, renting, lending or copying (including for private use), and
 * all other use of the program, which is not expressively permitted by IFS
 * Research & Development (IFS), is a violation of the rights of IFS. Such
 * violations will be reported to the appropriate authorities.
 *
 * VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD TO UP TO TWO
 * YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record.serialization;

import ifs.fnd.base.FndContext;
import ifs.fnd.base.ParseException;
import ifs.fnd.base.capability.ApplicationCapability;
import ifs.fnd.buffer.BufferUtil;
import ifs.fnd.buffer.UTF8ByteCounter;
import ifs.fnd.record.FndAttributeType;
import java.io.*;

/**
 * <B>Framework internal class:</B> Class used as output for characters and bytes during serialization of records.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class FndTokenWriter {
   private Writer writer;

   private FndByteBufferOutputStream out;

   private int count;

   private boolean lengthPrefixSupported = false;
   
   /**
    * Construct an instance that will write characters to a specified character
    * stream.
    * Note! This constructor is used by FndContext serialization procedure.
    */
   public FndTokenWriter(Writer output) {
      init();
      
      this.writer = output;
   }

   /**
    * Construct an instance that will write characters to a byte buffer with the specified initial size.
    */
   public FndTokenWriter(int initialSize) throws ParseException {
      init();
            
      this.out = new FndByteBufferOutputStream(initialSize);
      try {
         this.writer = new OutputStreamWriter(out, FndSerializeConstants.BUFFER_CHARSET);
      }
      catch(UnsupportedEncodingException e) {
         throw new ParseException(e, "CREATETOKENWRITTER:Cannot create FndTokenWriter: &1", e.getMessage());
      }
   }

   private void init() {
      this.lengthPrefixSupported = FndContext.getCurrentContext()
              .getSupportedCapabilities().contains(ApplicationCapability.LPTEXT);
   }

   /**
    * Construct an instance that will write characters to a byte buffer with the default initial size.
    */
   public FndTokenWriter() throws ParseException {
      this(1024);
   }

   /**
    * Write an String to the output stream. If the client supports 
    * length-prefixed strings, we will always send length prefixed strings.
    * @param token string to be written
    * @throws ParseException if an exception occurs while writing the token
    */
   public void writeStringToken(String token) throws ParseException {
      if (lengthPrefixSupported) {
         try {
            // following byte count is equal to:
            // token.getBytes(FndSerializeConstants.BUFFER_CHARSET).length
            int length = UTF8ByteCounter.count(token, UTF8ByteCounter.Type.STRING_TO_UTF8);

            BufferUtil.writeLengthPrefix(writer, length);
            writer.write(token);
         } catch (IOException ex) {
            throw new ParseException(ex, ex.getMessage());
         }
      } else {
         write(token);
      }
   }

   /**
    * Write a type to the stream. If the type is "T" or "A", they will be 
    * replaced with "LPT" and "LPA" if the client support length prefixed
    * strings
    * @param typeString
    * @throws ParseException 
    */
   public void writeStringType(String typeString) throws ParseException {
      if (lengthPrefixSupported) {
         if (typeString.equals(FndAttributeType.ALPHA.getTypeMarker()))
            write(BufferUtil.LENGTH_PREFIXED_ALPHA);
         else if (typeString.equals(FndAttributeType.TEXT.getTypeMarker()))
            write(BufferUtil.LENGTH_PREFIXED_TEXT);
         else
            throw new ParseException("typeString should be \"T\" or \"A\"");
      } else {
         write(typeString);
      }
   }
   /**
    * Write a non-length prefixed String to the output stream.
    */
   public void write(String token) throws ParseException {
      try {
         writer.write(token);
         count += token.length();
      }
      catch (IOException e) {
         throw new ParseException(e, e.toString());
      }
   }

   /**
    * Write a character to the output stream.
    */
   public void write(char delimiter) throws ParseException {
      try {
         writer.write(delimiter);
         count++;
      }
      catch (IOException e) {
         throw new ParseException(e, e.toString());
      }
   }

   /**
    * Perform a BASE64 encoding of an array of bytes and write the result to the output stream.
    */
   public void writeBase64(byte[] data) throws ParseException {
      checkByteBuffer();
      ByteArrayInputStream in = new ByteArrayInputStream(data);
      try {
         writer.flush();
         (new Base64()).encode(in, out);
      }
      catch (IOException e) {
         throw new ParseException(e, "BASE64ENCODE:IO error during BASE64 encoding: &1", e.getMessage());
      }
   }

   /**
    * Write a binary token to the underlying output stream.
    * The token may be BASE64 encoded or not (depending on FndContext parameter encodeBinaryValues).
    * When BASE64 encoding is not used then the method writes BINARY_INDICATOR character
    * and the data length (4 bytes, high byte first) followed by the token data.
    */
   public void writeBinaryToken(byte[] data) throws ParseException {
      checkByteBuffer();
      try {
         if(FndContext.getCurrentContext().encodeBinaryValues()) {
            writeBase64(data);
         }
         else {
            writer.flush();
            out.write(FndSerializeConstants.BINARY_INDICATOR);
            writeInt(data.length, out);
            out.write(data);
         }
      }
      catch (IOException e) {
         throw new ParseException(e, "WRITEBINARY:IO error when writing binary data: &1", e.getMessage());
      }
   }

   /**
    * Return the contents of this buffer as a string.
    */
   public String getBuffer() throws ParseException {
      try {
         return new String(getByteBuffer(), FndSerializeConstants.BUFFER_CHARSET);
      }
      catch(UnsupportedEncodingException e) {
         throw new ParseException(e, "STRBUFFERGET:Cannot create string form of buffer: &1", e.getMessage());
      }
   }

   /**
    * Return the contents of this buffer as array of bytes.
    */
   public byte[] getByteBuffer() throws ParseException {
      checkByteBuffer();
      try {
         writer.flush();
      }
      catch (IOException e) {
         throw new ParseException(e, "BYTEBUFFERGET:IO error during fetching byte data: &1", e.getMessage());
      }
      return out.getBytes();
   }

   /**
    * Return the number of characters written to the underlying stream.
    */
   public int getCount() {
      return count;
   }

   /**
    * Return the number of unused bytes in the byte buffer.
    */
   public int countUnusedBytes() throws ParseException {
      checkByteBuffer();
      return out.unusedSize();
   }

   /**
    * Verifies that the byte buffer is used by this FndTokenWriter.
    */
   private void checkByteBuffer() throws ParseException {
      if(out == null)
         throw new ParseException("BYTEBUFNOTUSED: The byte buffer is not used by this instance of FndTokenWriter.");
   }

   /**
    * Writes an integer value to an output stream as four bytes, high byte first.
    * The method may be used to encode the length of a binary token in a serialized stream.
    * @param value the value to write to the output stream
    * @param out the output stream to write the value to
    */
   private static void writeInt(int value, OutputStream out) throws IOException {
      out.write((value >>> 24) & 0xFF);
      out.write((value >>> 16) & 0xFF);
      out.write((value >>>  8) & 0xFF);
      out.write((value) & 0xFF);
   }

}