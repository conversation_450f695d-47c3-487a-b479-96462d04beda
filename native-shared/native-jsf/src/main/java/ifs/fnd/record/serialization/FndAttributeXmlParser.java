/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record.serialization;

import ifs.fnd.base.ParseException;
import ifs.fnd.record.FndAttribute;

/**
 * Interface defining callback method for parsing of an attribute value.
 */
public interface FndAttributeXmlParser {

   /**
    * Parse the XML stream to an attribute value
    * @param reader XML stream to parse
    * @param attribute the attribute to parse the XML stream into
    */
   void parseAttributeValue(FndXmlReader reader, FndAttribute attribute) throws ParseException;
}

