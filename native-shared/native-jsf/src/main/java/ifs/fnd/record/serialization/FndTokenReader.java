/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record.serialization;

import ifs.fnd.base.ParseException;
import java.io.*;

/**
 * <B>Framework internal class:</B> Class used for reading characters from serialized record stream.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class FndTokenReader {
   private static final char NULL_CHAR = '\u0000';

   private Reader input; // may become a Reader in JDK 1.1 (via InputStreamReader)
   private char[] buffer; // reused by getToken
   private int count; // number of characters in the token bufffer
   private char delimiter; // buffers one delimiter

   /**
    * Empty constructor used by subclasses.
    */
   FndTokenReader() {
   }

   /**
    * Construct an instance that will read bytes from the specified byte stream,
    * and then convert them to tokens and delimiters.
    *
   private FndTokenReader(InputStream input) {
      this.input = new BufferedReader(new InputStreamReader(input));
      buffer = new char[1024];
      count = 0;
   }*/

   /**
    * Construct an instance that will read tokens directly from the specified
    * character stream,
    */
   public FndTokenReader(Reader input) {
      this.input = input;
      buffer = new char[1024];
      count = 0;
   }

   /**
    * Construct an instance that will read tokens from the specified string
    */
   public FndTokenReader(String str) {
      this.input = new BufferedReader(new StringReader(str));
      buffer = new char[1024];
      count = 0;
   }

   /**
    * Force a delimiter from the input stream.
    * Throw ParseException if not found.
    */
   @SuppressWarnings("PMD.MissingBreakInSwitch")
   public char getDelimiter() throws ParseException {
      if (delimiter == NULL_CHAR) {
         int ch;
         try {
            ch = input.read();
         }
         catch (IOException e) {
            throw new ParseException(e, e.toString());
         }
         switch (ch) {
            case -1 :
               throw new ParseException("EOS: Expecting delimiter but found end-of-stream");

            /* TODO: dupelk Convert this to FndUtil.isDelimiter(ch) */
            case FndSerializeConstants.BEGIN_BUFFER_MARKER :
            case FndSerializeConstants.END_BUFFER_MARKER :
            case FndSerializeConstants.HEAD_MARKER :
            case FndSerializeConstants.NAME_MARKER :
            case FndSerializeConstants.TYPE_MARKER :
            case FndSerializeConstants.STATUS_MARKER :
            case FndSerializeConstants.VALUE_MARKER :
            case FndSerializeConstants.NULL_VALUE_MARKER :
            case FndSerializeConstants.NO_VALUE_MARKER :
            case FndSerializeConstants.INVALID_VALUE_MARKER :
            case FndSerializeConstants.ACTION_MARKER :
            case FndSerializeConstants.IDENTITY_MARKER :
            case FndSerializeConstants.COUNT_MARKER :
            case FndSerializeConstants.CHANGED_VALUE_MARKER :
            case FndSerializeConstants.UNCHANGED_VALUE_MARKER :
               //System.out.println(""+ch);
               return (char)ch;

            default :
               throw new ParseException("NODELIM: Expecting delimiter but found character code " + ch);
         }
      }

      char d = delimiter;
      delimiter = NULL_CHAR;
      //System.out.println(""+(int)d);
      return d;
   }

   /**
    * Force next token from the input stream.
    * Similar to getPooledToken, but returned String will not be pooled.
    * This version should be used when the returned value set is expected to be large.
    * Throw ParseException if not found.
    */
   @SuppressWarnings("PMD.MissingBreakInSwitch")
   public String getToken() throws ParseException {
      if (delimiter != NULL_CHAR)
         throw new ParseException("NOTOKEN: Expecting token but found delimiter code " + (int)delimiter);

      count = 0;

      while (true) {
         int ch;
         try {
            ch = input.read();
         }
         catch (Exception e) {
            throw new ParseException(e, e.toString());
         }

         switch (ch) {
            case -1 :
               if (count == 0)
                  throw new ParseException("ENDSTR: Unexpected end of stream");
               return new String(buffer, 0, count);

            /* TODO: dupelk Convert this to FndUtil.isDelimiter(ch) */
            case FndSerializeConstants.BEGIN_BUFFER_MARKER :
            case FndSerializeConstants.END_BUFFER_MARKER :
            case FndSerializeConstants.HEAD_MARKER :
            case FndSerializeConstants.NAME_MARKER :
            case FndSerializeConstants.TYPE_MARKER :
            case FndSerializeConstants.STATUS_MARKER :
            case FndSerializeConstants.VALUE_MARKER :
            case FndSerializeConstants.NULL_VALUE_MARKER :
            case FndSerializeConstants.NO_VALUE_MARKER :
            case FndSerializeConstants.INVALID_VALUE_MARKER :
            case FndSerializeConstants.ACTION_MARKER :
            case FndSerializeConstants.IDENTITY_MARKER :
            case FndSerializeConstants.COUNT_MARKER :
            case FndSerializeConstants.CHANGED_VALUE_MARKER :
            case FndSerializeConstants.UNCHANGED_VALUE_MARKER :
               delimiter = (char)ch;
               return new String(buffer, 0, count); // empty String OK.

            default :
               if (count == buffer.length) {
                  char[] newbuf = new char[2 * count];
                  System.arraycopy(buffer, 0, newbuf, 0, count);
                  buffer = newbuf;
               }

               buffer[count++] = (char)ch;
         }
      }
   }

   /**
    * Force a next delimiter from the input stream and throw ParseException
    * if it does not match the specified delimiter.
    */
   public void matchDelimiter(char ch) throws ParseException {
      char c = getDelimiter();
      if (c != ch)
         throw new ParseException("WRONGDELIM: Expecting delimiter code " + (int)ch + " but found code " + (int)c);
   }

   /**
    * Force next binary token from the input stream.
    * @return binary token data consumed from the input stream
    * @throws ParseException because this method is not implemented by class FndTokenReader
    */
   public byte[] getBinaryToken() throws ParseException {
      throw new ParseException("GETBINTOKEN: Method getBinaryToken is not implemented by class FndTokenReader.");
   }

   /**
    * Force next length prefixed text token form the input stream.
    * @return Length prefixed string token read from the input stream
    * @throws ParseException this method is not implemented in class FndTokenReader
    */
   public String getLPStringToken() throws ParseException {
      throw new ParseException("GETLPSTRTOKEN: Method getTextToken is not implemented by class FndTokenReader.");
   }
}
