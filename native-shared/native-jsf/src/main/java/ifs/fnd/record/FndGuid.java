/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 * ----------------------------------------------------------------------------
 */

package ifs.fnd.record;

import ifs.fnd.base.ApplicationException;
import ifs.fnd.base.SystemException;

/**
 * FndGuid attribute data type.
 * Attribute values are generated by default.
 */
public final class FndGuid extends FndAlpha {

   /**
    * Create a new instance.
    */
   public FndGuid() {
      super();
   }

   /**
    * Create a new instance with the specified name.
    * @param name attribute name
    */
   public FndGuid(String name) {
      super(name);
   }

   /**
    * Create a new instance based on a specified meta attribute.
    * @param meta meta-attribute
    */
   public FndGuid(FndAttributeMeta meta) {
      super(meta);
   }

   /**
    * Creates a new attribute based on meta-data.
    * @param meta Meta-data to base the attribute on
    * @return The created attribute
    */
   @Override
   protected FndAttribute newAttribute(FndAttributeMeta meta) {
      return new FndGuid(meta);
   }

   /**
    * Sets the value of the attribute from another FndGuid.
    * @param field Attribute from which to copy the value
    * @throws ApplicationException Value out of range
    */
   public void setValue(FndGuid field) throws ApplicationException {
      setValue(field.getValue());
   }

   /**
    * Sets the value of the attribute from another attribute.
    * The main difference between assign and setValue is that the value will be cloned
    * in assign (if necessary). The attribute value will never be cloned in setValue.
    * @param from Attribute to copy value from
    * @throws SystemException Thrown if cloning fails
    * @throws ApplicationException Thrown if value if out of range for this attribute
    */
   public void assign(FndGuid from) throws SystemException, ApplicationException {
      super.assign(from);
   }

}
