/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.record;

import ifs.fnd.base.*;
import java.math.BigDecimal;

/** FndNumber data type for attributes.
 */
public final class FndNumber extends FndAbstractNumber {

   /**
    * Create a new instance.
    */
   public FndNumber() {
      super(FndAttributeType.NUMBER);
   }

   /**
    * Create a new instance, also specifying attribute name.
    * @param name attribute name
    */
   public FndNumber(String name) {
      super(FndAttributeType.NUMBER, name);
   }

   /**
    * Create a new instance, also specifying attribute name and initial value.
    * @param name attribute name
    * @param value initial attribute value
    */
   public FndNumber(String name, Double value) {
      super(FndAttributeType.NUMBER, name, value);
   }

   /**
    * Create a new instance based on specified meta data.
    * @param meta attribute meta data
    */
   public FndNumber(FndAttributeMeta meta) {
      super(meta);
      setType(FndAttributeType.NUMBER);
   }

   /**
    * Create a new instance based on specified meta data. Also set initial attribute value.
    * @param meta meta data
    * @param value initial value
    */
   public FndNumber(FndAttributeMeta meta, Double value) {
      super(meta, value);
      setType(FndAttributeType.NUMBER);
   }

   /**
    * Creates a new attribute based on meta-data.
    * @param meta Meta-data to base the attribute on
    * @return The created attribute
    */
   @Override
   protected FndAttribute newAttribute(FndAttributeMeta meta) {
      return new FndNumber(meta);
   }

   /** Gets the attribute value.
    * @return Value
    */
   public Double getValue() {
      Object internalValue = internalGetValue();
      if(internalValue instanceof BigDecimal){
         //OData send BigDecimal type when Number without precision. So need to convert. Number is mapped with SQL Number in Mobile META
         return ((BigDecimal)internalValue).doubleValue();
      }
      else{
         return (Double)internalValue;
      }
   }

   /** Return the value of the attribute. If value is null, return defaultValue.
    *  @param defaultValue Value to return if attribute value is null.
    *  @return Attribute value.
    */
   public Double getValue(Double defaultValue) {
      return isNull() ? defaultValue : getValue();
   }

   /** Return the value of the attribute. If the value is null, return defaultValue.
    *  @param defaultValue Value to return if attribute value is null.
    *  @return Attribute value.
    */
   public double doubleValue(double defaultValue) {
      return isNull() ? defaultValue : getValue();
   }

   /** Sets the attribute value.
    * @param value Value
    */
   public void setValue(Double value) {
      internalSetValue(value);
   }

   /** Sets the attribute value.
    * @param value Value
    */
   public void setValue(double value) {
      internalSetValue(value);
   }

   /** Copies the value from another FndNumber.
    * @param field Attribute to copy from
    */
   public void setValue(FndNumber field) {
      setValue(field.getValue());
   }

   /** Sets the value of the attribute from another attribute.
    *  The main difference between assign and setValue is that the value will be cloned
    *  in assign (if necessary). The attribute value will never be cloned in setValue.
    * @param from Attribute to copy value from
    * @throws SystemException Thrown if cloning fails
    */
   public void assign(FndNumber from) throws SystemException {
      super.assign(from);
   }

   /** Sets the value from a string containing a number.
    * @param value String containing a number
    * @throws ParseException if <code>value</code> is of illegal format.
    */
   @Override
   public void parseString(String value) throws ParseException {
      try {
         if (value == null || value.length() == 0) {
            this.value = null;
         }
         else {
            this.value = new Double(value);
         }
         set();
         setExistent();
      }
      catch (NumberFormatException ex) {
         throw new ParseException(ex, "ILLEGALNUMVALUE: Illegal number value (&1) for attribute &2.&3", value, getParentRecord().getName(), getName());
      }
   }

   /** Compares two attributes. Used for sorting.
    * @param attr Attribute to compare with
    * @return 0 if equal
    */
   @Override
   public int compareTo(FndAttribute attr) {
      if (this == attr)
         return 0;

      if (attr instanceof FndNumber) {
         if (this.isNull() && attr.isNull())
            return 0;
         else if (!this.isNull())
            return this.getValue().compareTo(((FndNumber)attr).getValue());
      }
      return 1;
   }

   /** Create a new FndSqlValue based on the value of this attribute
    */
   @Override
   protected FndSqlValue toFndSqlValue() {
      if (this.isNull()) {
         FndSqlValue val = new FndSqlValue(this.getName(), this.getSqlType());
         val.setNull();
         return val;
      }
      else {
         return new FndSqlValue(this.getName(), this.getValue());
      }
   }

   /**
    * Get value from database and set on this attribute
    * @param data an instance of FndSqlData interface to get value from
    * @param colNr column/parameter index number
    */
   @Override
   protected void setSqlValue(FndSqlData data, int colNr) throws IfsException {
      this.setValue(data.getDouble(colNr));
   }

   /** Get SQL type
    */
   @Override
   protected FndSqlType getSqlType() {
      return FndSqlType.NUMBER;
   }

   /**
    * See {@link FndAttribute#createBetweenCondition(Object,Object) createBetweenCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createBetweenCondition(Double value1, Double value2) {
      return super.createBetweenCondition(value1, value2);
   }

   /**
    * See {@link FndAttribute#createEqualCondition(Object) createEqualCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createEqualCondition(Double value) {
      return super.createEqualCondition(value);
   }

   /**
    * See {@link FndAttribute#createGreaterThanCondition(Object) createGreaterThanCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createGreaterThanCondition(Double value) {
      return super.createGreaterThanCondition(value);
   }

   /**
    * See {@link FndAttribute#createGreaterThanOrEqualCondition(Object) createGreaterThanOrEqualCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createGreaterThanOrEqualCondition(Double value) {
      return super.createGreaterThanOrEqualCondition(value);
   }

   /**
    * See {@link FndAttribute#createLessThanCondition(Object) createLessThanCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createLessThanCondition(Double value) {
      return super.createLessThanCondition(value);
   }

   /**
    * See {@link FndAttribute#createLessThanOrEqualCondition(Object) createLessThanOrEqualCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createLessThanOrEqualCondition(Double value) {
      return super.createLessThanOrEqualCondition(value);
   }

   /**
    * See {@link FndAttribute#createNotEqualCondition(Object) createNotEqualCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createNotEqualCondition(Double value) {
      return super.createNotEqualCondition(value);
   }

}
