/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;

import java.util.*;

/**
 * Enumeration class representing the type of storage.
 */
public final class FndStorageType implements java.io.Serializable {
    static private List<FndStorageType> types = new ArrayList<>();
    private String type;

    private FndStorageType(String type) {
        this.type=type;
        types.add(this);
    }

   @Override
    public String toString() {
        return type;
    }

    static public List getStorageTypes() {
        return types;
    }

    public static final FndStorageType STANDARD_STORAGE = new FndStorageType("STANDARD_STORAGE");
}
