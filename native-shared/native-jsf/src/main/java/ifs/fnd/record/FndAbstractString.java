/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.record;

import ifs.fnd.base.ParseException;
import ifs.fnd.record.serialization.FndXmlSerializer;
import ifs.fnd.record.serialization.FndXmlString;

/**
 * Base class for string attributes.
 * Its value is stored as java String object.
 */
public abstract class FndAbstractString extends FndAbstractText {

   /**
    * if true the value (for FndText and FndAlpha) are serialized to buffer with
    * a length prefix.
    */
   private boolean lengthPrefixed = false;
   
   protected FndAbstractString(FndAttributeType type) {
      super(type);
   }

   protected FndAbstractString(FndAttributeType type, String name) {
      super(type, name);
   }

   protected FndAbstractString(FndAttributeType type, String name, String value) {
      super(type, name, value);
   }

   protected FndAbstractString(FndAttributeMeta meta) {
      super(meta);
   }

   protected FndAbstractString(FndAttributeMeta meta, String value) {
      super(meta, value);
   }

   /**
    * Create a "attribute between attribute1 and attribute2" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr1 the first attribute to compare this attribute to
    * @param attr2 the second attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createBetweenCondition(FndAbstractString attr1, FndAbstractString attr2) {
      return new FndSimpleCondition(this, FndQueryOperator.BETWEEN, attr1, attr2);
   }

   /**
    * Create a 'attribute = attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createEqualCondition(FndAbstractString attr) {
      return new FndSimpleCondition(this, FndQueryOperator.EQUAL, attr, null);
   }

   /**
    * Create a 'attribute > attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createGreaterThanCondition(FndAbstractString attr) {
      return new FndSimpleCondition(this, FndQueryOperator.GREATER_THAN, attr, null);
   }

   /**
    * Create a 'attribute >= attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createGreaterThanOrEqualCondition(FndAbstractString attr) {
      return new FndSimpleCondition(this, FndQueryOperator.GREATER_THAN_OR_EQUAL, attr, null);
   }

   /**
    * Create a 'attribute < attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createLessThanCondition(FndAbstractString attr) {
      return new FndSimpleCondition(this, FndQueryOperator.LESS_THAN, attr, null);
   }

   /**
    * Create a 'attribute <= attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createLessThanOrEqualCondition(FndAbstractString attr) {
      return new FndSimpleCondition(this, FndQueryOperator.LESS_THAN_OR_EQUAL, attr, null);
   }

   /**
    * Create a 'attribute <> attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createNotEqualCondition(FndAbstractString attr) {
      return new FndSimpleCondition(this, FndQueryOperator.NOT_EQUAL, attr, null);
   }

   /**
    * Create a "attribute like value" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribite to. Use wildcards '%' and '_'.
    * @return Created condition.
    */
   public FndSimpleCondition createLikeCondition(FndAbstractString attr) {
      return new FndSimpleCondition(this, FndQueryOperator.LIKE, attr, null);
   }

   /**
    * Create a "attribute not like attr" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribite to. Use wildcards '%' and '_'.
    * @return Created condition.
    */
   public FndSimpleCondition createNotLikeCondition(FndAbstractString attr) {
      return new FndSimpleCondition(this, FndQueryOperator.NOT_LIKE, attr, null);
   }

   /**
    * Create a "attribute like attr" condition on this attribute ignoring case.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribite to. Use wildcards '%' and '_'.
    * @return Created condition.
    */
   public FndSimpleCondition createLikeIgnoreCaseCondition(FndAbstractString attr) {
      return new FndSimpleCondition(this, FndQueryOperator.LIKE_IGNORE_CASE, attr, null);
   }

   /**
    * Create a "attribute not like attr" condition on this attribute ignoring case.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribite to. Use wildcards '%' and '_'.
    * @return Created condition.
    */
   public FndSimpleCondition createNotLikeIgnoreCaseCondition(FndAbstractString attr) {
      return new FndSimpleCondition(this, FndQueryOperator.NOT_LIKE_IGNORE_CASE, attr, null);
   }

   /**
    * Create a "attribute = attr" condition on this attribute ignoring case.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribite to
    * @return Created condition.
    */
   public FndSimpleCondition createEqualIgnoreCaseCondition(FndAbstractString attr) {
      return new FndSimpleCondition(this, FndQueryOperator.EQUAL_IGNORE_CASE, attr, null);
   }

   /** This method is specially used for supporting few non printalbe chars in OBJ_VERSION column and should be
    * overridden by attributes where the string representation doesn't match
    * that of it's internal storage object (value).
    * Parses a XML string to get the attribute's value.
    * @param s Value to set the attribute to
    * @throws ParseException if <code>value</code> cannot be parsed
    * (contains an illegal value).
    */
   @Override
   public void parseXmlString(String s) throws ParseException {
      if("OBJ_VERSION".equals(this.meta.getName()))
         parseString(FndXmlString.decode(s));
      else
         parseString(s);
   }

   /**
    * Formats this attribute's value to the xml stream.
    * @param s XML serializer to append the value to
    */
   @Override
   protected void formatValueToXml(FndXmlSerializer s) throws ParseException {
      String val = toString();
      int pos = FndXmlString.validateXmlText(val);
      if((pos >= 0) && (!"OBJ_VERSION".equals(this.meta.getName())))
         throw new ParseException("ILLEGALXMLCHAR:Attribute &1 cannot be converted to XML because its value contains an invalid XML character (Unicode: 0x&2).",
                                  getMeta() == null ? getName() : getMeta().getFullName(),
                                  Integer.toHexString(val.charAt(pos)).toUpperCase());
      super.formatValueToXml(s);
   }

   public boolean isLengthPrefixed() {
       return lengthPrefixed;
   }

   public void setLengthPrefixed() {
       this.lengthPrefixed = true;
   }
}
