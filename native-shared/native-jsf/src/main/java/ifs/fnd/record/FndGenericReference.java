/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;

import ifs.fnd.base.*;
import ifs.fnd.record.serialization.*;

/**
 * <B>Framework internal class:</B> Attribute that refers to the primary/alternate key of an aspect.
 * The attribute is not persistent (has no database storage). It acts as
 * parent-key-in-parent in aggregates storing aspects.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public final class FndGenericReference extends FndText {

   FndGenericReference(FndAttributeMeta meta) {
      super(meta);
   }

   @Override
   protected FndAttribute newAttribute(FndAttributeMeta meta) {
      return new FndGenericReference(meta);
   }

   /**
    * Gets the value of the attribute.
    * The value uniquely identifies the entity instance stored in the view owning this attribute.
    * The value will be created by concatenating the values of primary key attributes with the
    * entity name. As separator is used "^" character.
    * @return string representation of the primary key and the entity name for the view
    *         that owns this attribute
    */
   @Override
   public String getValue() {
      FndAbstractRecord rec = getParentRecord();
      FndCompoundReference primaryKey = rec.getPrimaryKey();
      FndAutoString buf = new FndAutoString();
      FndAttribute.Iterator attrs = primaryKey.iterator();
      while(attrs.hasNext()) {
         FndAttribute attr = attrs.next();
         buf.append(attr.toString());
         buf.append('^');
      }
      buf.append(rec.getEntity());
      String value = buf.toString();
      //TODO: check if the value should be stored, for example by super.parseString(value);
      return value;
   }

   /**
    * Checks if the attribute has a null value.
    * @return true if the primary in the view owning this attribut is null.
    */
   @Override
   public boolean isNull() {
      FndAbstractRecord rec = getParentRecord();
      FndCompoundReference primaryKey = rec.getPrimaryKey();
      return primaryKey.isNull();
   }

   /**
    * Sets the value of the attribute.
    * @param value Value
    * @throws ApplicationException because the value of an FndGenericReference cannot be modified.
    */
   @Override
   public void setValue(String value) throws ApplicationException {
      throw new ApplicationException("The value of a FndGenericReference cannot be modified.");
   }

   /**
    * Sets the value of the attribute from another attribute.
    * @param from Attribute to copy value from
    * @throws ApplicationException because the value of an FndGenericReference cannot be modified.
    */
   public void assign(FndGenericReference from) throws SystemException, ApplicationException {
      setValue(""); // will throw an exception
   }

   /**
    * Get value from database and set on this attribute
    * @param data an instance of FndSqlData interface to get value from
    * @param colNr column/parameter index number
    * @throws ApplicationException because the value of an FndGenericReference cannot be modified.
    */
   @Override
   protected void setSqlValue(FndSqlData data, int colNr) throws IfsException {
      setValue(""); // will throw an exception
   }

   /**
    * Returns string representation of the attribute's value.
    * @return Value as string
    */
   @Override
   public String toString() {
      return getValue();
   }
}
