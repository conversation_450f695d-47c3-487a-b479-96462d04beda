/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;

import ifs.fnd.record.serialization.*;
import ifs.fnd.base.ParseException;

/**
 * Abstract super-class for arrays and arrgegates
 */
public abstract class FndCompoundAttribute extends FndAttribute {

   private FndCompoundReference parentKeyInParent;

   /**
    * Constructs a new FndCompoundAttribute with the specified contents.
    */
   protected FndCompoundAttribute(FndAttributeType type) {
      super(type);
   }

   /**
    * Constructs a new FndCompoundAttribute with the specified contents.
    */
   protected FndCompoundAttribute(FndAttributeType type, String name) {
      super(type, name);
   }

   /**
    * Constructs a new FndCompoundAttribute with the specified contents.
    */
   protected FndCompoundAttribute(FndAttributeMeta meta) {
      super(meta);
   }

   /**
    * Constructs a new FndCompoundAttribute with the specified contents.
    */
   protected FndCompoundAttribute(FndAttributeMeta meta, FndCompoundReference parentKeyInParent, FndAttributeType type) {
      this(meta);
      this.parentKeyInParent = parentKeyInParent;
      setType(type);
   }

   /**
    * Sets the parentKeyInParent reference in this compound attribute.
    * This method is called during creation of generic aspect aggregates in
    * a persistent view.
    * @param parentKeyInParent a compound reference in the parent record
    */
   protected void setParentKeyInParent(FndCompoundReference parentKeyInParent) {
      this.parentKeyInParent = parentKeyInParent;
   }

   /**
    * Check if this is a compound attribute
    * @return true
    */
   @Override
   public boolean isCompound() {
      return true;
   }

   /**
    * Returns true if this array/aggregate contains parts which are persistent.
    * @return true if this array/aggregate has persistent elements or sub-elements
    *         at any level in master-detail hierarchy
    */
   public boolean hasPersistentParts() {
      //
      // An array/aggregate properly instantiated has meta attribute of type
      // FndCompoundAttributeMeta, which knows if it is persistent or not.
      // Other array/aggregates are assumed to be non-persistent.
      //
      if (getMeta() instanceof FndCompoundAttributeMeta)
         return getCompoundMeta().getElement().hasPersistentParts();
      else
         return false;
   }

   /**
    * Returns meta-data for this compound attribute.
    * @return Meta-data
    */
   public FndCompoundAttributeMeta getCompoundMeta() {
      return (FndCompoundAttributeMeta) getMeta();
   }

   /**
    * Returns reference in parent record acting as parent key for this array/aggregate relation.
    */
   public FndCompoundReference getParentKeyInParent() {
      return parentKeyInParent;
   }

   /**
    * Create a detail condition (EXIST_IN/NOT_EXISTS_IN) on this array/aggregate attribute.
    * This method is protected. A public method createDetailCondition is generated in
    * every array/aggregate. See the description of class {@link ifs.fnd.record.FndDetailCondition}
    * for example of application code that creates a detail condition.
    * @param detail A detail record of the type contained by this array/aggregate.
    *        Conditions on detail attributes should be added to this record.
    * @param category Query reference category, either exists or not exists.
    * @return A new FndDetailCondition object.
    */
   protected FndDetailCondition createCondition(FndPersistentView detail, FndQueryReferenceCategory category) {
      FndAbstractRecord parent = this.getParentRecord();
      if (parent != null)
         return new FndDetailCondition(this, detail, category);
      else
         return null;
   }

   /**
    * Returns boolean flag controlling the cascade-delete functionality.
    * @return true if elements of this array/aggregate should be automatically removed together with the master record.    */
   public boolean cascadeDelete() {
      return false;
   }

   /**
    * Returns true if this compound attribute contains elements that depend on the parent record.
    * @return true if this array/aggregate contains elements that depend on the record that owns this reference.
    */
   public boolean containsDependentDetails() {
      return false;
   }
      //
      // Arrays/aggregates generated using new record model have meta attribute of type
      // FndCompoundAttributeMeta, which knows if the elements are dependent or not.
      // Old array/aggregates contain always dependent details.
      //
      // FndAttributeMeta meta = getMeta();
      // if (meta instanceof FndCompoundAttributeMeta)
      //    return ((FndCompoundAttributeMeta) meta).containsDependentDetails();
      // else
      //    return true;

   /**
    * Sets parent key in a view becoming an element of this array/aggregate.
    * This method is overridden by generated array/aggregate subclasses that know which
    * compound reference in element view corresponds to master-detail
    * connection implemented by this compound attribute.
    * Also, this method sets the super-reference in the primary key of this view if
    * this view inherits part of its primary key from the parent record.
    */
   protected void connectElementToParent(FndAbstractRecord element) {
   }

   /**
    * Helper method called from generated array/aggregate classes.
    */
   protected void setParentKeyInElement(FndAbstractRecord element, FndCompoundReference ref) {
      element.setParentKey(ref);
   }

   /**
    * Connects an element-view to the master-detail structure represented by this compound attribute.
    * This method is called from arrays/aggregates during add/set operation.
    */
   protected void connectElement(FndAbstractRecord element) {
      element.setContainer(this);
      connectElementToParent(element); // dispatch to overridden method (new arrays/aggregates)
   }

   /**
    * Disconnects an element-view from the master-detail structure represented by this compound attribute.
    * This method is called from arrays during remove operation.
    * It clears the parent key variable in the element (detail) record.
    * Also, this method disconnects the super-reference from the primary key that
    * inherits some attributes from the parent record.
    */
   void disconnectElement(FndAbstractRecord element) {
      element.setContainer(null);
      setParentKeyInElement(element, null);
   }

   /**
    * Reconnect an attribute to it's parent (reset parentKeyInParent).
    * @param to view to connect the attribute to
    */
   void reconnect(FndView to) {
      if(parentKeyInParent != null) {
         for (int i = 0; i < to.getCompoundReferenceCount(); i++) {
            FndCompoundReference ref = to.getCompoundReference(i);
            if (ref.getMeta().match(parentKeyInParent.getMeta())) {
               parentKeyInParent = ref;
               return;
            }
         }
      }
   }

   // Formatting for comound attributes.
   /** Formats the whole attribute into an Fnd Buffer.
    * @param stream Stream to which to write the resulting buffer
    * @param mode enumeration value controlling serialization of CHANGED_VALUE flag
    * @param releaseMemory true if after formatting the attribute value should be cleared releasing the memory, false otherwise
    * @throws ParseException Attribute cannot be serialized into a buffer
    */
   @Override
   protected void format(FndTokenWriter stream, FndAbstractRecord.ChangedValueMode mode, boolean releaseMemory) throws ParseException {
      String name, state;
      FndAttributeType type;

      if ((name = getName()) != null) {
         stream.write(FndSerializeConstants.NAME_MARKER);
         stream.write(name);
      }

      if ((type = getType()) != null) {
         stream.write(FndSerializeConstants.TYPE_MARKER);
         stream.write(type.toString());
      }

      state = getState();
      if (!"".equals(state)) {
         stream.write(FndSerializeConstants.STATUS_MARKER);
         stream.write(state);
      }

      if (isNull()) {
         stream.write(FndSerializeConstants.NULL_VALUE_MARKER);
      }
      else {
         formatValue(stream, releaseMemory);
      }
   }

   /**
    * Approximates the size of the serialized form of this attribute.
    * @param changedValueMode current value of ChangedValueMode that controls formatting process
    * @return the number of bytes needed for the serialized form of this compound attribute
    * @see #format
    */
   @Override
   int serializedSize(FndAbstractRecord.ChangedValueMode changedValueMode) {
      int size = 0;

      // attribute name
      String name = getName();
      if (name != null)
         size += name.length() + 1;

      // attribute type
      FndAttributeType type = getType();
      if (type != null)
         size += type.toString().length() + 1;

      // attribute state (approximation)
      size += 3;

      if (isNull())
         size += 1;
      else
         size += serializedValueSize(changedValueMode);

      return size;
   }

   /**
    * Make the record owning this attribute dirty
    */
   void setParentDirty() {
      if (rec != null)
         rec.setDirty();
   }

   /**
    * An iterator over a list of compound attributes. This interface is similiar to
    * java.util.Iterator except for two things. Firstly, its next() method returns
    * FndCompoundAttribute rather than Object. Secondly, it has no remove() method.
    */
   public interface Iterator {

      /**
       * Returns true if the iteration has more elements.
       * @return true if the iterator has more elements.
       */
      boolean hasNext();

      /**
       * Returns the next element in the iteration.
       * @return the next element in the iteration.
       */
      FndCompoundAttribute next();
   }
}
