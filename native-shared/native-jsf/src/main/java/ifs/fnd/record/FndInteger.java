/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.record;

import ifs.fnd.base.*;

/** FndInteger datatype for attributes.
 */
public final class FndInteger extends FndAbstractNumber {

   /**
    * Create a new instance.
    */
   public FndInteger() {
      super(FndAttributeType.INTEGER);
   }

   /**
    * Create a new instance, also specifying attribute name.
    * @param name attribute name
    */
   public FndInteger(String name) {
      super(FndAttributeType.INTEGER, name);
   }

   /**
    * Create a new instance, also specifying attribute name and initial value.
    * @param name attribute name
    * @param value initial attribute value
    */
   public FndInteger(String name, Long value) {
      super(FndAttributeType.INTEGER, name, value);
   }

   /**
    * Create a new instance based on specified meta data.
    * @param meta attribute meta data
    */
   public FndInteger(FndAttributeMeta meta) {
      super(meta);
      setType(FndAttributeType.INTEGER);
   }

   /**
    * Create a new instance based on specified meta data. Also set initial attribute value.
    * @param meta meta data
    * @param value initial value
    */
   public FndInteger(FndAttributeMeta meta, Long value) {
      super(meta, value);
      setType(FndAttributeType.INTEGER);
   }

   /**
    * Creates a new attribute based on meta-data.
    * @param meta Meta-data to base the attribute on
    * @return The created attribute
    */
   @Override
   protected FndAttribute newAttribute(FndAttributeMeta meta) {
      return new FndInteger(meta);
   }

   /** Gets the value of the attribute.
    * @return Value
    */
   public Long getValue() {
      return (Long)internalGetValue();
   }

   /** Return the value of the attribute. If value is null, return defaultValue.
    *  @param defaultValue Value to return if attribute value is null.
    *  @return Attribute value.
    */
   public Long getValue(Long defaultValue) {
      return isNull() ? defaultValue : getValue();
   }

   /** Return the value of the attribute. If the value is null, return defaultValue.
    *  @param defaultValue Value to return if attribute value is null.
    *  @return Attribute value.
    */
   public long longValue(long defaultValue) {
      return isNull() ? defaultValue : getValue();
   }

   /** Sets the value of the attribute.
    * @param value Value
    */
   public void setValue(Long value) {
      internalSetValue(value);
   }

   /** Sets the value of the attribute.
    * @param value Value
    */
   public void setValue(long value) {
      internalSetValue(value);
   }

   /** Copies the value from another FndInteger.
    * @param field Attribute to copy value from.
    */
   public void setValue(FndInteger field) {
      setValue(field.getValue());
   }

   /** Sets the value of the attribute from another attribute.
    *  The main difference between assign and setValue is that the value will be cloned
    *  in assign (if necessary). The attribute value will never be cloned in setValue.
    * @param from Attribute to copy value from
    * @throws SystemException Thrown if cloning fails
    */
   public void assign(FndInteger from) throws SystemException {
      super.assign(from);
   }

   /** Set the value from a string containing an integer.
    * @param value String with an integer
    * @throws ParseException if <code>value</code> is of illegal format.
    */
   @Override
   public void parseString(String value) throws ParseException {
      try {
         if(value==null || value.length()==0) {
            this.value = null;
         }
         else {
            this.value = Long.valueOf(value);
         }
         set();
         setExistent();
      }
      catch (NumberFormatException ex) {
         throw new ParseException(ex, "ILLEGALINTVALUE: Illegal integer value (&1) for attribute &2.&3",
         value, getParentRecord().getName(), getName());
      }
   }

   /** Compares two attributes. Used for sorting.
    * @param attr Attribute to compare with
    * @return 0 if attribute values are equal, a value less than 0 if the value of
    * this attribute is less than the value of the specified attribute. A value greater than
    * 0 is returned if the value of this attribute is greater than the value of the specified attribute.
    */
   @Override
   public int compareTo(FndAttribute attr) {
      if (this == attr)
         return 0;

      if (attr instanceof FndInteger) {
         if (this.isNull() && attr.isNull())
            return 0;
         else if (!this.isNull())
            return this.getValue().compareTo(((FndInteger)attr).getValue());
      }
      return 1;
   }

   /** Return a FndSqlValue based on the value of this attribute
    *  @return new FndSqlValue
    */
   @Override
   protected FndSqlValue toFndSqlValue() {
      if (this.isNull()) {
         FndSqlValue val = new FndSqlValue(this.getName(), this.getSqlType());
         val.setNull();
         return val;
      }
      else {
         return new FndSqlValue(this.getName(), this.getValue());
      }
   }

   /**
    * Get value from database and set on this attribute
    * @param data an instance of FndSqlData interface to get value from
    * @param colNr column/parameter index number
    */
   @Override
   protected void setSqlValue(FndSqlData data, int colNr) throws IfsException {
      this.setValue(data.getLong(colNr));
   }

   /** Get SQL type
    */
   @Override
   protected FndSqlType getSqlType() {
      return FndSqlType.INTEGER;
   }

   /**
    * See {@link FndAttribute#createBetweenCondition(Object,Object) createBetweenCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createBetweenCondition(Long value1, Long value2) {
      return super.createBetweenCondition(value1, value2);
   }

   /**
    * See {@link FndAttribute#createEqualCondition(Object) createEqualCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createEqualCondition(Long value) {
      return super.createEqualCondition(value);
   }

   /**
    * See {@link FndAttribute#createGreaterThanCondition(Object) createGreaterThanCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createGreaterThanCondition(Long value) {
      return super.createGreaterThanCondition(value);
   }

   /**
    * See {@link FndAttribute#createGreaterThanOrEqualCondition(Object) createGreaterThanOrEqualCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createGreaterThanOrEqualCondition(Long value) {
      return super.createGreaterThanOrEqualCondition(value);
   }

   /**
    * See {@link FndAttribute#createLessThanCondition(Object) createLessThanCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createLessThanCondition(Long value) {
      return super.createLessThanCondition(value);
   }

   /**
    * See {@link FndAttribute#createLessThanOrEqualCondition(Object) createLessThanOrEqualCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createLessThanOrEqualCondition(Long value) {
      return super.createLessThanOrEqualCondition(value);
   }

   /**
    * See {@link FndAttribute#createNotEqualCondition(Object) createNotEqualCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createNotEqualCondition(Long value) {
      return super.createNotEqualCondition(value);
   }

   /**
    * Returns an FndInteger attribute holding the value represented by the specified String.
    * @param value the string to be parsed
    * @return a new instance of FndInteger with the specified value
    * @throws ParseException if the specified String has invalid format
    */
   public static FndInteger valueOf(String value) throws ifs.fnd.base.ParseException {
      FndInteger attr = new FndInteger();
      attr.parseString(value);
      return attr;
   }
}
