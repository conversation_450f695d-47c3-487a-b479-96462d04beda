/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;

import ifs.fnd.record.serialization.*;
import ifs.fnd.base.IfsException;
import ifs.fnd.base.EncryptionException;
import ifs.fnd.base.ParseException;
import java.util.List;

/**
 * <B>Framework internal class:</B> Used for accessing internal record and attribute methods.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public final class FndInternals {

   // All methods in this class are public instance methods, but the constructor
   // is private and is instantiated only once and stored as a static variable in
   // package ifs.fnd.internal -- the only package that can access methods in this class.

   static {
      FndInternals internals = new FndInternals();
      ifs.fnd.internal.FndRecordInternals.setFndInternals(internals);
      ifs.fnd.internal.FndAttributeInternals.setFndInternals(internals);
   }

   private FndInternals() {
   }

   /**
    * Add an attribute to a record.
    * @param rec Record to add attribute to.
    * @param attr Attribute to add.
    */
   public boolean add(FndAbstractRecord rec, FndAttribute attr) {
      return rec.add(attr);
   }

   /**
    * Get the attribute value as an object. (Does not work for FndBinary)
    * @param rec record to search for attribute in.
    * @param name Name of attribute to search for.
    */
   public Object getAttributeValue(FndAbstractRecord rec, String name) {
      return rec.getAttributeValue(name);
   }

   /**
    * Get the position (index) of an attribute in a record.
    * @param rec record to search for attribute in.
    * @param name Name of attribute to search for.
    */
   public int getAttributeIndex(FndAbstractRecord rec, String name) {
      return rec.getAttributeIndex(name);
   }

   /**
    * Add a record to the list of sub-records.
    * @param rec Record to add sub-record to.
    * @param record Record to add.
    */
   public boolean addRecord(FndAbstractRecord rec, FndAbstractRecord record) {
      return rec.addRecord(record);
   }

   /**
    * Remove an attribute from a record
    * @param rec record to remove attribute from
    * @param attr attribute to remove
    */
   public void remove(FndAbstractRecord rec, FndAttribute attr) {
      rec.removeAttribute(attr);
   }

   /**
    * Returns the dynamically added sub-records as a List
    * @param rec record to get sub-records for
    * @return list of all dynamically added sub-records
    */
   public List<? super FndAbstractRecord> getRecords(FndAbstractRecord rec) {
      return rec.getRecords();
   }

   /**
    * Format a record to XML stream.
    * @param rec the record to format
    * @param s XML serializer to append the contents of the record to
    * @param customAttr top level attribute (tested with "==" operator) which will be formatted by the custom formatter
    * @param customFormatter formatter that will format the custom attribute
    */
   public void formatToXml(FndAbstractRecord rec, FndXmlSerializer s, FndAttribute customAttr, FndAttributeXmlFormatter customFormatter) throws ParseException {
      rec.formatToXml(s, customAttr, customFormatter);
   }

   /**
    * Set the internal record on an aggregate.
    * @param agg Aggregate to set record on.
    * @param rec Record to set.
    */
   public void internalSetRecord(FndAbstractAggregate agg, FndAbstractRecord rec) {
      agg.internalSetRecord(rec);
   }

   /**
    * Get the internal record set on an aggregate.
    * @param agg Aggregate.
    * @return The record set on the aggregate.
    */
   public FndAbstractRecord internalGetRecord(FndAbstractAggregate agg) {
      return agg.internalGetRecord();
   }

   /**
    * Add a record to an array.
    * @param arr Array to add record to.
    * @param rec Record to add.
    * @return true
    */
   public boolean internalAdd(FndAbstractArray arr, FndAbstractRecord rec) {
      return arr.internalAdd(rec);
   }

   /**
    * Add a record to an array.
    * @param arr Array to add record to.
    * @param rec Record to add.
    * @param setDirty true if the array should be marked as dirty, false otherwise
    * @return true
    */
   public boolean internalAdd(FndAbstractArray arr, FndAbstractRecord rec, boolean setDirty) {
      return arr.internalAdd(rec, setDirty);
   }

   /**
    * Get a record from an array.
    * @param arr Array to get record from.
    * @param i Position in array.
    * @return The record on position i in arr.
    */
   public FndAbstractRecord internalGet(FndAbstractArray arr, int i) {
      return arr.internalGet(i);
   }

   /**
    * Removes and returns a record from an array.
    * @param arr Array to remove a record from.
    * @param i Array index
    * @return The removed record
    */
   public FndAbstractRecord internalRemove(FndAbstractArray arr, int i) {
      return arr.internalRemove(i);
   }

   /**
    * Get the internal records of an array as a List.
    * @param arr Array.
    * @return List of records in array.
    */
   public List<FndAbstractRecord> getInternalRecords(FndAbstractArray arr) {
      return arr.getInternalRecords();
   }

   /**
    * Add a list of records to an array.
    * @param arr Array to add records to.
    * @param records List of records to add.
    */
   public void load(FndAbstractArray arr, List<FndAbstractRecord> records) {
      arr.load(records);
   }

   /**
    * Set the internal value of an attribute. (Does not work for FndBinary.)
    * @param attr Attribute to set value on.
    * @param value Value to set on attribute.
    */
   public void internalSetValue(FndAttribute attr, Object value) {
      attr.internalSetValue(value);
   }

   /**
    * Get the internal value of an attribute. (Does not work for FndBinary.)
    * @param attr Attribute to get value from.
    * @return Value of the attribute.
    */
   public Object internalGetValue(FndAttribute attr) {
      return attr.internalGetValue();
   }

   /**
    * Set the internal counted value on attribute.
    * @param attr Attribute to set counted value on.
    * @param count Counted value.
    */
   public void setCount(FndAttribute attr, int count) {
      attr.setCount(count);
   }

   /**
    * Assign attribute values included in a compound reference.
    * @param ref1 compund reference to set attribute values in
    * @param ref2 compund reference to get attribute values from
    * @throws IfsException if there is a failure
    */
   public void internalAssign(FndCompoundReference ref1, FndCompoundReference ref2) throws IfsException {
      ref1.protectedAssign(ref2);
   }


   /**
    * Return a FndSqlValue based on the value of an attribute
    * @param attr attribute to set database value on
    * @return the SQL value
    */
   public FndSqlValue toFndSqlValue(FndAttribute attr) {
      return attr.toFndSqlValue();
   }
   
   /**
    * Sets the <code>FndAttribute.SET</code> flag on an attribute.
    * @param attr Attribute to set the flag on.
    */
   public void internalSet(FndAttribute attr) {
      attr.set();
   }

   /**
    * Get value from database and set on the specified attribute
    * @param attr attribute to set database value on
    * @param data an instance of FndSqlData interface to get value from
    * @param colNr column/parameter index number
    * @throws IfsException if there is a failure
    */
   public void setSqlValue(FndAttribute attr, FndSqlData data, int colNr) throws IfsException {
      attr.setSqlValue(data, colNr);
   }

   /**
    * Get SQL type for an attribute.
    * @param  attr attribute to get SQL type for.
    * @return An instance of FndSqlType representing SQL type of the attribute.
    */
   public FndSqlType getSqlType(FndAttribute attr) {
      return attr.getSqlType();
   }


   /**
    * Create an attribute with specified name.
    * @param name attribute name
    * @return new attribute instance
    */
   public FndAttribute newAttribute(String name) {
      return new FndAttribute(name);
   }

   /**
    * Create an attribute with specified type and ame.
    * @param type attribute type
    * @param name attribute name
    * @return new attribute instance
    */
   public FndAttribute newAttribute(String type, String name) {
      return new FndAttribute(type, name);
   }

   /**
    * Create an attribute with specified type, name and value.
    * @param type attribute types
    * @param name attribute name
    * @param value initial attribute value
    * @return new instance
    */
   public FndAttribute newAttribute(FndAttributeType type, String name, Object value) {
      return new FndAttribute(type, name, value);
   }


   /**
    * Formats the whole attribute into an Fnd Buffer.
    * @param attr attribute to format
    * @param stream Stream to which to write the resulting buffer
    * @throws ParseException Attribute cannot be serialized into a buffer
    */
   public void format(FndAttribute attr, FndTokenWriter stream) throws ParseException {
      // the default behaviour is to ignore CHANGED_VALUE flag and not release memeory
      attr.format(stream, FndAbstractRecord.IGNORE_CHANGED_VALUE, false);
   }

   /**
    * Parses an Fnd Buffer to get the attribute.
    * @param attr attribute to parse into
    * @param stream Stream from which to read the buffer
    * @throws ParseException Syntax error in buffer
    */
   public void parse(FndAttribute attr, FndTokenReader stream) throws ParseException {
      attr.parse(stream);
   }

   /**
    * Gets the identity of a record.
    * @param rec the record to get identity for
    * @return String identifying data contained in the record
    */
   public String getIdentity(FndAbstractRecord rec) {
      return rec.getIdentity();
   }

   /**
    * Assigns an identity to a record.
    * @param rec the record to assign the identity to
    * @param identity String identifying data contained in the record
    */
   public void setIdentity(FndAbstractRecord rec, String identity) {
      rec.setIdentity(identity);
   }

   /**
    * Gets database string representation of an attribute's value.
    * @param attr attribute to get database string representation for
    * @return string used to store the value of the attribute in the database,
    *         or null if the attribute has no value
    */
   public String toDatabaseString(FndAttribute attr) {
      return attr.toDatabaseString();
   }

   /**
    * Parses a database string to get an attribute's value.
    * @param attr attribute to parse into
    * @param dbValue database representation of the attribute value
    * @throws ParseException if the specified string contains an illegal value
    */
   public void parseDatabaseString(FndAttribute attr, String dbValue) throws ParseException {
      attr.parseDatabaseString(dbValue);
   }

   /**
    * Sets NOT_INSTALLED flag on an attribute.
    * @param attr attribute to set the flag on
    */
   public void setNotInstalled(FndAttribute attr) {
      attr.setNotInstalled();
   }

   /**
    * Gets the template record for a
    * {@link ifs.fnd.record.FndAbstractAggregate FndAbstractAggregate} object.
    */
   public FndAbstractRecord getTemplateRecord(FndAbstractAggregate agg) {
      return agg.getTemplateRecord();
   }

   /**
    * Encrypt given string value if global encryption is enabled.
    * @param value to be encrypted
    */
   public String encryptString(String value) throws EncryptionException{
      return FndEncrypter.encrypt(value);
   }

   /**
    * Sets a flag on a record indicating if the record is processed by importEntity operation.
    * @param rec the record to set the IMPORTING flag on
    * @param flag boolean flag
    */
   public void setImporting(FndAbstractRecord rec, boolean flag) {
      rec.setImporting(flag);
   }
}
