/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;

import ifs.fnd.base.*;
import ifs.fnd.buffer.BufferUtil;
import ifs.fnd.record.serialization.*;
import java.io.Serializable;
import java.util.ArrayList;

/**
 * Super-class for all attribute types like FndAlpha, FndDate of FndText.
 */
public class FndAttribute implements Cloneable, Serializable, FndSortField {

   /**
    * Meta-data of the attribute.
    */
   protected FndAttributeMeta meta;

   /**
    * Value of the attribute.
    */
   protected Object value;

   //Dirty flag. Set when the attribute is changed.
   private static final int DIRTY = 1;
   //Query flag
   private static final int QUERY = 2;
   //Exist flag. Non-existent attributes don't generate query conditions.
   private static final int EXIST = 4;
   //Indicates if the attribute has been set to a value or not (value might be null).
   private static final int SET = 8;
   //Indicates if the attribute must have a value
   private static final int MANDATORY = 16;
   //Indicates if the attribute may be updated
   private static final int UPDATEALLOWED = 32;
   //Transient (not-serialized) flag used to mark not-installed arrays and aggregates
   private static final int NOT_INSTALLED = 64;
   //Indicates if the attribute value has been changed
   private static final int CHANGED_VALUE = 128;

   //Current flag values
   private int flags = QUERY | UPDATEALLOWED;

   // The ordinal number for QueryResultCategory (QRC) is stored as 3 bits in the flags field.
   //
   //   0XXX0000 00000000 00000000 00000000
   //
   // The default value for QRC is 0 = INCLUDE.

   //Number of bits to shift the QRC ordinal in flags
   private static final int QRC_SHIFT = 28;

   //The mask used to retrieve QRC ordinal from from flags
   private static final int QRC_MASK = 7 << QRC_SHIFT;

   //The mask used to clear QRC bits in flags
   private static final int QRC_CLEAR_MASK = (1 << QRC_SHIFT) - 1;

   /**
    * Pointer to the parent record.
    */
   protected FndAbstractRecord rec = null;

   /**
    * Used to store attribute properties that are rarely used.
    */
   private ExtraInfo extra;

   /**
    * Class storing attribute properties that are rarely used.
    */
   private static class ExtraInfo implements Cloneable, Serializable {

      /**
       * Only used for attributes of UNKNOWN type.
       */
      private String type;

      /**
       * Set if a setState command supplied an unrecognized string.
       */
      private String unknownState;

      /**
       * Used to hold the value from a "select count(attr)" query.
       */
      private int count = -1;

      /**
       * Error message that describes why the current value is invalid.
       * If there are many errors on the current value then they are separated with new-line characters.
       * If this variable is null then the current value is assumed to be valid.
       */
      private String invalidValueInfo;

      /**
       * Clones this instance of ExtraInfo.
       * @return A shallow copy of this object
       * @throws CloneNotSupportedException if cloning is not supported
       */
      @Override
      public Object clone() throws CloneNotSupportedException {
         return (ExtraInfo) super.clone(); //Shallow copy
      }
   }

   /**
    * Gets/creates the instance of ExtraInfo for this attribute.
    */
   private ExtraInfo getOrCreateExtraInfo() {
      if(extra == null)
         extra = new ExtraInfo();
      return extra;
   }

   protected FndAttribute() {
   }

   protected FndAttribute(String name) {
      // Assume text if no type given
      this.meta = new FndAttributeMeta(FndAttributeType.TEXT, name);
   }

   protected FndAttribute(String type, String name) {
      FndAttributeType attrType = FndAttributeType.toAttributeType(type);
      if (attrType == null) {
         attrType = FndAttributeType.UNKNOWN;
         getOrCreateExtraInfo().type = type;
      }
      this.meta = new FndAttributeMeta(attrType, name);
      setExistent();
   }

   protected FndAttribute(String type, String name, Object value) {
      this(type, name);
      this.value = value;
      set();
   }

   protected FndAttribute(FndAttributeType type) {
      this.meta = new FndAttributeMeta(type);
   }

   protected FndAttribute(FndAttributeType type, String name) {
      this.meta = new FndAttributeMeta(type, name);
   }

   protected FndAttribute(FndAttributeType type, String name, Object value) {
      this(type, name);
      this.value = value;
      set();
      setExistent();
   }

   protected FndAttribute(FndAttributeMeta meta) {
      this.meta = meta;
      if(meta.isMandatory())
         setMandatory(true);
      if(!meta.isUpdateAllowed())
         setUpdateAllowed(false);
      //setUpdateAllowed(!meta.isServerGenerated() || meta.isUpdateAllowed());
   }

   protected FndAttribute(FndAttributeMeta meta, Object value) {
      this(meta);
      this.value = value;
      set();
      setExistent();
   }

   /**
    * Creates a new attribute of the same type using supplied meta-data.
    * @param meta Meta-data
    * @return Created attribute
    */
   protected FndAttribute newAttribute(FndAttributeMeta meta) {
      return new FndAttribute(meta);
   }

   /**
    * Check if a certain flag has been set.
    * @param flag The flag to check
    * @return True if flag is set
    */
   private boolean isFlagSet(int flag) {
      return ((flags & flag) != 0);
   }

   /**
    * Set a flag
    * @param flag
    */
   private void setFlag(int flag) {
      flags |= flag;
   }

   /**
    * Unset a flag
    * @param flag
    */
   private void unsetFlag(int flag) {
      flags &= ~flag;
   }

   /**
    * Retrieve the QRC ordinal number from flags.
    * @return an integer in range 0 .. 7
    */
   private int getQrc() {
      return (flags & QRC_MASK) >>> QRC_SHIFT;
   }

   /**
    * Store a QRC ordinal number in flags.
    * @param ordinal an integer in range 0 .. 7
    */
   private void setQrc(int ordinal) {
      flags = (flags & QRC_CLEAR_MASK) | (ordinal << QRC_SHIFT);
   }

   /**
    * Marks the value of this attribute as invalid.
    * The specified text will be not translated and it will replace
    * the existing invalid value info, if any.
    * @param info text that describes why the current value is invalid,
    *        or null to clear the invalid value status for this attribute.
    * @see #addInvalidValueInfo
    * @see #invalidate
    * @see #getInvalidValueInfo
    */
   public final void setInvalidValueInfo(String info) {
      getOrCreateExtraInfo().invalidValueInfo = info;
      setExistent();
   }

   /**
    * Marks the value of this attribute as invalid.
    * The specified text will be not translated.
    * If this attribute is already marked as invalid then the specified text will
    * be appended to the existing invalid value info.
    * @param info text that describes why the current value is invalid.
    * @see #setInvalidValueInfo
    * @see #invalidate
    * @see #getInvalidValueInfo
    */
   public final void addInvalidValueInfo(String info) {
      getOrCreateExtraInfo();

      if(extra.invalidValueInfo == null)
         extra.invalidValueInfo = info;
      else
         extra.invalidValueInfo += "\r\n" + info;
      setExistent();
   }

   /**
    * Gets the invalid value info for the current value.
    * @return text that describes why the current attribute value is invalid, or null
    *         if the value is assumed to be valid.
    * @see #invalidate
    */
   public final String getInvalidValueInfo() {
      return extra == null ? null : extra.invalidValueInfo;
   }

   /**
    * Marks the value of this attribute as invalid.
    * The method does not performs translation of the specified message.
    * @param msg text that describes why the current value is invalid.
    * @see #getInvalidValueInfo
    * @deprecated Use the method with a FndTranslatableText argument
    */
   public final void invalidate(String msg) {
      String info = FndTranslation.translate(msg);
      addInvalidValueInfo(info);
   }


   /**
    * Marks the value of this attribute as invalid.
    * The method does not performs translation of the specified message.
    * @param msg text that describes why the current value is invalid.
    * @param p1 first placeholder text
    * @see #getInvalidValueInfo
    * @deprecated Use the method with a FndTranslatableText argument
    */
   public final void invalidate(String msg, String p1) {
      String info = FndTranslation.translate(msg, p1);
      addInvalidValueInfo(info);
   }

   /**
    * Marks the value of this attribute as invalid.
    * The method does not performs translation of the specified message.
    * @param msg text that describes why the current value is invalid.
    * @param p1 first placeholder text
    * @param p2 second placeholder text
    * @see #getInvalidValueInfo
    * @deprecated Use the method with a FndTranslatableText argument
    */
   public final void invalidate(String msg, String p1, String p2) {
      String info = FndTranslation.translate(msg, p1, p2);
      addInvalidValueInfo(info);
   }

   /**
    * Marks the value of this attribute as invalid.
    * The method dose not performs translation of the specified message.
    * @param msg text that describes why the current value is invalid.
    * @param p1 first placeholder text
    * @param p2 second placeholder text
    * @param p3 third placeholder text
    * @see #getInvalidValueInfo
    * @deprecated Use the method with a FndTranslatableText argument
    */
   public final void invalidate(String msg, String p1, String p2, String p3) {
      String info = FndTranslation.translate(msg, p1, p2, p3);
      addInvalidValueInfo(info);
   }

   /**
    * Marks the value of this attribute as invalid.
    * The method dose not performs translation of the specified message.
    * @param msg text that describes why the current value is invalid.
    * @param p1 first placeholder text
    * @param p2 second placeholder text
    * @param p3 third placeholder text
    * @param p4 fourth placeholder text
    * @see #getInvalidValueInfo
    * @deprecated Use the method with a FndTranslatableText argument
    */
   public final void invalidate(String msg, String p1, String p2, String p3, String p4) {
      String info = FndTranslation.translate(msg, p1, p2, p3, p4);
      addInvalidValueInfo(info);
   }


   /**
    * Marks the value of this attribute as invalid.
    * The method dose not performs translation of the specified message.
    * @param msg text that describes why the current value is invalid.
    * @param p1 first placeholder text
    * @param p2 second placeholder text
    * @param p3 third placeholder text
    * @param p4 fourth placeholder text
    * @param p5 fifth placeholder text
    * @see #getInvalidValueInfo
    * @deprecated Use the method with a FndTranslatableText argument
    */
   public final void invalidate(String msg, String p1, String p2, String p3, String p4, String p5) {
      String info = FndTranslation.translate(msg, p1, p2, p3, p4, p5);
      addInvalidValueInfo(info);
   }

   /**
    * Marks the value of this attribute as invalid.
    * The method performs translation of the specified message.
    * @param msg text that describes why the current value is invalid.
    * @see #getInvalidValueInfo
    * @since 4.1.0
    */
   public final void invalidate(FndTranslatableText msg) {
      addInvalidValueInfo(msg!=null ? msg.translate() : null);
   }

   /**
    * Marks the value of this attribute as invalid.
    * The method performs translation of the specified message.
    * @param msg text that describes why the current value is invalid.
    * @param p1 first placeholder text
    * @see #getInvalidValueInfo
    * @since 4.1.0
    */
   public final void invalidate(FndTranslatableText msg, String p1) {
      addInvalidValueInfo(msg!=null ? msg.translate(p1) : null);
   }

   /**
    * Marks the value of this attribute as invalid.
    * The method performs translation of the specified message.
    * @param msg text that describes why the current value is invalid.
    * @param p1 first placeholder text
    * @param p2 second placeholder text
    * @see #getInvalidValueInfo
    * @since 4.1.0
    */
   public final void invalidate(FndTranslatableText msg, String p1, String p2) {
      addInvalidValueInfo(msg!=null ? msg.translate(p1, p2) : null);
   }

   /**
    * Marks the value of this attribute as invalid.
    * The method performs translation of the specified message.
    * @param msg text that describes why the current value is invalid.
    * @param p1 first placeholder text
    * @param p2 second placeholder text
    * @param p3 third placeholder text
    * @see #getInvalidValueInfo
    * @since 4.1.0
    */
   public final void invalidate(FndTranslatableText msg, String p1, String p2, String p3) {
      addInvalidValueInfo(msg!=null ? msg.translate(p1, p2, p3) : null);
   }

   /**
    * Marks the value of this attribute as invalid.
    * The method performs translation of the specified message.
    * @param msg text that describes why the current value is invalid.
    * @param p1 first placeholder text
    * @param p2 second placeholder text
    * @param p3 third placeholder text
    * @param p4 fourth placeholder text
    * @see #getInvalidValueInfo
    * @since 4.1.0
    */
   public final void invalidate(FndTranslatableText msg, String p1, String p2, String p3, String p4) {
      addInvalidValueInfo(msg!=null ? msg.translate(p1, p2, p3, p4) : null);
   }


   /**
    * Marks the value of this attribute as invalid.
    * The method performs translation of the specified message.
    * @param msg text that describes why the current value is invalid.
    * @param p1 first placeholder text
    * @param p2 second placeholder text
    * @param p3 third placeholder text
    * @param p4 fourth placeholder text
    * @param p5 fifth placeholder text
    * @see #getInvalidValueInfo
    * @since 4.1.0
    */
   public final void invalidate(FndTranslatableText msg, String p1, String p2, String p3, String p4, String p5) {
      addInvalidValueInfo(msg!=null ? msg.translate(p1, p2, p3, p4, p5) : null);
   }


   /**
    * Checks if the attribute has a value.
    * @return true if the attribute has a value
    * @throws ValidationException The attribute contains no value
    */
   public final boolean checkValuePresent() throws ValidationException {
      return checkValuePresent((FndTranslatableText)null, true);
   }

   /**
    * Checks if the attribute has a value.
    * @param abort true if execution should be aborted, false to store error message
    *        as invalid attribute info
    * @return true if the attribute has a value, false otherwise
    * @throws ValidationException The attribute contains no value
    * @see #invalidate
    */
   public final boolean checkValuePresent(boolean abort) throws ValidationException {
      return checkValuePresent((FndTranslatableText)null, abort);
   }

   /**
    * Checks if the attribute has a value, if not an exception is thrown with the provided message.
    * @param msg Error message
    * @return true if the attribute has a value
    * @throws ValidationException The attribute contains no value
    * @deprecated Use the method with a FndTranslatableText argument
    */
   public final boolean checkValuePresent(String msg) throws ValidationException {
      return checkValuePresent(msg, true);
   }

   /**
    * Checks if the attribute has a value, if not an exception is thrown with the provided message.
    * @param msg Error message
    * @return true if the attribute has a value
    * @throws ValidationException The attribute contains no value
    * @since 4.1.0
    */
   public final boolean checkValuePresent(FndTranslatableText msg) throws ValidationException {
      return checkValuePresent(msg, true);
   }

   /**
    * Checks if the attribute has a value.
    * @param msg Error message
    * @param abort true if execution should be aborted, false to store error message
    *        as invalid attribute info
    * @return true if the attribute has a value, false otherwise
    * @throws ValidationException The attribute contains no value
    * @see #invalidate
    * @deprecated Use the method with a FndTranslatableText argument
    */
   public final boolean checkValuePresent(String msg, boolean abort) throws ValidationException {
      if ((this.isSet() && this.isNull()) || !this.isSet()) {
         if (msg == null)
            validationError(abort, Texts.MUSTHAVEVALUE, meta.getFullName(), null, null, null, null);
         else
            validationError(abort, msg, meta.getFullName(), null, null, null, null);
         return false;
      }
      return true;
   }

   /**
    * Checks if the attribute has a value.
    * @param msg Error message
    * @param abort true if execution should be aborted, false to store error message
    *        as invalid attribute info
    * @return true if the attribute has a value, false otherwise
    * @throws ValidationException The attribute contains no value
    * @see #invalidate
    * @since 4.1.0
    */
   public final boolean checkValuePresent(FndTranslatableText msg, boolean abort) throws ValidationException {
      if (msg == null)
         msg = Texts.MUSTHAVEVALUE;
      if ((this.isSet() && this.isNull()) || !this.isSet()) {
         validationError(abort, msg, meta.getFullName(), null, null, null, null);
         return false;
      }
      return true;
   }

   /**
    * Checks the length of the value of the attribute against the max length specified in the meta-data.
    * @deprecated This method does nothing. Validation of range is implemented by database layer.
    */
   public final void checkRange() throws ValidationException {
   }

   /**
    * Checks the length of the value of the attribute against the max length specified in the meta-data.
    * @param abort true if execution should be aborted, false to store error message
    *        as invalid attribute info
    * @deprecated This method does nothing. Validation of range is implemented by database layer.
    */
   public final void checkRange(boolean abort) {
   }

   /**
    * Checks the length of the value of the attribute against the max length specified in the meta-data.
    * @param msg Error message
    * @deprecated Use the method with a FndTranslatableText argument
    * @deprecated This method does nothing. Validation of range is implemented by database layer.
    */
   public final void checkRange(String msg) {
   }

   /**
    * Checks the length of the value of the attribute against the max length specified in the meta-data.
    * @param msg Error message
    * @since 4.1.0
    * @deprecated This method does nothing. Validation of range is implemented by database layer.
    */
   public final void checkRange(FndTranslatableText msg) {
   }

   /**
    * Checks the length of the value of the attribute against the max length specified in the meta-data.
    * @param smsg Error message
    * @param abort true if execution should be aborted, false to store error message
    *        as invalid attribute info
    * @see #invalidate
    * @deprecated Use the method with a FndTranslatableText argument
    * @deprecated This method does nothing. Validation of range is implemented by database layer.
    */
   public final void checkRange(String smsg, boolean abort) {
   }

   /**
    * Checks the length of the value of the attribute against the max length specified in the meta-data.
    * @param msg Error message
    * @param abort true if execution should be aborted, false to store error message
    *        as invalid attribute info
    * @since 4.1.0
    * @deprecated This method does nothing. Validation of range is implemented by database layer.
    */
   public final void checkRange(FndTranslatableText msg, boolean abort) {
   }

   /**
    * Checks if the attribute value has been modified.
    * @throws ValidationException Attribute has been modified
    */
   public final void checkNotModified() throws ValidationException {
      checkNotModified((FndTranslatableText)null, true);
   }

   /**
    * Checks if the attribute value has been modified.
    * @param abort true if execution should be aborted, false to store error message
    *        as invalid attribute info
    * @throws ValidationException Attribute has been modified
    * @see #invalidate
    */
   public final void checkNotModified(boolean abort) throws ValidationException {
      checkNotModified((FndTranslatableText)null, abort);
   }

   /**
    * Checks if the attribute value has been modified.
    * @param msg Error message
    * @throws ValidationException Attribute has been modified
    * @deprecated Use the method with a FndTranslatableText argument
    */
   public final void checkNotModified(String msg) throws ValidationException {
      checkNotModified(msg, true);
   }

   /**
    * Checks if the attribute value has been modified.
    * @param msg Error message
    * @throws ValidationException Attribute has been modified
    * @since 4.1.0
    */
   public final void checkNotModified(FndTranslatableText msg) throws ValidationException {
      checkNotModified(msg, true);
   }

   /**
    * Checks if the attribute value has been modified.
    * @param msg Error message
    * @param abort true if execution should be aborted, false to store error message
    *        as invalid attribute info
    * @throws ValidationException Attribute has been modified
    * @see #invalidate
    * @deprecated Use the method with a FndTranslatableText argument
    */
   public final void checkNotModified(String msg, boolean abort) throws ValidationException {
      if (isDirty()) {
         if (msg == null)
            validationError(abort, Texts.NOMODIFY, meta.getFullName(), null, null, null, null);
         else
            validationError(abort, msg, meta.getFullName(), null, null, null, null);
      }
   }

   /**
    * Checks if the attribute value has been modified.
    * @param msg Error message
    * @param abort true if execution should be aborted, false to store error message
    *        as invalid attribute info
    * @throws ValidationException Attribute has been modified
    * @see #invalidate
    * @since 4.1.0
    */
   public final void checkNotModified(FndTranslatableText msg, boolean abort) throws ValidationException {
      if (isDirty()) {
         if (msg == null)
            msg = Texts.NOMODIFY;
         validationError(abort, msg, meta.getFullName(), null, null, null, null);
      }
   }

   /**
    * Performs all applicable primitive attribute checks based on meta-data
    * and flags about the attribute.
    * For example checkValuePresent will be performed if the attribute is mandatory,
    * checkFormat if it is of string type etc.
    * Attributes in aggregated records will be validated recursively.
    * Only dirty attributes will be validated.
    * @throws ValidationException if validation fails
    */
   public final void validate() throws ValidationException {
      validate(true);
   }

   /**
    * Performs all applicable primitive attribute checks based on meta-data
    * and flags about the attribute.
    * For example checkValuePresent will be performed if the attribute is mandatory,
    * checkFormat if it is of string type etc.
    * Attributes in aggregated records will be validated recursively.
    * Only dirty attributes will be validated.
    * @param abort true if execution should be aborted, false to store error message
    *        as invalid attribute info
    * @throws ValidationException if validation fails
    * @see #invalidate
    */
   public final void validate(boolean abort) throws ValidationException {
      if (!isDirty()) {
         return; //Validate only dirty attributes
      }

      if(rec == null && this instanceof FndAggregate)
         return; // Do not validate untyped (non-persistent) aggregates

      assert(rec != null);
      FndRecordState state = rec.getState();
      //Check if simple attribute may be updated
      if (state == FndRecordState.MODIFIED_RECORD && !isCompound() && isDirty() && !isUpdateAllowed()) {
         validationError(abort, Texts.VALIDATEATTRUPDATE, getName(), null, null, null, null);
         return;
      }
      //Check that a value is present for 'mandatory' attributes
      if (meta.isMandatory() || isMandatory()) {
         if (state == FndRecordState.NEW_RECORD) {
            if (!meta.isServerGenerated() && !meta.isEntityAttribute() && !(this instanceof FndGuid))
               if(!checkValuePresent(abort))
                  return;
         }
         else if (state == FndRecordState.MODIFIED_RECORD) {
            if(!checkValuePresent(abort))
               return;
         }
      }
      checkFormat(abort);

      //Validate nested existent attributes
      if (getType() == FndAttributeType.ARRAY) {
         //Call Validate for every record included in the array
         FndAbstractArray arr = (FndAbstractArray) this;
         for (int i = 0; i < arr.size(); i++) {
            FndAbstractRecord record = (FndAbstractRecord) arr.getInternalRecords().get(i);
            //Check if insert is allowed (for new records)
            if (state != FndRecordState.NEW_RECORD && !isUpdateAllowed() && record.getState() == FndRecordState.NEW_RECORD) {
               //TODO: The following line aborts execution. This may be changed in the future.
               validationError(true, Texts.VALIDATEARRINSERT, getName(), null, null, null, null);
            }
            //Validate record
            record.validate(abort);
         }
      }
      else if (getType() == FndAttributeType.AGGREGATE) {
         //Call Validate for the aggregated record
         FndAbstractAggregate aggr = (FndAbstractAggregate) this;
         FndAbstractRecord record = aggr.internalGetRecord();
         if (record != null) {
            //Check if insert is allowed
            if (state != FndRecordState.NEW_RECORD && !isUpdateAllowed() && record.getState() == FndRecordState.NEW_RECORD) {
               //TODO: The following line aborts execution. This may be changed in the future.
               validationError(true, Texts.VALIDATEAGGINSERT, getName(), null, null, null, null);
            }
            //Validate record
            record.validate(abort);
         }
      }
   }

   /**
    * Make sure all mandatory attributes have a value.
    * Recursively check all child records.
    * @throws ValidationException
    */
   final void validateMandatory() throws ValidationException {
      validateMandatory(true);
   }

   /**
    * Make sure all mandatory attributes have a value.
    * Recursively check all child records.
    * @param abort true if execution should be aborted, false to store error message
    *        as invalid attribute info
    * @throws ValidationException
    * @see #invalidate
    */
   final void validateMandatory(boolean abort) throws ValidationException {
      if (isMandatory()) {
         checkValuePresent(abort);
      }
      if (this instanceof FndAbstractArray) {
         FndAbstractArray arr = (FndAbstractArray) this;
         for (int i = 0; i < arr.size(); i++) {
            arr.internalGet(i).validateMandatory(abort);
         }
      } else if (this instanceof FndAbstractAggregate && !this.isNull()) {
         FndAbstractRecord record = ((FndAbstractAggregate) this).internalGetRecord();
         if (record != null)
            record.validateMandatory(abort);
      }
   }

   /**
    * Performs the checks (upper case) indicated by the meta-data.
    * @throws ValidationException A format check failed
    */
   public final void checkFormat() throws ValidationException {
      checkFormat(true);
   }

   /**
    * Performs the checks (upper case) indicated by the meta-data.
    * @param abort true if execution should be aborted, false to store error message
    *        as invalid attribute info
    * @throws ValidationException A format check failed
    * @see #invalidate
    */
   public final void checkFormat(boolean abort) throws ValidationException {
      //check case of all string types
      if (!isNull() && meta.isUpperCase() && (meta.getType() == FndAttributeType.ALPHA || meta.getType() == FndAttributeType.TEXT)) {
         checkUpperCase(abort);
      }
   }

   /**
    * Checks if the value is uppercase.
    * @throws ValidationException The value is not upper case
    */
   public final void checkUpperCase() throws ValidationException {
      checkUpperCase(true);
   }

   /**
    * Checks if the value is uppercase.
    * @param abort true if execution should be aborted, false to store error message
    *        as invalid attribute info
    * @throws ValidationException The value is not upper case
    * @see #invalidate
    */
   public final void checkUpperCase(boolean abort) throws ValidationException {
      if (!value.toString().equals(value.toString().toUpperCase())) {
         validationError(abort, Texts.UPPERVALUE, value.toString(), meta.getFullName(), null, null, null);
      }
   }

   /**
    * Throws ValidationException if abort is true, otherwise marks this attribute as invalid.
    * @deprecated Use the method with a FndTranslatableText argument
    */
   private void validationError(boolean abort, String msg, String p1, String p2, String p3, String p4, String p5) throws ValidationException {
      if(abort)
         throw new ValidationException(null, msg, p1, p2, p3, p4, p5);
      else
         invalidate(msg, p1, p2, p3, p4, p5);
   }

   /**
    * Throws ValidationException if abort is true, otherwise marks this attribute as invalid.
    * @since 4.1.0
    */
   private void validationError(boolean abort, FndTranslatableText msg, String p1, String p2, String p3, String p4, String p5) throws ValidationException {
      if(abort)
         throw new ValidationException(null, msg, p1, p2, p3, p4, p5);
      else
         invalidate(msg, p1, p2, p3, p4, p5);
   }

   /**
    * Returns the length of the attribute. This method should be overidden in all attribute types that
    * define the length to be anything other than the length of the value.toString().
    * @return Length
    */
   public int getLength() {
      if (!isNull() && (meta.getType() == FndAttributeType.ALPHA || meta.getType() == FndAttributeType.TEXT))
         return (this.value.toString().length());
      else
         return -1;
   }

   /**
    * Get the parent record for this attribute.
    * @return parent record
    */
   public final FndAbstractRecord getParentRecord() {
      return this.rec;
   }

   protected final void setParentRecord(FndAbstractRecord rec) {
      this.rec = rec;
   }

   /**
    * Copy the value of an attribut to this attribute (attributes must be of the same type).
    * (Override this method for attributes with non-standard value storage (arrays and aggregates)).
    * @param from Attribute to copy value from.
    * @throws SystemException
    */
   protected void assign(FndAttribute from) throws SystemException {
      try {
         if (from.isSet())
            internalSetValue(from.cloneValue());
      } catch (CloneNotSupportedException e) {
         throw new SystemException(e, "ATTRASSIGN:Could not assign &1 to &2.", from.getName(), getName());
      }
   }

   /**
    * Copy the attribute flags and value.
    * Depending on the implementation of cloneValue() the attribute value may
    * be copied or cloned.
    * @param target Attribute to copy to.
    * @param cloning true - clone value if necessary, false - never clone value
    */
   protected void copy(FndAttribute target, boolean cloning) throws CloneNotSupportedException, SystemException {
      target.flags = flags;
      target.extra = extra == null ? null : (ExtraInfo) extra.clone();

      if (cloning)
         target.value = cloneValue(); //Copy (possibly clone) attribute value.
      else
         target.value = value;
   }

   /**
    * Clone the attribute value.
    * This method must be overridden by subclasses where the value is a mutable object.
    * @return The value of the attribute (no cloning in this case).
    * @throws CloneNotSupportedException if cloning is not implemented
    */
   protected Object cloneValue() throws CloneNotSupportedException {
      /** There is no way for us to clone the value of this attribute.
       *  We do not know what the type of the value is.
       */
      return value;
   }

   /**
    * Copies the attribute.
    * The internal value of the attribute will be cloned if necessary.
    * @return A copy of the attribute.
    * @throws CloneNotSupportedException if cloning is not supported
    */
   @Override
   public Object clone() throws CloneNotSupportedException {
      FndAttribute attr = (FndAttribute) super.clone(); //Shallow copy
      attr.rec = null; //Disconnect attribute from parent record
      attr.value = cloneValue(); //Clone internal value
      return attr;
   }

   /**
    * Returns the name of the attribute
    * @return Name
    */
   public final String getName() {
      return meta.getName();
   }

   /**
    * Sets the name of the attribute
    * @param name Name
    */
   protected final void setName(String name) {
      meta.setName(name);
   }

   /**
    * Returns type of the attribute
    * @return Type
    */
   public final FndAttributeType getType() {
      if (meta != null)
         return meta.getType();
      else
         return null;
   }

   /**
    * Sets type of the attribute
    * @param type Type
    */
   protected final void setType(FndAttributeType type) {
      meta.setType(type);
   }

   /**
    * Gets inner types from the meta-data
    * @return Inner types
    */
   protected final FndAttributeType[] getInnerTypes() {
      return meta.getInnerTypes();
   }

   /**
    * Returns database column names from the meta-data.
    * @return Column names
    */
   protected final String[] getColumns() {
      return meta.getColumns();
   }

   /**
    * Returns string representation of attribute state as indicated by various flags.
    * @return State
    */
   public final String getState() {
      if (extra != null && extra.unknownState != null)
         return extra.unknownState;
      else {
         String state = "";

         if (isDirty())
            state = FndSerializeConstants.DIRTY_MARKER;
         else if (isQuery()) {
            state = FndSerializeConstants.QUERY_MARKER;
            state += FndQueryResultCategory.getInstance(getQrc()).toString();
         }
         if (isMandatory()) // && !meta.isServerGenerated())
            state += FndSerializeConstants.MANDATORY_MARKER;
         if (!isUpdateAllowed())
            state += FndSerializeConstants.UPDATE_NOT_ALLOWED_MARKER;

         return state;
      }
   }

   /**
    * Sets the attribute state.
    * @param state String to be parsed for state flags
    */
   public final void setState(String state) {
      if (state == null) {
         setQuery(false);
         include();
         setDirty(false);
      }
      else if ("IN".equals(state) || "OUT".equals(state) || "IN_OUT".equals(state) || state.startsWith("__")) {
         getOrCreateExtraInfo().unknownState = state;
      }
      else {
         setQuery(state.indexOf(FndSerializeConstants.QUERY_MARKER) >= 0);
         include(FndQueryResultCategory.getInstance(state));
         setDirty(state.indexOf(FndSerializeConstants.DIRTY_MARKER) >= 0);
         setMandatory(state.indexOf(FndSerializeConstants.MANDATORY_MARKER) >= 0);
         setUpdateAllowed(state.indexOf(FndSerializeConstants.UPDATE_NOT_ALLOWED_MARKER) < 0);
      }
   }

   /**
    * Returns meta-data for this attribute.
    * @return Meta-data
    */
   public final FndAttributeMeta getMeta() {
      return meta;
   }

   /**
    * Checks if this is a key.
    * @return true if attribute is key
    */
   public final boolean isKey() {
      return meta.isKey();
   }

   /**
    * Checks the dirty flag.
    * @return true if dirty
    */
   public final boolean isDirty() {
      return isFlagSet(DIRTY);
   }

   /**
    * Checks if this attribute is an array.
    * @return false
    */
   public boolean isVector() {
      return false;
   }

   /**
    * Checks the exist flag.
    * An attribute may be non-existent because a client application did not send it to
    * the server or because it has explicitly been set so.
    * @return true if the exist flag is set, false otherwise
    * @see #setNonExistent
    */
   public final boolean exist() {
      return isFlagSet(EXIST);
   }

   /**
    * Clears the contents of this attribute.
    * The method nullifies the value and sets the flag field to the default value.
    */
   public void clear() {
      value = null;
      flags = QUERY | UPDATEALLOWED;
      extra = null;
   }

   /**
    * Clears the exist flag.
    * A non-existent attribute is ignored by most framework methods,
    * for example such attribute is not saved in the database and is not sent back
    * to the client application.
    * @see #exist
    * @see FndAbstractRecord#setNonExistent
    */
   public void setNonExistent() {
      unsetFlag(EXIST);
   }

   /**
    * Sets the exist flag.
    * @see #exist
    */
   public final void setExistent() {
      setFlag(EXIST);
   }

   /**
    * Sets the CHANGED_VALUE flag on this attribute.
    */
   final void setChangedValue() {
      setFlag(CHANGED_VALUE);
   }

   /**
    * Gets the CHANGED_VALUE flag for this attribute.
    */
   final boolean getChangedValueFlag() {
      return isFlagSet(CHANGED_VALUE);
   }

   /**
    * Check if the attribute may be updated.
    * @return true if the attribute value may be updated
    */
   public final boolean isUpdateAllowed() {
      //for new records attributes with the SERVERGENERATED flag set to false can be inserted (==updated)
      if(rec!=null && !isCompound()) {
         FndRecordState recordState = rec.getState();
         if(recordState==FndRecordState.NEW_RECORD)
            return !meta.isServerGenerated();
      }
      return isFlagSet(UPDATEALLOWED);
   }

   /**
    * Set (or unset) the updateable flag on the attribute.
    * This affects buffer serialization and validations.
    * @param updateable indicates if the attribute is updateable
    */
   public final void setUpdateAllowed(boolean updateable) {
      if (updateable)
         setFlag(UPDATEALLOWED);
      else {
         unsetFlag(UPDATEALLOWED);
         setExistent();
      }

   }

   /**
    * Check if the attribute must have a value set
    * @return true if the attribute must have a value
    */
   public final boolean isMandatory() {
      return isFlagSet(MANDATORY);
   }

   /**
    * Set (or unset) the mandatory flag on the attribute.
    * This affects buffer serialization and validations.
    * @param mandatory indicates if mandatory should be true or false
    */
   public final void setMandatory(boolean mandatory) {
      if (mandatory) {
         setFlag(MANDATORY);
         setExistent();
      }
      else
         unsetFlag(MANDATORY);
   }

   /**
    * Sets NOT_INSTALLED flag on the attribute.
    */
   final void setNotInstalled() {
      setFlag(NOT_INSTALLED);
   }

   /**
    * Check if this (compound) attribute is installed.
    * @return true if the attribute is not installed.
    */
   final boolean isNotInstalled() {
      return isFlagSet(NOT_INSTALLED);
   }

   /**
    * Sets the dirty flag.
    */
   public final void setDirty() {
      setDirty(true);
   }

   /**
    * Sets or clears the dirty flag.
    * @param dirty Value to set the dirty flag to
    */
   public final void setDirty(boolean dirty) {
      if (dirty) {
         setFlag(DIRTY);
         setExistent();
      }
      else
         unsetFlag(DIRTY);

      if (dirty && rec != null)
         rec.propagateModify(isCompound()); //propagate modification to parent record
   }

   /**
    * Sets or clears the query flag.
    * @param query Value to set the query flag to
    */
   public final void setQuery(boolean query) {
      if (query)
         setFlag(QUERY);
      else
         unsetFlag(QUERY);
   }

   /**
    * Checks the query flag
    * @return true if query flag is set
    */
   public final boolean isQuery() {
      return isFlagSet(QUERY);
   }

   /**
    * Sets the exclude flag. Also sets the existent flag.
    * <P>See class {@link ifs.fnd.record.FndQueryResultCategory FndQueryResultCategory} for example of usage.
    */
   public final void exclude() {
      include(FndQueryResultCategory.EXCLUDE);
   }

   /**
    * Clears the exclude flag. Also sets the existent flag.
    * <P>See class {@link ifs.fnd.record.FndQueryResultCategory FndQueryResultCategory} for example of usage.
    */
   public final void include() {
      include(FndQueryResultCategory.INCLUDE);
   }

   /**
    * Sets the include category for this attribute. Also sets the existent flag.
    * <P>See class {@link ifs.fnd.record.FndQueryResultCategory FndQueryResultCategory} for example of usage.
    * @param  category any of the <code>FndQueryResultCategory</code>
    * enumeration values.
    */
   public final void include(FndQueryResultCategory category) {
      setQrc(category.getOrdinal());
      setExistent();
   }

   /** Checks the exclude flag.
    * @return true if exclude is set
    */
   public final boolean isExcluded() {
      return getQrc() == FndQueryResultCategory.EXCLUDE.getOrdinal();
   }

   /** Checks if the attribute has a null value.
    *  (Use isSet() if you want to check if the attribute has been given a value.)
    * @return true if no value
    */
   public boolean isNull() {
      return value == null;
   }

   /**
    * Clears attribute value.
    */
   public void setNull() {
      internalSetValue(null);
   }

   /**
    * Sets the value of the attribute. Use the typed setValue functions for
    * typed attributes instead.
    * @param value Value to set the attribute to
    */
   protected final void internalSetValue(Object value) {
      this.value = value;
      set();
      setDirty();
      setExistent();
      setQuery(false);
      setChangedValue();
   }

   /**
    * Gets the value of the attribute. Use the typed getValue function from
    * sub-classes when working with typed attributes.
    * @return Value
    */
   protected final Object internalGetValue() {
      return value;
   }

   // This method should be overridden by attributes where the string representation doesn't match
   // that of it's internal storage object (value).
   /** Returns string representation of the attribute's value.
    * @return Value as string
    */
   @Override
   public String toString() {
      if (value != null)
         return value.toString();
      return null;
   }

   /**
    * Gets database string representation of the attribute's value.
    * The default implementation of this method just calls toString(), so the method should
    * be overridden by attributes that have different string representations in database
    * storage and in serialized stream.
    * @return string used to store the value of this attribute in the database,
    *         or null if this attribute has no value
    */
   protected String toDatabaseString() {
      return toString();
   }

   // This method should be overridden by compound attributes
   /** Format the value of the attribute to an Fnd Buffer
    * @param stream Stream to which to write the buffer
    * @param releaseMemory true if after formatting the attribute value should be cleared releasing the memory, false otherwise
    * @throws ParseException Attribute's value cannot be serialized into a buffer
    */
   protected void formatValue(FndTokenWriter stream, boolean releaseMemory) throws ParseException {
      stream.write(FndSerializeConstants.VALUE_MARKER);

      String toSerialize;
      if (meta.isEncrypted()) {
         try {
            toSerialize = FndEncrypter.encrypt(toString());
         } catch (EncryptionException e) {
            throw new ParseException(
                  "ENCRYPTERROR: Error during encrypting attribute (&1) of record (&2): &3",
                  getName(), rec.getName(), e.getMessage());
         }
      } else {
         toSerialize = toString();
      }

      // Strings are handles differently so that we can length prefix them if
      // the client supports length prefixes.
      if (this.getType() == FndAttributeType.TEXT
              || this.getType() == FndAttributeType.ALPHA) {
         stream.writeStringToken(toSerialize);
      } else {
         stream.write(toSerialize);
      }
      if (releaseMemory)
         value = null;
   }

   /**
    * Approximates the size of the serialized value of this attribute.
    * This method takes care of all simple attribute types, but must be overridden for compound attributes.
    * @param changedValueMode current value of ChangedValueMode that controls formatting process
    * @return the number of bytes needed for the serialized value of this attribute
    * @see #formatValue
    */
   int serializedValueSize(FndAbstractRecord.ChangedValueMode changedValueMode) {
      int size = 0;
      FndAttributeType type = getType();

      if (type == FndAttributeType.ALPHA || type == FndAttributeType.ENUMERATION)
         size += toString().length();
      else if (type == FndAttributeType.TEXT)
         size += toString().length() * 105 / 100; // UTF8 encoding of non-english characters
      else if (type == FndAttributeType.BINARY || type == FndAttributeType.SIMPLE_ARRAY) {
         // Base64 encoding
         int len = ((FndBinary)this).getLength();
         size += len / 3 * 4;
         if(len % 3 > 0)
            size += 4;
      }
      else if (type == FndAttributeType.BOOLEAN)
         size += 5;
      else if (type == FndAttributeType.DATE)
         size += 10;
      else if (type == FndAttributeType.DECIMAL)
         size += 20;
      else if (type == FndAttributeType.INTEGER)
         size += 4;
      else if (type == FndAttributeType.NUMBER)
         size += 6;
      else if (type == FndAttributeType.TIME)
         size += 8;
      else if (type == FndAttributeType.TIMESTAMP)
         size += 18;
      else if (type == FndAttributeType.UNKNOWN)
         size += toString().length();

      return size;
   }

   /**
    * Formats the whole attribute into an Fnd Buffer.
    * @param stream Stream to which to write the resulting buffer
    * @param mode enumeration value controlling serialization of CHANGED_VALUE flag
    * @param releaseMemory true if after formatting the attribute value should be cleared releasing the memory, false otherwise
    * @throws ParseException Attribute cannot be serialized into a buffer
    */
   void format(FndTokenWriter stream, FndAbstractRecord.ChangedValueMode mode, boolean releaseMemory) throws ParseException {
      String name, state;
      FndAttributeType type;

      if ((name = getName()) != null) {
         stream.write(FndSerializeConstants.NAME_MARKER);
         stream.write(name);
      }

      if ((type = getType()) != null) {
         if (type == FndAttributeType.UNKNOWN) {
            if (extra != null && extra.type != null) {
               stream.write(FndSerializeConstants.TYPE_MARKER);
               stream.write(extra.type);
            }
         }
         else {
            String typeString = type.toString();

            // If we are formatting a String type ("T" or "A"), we will always
            // add a length prefix, if the client has that capability.

            // Some strings in the buffer are deserialized as FndAttribute with
            // a type of "T". Hence we shouldn't check the instance type
            // (this instanceof FndText || this instanceof FndAlpha) to check if an
            // attribute is of string type. Instead check of the type of the
            // attribute itself.

            stream.write(FndSerializeConstants.TYPE_MARKER);

            if (this.getType() == FndAttributeType.ALPHA
                    || this.getType() == FndAttributeType.TEXT) {
               stream.writeStringType(typeString);
            } else {
               stream.write(typeString);
            }
         }
      }

      state = getState();
      if (!"".equals(state)) {
         stream.write(FndSerializeConstants.STATUS_MARKER);
         stream.write(state);
      }

      if(extra != null && extra.invalidValueInfo != null) {
         stream.write(FndSerializeConstants.INVALID_VALUE_MARKER);
         stream.write(extra.invalidValueInfo);
      }

      if(extra != null && extra.count >= 0) {
         stream.write(FndSerializeConstants.COUNT_MARKER);
         stream.write(String.valueOf(extra.count));
      }
      else {
         if(mode == FndAbstractRecord.SERIALIZE_CHANGED_VALUE && isFlagSet(CHANGED_VALUE))
            stream.write(FndSerializeConstants.CHANGED_VALUE_MARKER);

         if(mode == FndAbstractRecord.SERIALIZE_UNCHANGED_VALUE && !isFlagSet(CHANGED_VALUE))
            stream.write(FndSerializeConstants.UNCHANGED_VALUE_MARKER);
         else if (!isSet())
            stream.write(FndSerializeConstants.NO_VALUE_MARKER);
         else if (isNull())
            stream.write(FndSerializeConstants.NULL_VALUE_MARKER);
         else
            formatValue(stream, releaseMemory);
      }
   }

   /**
    * Approximates the size of the serialized form of this attribute.
    * @param changedValueMode current value of ChangedValueMode that controls formatting process
    * @return the number of bytes needed for the serialized form of this attribute
    * @see #format
    */
   int serializedSize(FndAbstractRecord.ChangedValueMode changedValueMode) {
      int size = 0;

      // attribute name
      String name = getName();
      if (name != null)
         size += name.length() + 1;

      // attribute type
      FndAttributeType type = getType();
      if (type != null)
         size += type.toString().length() + 1;

      // attribute state (approximation)
      size += 3;

      // invalid value info
      String invalidValueInfo = getInvalidValueInfo();
      if(invalidValueInfo != null)
         size += invalidValueInfo.length() + 1;

      // attribute value
      boolean attrValue = !isNull();
      if(attrValue && !getChangedValueFlag()) {
         if(changedValueMode == FndAbstractRecord.SERIALIZE_UNCHANGED_VALUE)
            attrValue = false;
      }

      if(changedValueMode == FndAbstractRecord.SERIALIZE_CHANGED_VALUE && isFlagSet(CHANGED_VALUE))
         size += 1;

      if(attrValue)
         size += serializedValueSize(changedValueMode) + 1;
      else
         size += 1; // approximation of count or one of value markers

      return size;
   }

   // This method should be overridden by attributes where the string representation doesn't match
   // that of it's internal storage object (value).
   /** Parses a string to get the attribute's value.
    * @param value Value to set the attribute to
    * @throws ParseException if <code>value</code> cannot be parsed
    * (contains an illegal value).
    */
   public void parseString(String value) throws ParseException {
      if (value == null || value.length() == 0) {
         this.value = null;
      }
      else{
         if(meta.isEncrypted()) {
            try{
               this.value = FndEncrypter.decrypt(value);
            }
            catch(EncryptionException e) {
               throw new ParseException(e, "DECRYPTERROR: Error during decrypting attribute (&1) of record (&2): &3",getName(), rec.getName(), e.getMessage());
            }
         }
         else
            this.value = value;
      }
      set();
      setExistent();
   }

   /** This method is specially used for supporting few non printalbe chars and should be
    * overridden by attributes where the string representation doesn't match
    * that of it's internal storage object (value).
    * Parses a XML string to get the attribute's value.
    * @param s Value to set the attribute to
    * @throws ParseException if <code>value</code> cannot be parsed
    * (contains an illegal value).
    */
   public void parseXmlString(String s) throws ParseException {
      parseString(s);
   }

   /**
    * Parses a database string to get the attribute's value.
    * The default implementation of this method just calls parseString(), so the method
    * should be overridden by attributes that have different string representations
    * in database storage and in serialized stream.
    * @param dbValue database string value
    * @throws ParseException if the specified string contains an illegal value
    */
   protected void parseDatabaseString(String dbValue) throws ParseException {
      parseString(dbValue);
      setChangedValue();
   }

   //This method only makes sence if we're dealing with arrays/aggregates
   protected void parseBuffer(FndTokenReader stream) throws ParseException {
   }

   /**
    * Parses an Fnd Buffer to get the attribute.
    * @param stream Stream from which to read the buffer
    * @throws ParseException Syntax error in buffer
    */
   protected void parse(FndTokenReader stream) throws ParseException {
      FndAttributeType type;
      String name, state = null, info = null;
      char ch;
      String typeString;

      setExistent();

      ch = stream.getDelimiter();

      if (ch == FndSerializeConstants.NAME_MARKER) {
         name = stream.getToken();
         setName(name);
         ch = stream.getDelimiter();
      }

      if (ch == FndSerializeConstants.TYPE_MARKER) {
         typeString = stream.getToken();
         type = FndAttributeType.toAttributeType(typeString);
         setType(type);
         // In case of a Condition that is deserialized that has an LPT as one
         // of the parameters, above setType(type) has no effect. (Because by
         // this time the type is already set to that if FndAlpha or FndText.
         // Hence the requirement of a field to determine of a given attribute
         // is actually serialized as a length prefixed string.

         if (this instanceof FndAbstractString &&
             (BufferUtil.LENGTH_PREFIXED_ALPHA.equals(typeString) ||
              BufferUtil.LENGTH_PREFIXED_TEXT.equals(typeString))) {
            ((FndAbstractString)this).setLengthPrefixed();
         }

         ch = stream.getDelimiter();
      }

      if (ch == FndSerializeConstants.STATUS_MARKER) {
         state = stream.getToken();
         setState(state);
         ch = stream.getDelimiter();
      }

      if (ch == FndSerializeConstants.INVALID_VALUE_MARKER) {
         info = stream.getToken();
         ch = stream.getDelimiter();
      }

      if (ch == FndSerializeConstants.COUNT_MARKER) {
         String count = stream.getToken();
         try {
            setCount(Integer.parseInt(count));
         }
         catch(NumberFormatException nfe) {
            throw new ParseException(nfe, "PARSECOUNT:Invalid format for attribute count [&1].", count);
         }
      }
      else {
         if(ch == FndSerializeConstants.CHANGED_VALUE_MARKER) {
            setChangedValue();
            ch = stream.getDelimiter();
         }

         switch (ch) {
            case FndSerializeConstants.UNCHANGED_VALUE_MARKER :
               throw new ParseException("UNCHANGED_VALUE_MARKER: Parsing of UNCHANGED_VALUE_MARKER is not supported on server-side");

            case FndSerializeConstants.NO_VALUE_MARKER :
               unset();
               break;

            case FndSerializeConstants.NULL_VALUE_MARKER :
               value = null;
               set();
               break;

            case FndSerializeConstants.VALUE_MARKER :
               if(this instanceof FndBinary) {
                  ((FndBinary)this).setBinaryValue(stream.getBinaryToken());
               } else if ((this instanceof FndAbstractString) &&
                         ((FndAbstractString)this).isLengthPrefixed()) {
                  parseString(stream.getLPStringToken());
               } else {
                  parseString(stream.getToken());
               }
               break;

            case FndSerializeConstants.BEGIN_BUFFER_MARKER : //Leave it to the individual classes to handle this one...
               parseBuffer(stream);
               break;

            case FndSerializeConstants.END_BUFFER_MARKER :
               break;

            default :
               throw new ParseException("Expecting '*', '=' or '(' but found character code " + (int) ch);
         }
      }

      if(info != null)
         getOrCreateExtraInfo().invalidValueInfo = info; // delayed, because a call to set() clears invalidValueInfo
   }

   /**
    * Formats this object into the XML stream.
    * @param s XML serializer to which to append the attribute contents
    * @param customAttr top level attribute (tested with "==" operator) which will be formatted by the custom formatter
    * @param customFormatter formatter that will format the custom attribute value
    */
   protected void formatAttributeToXml(FndXmlSerializer s, FndAttribute customAttr, FndAttributeXmlFormatter customFormatter) throws ParseException{
      // If the attribute is non-existent, don't do anything.
      if (!exist())
         return;

      FndXmlElementAttributeList attributes = new FndXmlElementAttributeList();
      if(s.isFlagSet(FndXmlSerializer.FLAG_DATATYPE_INFO))
         attributes.add("ifsrecord:datatype", meta.getType().getName());

      if(isDirty() && s.isFlagSet(FndXmlSerializer.FLAG_DIRTY_INFO))
         attributes.add("ifsrecord:dirty", "true");

      if(isExcluded() && s.isFlagSet(FndXmlSerializer.FLAG_EXCLUDE_INFO))
         attributes.add("ifsrecord:exclude", "1");

      if (isNull()) {
         attributes.add("xsi:nil", "1");
         s.addElement(getName(), attributes);
         s.newLine();
      }
      else {
         s.startElement(getName(), attributes);

         if(this == customAttr)
            customFormatter.formatAttributeValue(this, ((FndXmlStreamSerializer)s).getWriter());
         else
            formatValueToXml(s);

         s.endElement(getName());
         s.newLine();

         if(s.destroyFormattedRecords())
            value = null;
      }
   }

   /** Formats this attribute's value to the xml stream.
    * @param s XML serializer to which to add the value
    */
   protected void formatValueToXml(FndXmlSerializer s) throws ParseException{
      if(meta.isEncrypted()) {
         try{
            s.append(s.encode(FndEncrypter.encrypt(toString())));
         }
         catch(EncryptionException e) {
            throw new ParseException(e, "ENCRYPTERROR: Error during encrypting attribute (&1) of record (&2): &3",getName(), rec.getName(), e.getMessage());
         }
      }
      else
         s.append(s.encode(toString()));
   }

   /**
    * Compares two attributes for sorting purposes. Overriden in sortable sub-classes.
    * @param attr the attribute to compare this object to.
    * @return 0
    */
   public int compareTo(FndAttribute attr) {
      // override this metod in sortable datatypes
      return 0;
   }

   /** Check if this is a compound attribute or not (array/aggregate)
    *  @return false for non-compound attributes
    */
   public boolean isCompound() {
      // override this method for compund attributes
      return false;
   }

   /** Return a FndSqlValue based on the value of this attribute
    */
   protected FndSqlValue toFndSqlValue() {
      //Should be overridden by all persistent attribute types
      return null;
   }

   /**
    * Get value from database and set on this attribute
    * (must be overridden for persistent attribute types)
    * @param data an instance of FndSqlData interface to get value from
    * @param colNr column/parameter index number
    */
   protected void setSqlValue(FndSqlData data, int colNr) throws IfsException {
      //override for all persistent attribute types
   }

   /**
    * Check if this attribute is a part of the primary key for the parent record
    * @return true if this attribute is a part of the primary key
    */
   public final boolean isPartOfPrimaryKey() {
      FndAttribute key = null;
      FndAttribute.Iterator keys = this.rec.keys();
      while (keys.hasNext()) {
         key = keys.next();
         if (this == key)
            return true;
      }
      return false;
   }

   /**
    * Get SQL type for attribute.
    */
   protected FndSqlType getSqlType() {
      //Override for all persistent attributes
      return null;
   }

   /**
    * Check if this is a long attribute or not (CLOB/BLOB storage).
    * @return <code>true</code> if this is a long attribute
    */
   public boolean isLong() {
      //Override this method for all attributes with long storage
      return false;
   }

   /**
    * Check if attribute has been set to a value or not.
    * (Attribute may be existent but not have a value set.)
    * Note that the value may be null.
    * @return true if the attribute has been set.
    */
   public final boolean isSet() {
      return isFlagSet(SET);
   }

   /**
    * Sets the set-flag. (Stupid name...)
    */
   protected final void set() {
      setFlag(SET);
      if(extra != null)
         extra.invalidValueInfo = null;
   }

   /**
    * Un-sets the set flag.
    */
   protected final void unset() {
      unsetFlag(SET);
   }

   /**
    * Check if the attribute has been given a non-null value.
    * @return true if the attribute is existent and has been given a non-null value.
    */
   public final boolean hasValue() {
      return exist() && isSet() && !isNull();
   }

   /**
    * Set the internal count value (used for select count(attr) type of queries).
    * @param count The number of counted results to set.
    */
   final void setCount(int count) {
      getOrCreateExtraInfo().count = count;
      setExistent();
   }

   /**
    * Get the result from a "select count(attr) from ..." query.
    * @return The returned integer value from the storage operation,
    *         or -1 if no count result has been stored in this attribute
    */
   int getCount() {
      if(extra == null)
         return -1;
      else
         return extra.count;
   }

   /** Get the query result category set on the attribute.
    *  @return Result category set on the attribute.
    */
   public final FndQueryResultCategory getResultCategory() {
      return FndQueryResultCategory.getInstance(getQrc());
   }

   /** Create a "attribute between value1 and value2" condition on this attribute.
    *  @param value1 First value.
    *  @param value2 Second value.
    *  @return Created condition.
    */
   protected FndSimpleCondition createBetweenCondition(Object value1, Object value2) {
      ArrayList<Object> l = new ArrayList<>(2);
      l.add(value1);
      l.add(value2);
      return new FndSimpleCondition(this, FndQueryOperator.BETWEEN, l);
   }

   /** Create a 'attribute = value" condition on this attribute.
    *  @param value Value attribute should be equal to.
    *  @return Created condition.
    */
   protected FndSimpleCondition createEqualCondition(Object value) {
      return new FndSimpleCondition(this, FndQueryOperator.EQUAL, value);
   }

   /** Create a "attribute > value" condition on this attribute.
    *  @param value Value this attribute should be greater than.
    *  @return Created condition.
    */
   protected FndSimpleCondition createGreaterThanCondition(Object value) {
      return new FndSimpleCondition(this, FndQueryOperator.GREATER_THAN, value);
   }

   /** Create a "attribute >= value" condition on this attribute.
    *  @param value Value this attribute should be greater than or equal to.
    *  @return Created condition
    */
   protected FndSimpleCondition createGreaterThanOrEqualCondition(Object value) {
      return new FndSimpleCondition(this, FndQueryOperator.GREATER_THAN_OR_EQUAL, value);
   }

   /** Create a "attribute < value" condition on this attribute.
    *  @param value Value this attribute should be less than.
    *  @return Created condition
    */
   protected FndSimpleCondition createLessThanCondition(Object value) {
      return new FndSimpleCondition(this, FndQueryOperator.LESS_THAN, value);
   }

   /** Create a "attribute <= value" condition on this attribute.
    *  @param value Value this attribute should be less than or equal to.
    *  @return Created condition.
    */
   protected FndSimpleCondition createLessThanOrEqualCondition(Object value) {
      return new FndSimpleCondition(this, FndQueryOperator.LESS_THAN_OR_EQUAL, value);
   }

   /** Create a "attribute <> value" condition on this attribute.
    *  @param value Value attribute should not be equal to.
    *  @return Created condition.
    */
   protected FndSimpleCondition createNotEqualCondition(Object value) {
      return new FndSimpleCondition(this, FndQueryOperator.NOT_EQUAL, value);
   }

   /** Return a string representation of the complete path to this attribute.
    *  This method is used from ValidationException to indicate the offending attribute.
    *  @return The complete path to the attribute.
    */
   final String getPath() {
      FndAbstractRecord parent = this.rec;
      if(parent == null)
         return getName();
      StringBuilder path = new StringBuilder();
      FndAttribute container = null;

      path.insert(0, getName());
      path.insert(0, ".");
      path.insert(0, parent.getName());

      container = parent.getContainer();
      while (container != null) {
         path.insert(0, ".");
         if (container instanceof FndAbstractAggregate)
            path.insert(0, "(0)");
         else if (container instanceof FndAbstractArray) {
            FndAbstractArray array = (FndAbstractArray) container;
            path.insert(0, "(" + array.internalIndexOf(parent) + ")");
         }

         if (container.getName() != null) { //Stop if there is no name for this array/aggregate
            path.insert(0, container.getName());
            parent = container.getParentRecord();
            if (parent != null) {
               path.insert(0, ".");
               path.insert(0, parent.getName());
               container = parent.getContainer();
            }
            else
               container = null;
         }
         else
            container = null;
      }

      return path.toString();
   }

   /**
    * An iterator over a list of attributes. This interface is similiar to
    * java.util.Iterator except for two things. Firstly, its next() method returns
    * FndAttribute rather than Object. Secondly, it has no remove() method.
    */
   public interface Iterator {

      /**
       * Returns true if the iteration has more elements.
       * @return true if the iterator has more elements.
       */
      boolean hasNext();

      /**
       * Returns the next element in the iteration.
       * @return the next element in the iteration.
       */
      FndAttribute next();
   }

   //==========================================================================
   // Implementation of interface FndSortField
   //==========================================================================

   /**
    * Returns an iterator over one attribute (this attribute).
    * @return iterator
    */
   @Override
   public final FndAttribute.Iterator sortFieldIterator() {
      return new FndAttribute.Iterator() {
         FndAttribute attr = FndAttribute.this;

         @Override
         public boolean hasNext() {
            return attr != null;
         }

         @Override
         public FndAttribute next() {
            FndAttribute tmp = attr;
            attr = null;
            return tmp;
         }
      };
   }

   /**
    * Creates a new descending FndSortField.
    * The new sort field will contain only this attribute
    * and the sort direction marked as descending.
    *
    * See class {@link FndQueryRecord} description for example of usage in a query.
    *
    * @return a new instance of FndSortField
    * @see FndAbstractArray#sort(FndSortField...) FndAbstractArray.sort
    */
   @Override
   public final FndSortField descending() {
      return new FndDescendingSortField(this);
   }

   /**
    * Returns FndSort.ASCENDING, which is the default sort direction for an attribute.
    * @return <code>FndSort.ASCENDING</code>
    */
   @Override
   public final FndSort.Direction getSortDirection() {
      return FndSort.ASCENDING;
   }
}
