/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.record;

import ifs.fnd.record.serialization.*;

/**
 * A record for an entity view.
 */
public class FndEntityView extends FndBaseEntityView {

   private static final FndAttributeMeta OBJVERSION_META = new FndAttributeMeta(true, "OBJ_VERSION", "ROWVERSION");
   private static final FndAttributeMeta CREATED_BY_META = new FndAttributeMeta(true, "CREATED_BY", "CREATED_BY", 30);
   private static final FndAttributeMeta CREATED_DATE_META = new FndAttributeMeta(true, "CREATED_DATE", "CREATED_DATE");

   public final FndInteger objVersion = new FndInteger(OBJVERSION_META);
   public final FndText createdBy = new FndText(CREATED_BY_META);
   public final FndTimestamp createdDate = new FndTimestamp(CREATED_DATE_META);

   /**
    * Framework internal constructor required by Externalizable interface.
    */
   public FndEntityView() {
      assert(false);
   }

   public FndEntityView(String name, String table, String entity) {
      super(new FndRecordMeta(name, table, entity));
      OBJVERSION_META.setRecordMeta(meta);
      CREATED_BY_META.setRecordMeta(meta);
      CREATED_DATE_META.setRecordMeta(meta);
      add(objVersion);
      add(createdBy);
      add(createdDate);
   }

   public FndEntityView(FndRecordMeta meta) {
      super(meta);
      OBJVERSION_META.setRecordMeta(meta);
      CREATED_BY_META.setRecordMeta(meta);
      CREATED_DATE_META.setRecordMeta(meta);
      add(objVersion);
      add(createdBy);
      add(createdDate);
   }

   @Override
   public FndAbstractRecord newInstance() {
      return new FndEntityView(meta);
   }

   /**
    * Controls if the parent record (if there is a parent) must be made dirty.
    * Records with no own objVersion attribute must propagate the dirty flag to it's parent.
    * @return false
    */
   @Override
   final boolean makeParentDirty() {
      return false;
   }

   /**
    * Format entity attributes to XML stream.
    * @param s XML serializer to format entity attributes to
    * @param attributes attribute list to add entity attributes to
    */
   @Override
   protected void formatEntityAttributesToXml(FndXmlSerializer s, FndXmlElementAttributeList attributes) {
      if (createdBy.hasValue())
         attributes.add("ifsrecord:CREATED_BY", createdBy.toString());

      if (createdDate.hasValue())
         attributes.add("ifsrecord:CREATED_DATE", createdDate.toString());

      if (objVersion.hasValue())
         attributes.add("ifsrecord:OBJ_VERSION", objVersion.toString());
   }

   /**
    * Check if an attribute is an entity attribute (like OBJ_ID or OBJ_VERSION).
    * @param attr attribute to check
    */
   @Override
   protected boolean isEntityAttribute(FndAttribute attr) {
      return attr == createdBy || attr == createdDate || attr == objVersion;
   }
}
