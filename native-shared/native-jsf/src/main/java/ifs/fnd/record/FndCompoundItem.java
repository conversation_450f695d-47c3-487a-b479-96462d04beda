/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;

import ifs.fnd.base.*;
import ifs.fnd.record.serialization.*;

/** Used by the framework to create aggregate-similar constructions.
 * A compound item contains one record just like an attribute, but it gets
 * serialized differently.
 */
public class FndCompoundItem extends FndAttribute {

   public FndCompoundItem(FndAttributeMeta meta) {
      super(meta);
   }

   /** Sets the record part of the item.
    * @param rec Record
    */
   public void setRecord(FndAbstractRecord rec) {
      internalSetValue(rec);
      if(rec instanceof FndCondition)
         ((FndCondition)rec).setOwner(getParentRecord());
   }

   /** Gets the record part of the item.
    * @return Record
    */
   public FndAbstractRecord getRecord() {
      return (FndAbstractRecord)internalGetValue();
   }

   /** Formats the value of the item into an Fnd Buffer.
    * @param stream Stream on which to write the resulting buffer
    * @param releaseMemory true if after formatting the attribute value should be cleared releasing the memory, false otherwise
    * @throws ParseException The value contains data that cannot be serialized to an Fnd buffer
    */
   @Override
   public void formatValue(FndTokenWriter stream, boolean releaseMemory) throws ParseException {
      getRecord().formatBuffer(stream);
      if(releaseMemory)
         value = null;
   }

   /**
    * Approximates the size of the serialized value of this attribute.
    * @param changedValueMode current value of ChangedValueMode that controls formatting process
    * @return the number of bytes needed for the serialized value of this attribute
    * @see #formatValue
    */
   @Override
   int serializedValueSize(FndAbstractRecord.ChangedValueMode changedValueMode) {
      int size = getRecord().serializedBufferSize(changedValueMode);
      return size;
   }

   /** Format the whole item into an Fnd buffer
    * @param stream Stream on which to write the resulting Fnd buffer
    * @param mode enumeration value controlling serialization of CHANGED_VALUE flag
    * @param releaseMemory true if after formatting the attribute value should be cleared releasing the memory, false otherwise
    * @throws ParseException Item contains data that cannot be serialized into an Fnd buffer
    */
   @Override
   public void format(FndTokenWriter stream, FndAbstractRecord.ChangedValueMode mode, boolean releaseMemory) throws ParseException {
      String name, type;
      FndAbstractRecord record;
      FndRecordState state;

      if((name = getName()) != null) {
         stream.write(FndSerializeConstants.NAME_MARKER);
         stream.write(name);
      }

      if((record = getRecord()) != null) {
         if ((type = record.getType()) != null) {
            stream.write(FndSerializeConstants.TYPE_MARKER);
            String pkg  = record.getMetaPackage();
            if (pkg != null)
               stream.write(pkg + "." + type);
            else
               stream.write(type);
         }
         if((state = record.getState()) != null) {
            stream.write(FndSerializeConstants.STATUS_MARKER);
            stream.write(state.toString());
         }
      }

      if(isNull()) {
         stream.write(FndSerializeConstants.NULL_VALUE_MARKER);
      } else {
         formatValue(stream, releaseMemory);
      }
   }

   /**
    * Approximates the size of the serialized form of this attribute.
    * @param changedValueMode current value of ChangedValueMode that controls formatting process
    * @return the number of bytes needed for the serialized form of this attribute
    * @see #format
    */
   @Override
   int serializedSize(FndAbstractRecord.ChangedValueMode changedValueMode) {
      int size = 0;

      // attribute name
      String name = getName();
      if (name != null)
         size += name.length() + 1;

      FndAbstractRecord r = getRecord();
      if(r != null) {
         // record type
         String type = r.getType();
         if(type != null) {
            size += 1;
            String pkg  = r.getMetaPackage();
            if (pkg != null)
               size += pkg.length() + 1 + type.length();
            else
               size += type.length();
         }

         // record state
         FndRecordState state = r.getState();
         if (state != null)
            size += state.toString().length() + 1;
      }

      if(isNull())
         size += 1;
      else
         size += serializedValueSize(changedValueMode) + 1;

      return size;
   }

   /** Parses an Fnd Buffer into this item.
    * @param stream Stream from which to read the buffer
    * @throws ParseException Syntax error in buffer
    */
   @Override
   public void parse(FndTokenReader stream) throws ParseException {
      String type= null, name= null, pkg=null;
      char ch;

      setExistent();
      ch = stream.getDelimiter();

      if(ch == FndSerializeConstants.NAME_MARKER) {
         name = stream.getToken();
         setName(name);
         ch = stream.getDelimiter();
      }
      if(ch == FndSerializeConstants.TYPE_MARKER) {
         type = stream.getToken();
         ch = stream.getDelimiter();
         //Check for correct type here
         int i = type.lastIndexOf('.');
         if (i>0) {
            pkg = type.substring(0,i);
            type = type.substring(i+1);
         }
         if (pkg==null) {
            if (this.getMeta()!=null)
               if (this.getMeta().getRecordMeta()!=null)
                  pkg = this.getMeta().getRecordMeta().getPackage();
         }
      }
      // value
      FndView view = null;

      if (FndSerializeConstants.COMPOUND_CONDITION_TAG.equals(type))
         view = new FndCondition();
      else if (FndSerializeConstants.SIMPLE_CONDITION_TAG.equals(type))
         view = new FndSimpleCondition();
      else if (FndSerializeConstants.DETAIL_CONDITION_TAG.equals(type))
         view = new FndDetailCondition();
      else {
         FndAbstractRecord parent = getParentRecord();
         if(parent instanceof FndDetailCondition) {
            FndDetailCondition parentCondition = (FndDetailCondition) parent;

            // retrieve container-attribute name from FndDetailCondition
            String containerName = parentCondition.container.getValue();

            // find master record
            FndAbstractRecord owner = parentCondition.findOwnerRecord();

            FndAttribute attr = owner.getAttribute(containerName);
            if(attr instanceof FndAbstractArray)
               view = (FndView) ((FndAbstractArray)attr).newRecord();
            else if(attr instanceof FndAbstractAggregate)
               view = (FndView) ((FndAbstractAggregate)attr).getTemplateRecord();
            else
               throw new ParseException(
                  "COMPITEMTYPE: Invalid type [&1] of container attribute [&2] in serialized compound item.",
                  attr == null ? "null" : attr.getClass().getName(),
                  containerName);

            parentCondition.initContainerMetaData((FndCompoundAttribute)attr);
         }
         else {
            view = new FndView(new FndRecordMeta(pkg, type));
         }
      }

      if(ch == FndSerializeConstants.STATUS_MARKER) {
         FndRecordState recState = FndRecordState.parseString(stream.getToken());
         view.setState(recState);
         ch = stream.getDelimiter();
      }
      else
         view.setState(null);

      if (ch == FndSerializeConstants.IDENTITY_MARKER) {
         view.setIdentity(stream.getToken());
         ch = stream.getDelimiter(); //Start buffer real data content
      }

      if(view instanceof FndCondition)
         ((FndCondition)view).setOwner(getParentRecord());
      view.parseBuffer(stream);
      value = view;
   }

   /** This one is needed for compound conditions.
    */
   public Object getValue() {
      return this.value;
   }

   /** Clone the internal (record) value.
    */
   @Override
   protected Object cloneValue() throws CloneNotSupportedException {
      if (!this.isNull())
         return this.getRecord().clone();
      else
         return null;
   }

   @Override
   protected FndAttribute newAttribute(FndAttributeMeta meta) {
      return new FndCompoundItem(meta);
   }
}