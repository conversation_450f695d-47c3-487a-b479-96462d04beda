/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;

import java.util.ArrayList;
import java.util.List;


/**
 * Immutable meta-object describing a validation object.
 */
public final class FndValidationMeta {

   private String        name;
   private FndRecordMeta view;
   private List<FndValidationAttributeMeta> attributes;
   private List<FndValidationDetailMeta>        detailValidations;

   /**
    * Constructor used to create a validation object meta.
    */
   public FndValidationMeta(String name, FndRecordMeta view) {
      this.name = name;
      this.view = view;
   }


   /**
    * Constructor used to create a validation object meta (used when Parameter list views).
    */
   public FndValidationMeta(String name, FndRecordMeta view, FndValidationDetailMeta[] detailValidations) {
      this(name ,view);
      if(detailValidations!=null){
         for(int i=0; i<detailValidations.length; i++) {
            add(detailValidations[i]);
         }
      }
   }

   /**
    * Returns the name of this validation object.
    */
   public String getName() {
      return name;
   }

   /**
    * Returns the view meta of this validation object.
    * @return an instance of FndRecordMeta
    */
   public FndRecordMeta getView() {
      return view;
   }

   /**
    * Add an attribute validation object
    */
   public void add(FndValidationAttributeMeta attribute) {
      if(attributes==null) {
         attributes = new ArrayList<>();
      }
      attributes.add(attribute);
   }

   /**
    * Add an attribute validation object based on simple attribute
    */
   public void add(FndValidationDetailMeta detail) {
      if(detailValidations==null) {
         detailValidations = new ArrayList<>();
      }
      detailValidations.add(detail);
   }

   /**
    * Returns array of attribute validations
    */
   public List attributeValidation(){
      return attributes;
   }

   /**
    * Add a detail validation object based on compound attribute
    */
   public FndValidationAttributeMeta getAttributeValidation(String metaName){
      if(attributes!=null){
         for(int i=0; i<attributes.size(); i++) {
            FndValidationAttributeMeta attr = (FndValidationAttributeMeta)attributes.get(i);
            if(attr!=null && attr.getAttribute()!=null&& attr.getAttribute().getName().equals(metaName)) {
               return attr;
            }
         }
      }
      return null;
   }

   /**
    * Returns array of detail validations
    */
   public List detailValidation(){
      return detailValidations;
   }

   public FndValidationMeta getDetailValidation(String metaName){
      if(detailValidations!=null){
         for(int i=0; i<detailValidations.size(); i++){
            FndValidationDetailMeta detail = (FndValidationDetailMeta)detailValidations.get(i);
            if(detail!=null && detail.getAttribute()!=null && detail.getAttribute().getName().equals(metaName)) {
               return detail.getValidation();
            }
         }
      }
      return null;
   }
}
