/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;

import java.io.OutputStream;

/**
 * Object that manages an output stream attached to a binary attribute.
 */
public interface FndOutputStreamManager extends FndStreamManager {

   /**
    * Gets the output stream managed by this stream manager.
    * The stream acts as destination for binary data to be fetched from the database.
    * @return an open output stream
    * @see FndStreamManager#close()
    * @see FndStreamManager#done(boolean)
    */
   OutputStream getOutputStream();
}
