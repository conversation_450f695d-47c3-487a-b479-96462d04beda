/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;

import ifs.fnd.base.*;
import java.util.*;
import java.io.*;
import ifs.fnd.record.serialization.*;

/**
 * A record containing a query to be sent to a query method.
 * Query records are based on normal views and should be created
 * with a condition view as parameter.
 * If any fields in the condition view are set, they will be regarded as simple "equals" conditions.
 * The {@link #maxRows} attribute may be used to limit the number of returned rows.
 * The {@link #skipRows} attribute may be used to omit some rows in the result set.
 * The {@link #setOrderBy setOrderBy} methods control the ordering of returned records.
 * <p>
 * The following example builds a typical query.
 * The methods {@link ifs.fnd.record.FndAbstractRecord#excludeQueryResults excludeQueryResults}
 * and {@link ifs.fnd.record.FndAttribute#include inculde}
 * are used to include only needed attributes in the result set.
 * A simple query condition is created on companyId attribute
 * (see {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for details
 * about more advanced query conditions).
 * The attributes maxRows and skipRows are set to 10 and 20 respectivelly.
 * Then the method setOrderBy is used to define the ordering of returned records.
 * Note how the primaryKey, a subclass of
 * {@link ifs.fnd.record.FndCompoundReference FndCompoundReference} is used to
 * include the primary key attributes (companyId and employeeId) in the result set
 * as well as in the orderBy list.
 * <pre>
 *    Employee emp = new Employee();
 *    emp.excludeQueryResults();
 *    emp.<B>primaryKey</B>.include();
 *    emp.objVersion.include();
 *    emp.name.include();
 *    emp.expireDate.include();
 *    emp.companyId.setValue("20");
 *
 *    EmployeeHandler handler = EmployeeHandlerFactory.getHandler();
 *    FndQueryRecord qry = new FndQueryRecord(emp);
 *    qry.maxRows.setValue(10);
 *    qry.skipRows.setValue(20);
 *    qry.setOrderBy(emp.expireDate.descending(), emp.<B>primaryKey</B>);
 *    EmployeeArray arr = (EmployeeArray) handler.query(qry); </pre>
 * The query results in the following server debug output. Note that the generated SELECT
 * statement will retrieve 30 rows from the database. The first 20 rows will be skipped,
 * while the next 10 will be returned to the caller.
 * <pre>
 *    SELECT A.OBJVERSION, A.COMPANY, A.EMPLOYEE_ID, A.NAME, A.EXPIRE_DATE
 *    FROM ifsapp.COMPANY_EMP A
 *    WHERE A.COMPANY = :1
 *    ORDER BY A.EXPIRE_DATE DESC, A.COMPANY, A.EMPLOYEE_ID
 *
 *    Max rows set to 30
 *    FndStatement: Binding parameters:
 *       1: (TEXT) = 20
 * </pre>
 * <p>
 *
 * Parameters passed to setOrderBy (instances of {@link FndSortField}) may be located in
 * the condition view or in any of its aggregates. In the following example rows will
 * be ordered by description located in ISOCountry aggregate.
 * <pre>
 *    PersonInfo person = new PersonInfo();
 *    person.excludeQueryResults();
 *    person.name.include();
 *    person.creationDate.include();
 *    person.country.include();
 *    person.iSOCountry.include();
 *    person.addCondition(person.country.createIsNotNullCondition());
 *
 *    PersonInfoHandler handler = PersonInfoHandlerFactory.getHandler();
 *    FndQueryRecord qry = new FndQueryRecord(person);
 *    qry.maxRows.setValue(10);
 *    qry.setOrderBy(<B>person.iSOCountry().description</B>, person.creationDate.descending());
 *    PersonInfoArray arr = (PersonInfoArray) handler.query(qry); </pre>
 * Note that in the following debug output there is no generated ORDER BY clasue. Sorting
 * of records is performed in memory after executing the master SELECT statement and
 * a number of detail SELECT statements, one per fetched master row.
 * <pre>
 *    SELECT A.NAME, A.CREATION_DATE, A.COUNTRY_DB
 *    FROM ifsapp.PERSON_INFO_PUBLIC A
 *    WHERE A.COUNTRY_DB IS NOT NULL
 *
 *    SELECT A.OBJID, A.OBJVERSION, A.COUNTRY_CODE, ...
 *    FROM ifsapp.ISO_COUNTRY_DEF A
 *    WHERE A.COUNTRY_CODE = :1
 * </pre>
 * If the ISOCountry aggregate had no custom storage defined in the model
 * then the same query would result in one SELECT statement joining the master
 * and detail tables. In such a case an <B>OREDR BY</B> cluse is used to sort
 * the joined records in database.
 * <pre>
 *    SELECT A.NAME, A.CREATION_DATE, A.COUNTRY_DB
 *    , B.OBJID, B.OBJVERSION, B.COUNTRY_CODE, B.DESCRIPTION, ...
 *    FROM ifsapp.PERSON_INFO_PUBLIC A, ifsapp.ISO_COUNTRY_DEF B
 *    WHERE (A.COUNTRY_DB = B.COUNTRY_CODE(+))
 *    AND A.COUNTRY_DB IS NOT NULL
 *    <B>ORDER BY</B> B.DESCRIPTION, A.CREATION_DATE DESC
 * </pre>
 *
 * See method {@link FndAbstractArray#sort(FndSortField...) FndAbstractArray.sort} that may
 * be used to sort arrays of records in memory.
 *
 * @see ifs.fnd.record.FndQueryResultCategory FndQueryResultCategory
 * @see ifs.fnd.record.FndCondition       FndCondition
 * @see ifs.fnd.record.FndSimpleCondition FndSimpleCondition
 * @see ifs.fnd.record.FndDetailCondition FndDetailCondition
 */
public class FndQueryRecord extends FndRecord implements FndQueryRecordSerialization, Serializable {
   private static final FndAttributeMeta skipRowsMeta = new FndAttributeMeta("SKIP_ROWS");
   private static final FndAttributeMeta maxRowsMeta = new FndAttributeMeta("MAX_ROWS");
   private static final FndAttributeMeta orderByMeta = new FndAttributeMeta("ORDER_BY");
   private static final FndAttributeMeta groupByMeta = new FndAttributeMeta("GROUP_BY");
   private static final FndAttributeMeta resultMeta = new FndAttributeMeta("RESULT");
   private static final FndAttributeMeta conditionMeta = new FndAttributeMeta("CONDITION");

   /**
    * The number of rows to skip in the result set.
    */
   public final FndInteger skipRows = new FndInteger(skipRowsMeta);

   /**
    * The maximum number of rows to return.
    */
   public final FndInteger maxRows = new FndInteger(maxRowsMeta);

   /**
    * String defining the ordering for the query.
    * @see #setOrderBy
    */
   public final FndText orderBy = new FndText(orderByMeta);

   /**
    * String defining the group by for the query.
    */
   public final FndText groupBy = new FndText(groupByMeta);

   /**
    * The result array for the query.
    */
   private final FndArray result = new FndArray(resultMeta);

   /**
    * The aggregate containing the record with query conditions.
    */
   public final FndAggregate condition = new FndAggregate(conditionMeta);

   private void initMeta(FndAbstractRecord view) {
      addAttributes();
      if (view != null)
         view.setQuery(true);
      condition.setRecord(view);
      result.setTemplateRecord(view);
   }

   /**
    * Constructs a new query record based on the specified condition record.
    * @param view record containing query conditions
    */
   public FndQueryRecord(FndAbstractRecord view) {
      super();
      this.meta = new FndRecordMeta(view.getModule(), view.getMetaPackage(), view.getType() + "_QUERY", null, view.getEntity());
      initMeta(view);
   }

   /**
    * Constructs a new query record.
    * @param meta meta-record describing the query record
    */
   public FndQueryRecord(FndRecordMeta meta) {
      super(meta);
      initMeta(null);
   }

   /**
    * Constructs a new query record based on the specified condition record.
    * @param view record containing query conditions
    * @param meta meta-record describing the query record
    */
   public FndQueryRecord(FndAbstractRecord view, FndRecordMeta meta) {
      super(meta);
      initMeta(view);
   }

   /**
    * Constructs a new query record.
    * @param type the type of the condition record
    */
   public FndQueryRecord(String type) {
      super(type);
      int pos = type.indexOf('.');
      if (pos == -1)
         initMeta(null);
      else {
         this.meta = new FndRecordMeta("", type.substring(0, pos++), type.substring(pos) + "_QUERY", null, null);
         addAttributes();
      }
   }

   /**
    * Constructs a new query record based on the specified condition record.
    * @param view record containing query conditions
    * @param type the type of the condition record
    */
   public FndQueryRecord(FndAbstractRecord view, String type) {
      super(type);
      initMeta(view);
   }

   /**
    * Constructor used during deserialization.
    * @param stream object used for reading characters from the serialized stream
    */
   public FndQueryRecord(FndTokenReader stream) throws ParseException {
      super();
      addAttributes();
      parse(stream);
   }

   /**
    * Constructor used during deserialization.
    * @param meta meta-record describing the query record
    * @param stream object used for reading characters from the serialized stream
    */
   public FndQueryRecord(FndRecordMeta meta, FndTokenReader stream) throws ParseException {
      super(meta);
      addAttributes();
      parse(stream);
   }

   /**
    * Constructor used during java standard deserialization.
    * Such constructor is mandatory in a class that implements Externalizable.
    * Note! This constructor creates an incompletely defined record and as such
    *       should be used only by standard java deserialization process.
    */
   public FndQueryRecord() {
      addAttributes();
   }

   /**
    * This method is called automatically when serializing this record.
    * @param out serialize to this stream
    * @throws IOException if serialization fails
    */
   @Override
   public void writeExternal(ObjectOutput out) throws IOException {
      // store the type of the condition record
      out.writeObject(condition.getRecord().getClass());

      // serialize the contents of this record
      super.writeExternal(out);
   }

   /**
    * This method is called automatically when deserializing this record.
    * @param in parse from this stream
    * @throws IOException if parse fails
    * @throws ClassNotFoundException if class for the serialized object can not be instantiated
    */
   @Override
   public void readExternal(ObjectInput in) throws IOException, ClassNotFoundException {
      // restore the type of the condition record
      Class cls = (Class)in.readObject();

      // recreate the query condition record
      FndAbstractRecord view;
      try {
         view = (FndAbstractRecord)cls.newInstance();
      }
      catch (IllegalAccessException | InstantiationException e) {
         throw new IOException("Failed de-serializing query condition record: " + e.getMessage(), e);
      }
       view.setQuery(true);
      condition.setRecord(view);
      result.setTemplateRecord(view);

      // recreate the meta-record
      this.meta = new FndRecordMeta(view.getModule(), view.getMetaPackage(), view.getType() + "_QUERY", null, view.getEntity());

      // deserialize the contents of this record
      super.readExternal(in);
   }

   /**
    * Returns a new query record of the same type as this query record.
    * @return new query record
    */
   @Override
   public FndAbstractRecord newInstance() {
      if(condition.getRecord()!=null)
         return new FndQueryRecord(condition.getRecord().newInstance());
      return new FndQueryRecord(meta);
   }

   private void addAttributes() {
      add(skipRows);
      add(maxRows);
      add(orderBy);
      add(groupBy);
      add(result);
      add(condition);
   }

   /**
    * Parses serialized data into this query record.
    * @return the list containing all records from the parsed result array
    */
   @Override
   public List<FndAbstractRecord> parseQueryRecord(FndTokenReader stream) throws ParseException {
      List<FndAbstractRecord> v;
      parse(stream);
      v = result.getInternalRecords();
      result.reset();
      return v;
   }

   /**
    * Serializes this query record.
    * @param stream the destination stream for serialized data
    * @param result the result array to assign to this query record before serialization
    */
   @Override
   public void formatQueryRecord(FndTokenWriter stream, FndAbstractArray result) throws ParseException {
      if (result.isNull()) {
         this.result.setNull();
         this.result.setDirty(false);
      }
      else
         if (result.exist()) {
            this.result.setExistent();
            this.result.load(result.getInternalRecords());
         }
      this.result.setUpdateAllowed(result.isUpdateAllowed());
      format(stream);
   }

   /**
    * Sets the advanced condition for the query.
    * Conditions can be created by using createSimpleCondition
    * on attributes and by combining them into compound conditions.
    * @param cond Condition for the advanced query
    */
   @Override
   public void setCondition(FndCondition cond) {
      FndAbstractRecord rec = condition.getRecord();
      rec.setCondition(cond);
   }

   /**
    * Gets the current advanced condition set on this query.
    * @return Advanced condition
    */
   @Override
   public FndCondition getCondition() {
      FndAbstractRecord rec = condition.getRecord();
      return rec.getCondition();
   }

   /**
    * Defines the ordering for this query.
    * The method sets the value of attribute orderBy.
    * <P>
    * See this class {@link FndQueryRecord description} for example of usage.
    *
    * @param fields instances of FndAttribute and/or FndCompoundReference that define
    *        the order of records returned by the query
    * @throws SystemException if the specified attributes/references are not contained in
    *         the condition record or in one of its aggregates
    */
   public void setOrderBy(FndSortField... fields) throws SystemException {
      String orderByStr = FndSort.formatOrderByClause(condition.getRecord(), fields);
      try {
         orderBy.setValue(orderByStr);
      }
      catch(ApplicationException e) {
         throw new SystemException(e, "SETORDERBY:Cannot set the value of FndQueryRecord.orderBy attribute");
      }
   }
}
