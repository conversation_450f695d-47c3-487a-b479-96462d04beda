/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.record;

/**
 * Attribute representing a state of an entity.
 */
public abstract class FndEntityState extends FndEnumeration {

   /**
    * Enumeration value representing a specific entity state.
    */
   public static abstract class Enum {

      private boolean isFinal;

      protected Enum(boolean isFinal) {
         this.isFinal = isFinal;
      }

      /**
       * Indicates if this state is a final state.
       * @return true if this state is a final state, false otherwise.
       */
      public boolean isFinal() {
         return isFinal;
      }

      /**
       * Get the value of this enumeration class.
       * @return string representation of the object
       */
      public abstract String getValue();

      /**
       * Get the database value of this enumeration class.
       * The default implementation of this method just calls getValue(), so the method should
       * be overridden by attributes that have different string representations in database
       * storage and in serialized stream.
       * @return string representation of the object used in database storage
       */
      public String getDatabaseValue() {
         return getValue();
      }
   }

   protected FndEntityState(FndAttributeMeta meta) {
      super(meta);
   }

   /**
    * Indicates if the owner of this attribute is in a final state.
    * @return true if this attribute holds an enumeration value representing a final state, false otherwise.
    */
   public boolean isFinal() {
      Enum state = (Enum) internalGetValue();
      return state == null ? false : state.isFinal();
   }

   /**
    * Gets the state value stored in this attribute.
    * @return a subclass of FndEnumeration.Enum representing the current state value
    */
   public Enum getStateValue() {
      return (Enum) internalGetValue();
   }
}