/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record.serialization;

import ifs.fnd.base.IfsRuntimeException;
import ifs.fnd.base.ParseException;
import ifs.fnd.service.IfsConstants;
import java.io.*;

/**
 * <B>Framework internal class:</B> Helper class to create xml document strings.
 * </P>
 * <P>The serialization flag values can be combined using the bitwise or
 * (<code>|</code>) operator. For example:</P>
 * <code>
 *
 * int flags = FndXmlString.FLAG_DATATYPE_INFO | FLAG_EXCLUDE_INFO;
 *
 *</code>
 * <P>The serialization flags, if enabled, are added as as XML attributes in the
 * <code>ifsrecord</code> namespace.</P>
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public final class FndXmlString implements FndXmlSerializer {

   /**
    * Buffer holding UTF-8 encoded XML document.
    */
   private FndByteBufferOutputStream doc;

   /**
    * Writer converting characters to bytes.
    */
   private Writer writer;

   /**
    * Holds the xml document's default namespace. The namespace is set on the
    * root element.
    */
   private String defaultNamespace;

   /**
    * The serialization flags set.
    */
   private int flags;

   /**
    * Sets whether any namespace attributes added to the root element by default.
    */
   private boolean addDefaultNamespaces = true;

   /**
    * The direction (REQUEST/RESPONSE) of the formatted record or array.
    */
   private FndRecordFormat.Direction direction;

   /**
    * Keeps track if any elements have been added. (to decide if namespaces
    * should be added to the element's attributes)
    */
   private boolean isFirstElement = true;

   /**
    * Flag determining if the produced XML should be properly indented or not.
    * Either way produces well-formed XML. Default is <code>false</code> to
    * maintain backwards compatability (it's also smaller in size).
    */
   private boolean indent = false;

   /**
    * The current indentation level when <code>indent == true</code>.
    */
   private int indentLevel = 0;

   /**
    * Internal flag keeping track of indentation.
    */
   private boolean indentNext = false;

   /**
    * The indentation string pre-padding each line when
    * <code>indentLevel > 0</code> and <code>indent == true</code>.
    */
   private String indentString = " ";

   /**
    * The flag indicating if the records and attributes
    * should be destroyed after formatting.
    */
   private boolean destroyFormattedRecords;

   /**
    * This Tag use for encoding unsupported chrs  .
    */
   private static final String UNSUPPORTED_CHR_TAG = "!!chr";

   /**
    * The platform's line separator sequence.
    */
   static final String NL = IfsConstants.LINE_SEPARATOR;

   /**
    * Constructs a FndXmlString instance with a xml header.
    * @param   xmlEncoding the character set encoding to be used on the XML.
    * @param   defaultNamespace  the default namespace to be added to the root
    * element (unless addDefaultNamespace will be set to <code>false</code>).
    * @param   flags serialization flags controlling which information on
    * records and attributes are serialized.
    * @param   direction   the direction (REQUEST/RESPONSE) of the formatted
    * record/array.
    * @param   omitXmlDeclaration   if <code>true</code> the XML declaration
    * (first line) is omitted from the result.
    */
   public FndXmlString(String xmlEncoding,
                         String defaultNamespace,
                         int flags,
                         FndRecordFormat.Direction direction,
                         boolean omitXmlDeclaration) {
      this.defaultNamespace = defaultNamespace;
      this.flags = flags;
      this.direction = direction;

      doc = new FndByteBufferOutputStream(1024);
      try {
         writer = new OutputStreamWriter(doc, FndSerializeConstants.BUFFER_CHARSET);
      }
      catch(UnsupportedEncodingException e) {
         throw new IfsRuntimeException(e, "XMLSTR_CREATE_WRITTER:Cannot create FndXmlString: &1", e.getMessage());
      }

      if(!omitXmlDeclaration) {
         append("<?xml version=\"1.0\" encoding=\"");
         append(xmlEncoding);
         append("\"?>");
         newLine();
      }
   }

   /**
    * Constructs a FndXmlString instance with a xml header.
    * @param   xmlEncoding the character set encoding to be used on the XML.
    * @param   defaultNamespace  the default namespace to be added to the root
    * element (unless addDefaultNamespace will be set to <code>false</code>).
    * @param   flags serialization flags controlling which information on
    * records and attributes are serialized.
    * @param   direction   the direction (REQUEST/RESPONSE) of the formatted
    * record/array.
    */
   public FndXmlString(String xmlEncoding,
                       String defaultNamespace,
                       int flags,
                       FndRecordFormat.Direction direction) {
      this(xmlEncoding, defaultNamespace, flags, direction, false);
   }

   /**
    * Constructs a FndXmlString instance with a xml header. Sets whether
    * record attributes are added or not.
    * @param   xmlEncoding the character set encoding to be used on the XML.
    * @param   defaultNamespace  the default namespace to be added to the root
    *          element (unless addDefaultNamespace will be set to <code>false</code>).
    * @param   addRecordAttributes flag indicating if the XML file should contain
    *          IFS specific record attributes (in namespace <code>ifsrecord</code>)
    * @param direction the direction (REQUEST/RESPONSE) of the formatted record/array
    */
   public FndXmlString(String xmlEncoding,
                       String defaultNamespace,
                       boolean addRecordAttributes,
                       FndRecordFormat.Direction direction) {
      this(xmlEncoding, defaultNamespace, (addRecordAttributes ? FLAG_ALL_INFO : 0), direction);
   }

   /**
    * Constructs a FndXmlString instance with a xml header. Sets whether
    * record attributes are added or not.
    * @param   xmlEncoding the character set encoding to be used on the XML.
    * @param   defaultNamespace  the default namespace to be added to the root
    *          element (unless addDefaultNamespace will be set to <code>false</code>).
    * @param   addRecordAttributes flag indicating if the XML file should contain
    *          IFS specific record attributes (in namespace <code>ifsrecord</code>)
    * @param direction the direction (REQUEST/RESPONSE) of the formatted record/array
    * @param   omitXmlDeclaration   if <code>true</code> the XML declaration
    * (first line) is omitted from the result.
    */
   public FndXmlString(String xmlEncoding,
                       String defaultNamespace,
                       boolean addRecordAttributes,
                       FndRecordFormat.Direction direction,
                       boolean omitXmlDeclaration) {
      this(xmlEncoding, defaultNamespace, (addRecordAttributes ? FLAG_ALL_INFO : 0), direction, omitXmlDeclaration);
   }

   /**
    * Constructs a FndXmlString instance with a xml header. Sets whether
    * record attributes are added or not.
    * @param   xmlEncoding the character set encoding to be used on the XML.
    * @param   defaultNamespace  the default namespace to be added to the root
    *          element (unless addDefaultNamespace will be set to <code>false</code>).
    * @param   addRecordAttributes flag indicating if the XML file should contain
    *          IFS specific record attributes (in namespace <code>ifsrecord</code>)
    */
   public FndXmlString(String xmlEncoding, String defaultNamespace, boolean addRecordAttributes) {
      this(xmlEncoding, defaultNamespace, addRecordAttributes, null);
   }

   /**
    * Constructs a FndXmlString instance with a xml header. With this constructor
    * a Business API is not in use.
    * @param   xmlEncoding the character set encoding to be used on the XML.
    * @param   defaultNamespace  the default namespace to be added to the root
    *          element (unless addDefaultNamespace will be set to <code>false</code>).
    */
   public FndXmlString(String xmlEncoding, String defaultNamespace) {
      this(xmlEncoding, defaultNamespace, false, null);
   }

   /**
    * Adds namespace attributes to the attributes list.
    */
   private FndXmlElementAttributeList addNameSpaces(FndXmlElementAttributeList attributes) {
      if (attributes == null)
         attributes = new FndXmlElementAttributeList();

      attributes.add("xmlns", defaultNamespace, true);
      if (addRecordAttributes())
         attributes.add("xmlns:ifsrecord", "urn:ifsworld-com:ifsrecord", true);
      attributes.add("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance", true);

      return attributes;
   }

   /**
    * Sets the flag indicating if the formatted records and attributes should be destroyed.
    * @param flag true to destroy formatted records and attributes
    */
   public void setDestroyFormattedRecords(boolean flag) {
      destroyFormattedRecords = flag;
   }

   /**
    * Gets the flag indicating if the formatted records and attributes should be destroyed.
    * @return true if the formatted records and attributes should be destroyed, false otherwise
    */
   @Override
   public boolean destroyFormattedRecords() {
      return destroyFormattedRecords;
   }

   /**
    * Returns <code>true</code> if some default namespaces should be added to
    * the root element or not. Default is <code>true</code>.
    * @return  <code>true</code> if the XML document's root element should have
    * some namespace attributes added automatically.
    */
   public boolean addDefaultNamespaces() {
      return addDefaultNamespaces;
   }

   /**
    * Adds a simple element to the document.
    * @param   name  the name of the element.
    * @param   attributes a FndXmlElementAttributeList object with name-value pairs constituting
    * the element's attributes.
    */
   @Override
   public void addElement(String name, FndXmlElementAttributeList attributes) {
      append("<", name);
      if (attributes != null && attributes.size() > 0)
         append(attributes.toString());
      append("/>");
   }

   /**
    * Returns <code>true</code> if the XML file in production should contain
    * IFS specific record attributes (in namespace <code>ifsrecord</code>). For
    * example BizApi's don't use them.
    * @return <code>true</code> if the XML document should contain any of the
    * record attributes (that is, if any serializaton flag is set).
    */
   @Override
   public boolean addRecordAttributes() {
      return flags > 0;
   }

   /**
    * Returns the direction (REQUEST/RESPONSE) of the formatted record or array.
    * @return an instance of FndRecordFormat.Direction, or null if the direction is unspecified.
    */
   @Override
   public FndRecordFormat.Direction getDirection() {
      return direction;
   }

   /**
    * Appends a <code>String</code> to the xml document being generated. If the
    * <code>String</code> is <code>null</code>, it will not be appended.
    * @param s1 string to append
    */
   @Override
   public void append(String s1) {  // Note: Only this method may append to 'doc' reference.
      try {
         if (s1 != null) {
            if(indent && indentNext) {
               for(int i=0; i<indentLevel; i++)
                  writer.write(indentString);
               indentNext = false;
            }

            writer.write(s1);
         }
      }
      catch(IOException e) {
         throw new IfsRuntimeException(e, "XMLSTR_APPEND_STR:Cannot append to FndXmlString: &1", e.getMessage());
      }
   }

   /**
    * Appends two <code>String</code>s to the xml document being generated.
    * If any of the <code>String</code>s are <code>null</code>, it will not be appended.
    * @param s1 first string
    * @param s2 second string
    */
   @Override
   public void append(String s1, String s2) {
      append(s1);
      append(s2);
   }

   /**
    * appends three <code>String</code>s to the xml document being generated.
    * If any of the <code>String</code>s are <code>null</code>, it will not be appended.
    * @param s1 first string
    * @param s2 second string
    * @param s3 third string
    */
   @Override
   public void append(String s1, String s2, String s3) {
      append(s1);
      append(s2);
      append(s3);
   }

   /**
    * Appends four <code>String</code>s to the xml document being generated.
    * If any of the <code>String</code>s are <code>null</code>, it will not be appended.
    * @param s1 first string
    * @param s2 second string
    * @param s3 third string
    * @param s4 fourth string
    */
   @Override
   public void append(String s1, String s2, String s3, String s4) {
      append(s1);
      append(s2);
      append(s3);
      append(s4);
   }

   /**
    * Appends five <code>String</code>s to the xml document being generated.
    * If any of the <code>String</code>s are <code>null</code>, it will not be appended.
    * @param s1 first string
    * @param s2 second string
    * @param s3 third string
    * @param s4 fourth string
    * @param s5 fifth string
    */
   @Override
   public void append(String s1, String s2, String s3, String s4, String s5) {
      append(s1);
      append(s2);
      append(s3);
      append(s4);
      append(s5);
   }

   /**
    * Writes an XML comment with the data enclosed.
    * @param data the data contained in the comment, may be null
    */
   @Override
   public void appendComment(String data) {
      append("<!--", data, "-->");
   }

   /**
    * Append UTF-8 encoded XML text to this document.
    * @param data array of bytes to append
    */
   @Override
   public void appendXml(byte[] data) {
      try {
         writer.flush();
         doc.write(data, 0, data.length);
      }
      catch (IOException e) {
         throw new IfsRuntimeException(e, "XMLSTR_APPEND_XML:Cannot append XML to FndXmlString: &1", e.getMessage());
      }
   }

   /**
    * Perform a BASE64 encoding of an array of bytes and append the result to this document.
    * @param data array of bytes to encode and append
    */
   @Override
   public void appendBase64(byte[] data) {
      ByteArrayInputStream in = new ByteArrayInputStream(data);
      try {
         writer.flush();
         (new Base64()).encode(in, doc);
      }
      catch (IOException e) {
         throw new IfsRuntimeException(e, "XMLSTR_APPEND_BASE64:Cannot append BASE64 data to FndXmlString: &1", e.getMessage());
      }
   }
   /**
    * Encodes necessary special characters in a string. XML requires some
    * characters (such as '&') to be specially encoded when used as attribute
    * values.
    * @param   s  the string to encode.
    * @return a String with the special characters encoded.
    */

   @Override
   public String encode(String s) {
      return xmlEncode(s);
   }

   /**
    * Encodes necessary special characters in a string. XML requires some
    * characters (such as '&') to be specially encoded when used as attribute
    * values. Identical to the method encode.
    * @param   s  the string to encode.
    * @return a String with the special characters encoded.
    */
   public static String xmlEncode(String s) {
      if(!needEncoding(s))
         return s;

      FndAutoString result = new FndAutoString(2 * s.length());
      char c;
      int len = s.length();
      for (int i = 0; i < len; i++) {
         c = s.charAt(i);
         switch (c) {
            case '&' :
               result.append("&amp;");
               break;
            case '\'' :
               result.append("&apos;");
               break;
            case '>' :
               result.append("&gt;");
               break;
            case '<' :
               result.append("&lt;");
               break;
            case '"' :
               result.append("&quot;");
               break;
            case '\r' :
               result.append("&#xD;");
               break;
            case '\n' :
               result.append("&#xA;");
               break;

            default :
               if(((int)c<32)&&(c!='\t'))
                  result.append(UNSUPPORTED_CHR_TAG+(int)c+";"); // encode any non-printable characters
               else
                  result.append(c);
         }
      }

      return result.toString();
   }

   private static boolean needEncoding(String s) {
      int len = s.length();
      for (int i = 0; i < len; i++) {
         switch (s.charAt(i)) {
            case '&' :
            case '\'':
            case '>' :
            case '<' :
            case '"' :
            case '\r' :
            case '\n' :
               return true;
            default :
               if((int)(s.charAt(i))<32)
                  return true;
         }
      }
      return false;
   }

   public static String decode(String s) {
      if(s.indexOf(UNSUPPORTED_CHR_TAG) < 0)
         return s;

      String[] subString = s.split(UNSUPPORTED_CHR_TAG);
      FndAutoString result = new FndAutoString(2 * s.length());
      if (subString.length>=0)
         result.append(subString[0]);
      for (int i =1 ;i<subString.length; i++){
         String sub=subString[i];
         int index= sub.indexOf(';');
         if (index>0) {
            String num = sub.substring(0,index);
            if(isNumeric(num)){
               try{
                  result.append((char)(Integer.parseInt(num)));
                  result.append(sub.substring(index+1));
               }
               catch(NumberFormatException e) {
                  result.append(UNSUPPORTED_CHR_TAG+sub);
               }
            }
            else
               result.append(UNSUPPORTED_CHR_TAG+sub);
         }
         else{
            result.append(UNSUPPORTED_CHR_TAG+sub);
         }
      }
      return result.toString();
   }

   private static boolean isNumeric(final String s) {
      final char[] numbers = s.toCharArray();
      for (int x = 0; x < numbers.length; x++) {
         final char c = numbers[x];
         if ((c >= '0') && (c <= '9')) continue;
         return false; // invalid
      }
      return true; // valid
   }

   /**
    * Adds an end element to the document.
    * @param   name  the name of the element.
    */
   @Override
   public void endElement(String name) {
      indentLevel--;
      append("</", name, ">");
   }

   /**
    * Checks if a serialization flag is set.
    * @param   flag  the flags to check. May be any of the <code>FLAG_</code>*
    * constants of this class (except FLAG_ALL_INFO).
    * @return  <code>true</code> if the flag is set, <code>false</code>
    * otherwise.
    */
   @Override
   public boolean isFlagSet(int flag) {
      return (flags & flag) != 0;
   }

   /**
    * Appends a new line sequence to the document.
    */
   @Override
   public void newLine() {
      append(NL);
      indentNext = true;
   }

   /**
    * Sets the flag if default namespace attributes should be added to the
    * root element or not.
    * @param   on if <code>true</code>, some default namespace attributes are
    * automatically added to the root element.
    */
   public void setAddDefaultNamespaces(boolean on) {
      this.addDefaultNamespaces = on;
   }

   /**
    * Sets the serialization flags.
    * @param   flags the flags to set. Can be any of the <code>FLAG_*</code>
    * constants of this class or a bitwise or (<code>|</code>) combination of
    * them (except for FLAG_ALL_INFO).
    */
   public void setFlags(int flags) {
      this.flags = flags;
   }

   /**
    * Sets whether to use proper indentation of the XML produced or not. Either
    * way, well-formed XML is produced.
    * @param   on if <code>true</code>, the XML produced is indented.
    */
   public void setIndent(boolean on) {
      indent = on;
   }

   /**
    * Adds a start element to the document.
    * @param   name  the name of the element.
    */
   @Override
   public void startElement(String name) {
      startElement(name, null);
   }

   /**
    * Adds a start element to the document.
    * @param   name  the name of the element.
    * @param   attributes a FndXmlElementAttributeList object with name-value pairs constituting
    * the element's attributes.
    */
   @Override
   public void startElement(String name, FndXmlElementAttributeList attributes) {
      if (isFirstElement) {
         if(addDefaultNamespaces)
            attributes = addNameSpaces(attributes);
         isFirstElement = false;
      }

      append("<", name);
      if (attributes != null && attributes.size() > 0)
         append(attributes.toString());
      append(">");

      indentLevel++;
   }

   /**
    * Returns a String representation of this object.
    * Note! This method converts the internal byte buffer to String.
    * @return string representation of this object
    * @see #getBytes()
    */
   @Override
   public String toString() {
      try {
         return new String(getBytes(), FndSerializeConstants.BUFFER_CHARSET);
      }
      catch(IOException e) {
         throw new IfsRuntimeException(e, "XMLSTR_CONVERT:Cannot convert FndXmlString: &1", e.getMessage());
      }
   }

   /**
    * Returns an XML document contained in this object.
    * @return UTF-8 encoded XML document being the contents of this object
    */
   public byte[] getBytes() {
      try {
         writer.flush();
         return doc.getBytes();
      }
      catch (IOException e) {
         throw new IfsRuntimeException(e, "XMLSTR_GETBYTES:Cannot get XML from FndXmlString: &1", e.getMessage());
      }
   }

   /**
    * Checks if a text contains valid XML characters.
    * In XML 1.0 characters 0 .. 31 are illegal (with the exception for HT, LF, CR)
    * @return -1 if the text is valid, otherwise the index of the first illegal character
    */
   public static int validateXmlText(String text) {
      if(text == null)
         return -1;

      int len = text.length();
      for(int i=0; i<len; i++) {
         char ch = text.charAt(i);
         if(ch < ' ') {
            switch(ch) {
               case '\t':
               case '\n':
               case '\r':
                  break;

               default:
                  return i;
            }
         }
      }
      return -1;
   }


   /**
    * Add xs:annotation and xs:documentation tags to the XML document.
    * @param termDef term definition text
    * @param definition technical description
    */
   @Override
   public void addXsdDocumentation(String termDef, String definition) throws ParseException {
      FndXmlStreamUtil.addXsdDocumentation(this, termDef, definition);
   }

   /**
    * Add documentation tag to the XML document.
    * @param termDef term definition text
    * @param definition technical description
    */
   @Override
   public void addWsdlDocumentation(String termDef, String definition) throws ParseException {
      FndXmlStreamUtil.addWsdlDocumentation(this, termDef, definition);
   }
}