/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;

/**
 * Base class for detail views to entities built with IFS Foundation1.
 */
public class FndLUDetailView extends FndLUEntityView {

   /**
    * Framework internal constructor required by Externalizable interface.
    */
    public FndLUDetailView() {
        assert(false);
    }

    protected FndLUDetailView(FndRecordMeta meta) {
        super(meta);
    }
}