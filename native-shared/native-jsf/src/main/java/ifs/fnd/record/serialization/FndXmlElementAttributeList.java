/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record.serialization;

import ifs.fnd.base.ParseException;
import java.util.*;

/**
 * <B>Framework internal class:</B> Class that handles a list of xml tag attributes. For internal use by the
 * IFS Java Application Server Framework.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public final class FndXmlElementAttributeList {
   /**
    * The list of entries.
    */
   private List<Entry> values;

   public FndXmlElementAttributeList() {
      values = new ArrayList<>();
   }

   /**
    * Adds an attribute to the list. The attribute will <I>not</I> be added if
    * either <code>name</code> or <code>value</code> is <code>null</code>.
    * @param   name  the name of the attribute.
    * @param   value the attribute's value.
    * @param   addFirst if <code>true</code> the attribute will be prepended to
    * the list of attributes.
    */
   public void add(String name, String value, boolean addFirst) {
      if (name != null && value != null) {
         if (addFirst)
            values.add(0, new Entry(name, value, true));
         else
            values.add(new Entry(name, value));
      }
   }

   /**
    * Adds an attribute to the list. The attribute will <I>not</I> be added if
    * either <code>name</code> or <code>value</code> is <code>null</code>.
    * @param   name  the name of the attribute.
    * @param   value the attribute's value.
    */
   public void add(String name, String value) {
      add(name, value, false);
   }

   /**
    * Clears all attributes from the list.
    */
   public void clear() {
      values.clear();
   }

   /**
    * Returns the number of attributes in the list.
    */
   public int size() {
      return values.size();
   }

   /**
    * Returns a String representation of this object.
    */
   @Override
   public String toString() {
      if(size() == 0)
         return "";
      FndAutoString s = new FndAutoString(64);
      Iterator itr = values.iterator();
      Entry next;
      while (itr.hasNext()) {
         next = (Entry) itr.next();
         s.append(" ");
         s.append(next.name);
         s.append("=\"");
         s.append(next.value);
         s.append("\"");
      }

      return s.toString();
   }

   /**
    * Writes all attributes to the specified XML stream.
    * @param writer XML writer to writes attributes to
    */
   public void write(FndXmlWriter writer) throws ParseException {
      if(size() == 0)
         return;
      Iterator itr = values.iterator();
      while (itr.hasNext()) {
         Entry attr = (Entry) itr.next();
         if(attr.isNamespace) {
            String name = attr.name;
            if(attr.isNamespace && attr.name.startsWith("xmlns:"))
               name = name.substring("xmlns:".length());
            writer.writeNamespace(name, attr.value);
         }
         else
            writer.writeAttribute(attr.name, attr.value);
      }
   }

   /**
    * Helper class for keeping the entries in the attribute list.
    */
   private static class Entry {
      String name, value;
      boolean isNamespace;

      public Entry(String name, String value) {
         this(name, value, false);
      }

      public Entry(String name, String value, boolean isNamespace) {
         this.name = name;
         this.value = value;
         this.isNamespace = isNamespace;
      }

      @Override
      public String toString() {
         return name + "=\"" + value + "\"";
      }
   }
}