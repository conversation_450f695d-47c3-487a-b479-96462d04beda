/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record.serialization;

import ifs.fnd.base.*;
import ifs.fnd.util.XmlUtil;

import javax.xml.namespace.QName;
import javax.xml.stream.*;

import java.io.*;

/**
 * Class that reads data from an XML stream.
 */
public class FndXmlReader {

   /**
    * Event types used by this class.
    */
   public static final int START_ELEMENT  = XMLStreamConstants.START_ELEMENT;
   public static final int END_ELEMENT    = XMLStreamConstants.END_ELEMENT;
   public static final int CHARACTERS     = XMLStreamConstants.CHARACTERS;
   public static final int START_DOCUMENT = XMLStreamConstants.START_DOCUMENT;
   public static final int END_DOCUMENT   = XMLStreamConstants.END_DOCUMENT;
   public static final int CDATA          = XMLStreamConstants.CDATA;
   public static final int SPACE          = XMLStreamConstants.SPACE;

   /**
    * XML stream to read character data from.
    */
   private XMLStreamReader reader;

   /**
    * Flag indicating if the last call to getElementText consumed a CDATA event.
    */
   private boolean wasCData;

   /**
    * Temporary buffer used to concatenate text events.
    */
   private FndAutoString text;

   /**
    * Create FndXmlReader for specified input stream and UTF-8 encoding.
    */
   public FndXmlReader(InputStream input) throws ParseException {
      this(null, input, FndXmlStreamUtil.UTF8);
   }

   /**
    * Create FndXmlReader for specified input stream and encoding.
    */
   public FndXmlReader(InputStream input, String encoding) throws ParseException {
      this(null, input, encoding);
   }

   /**
    * Create FndXmlReader for specified reader.
    */
   public FndXmlReader(Reader reader) throws ParseException {
      this(reader, null, null);
   }


   /**
    * Create FndXmlReader for specified reader or input stream and encoding.
    */
   private FndXmlReader(Reader reader, InputStream input, String encoding) throws ParseException {
      try {
         XMLInputFactory inFactory = XmlUtil.newXMLInputFactory();//XMLInputFactory.newInstance();

         inFactory.setProperty(XMLInputFactory.IS_NAMESPACE_AWARE ,             Boolean.TRUE);     //  true
         inFactory.setProperty(XMLInputFactory.IS_COALESCING ,                  Boolean.FALSE);    //  false
         inFactory.setProperty(XMLInputFactory.IS_REPLACING_ENTITY_REFERENCES,  Boolean.TRUE);     //  true
         inFactory.setProperty(XMLInputFactory.IS_SUPPORTING_EXTERNAL_ENTITIES, Boolean.FALSE);    //   -

         if (inFactory.isPropertySupported("http://java.sun.com/xml/stream/properties/report-cdata-event")) {
            inFactory.setProperty("http://java.sun.com/xml/stream/properties/report-cdata-event", Boolean.TRUE);
         }

         if(reader != null)
            this.reader = inFactory.createXMLStreamReader(reader);
         else if(encoding == null)
            this.reader = inFactory.createXMLStreamReader(input);
         else
            this.reader = inFactory.createXMLStreamReader(input, encoding);
         text = new FndAutoString();
      }
      catch(XMLStreamException e) {
         throw new ParseException(e, "XMLREADER_CREATE:Cannot create FndXmlWriter: &1", e.getMessage());
      }
   }

   /**
    * Returns the encapsulated instance of XMLStreamReader.
    */
   XMLStreamReader getXMLStreamReader() {
      return reader;
   }

   /**
    * Moves the cursor to the next parsing event.
    * The cursor stops on one of the following events:
    * START_DOCUMENT, END_DOCUMENT, START_ELEMENT, END_ELEMENT.
    * Other events like COMMENT are ignored.
    * Events of type CHARACTERS or CDATA should be consumed by calling getElementText on START_ELEMENT.
    * @return the type of the current event
    * @see #getElementText
    * @see #next(boolean)
    */
   public int next() throws ParseException {
      return next(false);
   }

   /**
    * Moves the cursor to the next parsing event.
    * The cursor stops on one of the following events:
    * START_DOCUMENT, END_DOCUMENT, START_ELEMENT, END_ELEMENT.
    * Other events like COMMENT are ignored.
    * <p>
    * If the parameter concatenateText is true then text events between START_ELEMENT and END_ELEMENT
    * will be concatenated and may be retrieved by calling getElementText on END_ELEMENT.
    * <p>
    * If the parameter concatenateText is false (which is the default value) then text events must by
    * consumed by calling getElementText on START_ELEMENT.
    * <p>
    * @param concatenateText if true then text events will be concatenated, otherwise they will be ignored
    * @return the type of the current event
    * @see #getElementText
    */
   public int next(boolean concatenateText) throws ParseException {
      try {
         wasCData = false;
         while(reader.hasNext()) {
            int type = reader.next();

            switch(type) {
               case FndXmlReader.START_ELEMENT:
                  text.clear();

               case FndXmlReader.START_DOCUMENT:
               case FndXmlReader.END_DOCUMENT:
               case FndXmlReader.END_ELEMENT:
                  return type;

               case FndXmlReader.CDATA:
                  wasCData = true;

               case FndXmlReader.CHARACTERS:
                  if(concatenateText)
                     text.append(reader.getText());
                  else if(!reader.isWhiteSpace())
                     throw new ParseException("XMLPARSE_NONSPACE: Unexpected non-whitespace text event at &1: &2",
                                               getLocation(), reader.getText());
                  break;

               case XMLStreamConstants.SPACE:
               case XMLStreamConstants.ENTITY_REFERENCE:
                  if(concatenateText)
                     text.append(reader.getText());
                  break;
                  
               default :
            }
         }
      }
      catch(XMLStreamException e) {
         throw new ParseException(e, "XMLREADER_NEXT:Cannot move to next XML event at &1: &2", getLocation(), e.getMessage());
      }

      throw new ParseException("XMLPARSE_EOS: Unexpected end of stream when reading next event at &1", getLocation());
   }

   /**
    * Returns an integer code that indicates the type of the event the cursor is pointing to.
    * @return the type of the current parsing event
    */
   public int getEventType() {
      return reader.getEventType();
   }

   /**
    * Gets the name of the current START_ELEMENT or END_ELEMENT event.
    * @return the QName for the current event.
    * @throws IllegalStateException - if the current event is not a START_ELEMENT or END_ELEMENT
    */
   public QName getName() {
      return reader.getName();
   }

   /**
    * Gets the content of a text-only element.
    * The current event must be of type START_ELEMENT or END_ELEMENT. The method concatenates text events
    * between START_ELEMENT and END_ELEMENT. COMMENT, DTD  and ENTITY_REFERENCE events are ignored.
    * CHARACTERS, CDATA and SPACE events are concatenated into one string value.
    * When the method returns the cursor is on END_ELEMENT.

    * @return string value corresponding to text-events between the START_ELEMENT and the corresponding END_ELEMENT,
    *         or null if there were no such events
    */
   public String getElementText() throws ParseException {

      try {
         switch(getEventType()) {
            case XMLStreamConstants.END_ELEMENT:
               return text.length() == 0 ? null : text.toString();

            case XMLStreamConstants.START_ELEMENT:
               break;

            default:
               throw new ParseException("XMLPARSE_STARTTEXT: Cursor must be on START_ELEMENT or END_ELEMENT to get element text. Current event type: &1 at &2",
                                        FndXmlStreamUtil.getEventTypeName(getEventType()), getLocation());
         }

         text.clear();

         while(reader.hasNext()) {
            int type = reader.next();
            switch(type) {
               case XMLStreamConstants.END_ELEMENT:
                  return text.length() == 0 ? null : text.toString();

               case XMLStreamConstants.CDATA:
                  wasCData = true;

               case XMLStreamConstants.CHARACTERS:
               case XMLStreamConstants.SPACE:
               case XMLStreamConstants.ENTITY_REFERENCE:
                  text.append(reader.getText());
                  break;

               case XMLStreamConstants.PROCESSING_INSTRUCTION:
               case XMLStreamConstants.COMMENT:
                  // ignore
                  break;

               default:
                  throw new ParseException("XMLPARSE_ELEMTEXT: Unexpected event type [&1] when reading element text content at &2",
                                           FndXmlStreamUtil.getEventTypeName(type), getLocation());
            }
         }
      }
      catch(XMLStreamException e) {
         throw new ParseException(e, "XMLREADER_TEXTELEM:Cannot read XML text content at &1: &2", getLocation(), e.getMessage());
      }


      throw new ParseException("XMLPARSE_ENDTEXT: Unexpected end of stream when reading text content at &1", getLocation());
   }

   /**
    * Indicates if the last call to getElementText consumed a CDATA event.
    * @return true if CDATA event was consumed during the last call to getElementText
    * @see #getElementText
    */
   public boolean wasCData() {
      return wasCData;
   }

   /**
    * Checks if the current event is whitespace.
    * @return Returns true if the cursor points to a character data event that consists only of whitespace
    */
   public boolean isWhiteSpace() {
      return reader.isWhiteSpace();
   }

   /**
    * Gets value of an XML attribute.
    * @param name the name of an attribute
    * @return returns the value of the attribute, or null if not found
    */
   public String getAttributeValue(QName name) {
      String prefix = name.getPrefix();
      String localName = name.getLocalPart();
      int count = reader.getAttributeCount();
      for(int i = 0; i < count; i++) {
         if(reader.getAttributePrefix(i).equals(prefix) && reader.getAttributeLocalName(i).equals(localName))
            return reader.getAttributeValue(i);
      }
      return null;
      //return reader.getAttributeValue(name.getNamespaceURI(), name.getLocalPart());
   }

   /**
    * Closes this reader and its underlying XML reader.
    * This method does not close the underlying input source.
    */
   public void close() throws ParseException {
      try {
         reader.close();
      }
      catch(XMLStreamException e) {
         throw new ParseException(e, "XMLREADER_CLOSE:Cannot close XML stream: &1", e.getMessage());
      }
   }

   /**
    * Get a string representation of the current location in the XML stream.
    */
   public String getLocation() {
      Location loc = reader.getLocation();
      return "[" + loc.getLineNumber() + ":" + loc.getColumnNumber() + "]";
   }

   /**
    * Skip all XML events until the element with given type and local name.
    * @param type START_ELEMENT or END_ELEMENT
    * @param name the local name of XML element to stop at
    *
    */
   public void skipUntil(int type, String name) throws ParseException {
      try {
         while(true) {
            if(reader.next() == type && reader.getName().getLocalPart().equals(name))
               return;
         }
      }
      catch(XMLStreamException e) {
         throw new ParseException(e, "XMLREADER_SKIP:Cannot skip events in XML stream: &1", e.getMessage());
      }
   }

   /**
    * Gets the number of attributes on the current START_ELEMENT.
    * This number excludes namespace definitions.
    * Attribute indices are zero-based.
    * @return the number of attributes
    */
   public int getAttributeCount() {
      return reader.getAttributeCount();
   }

   /**
    * Gets the local name of the attribute at the specified index.
    * @param index the position of the attribute
    * @return the local name of the attribute
    */
   public String getAttributeLocalName(int index) {
      return reader.getAttributeLocalName(index);
   }

   /**
    * Gets the value of the attribute at the specified index.
    * @param index the position of the attribute
    * @return the attribute value
    */
   public String getAttributeValue(int index) {
      return reader.getAttributeValue(index);
   }
}
