/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record.serialization;

import ifs.fnd.base.ParseException;
import ifs.fnd.log.*;
import ifs.fnd.sf.storage.*;
import ifs.fnd.util.IoUtil;
import ifs.fnd.util.Str;
import javax.xml.stream.*;
import java.io.*;


/**
 * Utility class for XML serialization using streaming XML parser.
 */
public final class FndXmlStreamUtil implements XMLStreamConstants {

   //Avoid instantiation
   private FndXmlStreamUtil() {
   }

   /**
    * String constant representing UTF-8 encoding.
    */
   public static final String UTF8 = "UTF-8";

   /**
    * Add xs:annotation and xs:documentation tags to the XML document.
    * @param writer XML writer to write data to
    * @param termDef term definition text
    * @param definition technical description
    */
   public static void addXsdDocumentation(FndXmlSerializer writer, String termDef, String definition) throws ParseException {
      if( !Str.isEmpty(termDef) || !Str.isEmpty(definition) ) {
         writer.startElement("xs:annotation");
         writer.newLine();
         writer.startElement("xs:documentation");
         
         writer.append( FndXmlDocumentationFormatter.formatDocumentationText(termDef, definition) );

         writer.endElement("xs:documentation");
         writer.newLine();
         writer.endElement("xs:annotation");
         writer.newLine();
      }
   }

   /**
    * Add documentation tag to the XML document.
    * @param writer XML writer to write data to
    * @param termDef term definition text
    * @param definition technical description
    */
   public static void addWsdlDocumentation(FndXmlSerializer writer, String termDef, String definition) throws ParseException {
      if( !Str.isEmpty(termDef) || !Str.isEmpty(definition) ) {
         writer.startElement("documentation");
         
         writer.append( FndXmlDocumentationFormatter.formatDocumentationText(termDef, definition) );
         
         writer.endElement("documentation");
         writer.newLine();
      }
   }

   /**
    * Returns name of an XML event type.
    * @param eventType the type of an XML event
    * @return string representation of the specified event type
    */
   public static String getEventTypeName(int eventType) {
      switch(eventType) {
         case START_ELEMENT          : return "START_ELEMENT";
         case END_ELEMENT            : return "END_ELEMENT";
         case CHARACTERS             : return "CHARACTERS";
         case ATTRIBUTE              : return "ATTRIBUTE";
         case NAMESPACE              : return "NAMESPACE";
         case PROCESSING_INSTRUCTION : return "PROCESSING_INSTRUCTION";
         case COMMENT                : return "COMMENT";
         case START_DOCUMENT         : return "START_DOCUMENT";
         case END_DOCUMENT           : return "END_DOCUMENT";
         case CDATA                  : return "CDATA";
         case ENTITY_DECLARATION     : return "ENTITY_DECLARATION";
         case ENTITY_REFERENCE       : return "ENTITY_REFERENCE";
         case NOTATION_DECLARATION   : return "NOTATION_DECLARATION";
         case SPACE                  : return "SPACE";
      }
      throw new IllegalArgumentException("Illegal XMLEvent type " + eventType);
   }
   
   private static String debugElement(int eventType, XMLStreamReader reader) {
      String typeName = getEventTypeName(eventType);
      switch(eventType) {
         case START_ELEMENT : 
         case END_ELEMENT   : return typeName + " ["+reader.getLocalName()+"]";
         case CDATA         :
         case CHARACTERS    : return typeName + " ["+reader.getText()+"]";
      }
      return typeName;
   }

   /**
    * Copies the current element or entire document depending on the first element,
    * which can be START_DOCUMENT or START_ELEMENT
    * @param reader source reader
    * @param writer destination writer
    * @return event type for next element
    */
   public static int copy(FndXmlReader reader, FndXmlWriter writer) throws ParseException {
      Logger log = LogMgr.getClassLogger(FndXmlStreamUtil.class);
      if(log.info) log.info("Copying XML document...");
      
      XMLStreamReader in  = reader.getXMLStreamReader();
      boolean entireDoc = false;

      try {
         if(in.hasNext()) {
            int type = in.getEventType(); //when XMLStreamReader is created, it is positioned at START_DOCUMENT event.
            if(log.debug) log.debug("First element: '&1'", debugElement(type, in));

            if(type == START_DOCUMENT) {
               entireDoc = true;
               type = in.next();
               skipSpaces(in, log);
            }

            type = copyElement(in, writer, log);

            if(log.debug) log.debug("Last copied element: '&1'", debugElement(type, in));
            if(entireDoc) {
               type = in.next();
               type = skipSpaces(in, log);
               if(type != END_DOCUMENT)
                  throw new ParseException("XMLUTIL_NOENDDOC: Malformed XML document: found '&1' when expecting end document.", debugElement(type, in));
            }
            return type;
         }
         if(log.info) log.info("The XML document doesn't containt any element. Niothing to copy.");
         return -1;
      } catch(XMLStreamException e) {
         throw new ParseException(e, "XMLUTIL_COPYDOC: Cannot copy XML document: &1", e.getMessage());
      }
   }

   /**
    * Copies the current element.
    * @param reader source reader
    * @param writer destination writer
    * @return event type for next element
    */
   private static int copyElement(XMLStreamReader reader, FndXmlWriter writer, Logger log) throws ParseException, XMLStreamException {
      int type = reader.getEventType();
      int level = 0;
      boolean inElement = false;

      do {
         if(log.debug) log.debug("Copying element: '&1'", debugElement(type, reader));
         switch(type) {
            case START_ELEMENT:
               level++;
               inElement = true;
               writeEntireStartElement(reader, writer);
               break;

            case END_ELEMENT:
               level--;
               if(level < 0)
                  throw new ParseException("XMLUTIL_ENDELEM: Malformed XML document: found end element without matching start element.");
               if(!inElement)
                  writer.newLine();
               writer.writeEndElement();
               writer.newLine();
               inElement = false;
               break;

            case CDATA:
               if(level == 0)
                  throw new ParseException("XMLUTIL_UNEXPCDATA: Malformed XML document: unexpected CDATA element before first start element.");
               writer.writeCData(reader.getText());
               break;

            case CHARACTERS:
               if(level == 0)
                  throw new ParseException("XMLUTIL_UNEXPCHARS: Malformed XML document: unexpected characters before first start element.");
               writer.writeCharacters(reader.getText());
               break;
               
            default:
               if(level == 0)
                  throw new ParseException("XMLUTIL_UNEXPELEM: Malformed XML document: unexpected element of type '&1' before first start element.", getEventTypeName(type));
         }

         if(level==0) break;
         type = reader.next();

      } while(reader.hasNext());

      if(level>0)
         throw new ParseException("XMLUTIL_NOENDELEM: Malformed XML document: missing end element.");

      return type;
   }
   
   private static int skipSpaces(XMLStreamReader reader, Logger log) throws XMLStreamException {
      int type = reader.getEventType();
      while(reader.hasNext() && type==SPACE) {
         if(log.debug) log.debug("Skiping SPACE...");
         type = reader.next();
      }
      return type;
   }

   private static void writeEntireStartElement(XMLStreamReader in, FndXmlWriter out) throws XMLStreamException, ParseException {
      writeStartElement(in, out);
      writeNamespaces(in, out);
      writeAttributes(in, out);
   }

   private static void writeStartElement(XMLStreamReader in, FndXmlWriter out) throws XMLStreamException, ParseException {
      String prefix = in.getPrefix();
      String localName = in.getLocalName();

      if(prefix != null && prefix.length() > 0)
         localName = prefix+":"+localName;

      out.writeStartElement(localName);
   }

   private static void writeNamespaces(XMLStreamReader in, FndXmlWriter out) throws XMLStreamException, ParseException {
      int count = in.getNamespaceCount();
      for(int i = 0; i < count; i++) {
         String prefix = in.getNamespacePrefix(i);
         String uri = in.getNamespaceURI(i);
         out.writeNamespace(prefix, uri);
      }
   }

   private static void writeAttributes(XMLStreamReader in, FndXmlWriter out) throws XMLStreamException, ParseException {
      int count = in.getAttributeCount();
      for(int i = 0; i < count; i++) {
         out.writeAttribute(in.getAttributePrefix(i), in.getAttributeLocalName(i), in.getAttributeValue(i));
      }
   }

   public static void main(String[] args) throws Exception {
      FndXmlReader reader = new FndXmlReader(new FileInputStream(args[0]));
      FndXmlWriter writer = new FndXmlWriter(new FileOutputStream(args[1]));
      IoUtil.stdoutln("Copying XML document from '"+args[0]+"' to '"+args[1]+"'");
      copy(reader, writer);
   }
}
