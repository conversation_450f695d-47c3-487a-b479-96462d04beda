/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;

import ifs.fnd.base.IfsRuntimeException;
import ifs.fnd.log.Logger;

import java.text.Collator;
import java.text.CollationKey;

/**
 * <B>Framework internal class:</B> Class representing a sequence of run-time sort key values in a record.
 * Every record stores one (constant) instance of FndSortKey during sort operation.
 * FndSortKey may contain references to record attributes or it may contain
 * converted attribute values, which is used to speed up comparison process.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public final class FndSortKey implements Comparable {

   private Object[] subkeys;  // where Object is FndAttribute or CollationKey

   private FndAbstractRecord rec;   // used for formatting of error messages from compareTo()
   private FndSort.OrderBy orderBy; // used for formatting of error messages from compareTo()

   /**
    * Create a sort key for a record using specified order by clause.
    * @throws IfsRuntimeException if the orderBy clause is invalid for the specified record.
    */
   FndSortKey(FndAbstractRecord rec, FndSort.OrderBy orderBy, Collator collator) {
      this.rec = rec;
      this.orderBy = orderBy;
      try {
         String path[][] = orderBy.getAttributePaths();
         subkeys = new Object[orderBy.getAttributeCount()];
         for(int i=0; i<subkeys.length; i++) {
            FndAttribute attr = FndSort.getAttribute(rec, path[i]);
            if(attr.isNull())
               subkeys[i] = null;
            else if(attr instanceof FndText)
               subkeys[i] = collator.getCollationKey(((FndText)attr).getValue());
            else
               subkeys[i] = attr;
         }
      }
      catch(Exception any) {
         throw new IfsRuntimeException(any, "NEWSORTKEY:Invalid order by clause [&1] for record &2.&3",
                                       orderBy.getText(),
                                       rec.getMeta().getPackage(),
                                       rec.getName());
      }
   }

   /**
    * Compare this sort key to another sort key.
    * Returns a negative integer, zero, or a positive integer as this object is less than,
    * equal to, or greater than the specified object.
    * @throws IfsRuntimeException if comparison failed for any reason
    */
   @Override
   public int compareTo(Object obj) {
      FndSort.Direction[] direction = orderBy.getSortDirections();
      FndSortKey sortKey1 = this;
      FndSortKey sortKey2 = (FndSortKey)obj;

      int size = sortKey1.subkeys.length;
      for(int i=0; i<size; i++) {
         try {
            Object o1 = sortKey1.subkeys[i];
            Object o2 = sortKey2.subkeys[i];
            //
            //  compare(null,x)    ->  1
            //  compare(null,null) ->  0
            //  compare(x,null)    -> -1
            //
            if(o1 == null && o2 == null) {
               return 0;
            }
            else if (o1 == null) {
               return 1;
            }
            else if (o2 == null) {
               return -1;
            }

            if(o1 instanceof CollationKey) {
               CollationKey k1 = (CollationKey)o1;
               CollationKey k2 = (CollationKey)o2;
               int r = Integer.signum(k1.compareTo(k2));
               if(r != 0)
                  return direction[i]==FndSort.ASCENDING ? r : -r;
            }
            else {
               FndAttribute a1 = (FndAttribute)o1;
               FndAttribute a2 = (FndAttribute)o2;
               int r = Integer.signum(a1.compareTo(a2));
               if(r != 0)
                  return direction[i]==FndSort.ASCENDING ? r : -r;
            }
         }
         catch(Exception any) {
            throw new IfsRuntimeException(any, "CMPSORTKEY:Sorting records of type &1.&2 by [&3] failed on key attribute at position &4 with error: &5",
                                          rec.getMeta().getPackage(),
                                          rec.getName(),
                                          orderBy.getText(),
                                          String.valueOf(i),
                                          any.toString());
         }
      }
      return 0;
   }

   /**
    * Prints to debug output the actual values of the attributes included in this sort key.
    */
   public void debug(Logger log) {
      log.debug("FndSortKey for record &1", rec.toString());
      String paths[][] = orderBy.getAttributePaths();
      int count = orderBy.getAttributeCount();
      for(int i=0; i<count; i++) {
         try {
            FndAttribute attr = FndSort.getAttribute(rec, paths[i]);
            String path = FndSort.formatAttributePath(rec, attr, attr);
            log.debug("   &1 = &2", path, attr.isNull() ? "null" : attr.toString());
         }
         catch(Exception any) {
            throw new IfsRuntimeException(any, "DEBUGSORTKEY:FndSortKey.debug() for record of type &1.&2 sorted by [&3] failed on key attribute at position &4",
                                          rec.getMeta().getPackage(),
                                          rec.getName(),
                                          orderBy.getText(),
                                          String.valueOf(i));
         }
      }
   }

}
