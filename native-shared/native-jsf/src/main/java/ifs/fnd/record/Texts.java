/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;

import ifs.fnd.base.FndTranslatableText;

final class Texts {

   private static final String PKG = Texts.class.getPackage().getName();

   private Texts() {
      //Prevent instantiations
   }

   //FndAttribute
   static final FndTranslatableText UPPERVALUE = new FndTranslatableText("UPPERVALUE", "The value '&1' of &2 must be in upper case", PKG);
   static final FndTranslatableText MUSTHAVEVALUE = new FndTranslatableText("MUSTHAVEVALUE", "&1 must have a value", PKG);
   static final FndTranslatableText LONGBVALUE = new FndTranslatableText("LONGBVALUE", "The value of &1 may not be longer than &2 bytes (the current value is &3 bytes)", PKG);
   static final FndTranslatableText LONGCVALUE = new FndTranslatableText("LONGCVALUE", "The value of &1 may not be longer than &2 characters (the current value is &3 characters)", PKG);
   static final FndTranslatableText NOMODIFY = new FndTranslatableText("NOMODIFY", "&1 may not be modified", PKG);
   static final FndTranslatableText VALIDATEATTRUPDATE = new FndTranslatableText("VALIDATEATTRUPDATE", "Attribute &1 may not be updated", PKG);
   static final FndTranslatableText VALIDATEARRINSERT = new FndTranslatableText("VALIDATEARRINSERT", "No records may be added to &1", PKG);
   static final FndTranslatableText VALIDATEAGGINSERT = new FndTranslatableText("VALIDATEAGGINSERT", "New record can not be set on &1", PKG);
   static final FndTranslatableText VALIDATEREMOVE = new FndTranslatableText("VALIDATEREMOVE", "&1 may not be removed", PKG);
   static final FndTranslatableText FINALSTATEMOD = new FndTranslatableText("FINALSTATEMOD", "Entity &1 is in its final state and may not be modified", PKG);

}
