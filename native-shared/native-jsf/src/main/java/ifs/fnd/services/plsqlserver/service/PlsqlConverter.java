/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.services.plsqlserver.service;

import ifs.fnd.base.FndContext;
import ifs.fnd.base.IfsException;
import ifs.fnd.base.SystemException;
import ifs.fnd.base.capability.ApplicationCapability;
import ifs.fnd.buffer.BufferUtil;
import ifs.fnd.buffer.Item;
import ifs.fnd.record.FndSqlType;
import ifs.fnd.record.FndSqlValue;
import ifs.fnd.record.serialization.FndSerializeConstants;
import ifs.fnd.record.serialization.FndUtil;
import ifs.fnd.sf.storage.FndStatement;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

/**
 *<B>Framework internal class:</B> Class that performs data conversion between Buffer types, PlsqlTypes and JDBC types.
 *<p>
 * The input and output Buffer's Items are built using
 * the following conventions:
 *<pre>
 *   Name    name of a stored procedure argument, bind variable or returned column
 *   Value   always of type String
 *   Type    type marker, depending on the mapping specified at construction time
 *   Status  argument direction: "IN", "OUT", "IN_OUT" (defaults to "IN_OUT")
 *</pre>
 *
 * An implementation of {@link BufferTypeMap} specified at construction time
 * controls the string representation of data types in buffers.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class PlsqlConverter {

   /**
    * Formatters used to parse/format Date items
    */
   private final SimpleDateFormat timestampFormatter, dateFormatter, timeFormatter;

   /**
    * Implementation of BufferTypeMap used to map item types to/from PLSQL types.
    */
   private final BufferTypeMap typemap;

   /**
    * boolean flag indicating if the current client supports length-prefixed
    * strings.
    */
   private boolean lengthPrefixSupported = false;

   /**
    * Constructs a PlsqlConverter
    * @param typemap implementation of BufferTypeMap used to map item
    *                types to/from PLSQL types.
    */
   public PlsqlConverter(BufferTypeMap typemap) {
      timestampFormatter = new SimpleDateFormat("yyyy-MM-dd-HH.mm.ss", Locale.US);  // PlsqlUtil.serverDateMask
      timestampFormatter.setTimeZone(TimeZone.getDefault()); //TimeZone.getTimeZone("UTC"));
      timestampFormatter.setLenient(false); // force strict usage of format mask

      dateFormatter = new SimpleDateFormat("yyyy-MM-dd", Locale.US);
      dateFormatter.setTimeZone(TimeZone.getDefault());
      dateFormatter.setLenient(false);

      timeFormatter = new SimpleDateFormat("HH.mm.ss", Locale.US);
      timeFormatter.setTimeZone(TimeZone.getDefault());
      timeFormatter.setLenient(false);

      this.typemap = typemap;

      this.lengthPrefixSupported = FndContext.getCurrentContext()
              .getSupportedCapabilities().contains(ApplicationCapability.LPTEXT);
   }


   /**
    * Converts the specified Item into a FndSqlValue, which acts as JDBC parameter.
    * The String value of the Item, depending on the type marker, is parsed
    * into a Java Object (Date, Double, String) and then converted into a FndSqlValue.
    * Direction of the parameter is defined by the Item's status (IN, OUT, IN_OUT).
    * @param update true if the current statement is an UPDATE or INSERT statement
    */
   public FndSqlValue itemToFndSqlValue(int nr, Item item, boolean query, boolean update) throws SystemException {

      String name   = item.getName();
      String value  = item.getString();
      String status = item.getStatus();
      String type   = item.getType();

      FndSqlValue fndvalue = null;
      boolean isNull = value==null;
      int direction;

      if("IN".equals(status)) {
         direction = FndSqlValue.DIRECTION_IN;
      }
      else if("OUT".equals(status)) {
         direction = FndSqlValue.DIRECTION_OUT;
      }
      else if("IN_OUT".equals(status)) {
         direction = FndSqlValue.DIRECTION_INOUT;
      }
      else if(status==null || "*".equals(status) || "-".equals(status)) {
         direction = query ? FndSqlValue.DIRECTION_IN : FndSqlValue.DIRECTION_INOUT;
      }
      else {
         throw new SystemException(Texts.BADITEMSTATUS, status);
      }

      PlsqlType pltype = typemap.itemTypeToPlsqlType(type);

      if(pltype==PlsqlType.TEXT) {
         // FndSqlType.LONG_TEXT will be bound using character stream
         boolean isLong;
         if(name != null && name.endsWith("_clob_")) {
            isLong = true;
         }
         else {
            isLong = ((direction == FndSqlValue.DIRECTION_IN)
                 && update
                 && value != null
                 && value.length() > 2000);
         }

         fndvalue = new FndSqlValue(name, value, true, isLong); // isText, isLong
      }
      else if(pltype==PlsqlType.LONG_TEXT) {
         fndvalue = new FndSqlValue(name, value, true, true);
      }
      else if(pltype==PlsqlType.NUMBER) {
         switch (item.getType()) {
            case "DEC":
               BigDecimal dec = value==null ? null : new BigDecimal(value);
               fndvalue = new FndSqlValue(name, dec);
               break;
            case "I":
               long l = value==null ? 0 : Long.parseLong(value);
               fndvalue = new FndSqlValue(name, l);
               break;
            default:
               double n = value==null ? Double.NaN : Double.parseDouble(value);
               fndvalue = new FndSqlValue(name, n);
               break;
         }
      }
      else if(pltype==PlsqlType.TIMESTAMP) {
         Date d = value==null ? new Date() : parseDate(value, item.getType());
         fndvalue = new FndSqlValue(name, new Timestamp(d.getTime()));
      }
      else if(pltype==PlsqlType.BINARY) {
         try {
            // FndSqlType.BINARY will be bound using binary stream
            byte[] data = value==null ? null : FndUtil.fromBase64Text(value);
            fndvalue = new FndSqlValue(name, data);
         }
         catch(IOException io) {
            throw new SystemException(io, io.getMessage());
         }
      }
      else {
         throw invalidPlsqlType(pltype.getName());
      }

      fndvalue.setDirection(direction);
      if(isNull) {
         fndvalue.setNull();
      }

      return fndvalue;
   }


   /**
    * Converts an JDBC Parameter (FndSqlValue) into the specified Item.
    * Only the item's value for OUT/INOUT parameter is changed.
    * Type BINARY is not supported as an OUT/INOUT parameter.
    *
    * Note! If an OUT/INOUT JDBC parameter corresponds to an IN parameter
    *       in PL/SQL then the value assigned to the item is undefined
    *       (it may be null or 0)
    */
   public void parameterToItem(FndStatement stmt,
                                 int i,
                                 FndSqlValue inParam,
                                 Item item ) throws Exception {

      if(inParam.getDirection()==FndSqlValue.DIRECTION_IN) {
         return;
      }

      String value = null;

      FndSqlType pltype = inParam.getType();

      if(pltype==FndSqlType.TEXT) {
         String s = stmt.getString(i);
         if(!stmt.parameterWasNull()) {
            value = s;
         }
      }
      else if(pltype==FndSqlType.LONG_TEXT) {
         String s = stmt.getLongText(i);
         if(!stmt.parameterWasNull()) {
            value = s;
         }
      }
      else if(pltype==FndSqlType.DECIMAL) {
         BigDecimal d = stmt.getBigDecimal(i);
         if(!stmt.parameterWasNull()) {
            value = d.toString();
         }
      }
      else if(pltype==FndSqlType.INTEGER) {
         long l = stmt.getLong(i);
         if(!stmt.parameterWasNull()) {
            value = Long.toString(l);
         }
      }
      else if(pltype==FndSqlType.NUMBER) {
         double d = stmt.getDouble(i);
         if(!stmt.parameterWasNull()) {
            value = PlsqlUtil.doubleToString(d);
         }
      }
      else if(pltype==FndSqlType.TIMESTAMP) {
         Date d = stmt.getTimestamp(i);
         if(!stmt.parameterWasNull()) {
            value = (d == null) ? null : getDateFormatter(item.getType()).format(d);
         }
      }
      else if(pltype==FndSqlType.BINARY) {
         byte[] b = stmt.getBinary(i);
         if (!stmt.parameterWasNull()) {
            value = FndUtil.toBase64Text(b);
         }
      }
      else {
         throw invalidPlsqlType(pltype.getName());
      }

      item.setValue(value);
   }

   /**
    * Converts the column at specified position in the result set into an Item.
    * @param columnType optional data type that overrides the type retrieved from the database (meta-data),
    *                   currently only FndSqlType.DECIMAL is supported
    */
   public Item columnToItem(FndStatement stmt,
                              ResultSetMetaData metadata,
                              int i,
                              FndSqlType columnType) throws IOException, IfsException, SQLException {

      String value = null;
      String name  = metadata.getColumnName(i);
      String type;

      if(columnType == FndSqlType.DECIMAL) {
         type = "DEC";
         BigDecimal dec = stmt.getBigDecimal(i);
         if(!stmt.resultWasNull()) {
            value = dec.toString();
         }
      }
      else {
         int coltype = metadata.getColumnType(i);

         //PlsqlUtil.debug("   name="+name+" type="+coltype);

         PlsqlType pltype = jdbcSqlTypeToPlsqlType(coltype);
         type = typemap.plsqlTypeToItemType(pltype);

         if(pltype==PlsqlType.TEXT) {
            if (lengthPrefixSupported) {
               type = BufferUtil.LENGTH_PREFIXED_TEXT;
            }

            String s = isLob(coltype) ? stmt.getLongText(i) : stmt.getString(i);
            if(!stmt.resultWasNull()) {
               value = s;
            }
         }
         else if(pltype==PlsqlType.NUMBER) {
            double n = stmt.getDouble(i);
            if(!stmt.resultWasNull()) {
               value = PlsqlUtil.doubleToString(n);
            }
         }
         else if(pltype==PlsqlType.TIMESTAMP) {
            Date d = stmt.getTimestamp(i);
            if(!stmt.resultWasNull()) {
               value = timestampFormatter.format(d);
            }
         }
         else if(pltype==PlsqlType.ROWID) {
            String r = stmt.getString(i);
            if(!stmt.resultWasNull()) {
               if(r.length()>30) {
                  r = new String(FndUtil.fromHexText(r), FndSerializeConstants.BUFFER_CHARSET); // fix bug in Oracle driver
               }
               value = r;
            }
         }
         else if(pltype==PlsqlType.BINARY) {
            byte[] data = isLob(coltype) ? stmt.getBinary(i) : stmt.getLongRaw(i);
            if(!stmt.resultWasNull() && data!=null) {
               value = FndUtil.toBase64Text(data);
            }
         }
         else{
            throw invalidPlsqlType(pltype.getName());
         }
      }

      return new Item(name,type,null,value);
   }

   /**
    * Converts JDBC column type to (supported) PlsqlType.
    */
   @SuppressWarnings("PMD.MissingBreakInSwitch")
   private static PlsqlType jdbcSqlTypeToPlsqlType(int sqltype) throws SystemException {

      switch(sqltype) {
         case java.sql.Types.DATE:
         case java.sql.Types.TIME:
         case java.sql.Types.TIMESTAMP:
            return PlsqlType.TIMESTAMP;

         case java.sql.Types.BIGINT:
         case java.sql.Types.SMALLINT:
         case java.sql.Types.TINYINT:
         case java.sql.Types.INTEGER:
         case java.sql.Types.DECIMAL:
         case java.sql.Types.DOUBLE:
         case java.sql.Types.FLOAT:
         case java.sql.Types.NUMERIC:
         case java.sql.Types.REAL:
            return PlsqlType.NUMBER;

         case -8: // Oracle's ROWID
            return PlsqlType.ROWID;

         case java.sql.Types.CHAR:
         case java.sql.Types.CLOB:
         case java.sql.Types.VARCHAR:
         case java.sql.Types.LONGVARCHAR:
            return PlsqlType.TEXT;

         case java.sql.Types.BLOB:
         case java.sql.Types.BINARY:
         case java.sql.Types.VARBINARY:
         case java.sql.Types.LONGVARBINARY:
            return PlsqlType.BINARY;

         default:
            throw new SystemException(Texts.BADJDBCTYPE, String.valueOf(sqltype));
      }
   }

   /**
    * Checks if a given JDBC type is a LOB.
    */
   @SuppressWarnings("PMD.MissingBreakInSwitch")
   private static boolean isLob(int sqltype) {
      switch(sqltype) {
         case java.sql.Types.CLOB:
         case java.sql.Types.BLOB:
            return true;
      }
      return false;
   }

   /**
    * Parse given (non empty) string into a Date, using private instance of DateFormat.
    */
   private java.util.Date parseDate(String text, String itemType) throws SystemException {

      SimpleDateFormat formatter = getDateFormatter(itemType);

      ParsePosition pos = new ParsePosition(0);
      Date d = formatter.parse(text, pos);

      if(pos.getIndex() < text.length()) {
         // Workaround to avoid parsing error occurring for midnight of DST starting day
         if ("DTS".equals(itemType) && text.endsWith("00.00.00")) {
            String truncatedDate = text.substring(0,text.length()-9);
            pos = new ParsePosition(0);
            d = dateFormatter.parse(truncatedDate,pos);

            if(pos.getIndex() < truncatedDate.length())
                throw new SystemException(Texts.PARSEDATE,text,formatter.toPattern());
         }
         else {
            throw new SystemException(Texts.PARSEDATE,text,formatter.toPattern());
         }
      }
      return d;
   }


   /**
    * Choose date formatter for parsing/formatting items of given type
    */
   private SimpleDateFormat getDateFormatter(String itemType) {
      if(typemap instanceof PlsqlInvocationBufferTypeMap) {
         //
         //  DTS   yyyy-MM-dd-HH.mm.ss
         //  DT    HH.mm.ss
         //  D     yyyy-MM-dd
         //
         switch (itemType) {
            case "DT":
               return timeFormatter;
            case "D":
               return dateFormatter;
            default:
               return timestampFormatter; // DTS
         }
      }
      else {
         return timestampFormatter; // WEB request D-items
      }
   }

   private SystemException invalidPlsqlType(String name) {
      return new SystemException(Texts.BADPLTYPE, name);
   }
}
