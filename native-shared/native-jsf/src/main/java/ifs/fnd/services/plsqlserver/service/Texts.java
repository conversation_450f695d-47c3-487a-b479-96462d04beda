/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.services.plsqlserver.service;

import ifs.fnd.base.FndTranslatableText;

/**
 * This class contains translatable texts used by the framework.
 */
final class Texts {

   private static final String PKG = Texts.class.getPackage().getName();

   private Texts() {
      //Prevent instantiations
   }

   //FndUserFeedback
   static final FndTranslatableText BADITEMSTATUS = new FndTranslatableText("BADITEMSTATUS", "Invalid Item status '&1'", PKG);
   static final FndTranslatableText BADJDBCTYPE = new FndTranslatableText("BADJDBCTYPE", "Cannot convert JDBC type &1 to PlsqlType", PKG);
   static final FndTranslatableText BADPLTYPE = new FndTranslatableText("BADPLTYPE", "Not supported PlsqlType &1", PKG);
   static final FndTranslatableText PARSEDATE = new FndTranslatableText("PARSEDATE", "Invalid value for date '&1'. It should match the mask: '&2'", PKG);
   static final FndTranslatableText BADCONVERTPLTYPE = new FndTranslatableText("BADCONVERTPLTYPE", "Cannot convert PlsqlType &1 to Item type", PKG);
   static final FndTranslatableText BADITEMTYPE2 = new FndTranslatableText("BADITEMTYPE2", "Cannot convert Item type &1 to FndSqlType", PKG);
   static final FndTranslatableText MISSINGSESSIONID = new FndTranslatableText("MISSINGSESSIONID", "Missing Dedicated-Session-Id in HTTP header", PKG);
   static final FndTranslatableText BADITEMTYPE = new FndTranslatableText("BADITEMTYPE", "Cannot convert Item type &1 to PlsqlType", PKG);
   static final FndTranslatableText NOAPPUSER = new FndTranslatableText("NOAPPUSER", "Could not get application user", PKG);
   static final FndTranslatableText INVOKECURSORONE = new FndTranslatableText("INVOKECURSORONE", "PLSQL Gateway method invokeRefCursor() supports only one command per invocation", PKG);
   static final FndTranslatableText INVOKEQUERYONE = new FndTranslatableText("INVOKEQUERYONE", "PLSQL Gateway method invokeQuery() supports only one command per invocation", PKG);
   static final FndTranslatableText INVOKESELECT = new FndTranslatableText("INVOKESELECT", "PLSQL Gateway method invokeSelect() supports only SELECT statements", PKG);
   static final FndTranslatableText INVOKECURSORSTMT = new FndTranslatableText("INVOKECURSORSTMT", "Invalid statement '&1' passed to PLSQL Gateway method invokeRefCursor()", PKG);
   static final FndTranslatableText BADAUTHTYPE = new FndTranslatableText("BADAUTHTYPE", "Cannot convert TypeOfEnumeration.Enum '&1' to Item Type", PKG);
   static final FndTranslatableText METADATADELIM = new FndTranslatableText("METADATADELIM", "Missing '&1' delimiter in meta-data for select columns: &2", PKG);
   static final FndTranslatableText BINDVARUNDEF = new FndTranslatableText("BINDVARUNDEF", "Undefined bind variable '&1'", PKG);
   static final FndTranslatableText BINDBYNAME = new FndTranslatableText("BINDBYNAME", "Bind by-name not supported for item type: &1", PKG);
   static final FndTranslatableText FNDSECQRYTRIM = new FndTranslatableText("FNDSECQRYTRIM", "Invalid object name '&1' passed to security query", PKG);
   static final FndTranslatableText FNDUSRDBOBJ = new FndTranslatableText("FNDUSRDBOBJ", "User '&1' is not allowed to access the database object '&2'", PKG);
   static final FndTranslatableText FNDPLSNODEFB = new FndTranslatableText("FNDPLSNODEFB", "DEFINE buffer is missing in PerRow command", PKG);
   static final FndTranslatableText FNDPLSATTRS = new FndTranslatableText("FNDPLSATTRS", "Bad attribute string &1", PKG);
   static final FndTranslatableText PROCEEDQUERY = new FndTranslatableText("PROCEEDQUERY","Do you want to proceed?", PKG);

      //PlsqlProxy
   static final FndTranslatableText IMPERSONATE_USER = new FndTranslatableText("IMPERSONATE_USER", "Access denied due to system privilege configuration", PKG);
   static final FndTranslatableText RUN_AS_DEDICATED = new FndTranslatableText("RUN_AS_DEDICATED", "Impersonate (RunAs) functionality is not supported for dedicated sessions", PKG);

   //PlsqlGatewayInvocationProcessor
   static final FndTranslatableText END_PLSQL_REQUEST = new FndTranslatableText("END_PLSQL_REQUEST", "Could not close PLSQL request context: &1", PKG);
   static final FndTranslatableText TRANSACTION_ID_MISMATCH = new FndTranslatableText("TRANSACTION_ID_MISMATCH", "Transaction ID mismatch. Client ID = [&1] Server ID = [&2]", PKG);
   static final FndTranslatableText BAD_POOL_CONFIG_PARAM = new FndTranslatableText("BAD_POOL_CONFIG_PARAM", "Invalid pool configuration parameter name [&1]", PKG);
   static final FndTranslatableText BAD_POOL_CONFIG_VALUE = new FndTranslatableText("BAD_POOL_CONFIG_VALUE", "Non-integer value [&1] for pool configuration parameter [&2]", PKG);

}