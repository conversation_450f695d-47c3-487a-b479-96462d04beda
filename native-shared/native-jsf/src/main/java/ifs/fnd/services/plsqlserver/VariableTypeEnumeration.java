/*
 * ==================================================================================
 * File:         VariableTypeEnumeration.java
 * Package:      Plsql_Server
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 * ==================================================================================
 */

package ifs.fnd.services.plsqlserver;

import ifs.fnd.record.*;
import java.util.*;
import ifs.fnd.base.ApplicationException;
import ifs.fnd.base.IfsRuntimeException;

/**
 * Enumeration class representing type of a <code>PropagatedVariable</code>.
 */
public class VariableTypeEnumeration extends FndEnumeration {
   public static final Enum ATTRIBUTE_NAME = Enum.ATTRIBUTE_NAME;
   public static final Enum BIND_VARIABLE = Enum.BIND_VARIABLE;

   public VariableTypeEnumeration() {
      super();
   }

   public VariableTypeEnumeration(String name) {
      super(name);
   }

   public VariableTypeEnumeration(FndAttributeMeta meta) {
      super(meta);
   }

   public void setValue(Enum value) {
      internalSetValue(value);
   }

   public Enum getValue() {
      return (Enum)internalGetValue();
   }

   public void parseString(String value)  throws ifs.fnd.base.ParseException {
      this.value = Enum.parseString(value);
      set();
   }

   public String toString() {
      Enum value = getValue();
      if(value==null)
         return null;
      return value.getValue();
   }

   public int compareTo(VariableTypeEnumeration attr) {
      return getValue().compareTo(attr.getValue());
   }

   public FndSimpleCondition createEqualCondition(Enum value) {
      return super.createEqualCondition(value);
   }

   public FndSimpleCondition createNotEqualCondition(Enum value) {
      return super.createNotEqualCondition(value);
   }

   public static FndEnumerationView toEnumerationView() {
      FndEnumerationView view = new FndEnumerationView();
      try {
         view.name.setValue("VARIABLE_TYPE");
      }
      catch (ApplicationException e) {
         throw new IfsRuntimeException(e, "");
      }
      view.addValue("ATTRIBUTE_NAME", "Attribute_Name");
      view.addValue("BIND_VARIABLE", "Bind_Variable");
      return view;
   }

   /**
    * Class representing a value stored in an attribute of type <code>VariableTypeEnumeration</code>.
    */
   public static final class Enum implements java.io.Serializable    {

     private String value;
     private static List<Enum> entries = new ArrayList<>();
     private static int nextOrdinal = 0;
     private final int ordinal = nextOrdinal++;
     private Enum(String value) {
        this.value = value;
        entries.add(this);
     }
     private Object readResolve() {
       return entries.get(ordinal);
     }
     public String getValue() {
        return value;
     }
     public String toString() {
        return getValue();
     }
     public static Enum parseString(String value) throws ifs.fnd.base.ParseException {
        Enum entry;
        if(value==null || value.length()==0)
           return null;
        for(int i=0; i<entries.size(); i++) {
          entry = (Enum)entries.get(i);
          if(entry.getValue().equalsIgnoreCase(value))
             return entry;
        }
        throw new ifs.fnd.base.ParseException("ILLEGALENUMVALUE: Illegal value: &1", value);
     }
     public int compareTo(Object o) {
        return ordinal - ((Enum)o).ordinal;
     }
      public static final Enum ATTRIBUTE_NAME  = new Enum("ATTRIBUTE_NAME");
      public static final Enum BIND_VARIABLE  = new Enum("BIND_VARIABLE");

   }

}
