/*
 * ==================================================================================
 * File:         PropagatedVariable.java
 * Package:      Plsql_Server
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 * ==================================================================================
 */

package ifs.fnd.services.plsqlserver;

import ifs.fnd.record.*;

/**
 * View representing a propagated variable contained in a PL/SQL <code>Command</code>.
 */
public class PropagatedVariable extends FndView {
   private static final FndRecordMeta viewMeta  = new FndRecordMeta("PLSQL_SERVER", "PROPAGATED_VARIABLE");

   private static final FndAttributeMeta attributeNameMeta  = new FndAttributeMeta(viewMeta, "ATTRIBUTE_NAME", null, 0, 30);
   private static final FndAttributeMeta parameterNameMeta  = new FndAttributeMeta(viewMeta, "PARAMETER_NAME", null, 0, 30);
   private static final FndAttributeMeta variableTypeMeta   = new FndAttributeMeta(viewMeta, "VARIABLE_TYPE");
   private static final FndAttributeMeta variableValueMeta  = new FndAttributeMeta(viewMeta, "VARIABLE_VALUE");

   public final FndAlpha                       attributeName  = new FndAlpha(attributeNameMeta);
   public final FndAlpha                       parameterName  = new FndAlpha(parameterNameMeta);
   public final VariableTypeEnumeration        variableType   = new VariableTypeEnumeration(variableTypeMeta);
   public final FndText                        variableValue  = new FndText(variableValueMeta, true);

   /**
    * @deprecated Use static method newRecord instead
    */
   public PropagatedVariable() {
      super(viewMeta);
      add(attributeName);
      add(parameterName);
      add(variableType);
      add(variableValue);
   }

   @Override
   public FndAbstractRecord newInstance() {
      return new PropagatedVariable();
   }

   public static PropagatedVariable newRecord() {
      return new PropagatedVariable();
   }

}
