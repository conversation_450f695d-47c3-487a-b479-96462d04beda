/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.services.plsqlserver.service;

import ifs.fnd.sf.FndServerContext;
import ifs.fnd.base.FndContext;

/**
 * <B>Framework internal class:</B> PLSQL-invocation specific extension of RequestContext.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class PlsqlInvocationContext extends RequestContext {

   /**
    * Construct a new context for a PLSQL invocation request.
    */
   public PlsqlInvocationContext() throws Exception {
      super(FndServerContext.getCurrentApplicationUser(),
            FndContext.getCurrentLanguage());
   }
}
