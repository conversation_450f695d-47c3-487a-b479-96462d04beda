/*
 * ==================================================================================
 * File:         TypeOfEnumeration.java
 * Package:      Plsql_Server
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 * ==================================================================================
 */

package ifs.fnd.services.plsqlserver;

import ifs.fnd.base.ApplicationException;
import ifs.fnd.base.IfsRuntimeException;
import ifs.fnd.record.*;
import java.util.*;

/**
 * Enumeration class representing type of an <code>AuthorizationObject</code>.
 */
public class TypeOfEnumeration extends FndEnumeration {
   public static final Enum VIEW_TYPE = Enum.VIEW_TYPE;
   public static final Enum PACKAGE_TYPE = Enum.PACKAGE_TYPE;
   public static final Enum METHOD_TYPE = Enum.METHOD_TYPE;
   public static final Enum PRES_OBJECT_TYPE = Enum.PRES_OBJECT_TYPE;

   public TypeOfEnumeration() {
      super();
   }

   public TypeOfEnumeration(String name) {
      super(name);
   }

   public TypeOfEnumeration(FndAttributeMeta meta) {
      super(meta);
   }

   public void setValue(Enum value) {
      internalSetValue(value);
   }

   public Enum getValue() {
      return (Enum)internalGetValue();
   }

   @Override
   public void parseString(String value)  throws ifs.fnd.base.ParseException {
      this.value = Enum.parseString(value);
      set();
   }

   @Override
   public String toString() {
      Enum val = getValue();
      if(val==null)
         return null;
      return val.getValue();
   }

   public int compareTo(TypeOfEnumeration attr) {
      return getValue().compareTo(attr.getValue());
   }

   public FndSimpleCondition createEqualCondition(Enum value) {
      return super.createEqualCondition(value);
   }

   public FndSimpleCondition createNotEqualCondition(Enum value) {
      return super.createNotEqualCondition(value);
   }

   public static FndEnumerationView toEnumerationView() {
      FndEnumerationView view = new FndEnumerationView();
      try {
         view.name.setValue("TYPE_OF");
      }
      catch (ApplicationException e) {
         throw new IfsRuntimeException(e, "");
      }
      view.addValue("VIEW_TYPE", "View_Type");
      view.addValue("PACKAGE_TYPE", "Package_Type");
      view.addValue("METHOD_TYPE", "Method_Type");
      view.addValue("PRES_OBJECT_TYPE", "Pres_Object_Type");
      return view;
   }

   /**
    * Class representing a value stored in an attribute of type <code>TypeOfEnumeration</code>.
    */
   public static final class Enum implements java.io.Serializable    {

     private String value;
     private static List<Enum> entries = new ArrayList<>();
     private static int nextOrdinal = 0;
     private final int ordinal = nextOrdinal++;
     private Enum(String value) {
        this.value = value;
        entries.add(this);
     }
     private Object readResolve() {
       return entries.get(ordinal);
     }
     public String getValue() {
        return value;
     }
      @Override
     public String toString() {
        return getValue();
     }
     public static Enum parseString(String value) throws ifs.fnd.base.ParseException {
        Enum entry;
        if(value==null || value.length()==0)
           return null;
        for(int i=0; i<entries.size(); i++) {
          entry = (Enum)entries.get(i);
          if(entry.getValue().equalsIgnoreCase(value))
             return entry;
        }
        throw new ifs.fnd.base.ParseException("ILLEGALENUMVALUE: Illegal value: &1", value);
     }
     public int compareTo(Object o) {
        return ordinal - ((Enum)o).ordinal;
     }
      public static final Enum VIEW_TYPE  = new Enum("VIEW_TYPE");
      public static final Enum PACKAGE_TYPE  = new Enum("PACKAGE_TYPE");
      public static final Enum METHOD_TYPE  = new Enum("METHOD_TYPE");
      public static final Enum PRES_OBJECT_TYPE  = new Enum("PRES_OBJECT_TYPE");

   }

}
