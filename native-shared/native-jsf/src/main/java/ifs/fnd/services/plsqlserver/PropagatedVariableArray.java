/*
 * ==================================================================================
 * File:         PropagatedVariableArray
 * Package:      Plsql_Server
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 * ==================================================================================
 */

package ifs.fnd.services.plsqlserver;

import ifs.fnd.record.*;
import ifs.fnd.record.serialization.*;
import ifs.fnd.base.*;

/**
 * Array of <code>PropagatedVariable</code>.
 */
public class PropagatedVariableArray extends FndAbstractArray {

   public PropagatedVariableArray() {
      super();
   }

   public PropagatedVariableArray(FndAttributeMeta meta) {
      super(meta);
   }

   public boolean add(PropagatedVariable propagatedVariable) {
      return internalAdd(propagatedVariable);
   }

   public void add(int index, PropagatedVariable propagatedVariable) {
      internalAdd(index, propagatedVariable);
   }

   public boolean contains(PropagatedVariable propagatedVariable) {
      return internalContains(propagatedVariable);
   }

   public PropagatedVariable firstElement() {
      return (PropagatedVariable)internalFirstElement();
   }

   public PropagatedVariable get(int index) {
      return (PropagatedVariable)internalGet(index);
   }

   public int indexOf(PropagatedVariable propagatedVariable) {
      return internalIndexOf(propagatedVariable);
   }

   public PropagatedVariable lastElement() {
      return (PropagatedVariable)internalLastElement();
   }

   public int lastIndexOf(PropagatedVariable propagatedVariable) {
      return internalLastIndexOf(propagatedVariable);
   }

   public PropagatedVariable remove(int index) {
      return (PropagatedVariable)internalRemove(index);
   }

   public PropagatedVariable set(int index, PropagatedVariable propagatedVariable) {
      return (PropagatedVariable)internalSet(index, propagatedVariable);
   }

   @Override
   public FndAbstractRecord newRecord() {
      return PropagatedVariable.newRecord();
   }

   @Override
   protected FndAbstractRecord newRecord(FndTokenReader stream) throws ParseException {
      PropagatedVariable propagatedVariable = PropagatedVariable.newRecord();
      propagatedVariable.parse(stream);
      return propagatedVariable;
   }
   @Override
   protected FndAttribute newAttribute(FndAttributeMeta meta) {
       return new PropagatedVariableArray(meta);
   }

}
