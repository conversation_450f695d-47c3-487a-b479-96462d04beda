/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.services.plsqlserver.service;

import ifs.fnd.base.SystemException;
import ifs.fnd.buffer.BufferUtil;
import ifs.fnd.record.FndSqlType;
import ifs.fnd.util.Str;

/**
 * <B>Framework internal class:</B> PLSQL-invocation specific implementation of BufferTypeMap.
 * It defines the mapping used by Plsql invocation (T,A,ENUM, DTS,DT,D, N,I,DEC, R,R.B64).
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class PlsqlInvocationBufferTypeMap implements BufferTypeMap {

   /**
    * Maps Item type to PlsqlType
    * @throws ifs.fnd.base.SystemException
    */
   @Override
   public PlsqlType itemTypeToPlsqlType(String type) throws SystemException {
      switch(Str.nvl(type, "null")) {
         case "T":
         case "A":
         case "ENUM":
         case BufferUtil.LENGTH_PREFIXED_TEXT:
         case BufferUtil.LENGTH_PREFIXED_ALPHA:
            return PlsqlType.TEXT;

         case "LT":
            return PlsqlType.LONG_TEXT;

         case "N":
         case "I":
         case "DEC":
            return PlsqlType.NUMBER;

         case "DTS":
         case "DT":
         case "D":
            return PlsqlType.TIMESTAMP;

         case "R":
         case "R.B64":
            return PlsqlType.BINARY;

         default:
            throw new SystemException(Texts.BADITEMTYPE, type);
      }
   }

   /**
    * Maps PlsqlType to Item type
    */
   @Override
   public String plsqlTypeToItemType(PlsqlType type) throws SystemException {
      if(type==PlsqlType.TEXT)
         return "T";
      else if(type==PlsqlType.NUMBER)
         return "N";
      else if(type==PlsqlType.TIMESTAMP)
         return "DTS";
      else if(type==PlsqlType.ROWID)
         return "T";
      else if(type==PlsqlType.BINARY)
         return "R.B64";
      else
         throw new SystemException(Texts.BADCONVERTPLTYPE, type.getName());
   }

   /**
    * Maps Item type to FndSqlType.
    * @param type the string value of an Item type
    * @return the corresponding FndSqlType
    * @throws SystemException if a conversion error occurs
    */
   @Override
   public FndSqlType itemTypeToFndSqlType(String type) throws SystemException {
      switch(Str.nvl(type, "null")) {
         case "T":
         case "A":
         case "ENUM":
         case BufferUtil.LENGTH_PREFIXED_TEXT:
         case BufferUtil.LENGTH_PREFIXED_ALPHA:
            return FndSqlType.TEXT;

         case "LT":
            return FndSqlType.LONG_TEXT;

         case "N":
         case "I":
            return FndSqlType.NUMBER;

         case "DEC":
            return FndSqlType.DECIMAL;

         case "DTS":
         case "DT":
         case "D":
            return FndSqlType.TIMESTAMP;

         case "R":
         case "R.B64":
            return FndSqlType.BINARY;

         default:
            throw new SystemException(Texts.BADITEMTYPE2, type);
      }
   }
}

