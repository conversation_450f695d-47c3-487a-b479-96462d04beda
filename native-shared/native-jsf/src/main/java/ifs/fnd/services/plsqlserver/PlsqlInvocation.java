/*
 * ==================================================================================
 * File:         PlsqlInvocation.java
 * Package:      Plsql_Server
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 * ==================================================================================
 */

package ifs.fnd.services.plsqlserver;

import ifs.fnd.record.*;

/**
 * View representing a parameter passed to PlsqlGateway.invoke() operation.
 */
public class PlsqlInvocation extends FndView {
   public static final FndRecordMeta viewMeta  = new FndRecordMeta("PLSQL_SERVER", "PLSQL_INVOCATION");

   private static final FndAttributeMeta systemNameMeta    = new FndAttributeMeta(viewMeta, "SYSTEM_NAME", null, 0, 50);
   private static final FndAttributeMeta userSessionMeta   = new FndAttributeMeta(viewMeta, "USER_SESSION");
   private static final FndAttributeMeta transactionIdMeta = new FndAttributeMeta(viewMeta, "TRANSACTION_ID", null, 0, 200);

   private static final FndAttributeMeta commandsMeta = new FndAttributeMeta(viewMeta, "COMMANDS");

   public final FndAlpha   systemName    = new FndAlpha(systemNameMeta);
   public final FndBoolean userSession   = new FndBoolean(userSessionMeta);
   public final FndText    transactionId = new FndText(transactionIdMeta);

   public final CommandArray commands = new CommandArray(commandsMeta);

   /**
    * Create a new instance of PlsqlInvocation.
    */
   public PlsqlInvocation() {
      super(viewMeta);
      add(systemName);
      add(userSession);
      add(transactionId);
      add(commands);
   }

   @Override
   public FndAbstractRecord newInstance() {
      return new PlsqlInvocation();
   }

   public static PlsqlInvocation newRecord() {
      return new PlsqlInvocation();
   }
}
