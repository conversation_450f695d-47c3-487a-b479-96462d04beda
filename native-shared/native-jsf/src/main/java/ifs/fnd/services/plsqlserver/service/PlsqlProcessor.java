/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.services.plsqlserver.service;

import ifs.fnd.log.*;
import ifs.fnd.buffer.*;
import ifs.fnd.service.IfsProperties;
import ifs.fnd.util.*;
import ifs.fnd.base.*;
import ifs.fnd.sf.storage.FndStatement;
import ifs.fnd.sf.storage.FndConnection;
import ifs.fnd.sf.storage.FndTaggedCursor;
import ifs.fnd.sf.storage.FndTaggedConnection;
import ifs.fnd.sf.storage.FndRefCursor;
import ifs.fnd.record.FndSqlValue;
import ifs.fnd.record.FndSqlType;

import java.sql.*;
import java.util.*;

/**
 * <B>Framework internal class:</B>
 * Class that performs SQL statements and stored procedure calls using JDBC
 * interface. It performs data conversion using private instance of
 * {@link PlsqlConverter}
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class PlsqlProcessor {
   private final FndConnection connection;

   private final PlsqlConverter converter;

   private final BufferTypeMap typemap;

   private final Logger log = LogMgr.getDatabaseLogger();

   private static final int MAX_FETCH_SIZE = IfsProperties.getSnapshot().getMaxFetchSize();

   private static final int DEFAULT_FETCH_SIZE = IfsProperties.getSnapshot().getDefaultFetchSize();


   /**
    * @param connection
    *           the database connection used to perform statements
    * @param typemap
    *           implementation of BufferTypeMap used to map item types to/from
    *           PLSQL types.
    */
   public PlsqlProcessor(FndConnection connection, BufferTypeMap typemap) {
      this.connection = connection;
      this.typemap = typemap;
      converter = new PlsqlConverter(typemap);
   }

   /**
    * Perform a SQL select statement using given CommandInstance as the source
    * (sql statement, command options) and destination (result, result info) of
    * the command.
    */
   public void query(CommandInstance cmd) throws Exception {
      FndStatement stmt = null;
      String cursorId = cmd.getCursorId();

      boolean closeStmt = true; // set to false when stmt is added to FndTaggedConnection as a cursor

      try {
         stmt = connection.createStatement();

         Buffer bindvars = cmd.getBindVariables();
         int bindcount = bindvars == null ? 0 : bindvars.countItems();

         for (int i = 0; i < bindcount; i++) {
            stmt.defineParameter(converter.itemToFndSqlValue(i + 1, bindvars.getItem(i), true, false));
         }

         stmt.prepare(cmd.getSql());

         // Set statement limits. Be careful with overflow of maxRows.
         int maxRows = cmd.getMaxRows();
         int skipRows = cmd.getSkipRows();
         if (maxRows == 0) {
            maxRows = Integer.MAX_VALUE;
         }

         if (maxRows < Integer.MAX_VALUE) {
            stmt.setFetchSize(Math.min(maxRows + skipRows, MAX_FETCH_SIZE));
         }
         else {
            stmt.setFetchSize(DEFAULT_FETCH_SIZE);
         }

         // do not call.setMaxRows() if cursorId != null (client may want to fetch all rows)
         if(cursorId == null && maxRows < Integer.MAX_VALUE && !cmd.getCountRows()) {
            stmt.setMaxRows(maxRows + skipRows + 1);
         } // one extra fetch to check partialResult

         // parse client-side meta-data for select columns
         ArrayList<FndSqlType> columnTypes = defineColumnTypes(stmt, cmd.getSelectColumnsMetaData());

         stmt.executeQuery();

         Buffer result = cmd.getResultBuffer();
         if(cursorId != null) {
            ((FndTaggedConnection)connection).addCursor(cursorId, stmt, columnTypes);
            closeStmt = false;
         }

         resultSetToBuffer(stmt, result, cmd, columnTypes);
      }
      finally {
         if (stmt != null && closeStmt) {
            stmt.close();
         }
      }
   }

   /**
    * Fetches data from an open cursor.
    * @param stmt an open SELECT statement
    * @param cmd CommandInstance that acts as the source (command options) and destination (result, result info) of the command.
    */
   public void fetch(FndStatement stmt, CommandInstance cmd) throws Exception {
      Buffer result = cmd.getResultBuffer();

      FndTaggedCursor cursor = null;
      String cursorId = cmd.getCursorId();
      if(cursorId != null) {
         FndTaggedConnection taggedConn = (FndTaggedConnection) connection;
         cursor = taggedConn.getCursor(cursorId);
      }

      List<FndSqlType> columnTypes = cursor == null ? null : cursor.getColumnTypes();
      resultSetToBuffer(stmt, result, cmd, columnTypes);
   }

   /**
    * Converts a JDBC result set to a Buffer by transforming every fetched row
    * to a DATA item. The fetching process is controlled by command options
    * (maxRows, skipRows, CountRows) retrieved from the specified
    * CommandInstance. Extra information about the result (rowCount,
    * partialResult) is passed to the CommandInstance.setResultInfo().
    */
   private void resultSetToBuffer(FndStatement stmt, Buffer result, CommandInstance cmd, List<FndSqlType> columnTypes) throws Exception {
      stmt.clearFetchedSize();

      ResultSet rs = stmt.getResult();
      ResultSetMetaData metadata = rs.getMetaData();

      //if(log.trace)
      //   debugResultSetMetaData(metadata);

      int columnCount = metadata.getColumnCount();

      int maxRows = cmd.getMaxRows();
      int skipRows = cmd.getSkipRows();
      if (maxRows == 0) {
         maxRows = Integer.MAX_VALUE;
      }

      if (skipRows > 0) {
         if (log.trace) {
            log.trace("Skipping &1 row(s)", skipRows);
         }
         for (int i = 0; i < skipRows; i++) {
            if (!rs.next()) {
               break;
            }
         }
         //rs.absolute(skipRows);
      }

      int fetchCount = 0;
      while (fetchCount < maxRows && rs.next()) {
         fetchCount++;
         Buffer row = result.newInstance();

         long size1 = stmt.getFetchedSize();

         result.addItem(new Item("DATA", cmd.getResultRecordType(), null, row));
         for (int i = 0; i < columnCount; i++) {
            FndSqlType columnType = columnTypes == null ? null : columnTypes.get(i);
            Item item = converter.columnToItem(stmt, metadata, i + 1, columnType);
            row.addItem(item);
         }

         long size2 = stmt.getFetchedSize();
         if (log.trace) {
            log.trace("Row &1 fetched (&2/&3 bytes)", fetchCount, size2 - size1, size2);
         }

         reportWarnings(rs);
      }

      Boolean partialResult = null;
      int rowCount = -1;

      if (fetchCount < maxRows) {
         partialResult = Boolean.FALSE;
         rowCount = fetchCount + skipRows;
         String cursorId = cmd.getCursorId();
         if(cursorId != null) {
            if (log.trace) {
               log.trace("Closing tagged cursor [&1] because fetchCount=&2 < maxRows=&3", cursorId, fetchCount, maxRows);
            }
            ((FndTaggedConnection)connection).closeCursor(cursorId);
            cmd.setCursorId(null);
         }
      }
      else {
         if(cmd.getCursorId() != null) {
            // partialResult and rowCount will be undefined (we cannot call rs.next() to check)
            assert true;
         }
         else if (rs.next()) {
            partialResult = Boolean.TRUE;
            if (cmd.getCountRows()) {
               fetchCount++;
               while (rs.next()) {
                  fetchCount++;
               }
               rowCount = fetchCount + skipRows;
            }
         }
         else {
            partialResult = Boolean.FALSE;
            rowCount = fetchCount + skipRows;
         }
      }

      cmd.setQueryResultInfo(rowCount, partialResult);

      if (log.trace) {
         log.trace("Fetched &1 bytes from database.", stmt.getFetchedSize());
      }

      if(connection instanceof FndTaggedConnection) {
         ((FndTaggedConnection) connection).spoolPlSqlTrace(stmt);
      }
   }

   /**
    * Execute a stored procedure using given CommandInstance as the source (sql
    * statement, bind variables, command options) and destination (bind
    * variables) of the command. Each bind variable will be transformed into a
    * JDBC parameter. OUT and INOUT variables will be modified directly in the
    * bind variable buffer.
    * If returnRefCursor is true then the the first bind parameter is assumed to
    * be an OUT-parameter of type REF CURSOR, while all other parameters are assumed to
    * be IN-parameters.
    * @param returnRefCursor true if FndRefCursor should be return, false otherwise
    * @return FndRefCursor or null depending on value of parameter returnRefCursor
    */
   public FndRefCursor call(CommandInstance cmd, boolean returnRefCursor) throws Exception {
      FndStatement stmt = null;
      FndRefCursor refCursor = null;

      try {
         String sql = cmd.getSql();

         boolean isUpdateOrInsert = isUpdateOrInsert(sql);

         stmt = connection.createStatement();

         Buffer bindvars = cmd.getBindVariables();

         int bindcount = bindvars == null ? 0 : bindvars.countItems();
         FndSqlValue[] params = new FndSqlValue[bindcount];

         for (int i = 0; i < bindcount; i++) {
            if(i == 0 && returnRefCursor && stmt.defineRefCursorVariable()) {
               stmt.defineOutParameter("CURSOR", FndSqlType.REF_CURSOR);
            }
            else {
               params[i] = converter.itemToFndSqlValue(i + 1, bindvars.getItem(i), false, isUpdateOrInsert);
               stmt.defineParameter(params[i]);
            }
         }

         stmt.prepareCall(sql);
         stmt.execute();

         if(returnRefCursor) {
            refCursor = stmt.getRefCursor();
         }
         else {
            for (int i = 0; i < bindcount; i++) {
               converter.parameterToItem(stmt, i + 1, params[i], bindvars.getItem(i));
            }
         }

         if (log.trace) {
            log.trace("Statement performed.");
         }
         return refCursor;
      }
      finally {
         if (stmt != null && (!returnRefCursor || stmt.closeRefCursorStatement())) {
            stmt.close();
         }
      }
   }

   private static boolean isUpdateOrInsert(String sql) {
      StringTokenizer st = new StringTokenizer(sql);
      if(st.hasMoreTokens()) {
         String keyword = st.nextToken();
         if("INSERT".equalsIgnoreCase(keyword) || "UPDATE".equalsIgnoreCase(keyword)) {
            return true;
         }
      }
      return false;
   }

   private ArrayList<FndSqlType> defineColumnTypes(FndStatement stmt, String selectColumns) throws Exception {
      if(Str.isEmpty(selectColumns)) {
         return null;
      }

      if(log.trace) {
         log.trace("PlsqlProcessor.defineColumnTypes(): selectColumns: &1", selectColumns);
      }

      boolean supportsDefineColumnType = stmt.supportsDefineColumnType();
      ArrayList<FndSqlType> types = new ArrayList<>();

      int colNr = 0;
      StringTokenizer x = new StringTokenizer(selectColumns, ",");
      while(x.hasMoreTokens()) {
         String col = x.nextToken();
         colNr++;
         int delim = col.indexOf('^');
         if(delim < 0) {
            throw new SystemException(Texts.METADATADELIM, "^", selectColumns);
         }
         String type = col.substring(0, delim);

         if("DEC".equals(type)) {
            types.add(FndSqlType.DECIMAL);
            continue;
         }

         if("?".equals(type)) {
            types.add(null);
            continue;
         }

         if(!supportsDefineColumnType) {
            types.add(null);
            continue;
         }

         int size = Integer.parseInt(col.substring(delim + 1));
         FndSqlType fndType = typemap.itemTypeToFndSqlType(type);
         types.add(fndType);

         if(!needsSize(fndType)) {
            size = -1;
         }

         if(log.debug) {
            log.debug("   [&1] &2 &3", String.valueOf(colNr), fndType.getName(), String.valueOf(size));
         }
      }

      if(log.trace) {
         log.trace("PlsqlProcessor.defineColumnTypes(): columnTypes: &1", types.toString());
      }

      return types;
   }

   private boolean needsSize(FndSqlType type) {
      return type == FndSqlType.BINARY || type == FndSqlType.STRING || type == FndSqlType.TEXT;
   }

//   private void debugResultSetMetaData(ResultSetMetaData meta) throws SQLException {
//      log.trace("ResultSet meta data");
//      int columnCount = meta.getColumnCount();
//      for (int i = 1; i <= columnCount; i++) {
//         String name  = meta.getColumnName(i);
//         String type = meta.getColumnTypeName(i);
//         int precision = meta.getPrecision(i);
//         int scale = meta.getScale(i);
//         log.trace("   &1 &2 precision=&3 scale=&4", name, type, String.valueOf(precision), String.valueOf(scale));
//      }
//   }

   private void reportWarnings(ResultSet rs) throws SQLException {
      if(!log.trace) {
         return;
      }
      SQLWarning warning = rs.getWarnings();
      while (warning != null) {
         log.trace("   SQLWarning: &1 SQLSTATE=&2, ERRORCODE=&3", warning.getMessage(), warning.getSQLState(), Integer.toString(warning.getErrorCode()));
         warning = warning.getNextWarning();
      }
   }
}

