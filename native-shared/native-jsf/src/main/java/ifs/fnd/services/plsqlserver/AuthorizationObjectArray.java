/*
 * ==================================================================================
 * File:         AuthorizationObjectArray
 * Package:      Plsql_Server
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 * ==================================================================================
 */

package ifs.fnd.services.plsqlserver;

import ifs.fnd.record.*;
import ifs.fnd.record.serialization.*;
import ifs.fnd.base.*;

/**
 * Array of <code>AuthorizationObject</code>.
 */
public class AuthorizationObjectArray extends FndAbstractArray {

   public AuthorizationObjectArray() {
      super();
   }

   public AuthorizationObjectArray(FndAttributeMeta meta) {
      super(meta);
   }

   public boolean add(AuthorizationObject authorizationObject) {
      return internalAdd(authorizationObject);
   }

   public void add(int index, AuthorizationObject authorizationObject) {
      internalAdd(index, authorizationObject);
   }

   public boolean contains(AuthorizationObject authorizationObject) {
      return internalContains(authorizationObject);
   }

   public AuthorizationObject firstElement() {
      return (AuthorizationObject)internalFirstElement();
   }

   public AuthorizationObject get(int index) {
      return (AuthorizationObject)internalGet(index);
   }

   public int indexOf(AuthorizationObject authorizationObject) {
      return internalIndexOf(authorizationObject);
   }

   public AuthorizationObject lastElement() {
      return (AuthorizationObject)internalLastElement();
   }

   public int lastIndexOf(AuthorizationObject authorizationObject) {
      return internalLastIndexOf(authorizationObject);
   }

   public AuthorizationObject remove(int index) {
      return (AuthorizationObject)internalRemove(index);
   }

   public AuthorizationObject set(int index, AuthorizationObject authorizationObject) {
      return (AuthorizationObject)internalSet(index, authorizationObject);
   }

   @Override
   public FndAbstractRecord newRecord() {
      return AuthorizationObject.newRecord();
   }

   @Override
   protected FndAbstractRecord newRecord(FndTokenReader stream) throws ParseException {
      AuthorizationObject authorizationObject = AuthorizationObject.newRecord();
      authorizationObject.parse(stream);
      return authorizationObject;
   }
   @Override
   protected FndAttribute newAttribute(FndAttributeMeta meta) {
       return new AuthorizationObjectArray(meta);
   }

}
