/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.services.plsqlserver.service;

import ifs.fnd.base.SystemException;
import ifs.fnd.record.FndSqlType;


/**
 * <B>Framework internal class:</B> Interface that links Buffer types and PLSQL types.
 * It maps a string value of {@link ifs.fnd.buffer.Item Item} type
 * to an instance of class {@link PlsqlType} and vice versa.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public interface BufferTypeMap {

   /**
    * Maps Item type to PlsqlType.
    * @param type the string value of an Item type
    * @return the corresponding PlsqlType
    * @throws SystemException if a conversion error occurs
    */
   PlsqlType itemTypeToPlsqlType(String type) throws SystemException;

   /**
    * Maps PlsqlType to Item type.
    * @param type an instance of PlsqlType
    * @return the corresponding string value of Item type
    * @throws SystemException if a conversion error occurs
    */
   String plsqlTypeToItemType(PlsqlType type) throws SystemException;

   /**
    * Maps Item type to FndSqlType.
    * @param type the string value of an Item type
    * @return the corresponding FndSqlType
    * @throws SystemException if a conversion error occurs
    */
   FndSqlType itemTypeToFndSqlType(String type) throws SystemException;
}

