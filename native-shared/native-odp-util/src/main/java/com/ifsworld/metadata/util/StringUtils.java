package com.ifsworld.metadata.util;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

public class StringUtils {
   private static final Map<Character, String> characterEntityMap;

   static {
      characterEntityMap = new HashMap<>();

      // Possible controls and replacement character entity reference (Statically
      // assigned)
      // To match with XML 1.1 (https://www.w3.org/TR/xml11/#charsets)
      // NOTE - NULL(x00) is invalid
      for (int i = 1; i <= 31; i++) {
         characterEntityMap.put((char) i, getCharacterEntity(i));
      }

   }

   private static final Pattern WHITESPACE_PATTERN = Pattern.compile("\\s");

   private StringUtils() {
   }

   public static String replaceAllWhiteSpace(String input) {
      return WHITESPACE_PATTERN.matcher(input).replaceAll(" ");
   }

   public static String getXmlEscapedString(final String str) {
      final StringBuilder builder = new StringBuilder();

      for (char c : str.toCharArray()) {
         if (characterEntityMap.containsKey(c)) {
            builder.append(characterEntityMap.get(c));
         } else {
            builder.append(c);
         }
      }
      return builder.toString();
   }

   private static String getCharacterEntity(final int i) {
      StringBuilder stringBuilder = new StringBuilder("&#00");

      String hexString = Integer.toHexString(i);

      if (hexString.length() == 1) {
         stringBuilder.append('0');
      }

      return stringBuilder.append(hexString).append(';').toString();
   }

   /**
    * Sanitize a message by replacing new line characters with "_", and then HTML
    * encoding.
    *
    * @param message log massage to be sanitized.
    * @return sanitized message.
    */
   public static String sanitize(final String message) {
      if (message == null) {
         return "[null]";
      }

      final String sanitizedMessage = EncodingSupport.encodeForHtml(message.replace('\n', '_').replace('\r', '_'));

      if (!message.equals(sanitizedMessage)) {
         return sanitizedMessage + " (Sanitized)";
      } else {
         return sanitizedMessage;
      }
   }

   /**
    * Locates the first occasion of given character group and returns the full
    * enclosed content between the enclosing characters not including the enclosing
    * characters. Used to extract character groups like (), [] and {}. The returned
    * string will contain any nested character groups within the parsed group.
    * 
    * @param str   - The string to parse the content from.
    * @param open  - Open char
    * @param close - Closing char
    * @return
    */
   public static String extractWeightedGroup(final String str, final char open, final char close) {
      final int start = str.indexOf(open);
      return start == -1 ? "" : extractWeightedGroup(str, open, close, start + 1);
   }

   /**
    * Parse given string for the closing character of the weighted group. This
    * method is used to get the full enclosed content between enclosing characters
    * like (), [] and {}. The returned string will contain any nested character
    * groups within the parsed group.
    * 
    * @param str   - The string to parse the content from.
    * @param open  - Open char
    * @param close - Closing char
    * @param start - Start position for the search if given this should point to
    *              the first character after an open char within the str.
    * @return
    */
   public static String extractWeightedGroup(final String str, final char open, final char close, final int start) {
      final StringBuilder builder = new StringBuilder();
      final char[] characters = str.toCharArray();
      int opened = 1;
      for (int i = start; i < characters.length; i++) {
         if (characters[i] == close) {
            opened--;
         } else if (characters[i] == open) {
            opened++;
         }
         if (opened == 0) {
            return builder.toString();
         }
         builder.append(characters[i]);
      }
      return "";
   }
}
