package com.ifsworld.metadata.exception;

public class MetaInformationException extends ClientMetadataProviderFrameworkException {

  private static final long serialVersionUID = 1L;

  public MetaInformationException(final String msg, final Throwable e) {
    super(msg, e, FndErrorCode.META_INFORMATION_ERROR);
  }

  public MetaInformationException(final String msg, final Throwable e, final int statusCode) {
    super(msg, e, statusCode, FndErrorCode.META_INFORMATION_ERROR);
  }
}
