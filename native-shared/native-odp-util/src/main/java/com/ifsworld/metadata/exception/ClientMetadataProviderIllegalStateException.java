package com.ifsworld.metadata.exception;

/**
 * Exception class mapped to FndErrorCode.CMP_ILLEGAL_STATE
 *
 * <AUTHOR> RnD
 */
public class ClientMetadataProviderIllegalStateException extends ClientMetadataProviderException {
  private static final long serialVersionUID = 1L;
  
  public ClientMetadataProviderIllegalStateException(final String msg, final Throwable e) {
    super(msg, e, FndErrorCode.CMP_ILLEGAL_STATE);
  }

  public ClientMetadataProviderIllegalStateException(final String msg, final int statusCode) {
    super(msg, statusCode, FndErrorCode.CMP_ILLEGAL_STATE);
  }

  public ClientMetadataProviderIllegalStateException(final String msg, final Throwable e, final int statusCode) {
    super(msg, e, statusCode, FndErrorCode.CMP_ILLEGAL_STATE);
  }
}
