package com.ifsworld.metadata.exception;

/**
 * Exception class for client metadata provider exceptions
 *
 * <AUTHOR> RnD
 */
public class ClientMetadataProviderException extends ClientMetadataProviderFrameworkException {
  private static final long serialVersionUID = 1L;
  public ClientMetadataProviderException(final String msg) {
    super(msg, FndErrorCode.CLIENT_METADATA_PROVIDER_ERROR);
  }

  public ClientMetadataProviderException(final Throwable e) {
    super(e, FndErrorCode.CLIENT_METADATA_PROVIDER_ERROR);
  }

  public ClientMetadataProviderException(final String msg, final Throwable e) {
    super(msg, e, FndErrorCode.CLIENT_METADATA_PROVIDER_ERROR);
  }

  public ClientMetadataProviderException(final String msg, final int statusCode) {
    super(msg, statusCode, FndErrorCode.CLIENT_METADATA_PROVIDER_ERROR);
  }

  public ClientMetadataProviderException(final String msg, final Throwable e, final int statusCode) {
    super(msg, e, statusCode, FndErrorCode.CLIENT_METADATA_PROVIDER_ERROR);
  }

  public ClientMetadataProviderException(final String msg, final FndErrorCode fndErrorCode) {
    super(msg, fndErrorCode);
  }

  public ClientMetadataProviderException(final Throwable e, FndErrorCode fndErrorCode) {
    super(e, fndErrorCode);
  }

  public ClientMetadataProviderException(final String msg, final Throwable e, final FndErrorCode fndErrorCode) {
    super(msg, e, fndErrorCode);
  }

  public ClientMetadataProviderException(final String msg, final int statusCode, final FndErrorCode fndErrorCode) {
    super(msg, statusCode, fndErrorCode);
  }

  public ClientMetadataProviderException(final String msg, final Throwable e, final int statusCode, final FndErrorCode fndErrorCode) {
    super(msg, e, statusCode, fndErrorCode);
  }
}
