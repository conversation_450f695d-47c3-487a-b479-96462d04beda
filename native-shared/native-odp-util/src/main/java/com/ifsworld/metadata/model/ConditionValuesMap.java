package com.ifsworld.metadata.model;

import java.util.HashMap;
import java.util.List;

/**
 * Specialized map class that will keep all keys in lowerkey
 * so that the use of the context values is handled as case insensitive.
 */
public class ConditionValuesMap extends HashMap<String, ConditionValue> {
    static final long serialVersionUID = 1L;

    public ConditionValue put(final String key, final String value) {
        return super.put(key.toLowerCase(), new ConditionValue(value));
    }

    public ConditionValue put(final String key, final List<String> values) {
        return super.put(key.toLowerCase(), new ConditionValue(values));
    }

    public ConditionValue get(final String key) {
        return super.get(key.toLowerCase());
    }
}