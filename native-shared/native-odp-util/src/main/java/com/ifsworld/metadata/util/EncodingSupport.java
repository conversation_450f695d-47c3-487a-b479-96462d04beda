package com.ifsworld.metadata.util;

import static com.ifsworld.metadata.util.Encoders.Encoding.*;

public class EncodingSupport {
  private EncodingSupport() {
  }

  public static String encodeForHtml(final String html) {
    return Encoders.getEncoder(HTML).encode(html);
  }

  public static String encodeForJavaScript(final String js) {
    return Encoders.getEncoder(JS).encode(js);
  }

  public static String encodeForSql(final String sql) {
    return Encoders.getEncoder(SQL).encode(sql);
  }
}
