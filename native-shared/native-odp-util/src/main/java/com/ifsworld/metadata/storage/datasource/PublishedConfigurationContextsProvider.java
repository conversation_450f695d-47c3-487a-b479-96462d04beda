package com.ifsworld.metadata.storage.datasource;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class PublishedConfigurationContextsProvider {

   private static final Logger LOGGER = Logger.getLogger(PublishedConfigurationContextsProvider.class.getName());
   // TODO: UPD8 development, When fndcob published_scope_per_model view is in
   // place, below select should be exchanged.
   // private static final String READ_PUBLISHED_SCOPES = "SELECT * FROM
   // published_scope_per_model WHERE model_id = ?";
   private static final String READ_PUBLISHED_SCOPES = "SELECT DISTINCT model_id, scope_id FROM fnd_model_design_data WHERE model_id = ? AND layer_no = 2";
   protected static final Map<String, List<String>> CACHED_USED_CONTEXTS_IN_MODEL = new LinkedHashMap<String, List<String>>();

   /**
    * Clears all cached data
    */
   public static void clearCache() {
      CACHED_USED_CONTEXTS_IN_MODEL.clear();
   }

   /**
    * Returns current size of cache.
    * @return
    */
    public static int size() {
      return CACHED_USED_CONTEXTS_IN_MODEL.size();
   }

   /**
    * Add an entry to the cache
    * this api is added for unit testing purposes.
    * @param client - Client model
    * @param contexts - List of contexts
    */
   public static void put(String client, List<String> contexts) {
      CACHED_USED_CONTEXTS_IN_MODEL.put(client, contexts);
   }


   /**
    * Get a list of configuration contexts id's that is used in a published
    * configuration for given client model.
    * 
    * @param modelName          - Name of client model to enumerate context IDs for.
    * @param connectionProvider - connection povider
    * @return A list of context IDs
    */
   public static List<String> getScopesUsedInModel(final String modelName,
         final DatabaseConnectionProvider connectionProvider) {

      final String modelId = getClientModelId(modelName);
      List<String> usedScopes = CACHED_USED_CONTEXTS_IN_MODEL.get(modelId);
      return usedScopes != null ? usedScopes : loadScopesUsedInModelFromDb(modelId, connectionProvider);
   }

   /**
    * Load list context used in published client configurations
    * 
    * @see getScopesUsedInModel
    * @param modelName          - Name of client model to enumerate context IDs for.
    * @param connectionProvider - connection povider
    * @return A list of context IDs
    */
   private static synchronized List<String> loadScopesUsedInModelFromDb(final String modelId,
         final DatabaseConnectionProvider connectionProvider) {

      // Subsequent calls that are waiting for synchronization should check
      // if prior calls have initiated the cache.
      if (CACHED_USED_CONTEXTS_IN_MODEL.containsKey(modelId)) {
         return CACHED_USED_CONTEXTS_IN_MODEL.get(modelId);
      }
      final Connection connection = connectionProvider.getSystemConnection();
      try (final PreparedStatement preparedStatement = connection.prepareCall(READ_PUBLISHED_SCOPES)) {
         preparedStatement.setString(1, modelId);
         final ResultSet resultSet = preparedStatement.executeQuery();
         final List<String> usedScopes = new ArrayList<String>();
         while (resultSet.next()) {
            usedScopes.add(resultSet.getString("SCOPE_ID"));
         }
         CACHED_USED_CONTEXTS_IN_MODEL.put(modelId, usedScopes);
         return usedScopes;
      } catch (final SQLException ex) {
         LOGGER.log(Level.INFO, "Error when fetching used contexts.", ex);
         CACHED_USED_CONTEXTS_IN_MODEL.put(modelId, null);
         return null;
      }
   }

   private static String getClientModelId(final String modelName) {
      return "ClientMetadata.client:" + modelName;
   }
}
