package com.ifsworld.metadata.model;

public class ConfigurationContextMapping {
   private final String contextId;
   private final String label;
   private final ExpressionEvaluateInterface condition;
   
   public ConfigurationContextMapping(
           final String contextId,
           final String label,
           final ExpressionEvaluateInterface condition) {
      this.contextId = contextId;
      this.label = label;
      this.condition = condition;
   }
   
   public static ConfigurationContextMapping FromString(
           final String contextId,
           final String label,
           final String condition) {
      return new ConfigurationContextMapping(contextId, label, Condition.fromString(condition));
   }

   /**
    * @return the ContextId
    */
   public String getContextId() {
      return contextId;
   }

   /**
    * @return the label
    */
   public String getLabel() {
      return label;
   }

   /**
    * @return the condition
    */
   public ExpressionEvaluateInterface getCondition() {
      return this.condition;
   }

   /**
    * Evaluates the expression
    * @param values - Values to validate expression against. 
    */
    public boolean evaluate(final ConditionValuesMap values) {
      return this.condition != null && this.condition.evaluate(false, values);
   }  
}
