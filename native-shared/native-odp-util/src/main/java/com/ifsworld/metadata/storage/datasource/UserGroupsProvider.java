package com.ifsworld.metadata.storage.datasource;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class UserGroupsProvider {

   private static final Logger LOGGER = Logger.getLogger(UserGroupsProvider.class.getName());

   private static final Map<String, List<String>> CACHED_USERGROUPS_PER_USER = new HashMap<String, List<String>>();
   private static final String READ_EMP_GROUPS_STATEMENT = "SELECT a.name FROM user_group a, user_group_user b\n"
         + "WHERE a.user_group_id = b.user_group_id AND b.user_id = ?";


   /**
    * Clears the in memory cache of user in usergroups.
    */
   public static void clearCache() {
      CACHED_USERGROUPS_PER_USER.clear();
   }

   /**
    * Returns current size of cache.
    * @return
    */
   public static int size() {
      return CACHED_USERGROUPS_PER_USER.size();
   }

   /**
    * Add an entry to the cache
    * this api is added for unit testing purposes.
    * @param userId - User identifier
    * @param groups - List of groups
    */
   public static void put(String userId, List<String> groups) {
      CACHED_USERGROUPS_PER_USER.put(userId, groups);
   }
   
   /**
    * Fetch user groups give user id is part of.
    * 
    * @param userId            - User identifier to search with.
    * @param connectionProvider - Connection provider used to fetch the data.
    * @return List of usergroups
    */
   public static List<String> getUserGroups(final String userId, final DatabaseConnectionProvider connectionProvider) {
      List<String> result = CACHED_USERGROUPS_PER_USER.get(userId);
      if (result == null) {
         result = loadUserGroupsFromDb(userId, connectionProvider);
         CACHED_USERGROUPS_PER_USER.put(userId, result);
      }
      return result;
   }

   /**
    * Fetch user groups give user id is part of.
    * 
    * @param userId            - User identifier to search with.
    * @param connectionProvider - Connection provider used to fetch the data.
    * @return List of usergroups
    */
   private static List<String> loadUserGroupsFromDb(final String userId, final DatabaseConnectionProvider connectionProvider) {

      final List<String> result = new ArrayList<String>();
      final Connection connection = connectionProvider.getSystemConnection();
      try (final PreparedStatement preparedStatement = connection.prepareCall(READ_EMP_GROUPS_STATEMENT)) {

         preparedStatement.setString(1, userId);
         final ResultSet resultSet = preparedStatement.executeQuery();

         while (resultSet.next()) {
            String group = resultSet.getString("NAME");
            result.add(group);
         }
      } catch (final SQLException ex) {
         LOGGER.log(Level.INFO, "Unexpected error while fetching usergroups.", ex);
      }
      return result;
   }
}