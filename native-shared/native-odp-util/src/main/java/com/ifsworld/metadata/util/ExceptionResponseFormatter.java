package com.ifsworld.metadata.util;

import com.ifsworld.metadata.exception.*;

import jakarta.json.*;
import java.io.IOException;
import java.io.StringWriter;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.ResourceBundle;

public class ExceptionResponseFormatter {
  // String constants for error response constants
  private static final String CODE = "code";
  private static final String MESSAGE = "message";
  private static final String TRACE = "trace";
  private static final String ERROR = "error";
  private static final String INNER_ERROR = "innerError";
  private static final String DETAILS = "details";
  private static final String TYPE = "type";

  private Throwable exception;
  private static ResourceBundle resourceBundle;
  static {
    ExceptionResponseFormatter.resourceBundle = ResourceBundle.getBundle("message");
  }

  public ExceptionResponseFormatter(final Throwable exception) {
    this.exception = exception;
  }

  public String getJsonContent(final boolean debugEnabled) {

    final String fndErrorCode;
    final String fndMessage;

    if (exception instanceof ClientMetadataProviderBaseException) {
      final ClientMetadataProviderBaseException clientMetadataProviderBaseException = (ClientMetadataProviderBaseException) exception;
      fndErrorCode = clientMetadataProviderBaseException.getFndErrorCode();
    } else {
      fndErrorCode = FndErrorCode.UNEXPECTED.getFndErrorCode();
    }

    fndMessage = resourceBundle.getString(fndErrorCode);

    final JsonObjectBuilder errorResponseBuilder = Json.createObjectBuilder()
      .add(CODE, fndErrorCode)
      .add(MESSAGE, fndMessage);

    addTraceAndDetails(errorResponseBuilder, exception, debugEnabled);

    final JsonObject errorResponse = Json.createObjectBuilder()
      .add(ERROR, errorResponseBuilder.build())
      .build();
    try (final StringWriter writer = new StringWriter();
         final JsonWriter jsonWriter = Json.createWriter(writer)) {
      jsonWriter.writeObject(errorResponse);
      return writer.toString();
    } catch (IOException e) {
      throw new ClientMetadataProviderException("Error while closing writers.", e);
    }
  }

  // Support method to add details section and innerError section while traversing inner exceptions. Method will
  // internally consider about DebugFlags and Fnd exception type
  private static void addTraceAndDetails(final JsonObjectBuilder mainJsonObject, final Throwable exception, final boolean debugEnabled) {
    final boolean detailEnabled = exception instanceof DatabaseException || debugEnabled
                                  || exception instanceof ClientMetadataProviderSerializationException; // exception occurred inside olingo code.

    final boolean traceEnabled = DebugFlags.showStackTrace() || DebugFlags.isDevelopmentEnv();

    if (traceEnabled || detailEnabled) {
      // We will traverse inner exceptions
      final JsonArrayBuilder traceArrayBuilder = Json.createArrayBuilder();
      final JsonArrayBuilder details = Json.createArrayBuilder();

      Throwable currentException = exception;
      do {
        if (traceEnabled) {
          final JsonObjectBuilder innerObj = Json.createObjectBuilder()
            .add(TYPE, currentException.getClass().getName())
            .add(MESSAGE,
              currentException.getMessage() != null ? currentException.getMessage() : "");

          final JsonArrayBuilder traceArray = Json.createArrayBuilder();
          Arrays.asList(currentException.getStackTrace())
            .forEach(element -> traceArray.add(element.toString()));

          innerObj.add(TRACE, traceArray.build());

          traceArrayBuilder.add(innerObj.build());
        }

        if (currentException instanceof SQLException && detailEnabled) {
          final SQLException sqlException = (SQLException) currentException;

          final JsonObjectBuilder detailObj =
            Json.createObjectBuilder().add(CODE, sqlException.getErrorCode());

          final String[] messageSplit = sqlException.getMessage().split("\n");
          detailObj.add(MESSAGE, messageSplit.length > 0 ? messageSplit[0] : "");

          details.add(detailObj.build());
        }

        if (currentException instanceof ClientMetadataProviderSerializationException) {
          final JsonObjectBuilder detailObj = Json.createObjectBuilder();
          detailObj.add(
            MESSAGE,
            currentException.getCause() != null ? currentException.getCause().getMessage() : "");
          details.add(detailObj.build());
        }

      } while ((currentException = currentException.getCause()) != null);

      // Though we traversed and collected data, we now check whether we need to add them
      if (traceEnabled) {
        mainJsonObject.add(
          INNER_ERROR,
          Json.createObjectBuilder().add(TRACE, traceArrayBuilder.build()).build());
      }

      if (detailEnabled) {
        mainJsonObject.add(DETAILS, details.build());
      }
    }
  }

}
