package com.ifsworld.metadata.exception;

/**
 * Exception class mapped to FndErrorCode.CMP_SERIALIZATION_ERROR
 *
 * <AUTHOR> RnD
 */
public class ClientMetadataProviderSerializationException extends ClientMetadataProviderException {
  private static final long serialVersionUID = 1L;
  
  public ClientMetadataProviderSerializationException(final String msg, final Throwable e) {
    super(msg, e, FndErrorCode.CMP_SERIALIZATION_ERROR);
  }

  public ClientMetadataProviderSerializationException(final String msg, final int statusCode) {
    super(msg, statusCode, FndErrorCode.CMP_SERIALIZATION_ERROR);
  }

  public ClientMetadataProviderSerializationException(final Throwable e) {
    super(e, FndErrorCode.CMP_SERIALIZATION_ERROR);
  }

  public ClientMetadataProviderSerializationException(final String msg) {
    super(msg, FndErrorCode.CMP_SERIALIZATION_ERROR);
  }

  public ClientMetadataProviderSerializationException(final String msg, final Throwable e, final int statusCode) {
    super(msg, e, statusCode, FndErrorCode.CMP_SERIALIZATION_ERROR);
  }
}
