/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package com.ifsworld.fnd.mobile.common;

import com.ifsworld.fnd.common.exceptions.ODataProviderIllegalStateException;
import com.ifsworld.fnd.odp.api.metadata.model.EnumType;
import com.ifsworld.fnd.odp.api.metadata.model.Projection;
import com.ifsworld.fnd.odp.api.types.ModelDataType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import jakarta.json.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

import static com.ifsworld.fnd.odp.api.types.ModelDataType.PrimitiveType.BOOLEAN;
import static com.ifsworld.fnd.odp.api.types.ModelDataType.VariableType.ENUMERATION;
import static com.ifsworld.fnd.storage.StorageProviderUtils.getEnumDbValueFromValueInteger;
import static java.lang.String.format;


/**
 *
 * <AUTHOR> RnD
 */
public class ODataFormattingUtility {

   private static final Logger LOG = LogManager.getLogger(ODataFormattingUtility.class);
   public static final String DATE_FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss";

   private ODataFormattingUtility() {
      //no instances allowed
   }


   private static Supplier<ODataProviderIllegalStateException> getExceptionSupplier(final String msg) {
      return () -> new ODataProviderIllegalStateException(msg);
   }

   private static Object convertParameterValueToDBValue(
           final Object originalParamValue,
           final String parameterName,
           final ModelDataType paramDataType,
           final Projection projection) {

      final Object dbParamValue;

      if (paramDataType.isOfType(BOOLEAN)) {
         dbParamValue
                 = paramDataType.getTypeInfo().orElseThrow(
                 getExceptionSupplier(
                         format("Boolean parameter '%s' TypeInfo can not be null.", parameterName)))
                 .getTrueFalseValue(originalParamValue);

      } else if (paramDataType.isOfType(ENUMERATION)) {
         final EnumType enumType
                 = projection.getEnumType(
                 paramDataType.getSubType().orElseThrow(
                         getExceptionSupplier(format("Enum parameter '%s' subtype can not be null.",
                                 parameterName))));
         if (originalParamValue instanceof Integer) {
            // [AsBaLk] if operation bound entity key is a composite key with an enum key, you might get a
            // number in originalParamValue (since we get the originalParamValue value from entity properties)
            dbParamValue = getEnumDbValueFromValueInteger(originalParamValue, enumType);
         } else {
            // if operation parameter is an enum parameter then we can get the correct string db value
            dbParamValue
                    = enumType.getEnumDbValueFromIdentifier(
                    originalParamValue != null ? originalParamValue.toString() : null);
         }
      } else {
         dbParamValue = originalParamValue;
      }
      return dbParamValue;
   }


   public static Map<String, Object> toMap(JsonObject jsonobj)  throws JsonException {
      Map<String, Object> map = new HashMap<>();
      for (Object keyObj : jsonobj.keySet()) {
         String key = (String)keyObj;
         Object value = jsonobj.get(key);
         if (value instanceof JsonArray) {
            value = toList((JsonArray) value);
         } else if (value instanceof JsonObject) {
            value = toMap((JsonObject) value);
         }
         else if(((JsonValue) value).getValueType() == JsonValue.ValueType.STRING) {
            map.put(key,  ((JsonString)value).getString());
         }
         else if(((JsonValue) value).getValueType() == JsonValue.ValueType.NUMBER) {
            map.put(key,  ((JsonNumber)value).doubleValue());
         }
         else if(((JsonValue) value).getValueType() == JsonValue.ValueType.TRUE) {
            map.put(key,  true);
         }
         else if(((JsonValue) value).getValueType() == JsonValue.ValueType.FALSE) {
            map.put(key,  false);
         }
         else{
            map.put(key, value);
         }

      }   return map;
   }

   public static List<Object> toList(JsonArray array) throws JsonException {
      List<Object> list = new ArrayList<Object>();
      for(int i = 0; i < array.size(); i++) {
         Object value = array.get(i);
         if (value instanceof JsonArray) {
            value = toList((JsonArray) value);
         }
         else if (value instanceof JsonObject) {
            value = toMap((JsonObject) value);
         }
         list.add(value);
      }   return list;
   }
}
