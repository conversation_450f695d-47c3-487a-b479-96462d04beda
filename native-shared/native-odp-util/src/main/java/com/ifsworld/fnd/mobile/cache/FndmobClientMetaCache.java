/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.ifsworld.fnd.mobile.cache;

import com.ifsworld.fnd.mobile.MobileLogTypeEnumeration;
import com.ifsworld.fnd.mobile.MobileLogUtilities;
import com.ifsworld.fnd.mobile.MobileLogUtilitiesFactory;
import com.ifsworld.fnd.mobile.common.MobileCommonUtilities;
import com.ifsworld.fnd.mobile.exceptions.MetaDataException;
import com.ifsworld.metadata.storage.datasource.ConfigurationContextMappingsProvider;
import org.apache.logging.log4j.LogManager;
import com.ifsworld.metadata.storage.datasource.*;

import java.sql.*;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class FndmobClientMetaCache {

    private static final Map<String, MetaArtifact> APP_META_CACHE = new ConcurrentHashMap<>();
    private static final Map<String, MetaArtifact> CLIENT_META_CACHE = new ConcurrentHashMap<>();
    private static final org.apache.logging.log4j.Logger LOG = LogManager.getLogger(FndmobClientMetaCache.class);
    private static final String GLOBAL_CONTEXT = "global";
    private static final String STMT_GET_META =
                    "DECLARE " +
                    "BEGIN " +
                    "   ? := Model_Design_SYS.Get_Offline_Data_Content_(Model_Design_SYS.CLIENT_METADATA, ?, ?, ?); " +
                    "END;";
    private static final String STMT_GET_APPLICATION = "select client_name from mobile_application where app_name = ?";
    private static final String STMT_GET_APP_META_CACHE_VERSION = "select client_meta_cache_version from mobile_application_version where app_name=? and active='TRUE'";
    private static final String STMT_GET_CLIENT_META_CACHE_VERSION = "select client_meta_cache_version from mobile_application_version where active='TRUE' AND " +
            "app_name IN (select a.app_name from mobile_application a " +
            "where ? IN (select regexp_substr(a.client_name,'[^,]+', 1, level) from dual connect by regexp_substr(a.client_name, '[^,]+', 1, level) is not null ))";
    private static final String STMT_GET_APPS_USING_CLIENT = "select a.app_name from mobile_application a " +
            "where ? IN (select regexp_substr(a.client_name,'[^,]+', 1, level) from dual connect by regexp_substr(a.client_name, '[^,]+', 1, level) is not null )";

    enum MetaArtifactType {
        APP, CLIENT;
        @Override
        public String toString() {
            return name().toLowerCase();
        }
    }

    private FndmobClientMetaCache() {
    }

    public static class MetaArtifact {

        private final String name;
        private final String clientName;
        private final String json;
        private Date cacheVersion;
        boolean needsRefreshing = false;

        public MetaArtifact(String name, String clientName, String json, Date cacheVersion) {
            this.name = name;
            this.clientName = clientName;
            this.json = json;
            this.cacheVersion = cacheVersion;
        }

        public String getJson() {
            return json;
        }

        public String getName() {
            return name;
        }

        public String getClientName() {
            return clientName;
        }
    }

    private static String formatKey(String appName, String lng, String scope) {
        return appName + "^" + lng + "^" + scope + "^";
    }

    public static String getAppData(Connection connection, String appName, String scope, String locale) {
        if (scope == null || scope.isEmpty()) {
            scope = GLOBAL_CONTEXT;
        }
        String key = formatKey(appName, locale, scope);

        //Invalidate Cache based on database version
        validateCacheFromDatabase(connection, appName, locale, scope, MetaArtifactType.APP);

        //Load meta data if needed
        if (cacheNeedsRefreshing(appName, locale, MetaArtifactType.APP, scope)) {
            refreshCache(connection, appName, locale, scope, getCacheVersion(connection, appName, MetaArtifactType.APP));
        }

        MetaArtifact appMeta = APP_META_CACHE.get(key);
        if (appMeta != null) {
            return appMeta.json;
        } else {
            raiseMetaNotFoundException(appName);
            return null;
        }
    }

    public static String getClientData(Connection connection, String name, String scope, String locale) {
        MobileLogUtilities logUtilHandler = MobileLogUtilitiesFactory.getHandler(connection);
        String key = formatKey(name, locale, scope);
        try {
            if (scope == null || scope.isEmpty()) {
                scope = GLOBAL_CONTEXT;
            }
            //Invalidate Cache based on database version
            validateCacheFromDatabase(connection, name, locale, scope, MetaArtifactType.CLIENT);
            //Need to refresh all apps that contain this client
            if (cacheNeedsRefreshing(name, locale, MetaArtifactType.CLIENT, scope)) {
                refreshAppsUsingClient(connection, name, locale,scope);
            }
        }
        catch (Exception e){
            LOG.error("Unexpected error occurred in getClientData method", e);
            logUtilHandler.saveAppLog(name, "", MobileCommonUtilities.getDetailError(e), MobileLogTypeEnumeration.ERROR);
        }
        MetaArtifact clientMeta = CLIENT_META_CACHE.get(key);
        if (clientMeta != null) {
            return clientMeta.json;
        } else {
            raiseMetaNotFoundException(name);
            return null;
        }
    }

    private static void refreshAppsUsingClient(Connection connection, String clientName, String lng, String scope) {
        MobileLogUtilities logUtilHandler = MobileLogUtilitiesFactory.getHandler(connection);
        String appName;
        try (final PreparedStatement stmt = connection.prepareStatement(STMT_GET_APPS_USING_CLIENT)) {
            stmt.setString(1, clientName);
            ResultSet rs = stmt.executeQuery();
            while(rs.next()) {
                appName = rs.getString(1);
                refreshCache(connection, appName, lng, scope, getCacheVersion(connection, appName, MetaArtifactType.APP));
            }
        } catch (SQLException e) {
            LOG.error("Unexpected error occurred in refreshAppsUsingClient method", e);
            logUtilHandler.saveAppLog(clientName, "", MobileCommonUtilities.getDetailError(e), MobileLogTypeEnumeration.ERROR);
        }
    }

    private static boolean cacheNeedsRefreshing(String name, String lng, MetaArtifactType type, String scope) {
        String key = formatKey(name, lng, scope);
        MetaArtifact artifact = (type == MetaArtifactType.APP) ? APP_META_CACHE.get(key) : CLIENT_META_CACHE.get(key);

        if (artifact == null || artifact.json == null || artifact.json.trim().length() == 0) {
            return true;
        }
        //if any of the clients in this app needs refreshing, do refresh the whole app
        if ( type == MetaArtifactType.APP && artifact.clientName != null && !artifact.clientName.trim().isEmpty()) {
            String[] clientsArray = artifact.clientName.split(",");
            for (String clientName : clientsArray) {
                MetaArtifact clientArtifact = CLIENT_META_CACHE.get(formatKey(clientName, lng, scope));
                if (clientArtifact == null || clientArtifact.needsRefreshing){
                    return true;
                }
            }
        }
        return artifact.needsRefreshing;
    }

    private static void raiseMetaNotFoundException(String name) throws MetaDataException {
        throw new MetaDataException(String.format("Meta Data not found. Artifact Name=%s", name));
    }


    private static synchronized void refreshCache(Connection connection, String appName, String lng, String scope, Date cacheVersion) {
        if (!cacheNeedsRefreshing(appName, lng, MetaArtifactType.APP, scope)) { //another thread has already updated the cache
            return;
        }
        MetaArtifact layout = getAppMetadataContent(connection, appName, lng, scope, 0);
        if (layout == null) {
            return;
        }
        layout.needsRefreshing = false;
        layout.cacheVersion = cacheVersion;
        APP_META_CACHE.put(formatKey(appName, lng, scope), layout);

        if (layout.clientName != null && !layout.clientName.trim().isEmpty()) {
            String[] clientsArray = layout.clientName.split(",");
            for (String clientName : clientsArray) {
                layout = getClientMetadataContent(connection, clientName, lng, scope, 0);
                if (layout != null) {
                    layout.needsRefreshing = false;
                    layout.cacheVersion = cacheVersion;
                    CLIENT_META_CACHE.put(formatKey(clientName, lng, scope), layout);
                }
            }
        }
        if (LOG.isDebugEnabled()) {
            LOG.info("Loaded Client Meta Data for app: {}, Language: {}, Scope: {}", appName, lng, scope);
        }
    }

    public static MetaArtifact getAppMetadataContent(Connection connection, String appName, String locale, String scope, int retryAttempts) {
        return getMetadataContent(connection, appName, MetaArtifactType.APP, locale, scope, retryAttempts);
    }

    public static MetaArtifact getClientMetadataContent(Connection connection, String clientName, String locale, String scope, int retryAttempts) {
        return getMetadataContent(connection, clientName, MetaArtifactType.CLIENT, locale, scope, retryAttempts);
    }

    private static MetaArtifact getMetadataContent(Connection connection, String name, MetaArtifactType type, String locale, String scope, int retryAttempts) {
        MobileLogUtilities logUtilHandler = MobileLogUtilitiesFactory.getHandler(connection);

        String clientName = null;
        if (type == MetaArtifactType.APP) {
            try (final PreparedStatement stmt = connection.prepareStatement(STMT_GET_APPLICATION)) {
                stmt.setString(1, name);
                ResultSet rs = stmt.executeQuery();
                if (rs.next()) {
                    clientName = rs.getString(1);
                } else {
                    logUtilHandler.saveSystemLog(String.format("Application %s not found.", name), MobileLogTypeEnumeration.ERROR);
                    return null;
                }
            } catch (SQLException e) {
                LOG.error("Unexpected error occurred in getMetadataContent method", e);
                logUtilHandler.saveAppLog(name, "", MobileCommonUtilities.getDetailError(e), MobileLogTypeEnumeration.ERROR);
                return null;
            }

            if (LOG.isDebugEnabled()) {
                LOG.info("Get Meta Data content (Artifact Name: {}, Language: {}, Scope: {})", name, locale, scope);
            }
        }
        if (scope.isEmpty()) {
            scope = GLOBAL_CONTEXT;
        }

        logUtilHandler.saveAppLog(name, "", "Getting (" + type.toString() + ") Meta Data content (" + name + "," + locale + "," + scope + ")", MobileLogTypeEnumeration.INFO);

        try (final CallableStatement activateStmt = connection.prepareCall(STMT_GET_META)) {
            activateStmt.registerOutParameter(1, Types.CLOB);
            activateStmt.setString(2, type.toString());
            activateStmt.setString(3, name);
            activateStmt.setString(4, scope);
            activateStmt.execute();
            return new MetaArtifact(name, clientName, activateStmt.getString(1), getCacheVersion(connection, name, type));
        } catch (SQLException e) {
            String error = e.getMessage();
            if (error.contains("ORA-04068") && retryAttempts < 3) {
                // KATH: If a projection model has been altered and deployed to database,
                // then invoking the getMeta can result ORA-04068: existing state of packages has been discarded
                // In such cases we retry getMeta 3 times.
                return getMetadataContent(connection, name, type, locale, scope, ++retryAttempts);
            }
        } catch (Exception e) {
            LOG.error("Unexpected error occurred in getMetadataContent method", e);
            logUtilHandler.saveAppLog(name, "", MobileCommonUtilities.getDetailError(e),MobileLogTypeEnumeration.ERROR);
        }
        return null;
    }

    /*
     * Validate cache version against database server
     */
    private static void validateCacheFromDatabase(Connection connection, String name, String lng, String scope, MetaArtifactType type) {
        String key = formatKey(name, lng, scope);
        MetaArtifact artifact;
        if (type == MetaArtifactType.APP) {
            artifact = APP_META_CACHE.get(key);
        } else {
            artifact = CLIENT_META_CACHE.get(key);
        }
        if (artifact != null && !artifact.needsRefreshing) {
            Date cacheVersion = getCacheVersion(connection, name, type);
            if (artifact.cacheVersion.compareTo(cacheVersion) != 0) {
                artifact.needsRefreshing = true;
                ConfigurationContextMappingsProvider.clearCache();
            }
        }
    }

    /**
     * @param name String
     */
    private static Date getCacheVersion(Connection connection, String name, MetaArtifactType type) {
        MobileLogUtilities logUtilHandler = MobileLogUtilitiesFactory.getHandler(connection);
        String sql = (type == MetaArtifactType.APP) ? STMT_GET_APP_META_CACHE_VERSION : STMT_GET_CLIENT_META_CACHE_VERSION;
        try (final PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, name);
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getDate(1);
            }
        } catch (SQLException e) {
            LOG.error("Unexpected error occurred in getCacheVersion method", e);
            logUtilHandler.saveAppLog(name, "", MobileCommonUtilities.getDetailError(e), MobileLogTypeEnumeration.ERROR);
        }
        raiseMetaNotFoundException(name);
        return null;
    }

    /**
     * @param appName      String
     * @param cacheVersion Date
     */
    public static void clearCache(String appName, Date cacheVersion) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("Clearing fndmob client meta cache");
        }
        // Clear cache by validating cache version
        // This will invalidate all the caches under the same app name
        boolean isNeedRefreshContext = false;
        for (MetaArtifact appLayout : APP_META_CACHE.values()) {
            if (appName.equals(appLayout.name)
                    && !appLayout.needsRefreshing
                    && (appLayout.cacheVersion == null || cacheVersion == null || appLayout.cacheVersion.compareTo(cacheVersion) != 0)) {
                appLayout.needsRefreshing = true;
                clearClientCache(appLayout.clientName);
                isNeedRefreshContext = true;
            }
        }
        if(isNeedRefreshContext){
            ConfigurationContextMappingsProvider.clearCache();
            UserGroupsProvider.clearCache();
            PublishedConfigurationContextsProvider.clearCache();
        }
    }

    private static void clearClientCache(String clientNames) {
        if (clientNames != null && !clientNames.trim().isEmpty()) {
            String[] clientsArray = clientNames.split(",");
            for (String clientName : clientsArray) {
                for (MetaArtifact clientLayout : CLIENT_META_CACHE.values()) {
                    if (clientLayout.name.equals(clientName)) {
                        clientLayout.needsRefreshing = true;
                    }
                }
            }
        }
    }
}

