package com.ifsworld.fnd.mobile;

import com.ifsworld.fnd.mobile.common.MobileCommonUtilities;
import com.ifsworld.fnd.mobile.exceptions.NativeOfflineException;
import org.apache.logging.log4j.LogManager;

import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

/*
 * Implementation class for Mobile Attachments.
 */

public class MobileAttachmentUtilities {

   private static final String DOCMAN_UTIL_CLASS_NAME = "com.ifsworld.docman.projection.util.DocmanFileOperationUtil";
   private static final String MEDIA_UTIL_CLASS_NAME = "com.ifsworld.appsrv.projection.util.MediaItemUtil";
   private static final org.apache.logging.log4j.Logger LOG = LogManager.getLogger(MobileAttachmentUtilities.class);


   public static void uploadFile(final Map<String, Object> parameters, final String contentLength, final InputStream inputStream, Connection connection) throws NativeOfflineException {
      final Class<?>[] parameterTypes = new Class<?>[]{Map.class, Connection.class};
      Object[] args;
      Class<?> docmanFileOperationUtil;
      Object docmanObj;
      Method method;

      try {
         docmanFileOperationUtil = Class.forName(DOCMAN_UTIL_CLASS_NAME);
         docmanObj = docmanFileOperationUtil.getDeclaredConstructor().newInstance();
         method = docmanFileOperationUtil.getMethod("uploadFile", parameterTypes);

         Object fileName = parameters.get("FileName");
         if (fileName != null && (fileName.toString().length() != 0)) {
            parameters.put("OriginalFileData", inputStream);
            parameters.put("FileData", inputStream);
            parameters.put("FileLength", contentLength);
            Object fileNoObject = parameters.get("FileNo");
            if (fileNoObject != null) {
               if (fileNoObject instanceof Double) {
                  int fileNo = ((Double) fileNoObject).intValue();
                  parameters.put("FileNo", fileNo);
               }
            }

            args = new Object[]{parameters, connection};
            method.invoke(docmanObj, args);
         } else {
            throw new NativeOfflineException("Error when retrieving name from this file.");
         }
      } catch (ClassNotFoundException | NoSuchMethodException | IllegalAccessException | InstantiationException | InvocationTargetException
         ex) {
            // Use ExtensionException to return error in correct format to client
            throw new NativeOfflineException(MobileCommonUtilities.getDetailError(ex));
         }
   }

   public static Map<String, Object> getDocFileStream(final Map<String, Object> parameters, final Connection connection) throws NativeOfflineException {
      final Class<?>[] parameterTypes = new Class<?>[]{Map.class, Connection.class};
      Object[] args = new Object[]{parameters, connection};
      Class<?> docmanFileOperationUtil;
      Object docmanObj;
      Method method;

      try {
         docmanFileOperationUtil = Class.forName(DOCMAN_UTIL_CLASS_NAME);
         docmanObj = docmanFileOperationUtil.getDeclaredConstructor().newInstance();
         method = docmanFileOperationUtil.getMethod("viewFile", parameterTypes);
         Map<String, Object> result = (Map) method.invoke(docmanObj, args);
         Object stream = result.remove("ViewFile");
         result.put("GetDocFileStream", stream);
         return result;
      } catch (ClassNotFoundException | NoSuchMethodException | IllegalAccessException | InstantiationException | InvocationTargetException
              ex) {
         // Use ExtensionException to return error in correct format to client
         throw new NativeOfflineException(MobileCommonUtilities.getDetailError(ex));
      }
   }

   public static void uploadMedia(final Map<String, Object> parameters, final String contentLength, final InputStream inputStream, Connection connection) throws NativeOfflineException {
      final Class<?>[] parameterTypes = new Class<?>[]{Map.class, Connection.class};
      Object[] args;
      Class<?> mediaItemUtil;
      Object mediaObj;
      Method method;

      try {
         mediaItemUtil = Class.forName(MEDIA_UTIL_CLASS_NAME);
         mediaObj = mediaItemUtil.getDeclaredConstructor().newInstance();
         method = mediaItemUtil.getMethod("updateMediaItem", parameterTypes);

         Object itemIdObject = parameters.get("ItemId");
         if (itemIdObject != null) {
            Long itemId = null;
            if(itemIdObject instanceof BigDecimal){
               itemId = ((BigDecimal)itemIdObject).longValue();
            }
            else{
               itemId = ((Double)itemIdObject).longValue();
            }
            //Converting the ItemId from BigDecimal to Long for MediaItemUtil
            parameters.put("ItemId", itemId);
            parameters.put("FileName", getMediaFileName(itemId, connection));
            parameters.put("MediaObject", inputStream);
            parameters.put("FileLength", contentLength);
            args = new Object[]{parameters, connection};
            method.invoke(mediaObj, args);
         } else {
            throw new NativeOfflineException("Error when retrieving item id of media.");
         }
      } catch (ClassNotFoundException | NoSuchMethodException | IllegalAccessException | InstantiationException | InvocationTargetException
              ex) {
         // Use ExtensionException to return error in correct format to client
         throw new NativeOfflineException(MobileCommonUtilities.getDetailError(ex));
      }
   }

   public static Map<String, Object> getMediaFileStream(final Map<String, Object> parameters, final Connection connection) throws NativeOfflineException {
      final Class<?>[] parameterTypes = new Class<?>[]{Map.class, Connection.class};
      Object[] args = new Object[]{parameters, connection};
      Class<?> mediaItemUtil;
      Object mediaObj;
      Method method;

      try {
         mediaItemUtil = Class.forName(MEDIA_UTIL_CLASS_NAME);
         mediaObj = mediaItemUtil.getDeclaredConstructor().newInstance();
         method = mediaItemUtil.getMethod("readMediaItem", parameterTypes);
         Map<String, Object> result = (Map)method.invoke(mediaObj, args);
         result.put("GetMediaFileStream", result.get("MediaObject"));
         result.remove("MediaObject");
         return result;
      } catch (ClassNotFoundException | NoSuchMethodException | IllegalAccessException | InstantiationException | InvocationTargetException
              ex) {
         // Use ExtensionException to return error in correct format to client
         throw new NativeOfflineException(MobileCommonUtilities.getDetailError(ex));
      }
   }

   private static String getMediaFileName(Long itemId, Connection connection) throws NativeOfflineException {
      String fileName = null;
      try (PreparedStatement pstmt = connection.prepareStatement("SELECT Media_Item_API.Get_Media_File(?) MediaFile FROM DUAL")) {
         pstmt.setLong(1, itemId);
         ResultSet resultSet = pstmt.executeQuery();
         while (resultSet.next()) {
            fileName = resultSet.getString("MediaFile");
         }
      } catch (SQLException e) {
         throw new NativeOfflineException("Failed to get Media File name. " + e.getMessage());
      }

      if(fileName == null){
         throw new NativeOfflineException("Failed to get Media File name. Media Item does not exist.");
      }
      return fileName;
   }
}