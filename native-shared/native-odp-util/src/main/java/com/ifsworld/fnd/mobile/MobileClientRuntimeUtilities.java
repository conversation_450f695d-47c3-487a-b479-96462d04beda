package com.ifsworld.fnd.mobile;

import com.ifsworld.fnd.mobile.cache.FndmobClientMetaCache;
import com.ifsworld.fnd.mobile.cache.clientcontext.ContextProvider;
import com.ifsworld.fnd.mobile.common.*;
import com.ifsworld.fnd.mobile.exceptions.NativeOfflineException;
import com.ifsworld.fnd.mobile.model.PushDeviceSettings;
import com.ifsworld.fnd.mobile.model.PushHubSettings;
import com.ifsworld.fnd.mobile.remote.NativeNotificationAccess;
import com.ifsworld.fnd.mobile.remote.NativeExecutorAccess;
import com.ifsworld.fnd.storage.model.DBStructure;
import jakarta.json.Json;
import jakarta.json.JsonObject;
import jakarta.json.JsonObjectBuilder;
import org.apache.logging.log4j.LogManager;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Types;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/*
 * Implementation class for Mobile Runtime.
 */

public class MobileClientRuntimeUtilities {

    private static final org.apache.logging.log4j.Logger LOG = LogManager.getLogger(MobileClientRuntimeUtilities.class);
    // ===========================================
    //            Activate Device
    // ===========================================
    //   device_id_           OUT NOCOPY VARCHAR2,
    //   user_id_             OUT NOCOPY VARCHAR2,
    //   user_name_           OUT NOCOPY VARCHAR2,
    //   locale_code_         OUT NOCOPY VARCHAR2,
    //   pin_authentication_  OUT NOCOPY VARCHAR2,
    //   client_meta_version_ OUT NOCOPY VARCHAR2,
    //   app_name_            IN         VARCHAR2,
    //   device_identifier_   IN         VARCHAR2,
    //   brand_               IN         VARCHAR2,
    //   carrier_             IN         VARCHAR2,
    //   client_db_           IN         VARCHAR2,
    //   client_runtime_      IN         VARCHAR2,
    //   model_               IN         VARCHAR2,
    //   os_                  IN         VARCHAR2,
    //   os_version_          IN         VARCHAR2,
    //   client_version_      IN         VARCHAR2,
    //   platform_            IN         VARCHAR2)
    private static final String ACTIVATE_DEVICE_STMT =
            "BEGIN\n"
                    + "   Mobile_Client_SYS.Activate_Device__(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);\n"
                    + "END;";

    private static final String MOBILE_CONTEXT = "MobileContext";
    private static final String SCOPE_ID = "ScopeId";
    private static final String BRANDING_CODE = "BrandingCode";
    private static final String LOCALE = "Locale_$";
    private static final String PREFERRED_USERNAME = "PreferredUsername";

    private MobileClientRuntimeUtilities() {

    }

    public static Map<String, Object> activateDevice(final Map<String, Object> parameters, final Connection connection) throws NativeOfflineException {
        DBStructure mobileContext = (DBStructure) parameters.get(MOBILE_CONTEXT);
        DBStructure clientInfo = (DBStructure) parameters.get("ClientInfo");

        String appName = mobileContext.getString("AppName");
        String brand = clientInfo.getString("Brand");
        String deviceIdentifier = clientInfo.getString("DeviceIdentifier");
        String model = clientInfo.getString("Model");
        String os = clientInfo.getString("Os");
        String osVersion = clientInfo.getString("OsVersion");
        String clientVersion = clientInfo.getString("ClientVersion");
        String platform = clientInfo.getString("Platform");
        String carrier = clientInfo.getString("Carrier");
        String clientDb = clientInfo.getString("ClientDb");
        String clientRuntime = clientInfo.getString("ClientRuntime");

        try (final CallableStatement activateStmt = connection.prepareCall(ACTIVATE_DEVICE_STMT)) {
            activateStmt.registerOutParameter(1, Types.VARCHAR);
            activateStmt.registerOutParameter(2, Types.VARCHAR);
            activateStmt.registerOutParameter(3, Types.VARCHAR);
            activateStmt.registerOutParameter(4, Types.VARCHAR);
            activateStmt.registerOutParameter(5, Types.VARCHAR);
            activateStmt.registerOutParameter(6, Types.VARCHAR);
            activateStmt.registerOutParameter(7, Types.CLOB);
            activateStmt.setString(8, appName);
            activateStmt.setString(9, deviceIdentifier);
            activateStmt.setString(10, brand);
            activateStmt.setString(11, carrier);
            activateStmt.setString(12, clientDb);
            activateStmt.setString(13, clientRuntime);
            activateStmt.setString(14, model);
            activateStmt.setString(15, os);
            activateStmt.setString(16, osVersion);
            activateStmt.setString(17, clientVersion);
            activateStmt.setString(18, platform);

            activateStmt.execute();
            String userId = activateStmt.getString(2);//user_id

            JsonObjectBuilder returnObject = Json.createObjectBuilder();
            returnObject.add("DeviceId", activateStmt.getString(1)); //device_id
            returnObject.add("UserId", userId); //user_id
            returnObject.add("UserName", activateStmt.getString(3)); //user_name
            returnObject.add("LocaleCode", activateStmt.getString(4)); //locale_code
            clearClientMetaCache(appName, activateStmt.getString(5)); // Should clear cache before call getClientContext
            returnObject.add("PinAuthentication", activateStmt.getString(6)); //deprecated - pin_authentication
            returnObject.add("ClientParams", activateStmt.getString(7)); // Client Parameters

            //Resolve Context
            returnObject.add(SCOPE_ID, ContextProvider.getClientContext(connection, userId, appName));
            returnObject.add(BRANDING_CODE, ContextProvider.getBrandingContextCode(connection, userId));

            final ResourceBundle resourceBundle = ResourceBundle.getBundle("odp-app-info");
            returnObject.add("FndmobVersion", resourceBundle.getString("Implementation-Version"));

            Map<String, Object> returnMap = new HashMap<>();
            returnMap.put("ActivateDevice", new ByteArrayInputStream(returnObject.build().toString().getBytes(StandardCharsets.UTF_8)));
            return returnMap;

        } catch (RuntimeException | SQLException ex) {
            Logger.getLogger(MobileClientRuntimeUtilities.class.getName()).log(Level.WARNING, null, ex);
            LOG.error(MobileCommonUtilities.getDetailError(ex));
            throw new NativeOfflineException(ExceptionFormatter.getFormattedMessage(ex), ex);
        }
    }

    public static Map<String, Object> pushRegistry(final Map<String, Object> parameters, final Connection connection) throws NativeOfflineException {
        try {
            DBStructure mobileContext = (DBStructure) parameters.get(MOBILE_CONTEXT);
            Object[] mobileContextAttributes = mobileContext.getAttributes();
            String appName = (String) mobileContextAttributes[0];
            String deviceId = (String) mobileContextAttributes[1];

            String pnsHandle = (String) parameters.get("PnsHandle");
            String registrationId = (String) parameters.get("RegistrationId");
            String deviceOS = (String) parameters.get("ClientOS");
            String preferredUsername = (String) parameters.get(PREFERRED_USERNAME);

            MobileContext context = MobileContext.with(appName, preferredUsername, deviceId, null, null, null).build();
            PushHubSettings hubSettings = PushDbAccess.getHubSettings(context.getAppName(), connection);

            // Check the validity of previous push registration and send those to the client instead of creating new
            boolean isActive = false;
            PushDeviceSettings pushDeviceSettings = PushDbAccess.getPushDeviceSettings(context, connection);

            // Set ApiVersion 1.0 always (This can be use to migrate push drivers in the future)
            String apiVersion = PushHubSettings.PUSH_API_VERSION;

            //Client want to unregister from hub
            if (MobileCommonUtilities.isEmpty(pnsHandle)) {
                // Unregister PNS handler using old registration Id
                PushRegContextBase pushRegContext = PushRegContext.with(
                                hubSettings.getConnectionString(),
                                hubSettings.getHubPath(),
                                null,
                                null,
                                pushDeviceSettings.getRegistrationId())
                        .buildForUnRegister();

                NativeNotificationAccess.getInstance().unRegister(pushRegContext);
            } else {
                // Register new one by sending pns handler and old registration id to remove old one from the hub.
                PushRegContextBase pushRegContext = PushRegContext.with(
                                hubSettings.getConnectionString(),
                                hubSettings.getHubPath(),
                                deviceOS,
                                pnsHandle,
                                pushDeviceSettings != null && pushDeviceSettings.getRegistrationId() != null ? pushDeviceSettings.getRegistrationId() : registrationId)
                        .buildForRegister();

                //Updated Push Settings
                pushDeviceSettings = new PushDeviceSettings(NativeNotificationAccess.getInstance().register(pushRegContext), deviceOS, apiVersion, "TRUE");
                isActive = true;
            }

            //Update Push settings in device app
            PushDbAccess.setPushSettings(context, pushDeviceSettings, isActive, connection);

            JsonObject result = getPushRegistryDetails(deviceId, pnsHandle, pushDeviceSettings.getExpirationTime(), pushDeviceSettings.getRegistrationId());
            Map<String, Object> returnMap = new HashMap<>();
            returnMap.put("PushRegistry", new ByteArrayInputStream(result.toString().getBytes(StandardCharsets.UTF_8)));

            return returnMap;
        } catch (NativeStorageException | ParseException | RuntimeException ex) {
            Logger.getLogger(MobileClientRuntimeUtilities.class.getName()).log(Level.SEVERE, null, ex);
            LOG.error(MobileCommonUtilities.getDetailError(ex));
            throw new NativeOfflineException(ExceptionFormatter.getFormattedMessage(ex), ex);
        }
    }

    public static Map<String, Object> metaData(final Map<String, Object> parameters, final Connection connection) {
        //temporary redirect until change in ifsapp repo is merged
        return appMetaData(parameters, connection);
    }

    public static Map<String, Object> appMetaData(final Map<String, Object> parameters, final Connection connection) {
        DBStructure mobileContext = (DBStructure) parameters.get(MOBILE_CONTEXT);
        String scopeId = (String) parameters.get(SCOPE_ID);
        String locale = (String) parameters.getOrDefault(LOCALE, "en");

        Object[] mobileContextAttributes = mobileContext.getAttributes();
        String appName = (String) mobileContextAttributes[0];

        String metaData = FndmobClientMetaCache.getAppData(connection, appName, scopeId, locale);

        JsonObjectBuilder returnObject = Json.createObjectBuilder();
        returnObject.add("Data", metaData);
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("MetaData", new ByteArrayInputStream(returnObject.build().toString().getBytes(StandardCharsets.UTF_8)));
        return returnMap;
    }

    public static Map<String, Object> clientMetaData(final Map<String, Object> parameters, final Connection connection) {
        String name = (String) parameters.get("ClientName");
        String scopeId = (String) parameters.get(SCOPE_ID);
        String locale = (String) parameters.getOrDefault(LOCALE, "en");

        String metaData = FndmobClientMetaCache.getClientData(connection, name, scopeId, locale);

        JsonObjectBuilder returnObject = Json.createObjectBuilder();
        returnObject.add("Data", metaData);
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("ClientMetaData", new ByteArrayInputStream(returnObject.build().toString().getBytes(StandardCharsets.UTF_8)));
        return returnMap;
    }

    private static void clearClientMetaCache(String appName, String clientMetaVersion) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(ODataFormattingUtility.DATE_FORMAT_PATTERN);
            Date cacheVersion = sdf.parse(clientMetaVersion);
            FndmobClientMetaCache.clearCache(appName, cacheVersion);
        } catch (java.text.ParseException ex) {
            if (LOG.isDebugEnabled()) {
                LOG.debug(String.format("Invalid cache version : %s", MobileCommonUtilities.getDetailError(ex)));
            }
        }
    }

    private static JsonObject getPushRegistryDetails(String deviceId, String pnsHandle, Date expirationTime, String registrationResultId) {
        JsonObjectBuilder jsonBuilder = Json.createObjectBuilder();
        jsonBuilder.add("DeviceId", deviceId);

        //Mobile client expects ExpirationTime String like this way "/Date(253402300799999)/"
        String expirationTimeStr = "/Date(" + (expirationTime.getTime()) + ")/";
        jsonBuilder.add("ExpirationTime", expirationTimeStr);

        //pnsHandler is null when client unregister
        if (pnsHandle != null) {
            jsonBuilder.add("PnsHandle", pnsHandle);
        }
        //RegistrationId is null when client unregister
        if (registrationResultId != null) {
            jsonBuilder.add("RegistrationId", registrationResultId);
        }

        // client does not want the real tag, it's a (slight) security risk
        // but send something so that clients that expect a tag value do not fail.
        jsonBuilder.add("PushTag", "dummy-tag");
        return jsonBuilder.build();
    }

    public static Map<String, Object> getContainerVersion(final Map<String, Object> parameters, final Connection connection) {
        String containerName = (String) parameters.get("Name");
        return NativeExecutorAccess.getInstance().getVersion();
    }
}
