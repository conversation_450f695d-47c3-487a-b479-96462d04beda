<?xml version="1.0" encoding="UTF-8"?>
<!--
                 IFS Research & Development

  This program is protected by copyright law and by international
  conventions. All licensing, renting, lending or copying (including
  for private use), and all other use of the program, which is not
  expressively permitted by IFS Research & Development (IFS), is a
  violation of the rights of IFS. Such violations will be reported to the
  appropriate authorities.

  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
   <modelVersion>4.0.0</modelVersion>

   <artifactId>native-odp-util</artifactId>
   <packaging>jar</packaging>
   <version>25.2.0-DEV</version>
   <name>IFS Native OData Utilities</name>

   <parent>
      <artifactId>native-shared</artifactId>
      <groupId>com.ifs.fnd</groupId>
      <version>25.2.0-DEV</version>
   </parent>

   <dependencies>

      <!-- Start native-shared references -->
      <dependency>
         <groupId>com.ifs.fnd.native.shared</groupId>
         <artifactId>native-shared-lib</artifactId>
      </dependency>
      <dependency>
         <groupId>${ifs.odata.groupId}</groupId>
         <artifactId>odata-provider-common</artifactId>
         <version>${ifs.odata.version}</version>
         <exclusions>
            <exclusion>
               <groupId>com.fasterxml.jackson.core</groupId>       <!-- This dependency was added to fix CVE-2023-35116, CVE-2022-42003 and CVE-2022-42004 -->
               <artifactId>jackson-databind</artifactId>           <!-- Verify to remove this with next odata-server-core/api upgrade 4.9.0.1-ifs+ -->
            </exclusion>
            <exclusion>
               <groupId>com.fasterxml.jackson.core</groupId>       <!-- This dependency was added to fix CVE-2023-35116, CVE-2022-42003 and CVE-2022-42004 -->
               <artifactId>jackson-core</artifactId>               <!-- Verify to remove this with next odata-server-core/api upgrade 4.9.0.1-ifs+ -->
            </exclusion>
         </exclusions>
      </dependency>
      <dependency>
         <groupId>${ifs.odata.groupId}</groupId>
         <artifactId>storage-provider</artifactId>
         <version>${ifs.odata.version}</version>
      </dependency>
      <!-- End native-shared references -->

      <!-- Start third party references -->
      <dependency>
         <groupId>jakarta.platform</groupId>
         <artifactId>jakarta.jakartaee-api</artifactId>
      </dependency>
      <dependency>
         <groupId>junit</groupId>
         <artifactId>junit</artifactId>
      </dependency>
      <dependency>
         <groupId>org.apache.logging.log4j</groupId>
         <artifactId>log4j-api</artifactId>
      </dependency>
      <dependency>
         <groupId>org.apache.logging.log4j</groupId>
         <artifactId>log4j-core</artifactId>
      </dependency>
      <dependency>
         <groupId>com.oracle.ojdbc</groupId>
         <artifactId>ojdbc10</artifactId>
         <scope>provided</scope>
      </dependency>
       <!-- End third party references -->

   </dependencies>

   <build>

      <pluginManagement>
         <plugins>
            <plugin>
               <groupId>org.apache.maven.plugins</groupId>
               <artifactId>maven-compiler-plugin</artifactId>
               <version>${compiler-plugin.version}</version>
               <configuration>
                  <source>17</source>
                  <target>17</target>
               </configuration>
            </plugin>
         </plugins>
      </pluginManagement>
   </build>
   <distributionManagement>
      <site>
         <id>defaultsite</id>
         <url>http://localhost:8080</url>
      </site>
      <repository>
         <id>deploy-releases</id>
         <name>Internal Releases</name>
         <!--suppress UnresolvedMavenProperty -->
         <url>
            http://${nexus-user}:${nexus-password}@cmbpde1645.corpnet.ifsworld.com:8081/repository/releases/
         </url>
      </repository>
      <snapshotRepository>
         <id>deploy-snapshots</id>
         <name>Internal Snapshots</name>
         <!--suppress UnresolvedMavenProperty -->
         <url>
            http://${nexus-user}:${nexus-password}@cmbpde1645.corpnet.ifsworld.com:8081/repository/snapshots/
         </url>
      </snapshotRepository>
   </distributionManagement>
   <groupId>com.ifs.fnd.native.shared</groupId>
   <description>ODP util class library for native mobile</description>
</project>