<?xml version="1.0" encoding="UTF-8"?>
<project name="IFS Common Library 11" default="build" basedir=".">

  <!--
  ***************************************************************************
  *                              Properties                                 *
  ***************************************************************************
  -->

  <property name="basedir"          value="."/>
  <property name="src"              value="${basedir}/src"/>
  <property name="build"            value="${basedir}/build"/>
  <property name="dist"             value="${basedir}/../../lib"/>
  <property name="doc"              value="${basedir}/doc"/>

  <!--
  ***************************************************************************
  *                              Targets                                    *
  ***************************************************************************
  -->

  <target name="clean-build" depends="clean, build" description="Clean build"/>


  <target name="clean" description="Clean everything">
    <!-- Delete the ${build} and ${dist} directory trees -->
    <delete dir="${build}"/>
    <delete dir="${doc}"/>
  </target>


  <target name="-init">
    <tstamp/>
    <mkdir dir="${build}"/>
  </target>


  <target name="compile" depends="-init" description="Compile the source code">
    <echo message="Compiling IFS Common Library"/>
    <javac srcdir="${src}"
           debug="on"
           debuglevel="lines,vars,source"
           optimize="yes"
           deprecation="on"
           source="17"
           target="17"
           destdir="${build}"
           includeantruntime="false">
      <compilerarg value="-Xlint:unchecked"/>
    </javac>
  </target>


  <target name="build" depends="compile" description="Build the JAR file">
    <echo message="Building ifs-fnd-common.jar ..."/>
    <jar destfile="${dist}/ifs-fnd-common.jar" basedir="${build}"/>
  </target>


  <target name="doc" description="Generate API documentation">
    <delete dir="${doc}"/>
    <mkdir dir="${doc}"/>
    <javadoc
       packagenames="ifs.fnd.*"
       classpath="${build}"
       sourcepath="${src}"
       destdir="${doc}"
       version="true"
       use="true"
       source="17"
       windowtitle="IFS Common Utilities API documentation"/>
  </target>


</project>
