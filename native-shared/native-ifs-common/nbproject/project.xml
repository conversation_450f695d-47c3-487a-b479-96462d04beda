<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://www.netbeans.org/ns/project/1">
    <type>org.netbeans.modules.ant.freeform</type>
    <configuration>
        <general-data xmlns="http://www.netbeans.org/ns/freeform-project/1">
            <!-- Do not use Project Properties customizer when editing this file manually. 
 To prevent the customizer from showing, create nbproject/project.properties file and enter 
auxiliary.show.customizer=false 
property there. Adding 
auxiliary.show.customizer.message=<message>
 will show your customized message when someone attempts to open the customizer.  -->
            <name>Native IFS Common Library</name>
            <properties/>
            <folders>
                <source-folder>
                    <label>src</label>
                    <type>java</type>
                    <location>src</location>
                </source-folder>
            </folders>
            <ide-actions>
                <action name="build">
                    <script>nbproject/nbjdk.xml</script>
                    <target>build</target>
                </action>
                <action name="clean">
                    <script>nbproject/nbjdk.xml</script>
                    <target>clean</target>
                </action>
                <action name="javadoc">
                    <script>nbproject/nbjdk.xml</script>
                    <target>doc</target>
                </action>
                <action name="rebuild">
                    <script>nbproject/nbjdk.xml</script>
                    <target>clean</target>
                    <target>build</target>
                </action>
            </ide-actions>
            <view>
                <items>
                    <source-folder style="packages">
                        <label>src</label>
                        <location>src</location>
                    </source-folder>
                    <source-file>
                        <location>build.xml</location>
                    </source-file>
                </items>
                <context-menu>
                    <ide-action name="build"/>
                    <ide-action name="rebuild"/>
                    <ide-action name="clean"/>
                    <ide-action name="javadoc"/>
                </context-menu>
            </view>
            <subprojects/>
        </general-data>
        <java-data xmlns="http://www.netbeans.org/ns/freeform-project-java/4">
            <compilation-unit>
                <package-root>src</package-root>
                <source-level>17</source-level>
            </compilation-unit>
        </java-data>
    </configuration>
</project>
