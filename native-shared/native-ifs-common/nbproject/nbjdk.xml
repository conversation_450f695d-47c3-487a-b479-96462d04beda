<?xml version="1.0" encoding="UTF-8"?>
<project basedir=".." name="Native_IFS_Common_Library">
    <property file="nbproject/nbjdk.properties"/>
    <property location="${netbeans.user}/build.properties" name="user.properties.file"/>
    <property file="${user.properties.file}"/>
    <import file="jdk.xml"/>
    <target depends="-jdk-init" name="build">
        <ant inheritall="false" target="build"/>
    </target>
    <target depends="-jdk-init" name="clean">
        <ant inheritall="false" target="clean"/>
    </target>
    <target depends="-jdk-init" name="doc">
        <ant inheritall="false" target="doc"/>
    </target>
</project>
