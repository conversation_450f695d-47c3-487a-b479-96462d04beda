/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.buffer;

import ifs.fnd.service.Util;
import java.io.IOException;
import java.io.UnsupportedEncodingException;

/**
 * <B>Framework internal class:</B> Extension of FndTokenReader that reads serialized stream from an array of bytes.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public final class ByteArrayTokenReader
{
   // the byte buffer with complete serialized stream
   private byte[] buf;

   // current (next to read) position in the byte buffer
   private int pos;

   /**
    * Construct a token reader that will read bytes from the specified byte array.
    * @param buf a buffer with complete serialized stream
    */
   public ByteArrayTokenReader(byte[] buf) {
      this.buf = buf;
      pos = 0;
   }

   /**
    * Force a delimiter from the input stream.
    * @return the consumed delimiter character
    * @throws ParseException if no delimiter has been found at the current position in the input stream
    */
   public char getDelimiter() throws BufferFormatException, IOException {
      if(pos >= buf.length)
         throw new BufferFormatException.EndOfStreamException("Expecting delimiter but found end-of-stream");

      byte ch = buf[pos++];

      if(isDelimiter(ch))
         return (char)ch;
      else
         throw new BufferFormatException("Expecting delimiter but found character code " + ((int)ch));
   }

   /**
    * Force a next delimiter from the input stream and throws BufferFormatException
    * if it does not match the specified delimiter.
    */
   public void matchDelimiter(char ch) throws BufferFormatException, IOException {
      char c = getDelimiter();
      if (c != ch)
         throw new BufferFormatException("Expecting delimiter code " + (int) ch + " but found code " + (int) c);
   }

   /**
    * Force next token from the input stream.
    * @return the next String token from the stream
    * @throws BufferFormatException if no delimiter has been found or the consumed bytes could not have been converted to a String
    */
   public String getToken() throws BufferFormatException, IOException {
      for(int i = pos; i < buf.length; i++) {
         if(isDelimiter(buf[i])) {
            try {
               String token = new String(buf, pos, i - pos, "UTF8");
               pos = i;
               return token;
            }
            catch (UnsupportedEncodingException e) {
               throw new BufferFormatException(e.toString());// NOPMD
            }
         }
      }
      throw new BufferFormatException.EndOfStreamException("Expecting token but found end-of-stream");
   }

   public String getPrefixedToken() throws BufferFormatException {
      if (pos >= buf.length)
         throw new BufferFormatException.EndOfStreamException("Expecting binary token but found end-of-stream");
      
      // Since we added a new token types for length-prefixed strings, we do not
      // need to check if the client is capable of handling them. Client will
      // only send length-prefixed tokens if it is capable of handling them.
      try {
         int tokenLen = getStringLengthPrefix();

         if (buf.length - pos < tokenLen)
            throw new BufferFormatException.EndOfStreamException("Expecting length prefixed token data but found end-of-stream");

         String token = new String(buf, pos, tokenLen, "UTF-8");
         pos += tokenLen;
         return token;
      } catch (UnsupportedEncodingException ex) {
         throw new BufferFormatException(ex);
      }
   }
   
   /**
    * Converts length prefix bytes from the buffer into a length value. This
    * method deals with new variable length length prefixes.
    */
   private int getStringLengthPrefix() {
      /* not required to check if the input bytes has enough room for a length
         prefix. this check is performed while reading the prefix. */
      int prefixVal = BufferUtil.readLengthPrefix(buf, pos);
      pos += BufferUtil.getPrefixSize(prefixVal);
      return prefixVal;
   }
   
   /**
    * Force next binary token from the input stream.
    * The token may be BASE64 encoded or not (if BINARY_INDICATOR is the first character of the token).
    * A serialized non-encoded binary token consists of the data length (4 bytes, high byte first) followed by the data bytes.
    * @return binary token data consumed from the input stream encoded as Base64 characters
    * @throws BufferFormatException if the input stream does not contain a properly serialized binary token
    * @throws IOException           if en I/O error occurs
    */
   public String getBinaryToken() throws BufferFormatException, IOException {
      if(pos >= buf.length)
         throw new BufferFormatException.EndOfStreamException("Expecting binary token but found end-of-stream");

      if(buf[pos] == BinaryBufferFormatter.BINARY_INDICATOR) {
         pos++;

         if(buf.length - pos < 4)
            throw new BufferFormatException.EndOfStreamException("Expecting binary token length but found end-of-stream");

         int tokenLen = toInt(buf, pos);
         pos += 4;

         if(buf.length - pos < tokenLen)
            throw new BufferFormatException.EndOfStreamException("Expecting binary token data but found end-of-stream");

         byte[] token = new byte[tokenLen];
         System.arraycopy(buf, pos, token, 0, tokenLen);
         pos += tokenLen;
         try {
            return Util.toBase64Text(token);
         }
         catch(IOException e) {
            throw new BufferFormatException(e.toString());// NOPMD
         }
      }
      else {
         return getToken();
      }
   }

   /**
    * Match the specified byte against delimiters defined in FndSerializeConstants.
    */
   @SuppressWarnings("PMD.MissingBreakInSwitch")
   private boolean isDelimiter(int ch) {
      switch(ch) {
         case StandardBufferFormatter.BEGIN_BUFFER_MARKER :
         case StandardBufferFormatter.END_BUFFER_MARKER :
         case StandardBufferFormatter.HEAD_MARKER :
         case StandardBufferFormatter.NAME_MARKER :
         case StandardBufferFormatter.TYPE_MARKER :
         case StandardBufferFormatter.STATUS_MARKER :
         case StandardBufferFormatter.VALUE_MARKER :
         case StandardBufferFormatter.NULL_VALUE_MARKER :
         case StandardBufferFormatter.NO_VALUE_MARKER :
         case StandardBufferFormatter.IDENTITY_MARKER :
         case StandardBufferFormatter.CHANGED_VALUE_MARKER :
         case StandardBufferFormatter.UNCHANGED_VALUE_MARKER :
            return true;
      }
      return false;
   }

   /**
    * Convert four bytes (high byte first) to an int.
    */
   private static int toInt(byte[] b, int index) {
      return ((b[index + 0] & 0xFF) << 24) +
             ((b[index + 1] & 0xFF) << 16) +
             ((b[index + 2] & 0xFF) <<  8) +
             ((b[index + 3] & 0xFF));
   }

   /**
    * Gets the number of un-consumed bytes from the internal byte buffer.
    * @return number of bytes
    */
   public int getUnconsumedByteCount() {
      return pos - buf.length;
   }

}
