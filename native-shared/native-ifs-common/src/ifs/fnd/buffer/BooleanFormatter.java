/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.buffer;

import ifs.fnd.service.FndException;
import ifs.fnd.util.Str;

/**
 * An instance of this class can format and parse Boolean values.
 * The only supported Type ID is {@link DataFormatter#BOOLEAN}.
 */
public final class BooleanFormatter extends DataFormatter implements Cloneable {

   /**
    * Constructs a new instance of BooleanFormatter.
    * @param typeId must be {@link DataFormatter#BOOLEAN}
    * @param mask format mask
    */
   public BooleanFormatter(int typeId, String mask) throws FndException {// NOPMD
      init(typeId);
   }

   /**
    * Constructs a new instance of BooleanFormatter.
    * @param params this parameter is ignored
    */
   public BooleanFormatter(Buffer params) throws FndException {// NOPMD
      this();
   }

   /**
    * Constructs a new instance of BooleanFormatter.
    */
   public BooleanFormatter() throws FndException {
      init(DataFormatter.BOOLEAN);
   }

   private void init(int typeId) throws FndException {
      switch (typeId) {
         case DataFormatter.BOOLEAN :
            break;

         default :
            throw new FndException("FNDBOOLTYP: Invalid Type Id '&1' for BooleanFormatter", DataFormatter.getTypeName(typeId));
      }
   }

   @Override
   public int getTypeId() {
      return BOOLEAN;
   }

   @Override
   public String format(Object value) {
      return value == null ? null : ((Boolean) value ? "TRUE" : "FALSE");
   }

   @Override
   public Object parse(String text) throws FndException {
      if (Str.isEmpty(text))
         return null;
      else if ("TRUE".equals(text))
         return Boolean.TRUE;
      else if ("FALSE".equals(text))
         return Boolean.FALSE;
      else
         throw new FndException("FNDPARSEBOOL: Cannot parse string '&1' into Boolean value.", text);
   }

   //==========================================================================
   //  Cloning
   //==========================================================================

   private BooleanFormatter(boolean dummy) {// NOPMD
   }

   /**
    * Clone this BooleanFormatter.
    */
   @Override
   public Object clone() {
      return new BooleanFormatter(true);
   }
}
