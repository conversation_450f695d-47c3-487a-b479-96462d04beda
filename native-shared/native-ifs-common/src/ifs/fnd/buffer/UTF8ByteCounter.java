package ifs.fnd.buffer;

/**
 * <code>UTF8ByteCounter</code> is used to count the number of UTF-8 bytes in a
 * given string or a character stream. Due to the differences in byte
 * representation when reading invalid surrogate pairs from an input stream and
 * when converting a string to corresponding UTF-8 bytes using
 * <code>String.getBytes("utf-8")</code>, an object of this class must be
 * initialized by providing <code>UTF8ByteCounter.Type.INPUT_STREAM</code> or
 * <code>UTF8ByteCounter.Type.STRING_TO_UTF8</code> accordingly.
 * 
 * <AUTHOR>
 */
public class UTF8ByteCounter {

   private int count = 0;
   private boolean seenHighSurrogate;
   int invalidSurrogateBytes;

   /**
    * Represents the type of a UTF8ByteCounter object.
    */
   public enum Type {
      INPUT_STREAM, STRING_TO_UTF8
   }

   /**
    * Initialized a <code>UTF8ByteCounter</code> with the specified type.
    *
    * @param type type of the UTF8ByteCounter
    */
   public UTF8ByteCounter(Type type) {
      if (type == Type.STRING_TO_UTF8) {
         this.invalidSurrogateBytes = 1;
      } else {
         this.invalidSurrogateBytes = 3;
      }
   }

   /**
    * Counts the number of UTF-8 encoded bytes for a given string using the
    * specified counting type.
    *
    * @param s String to be counted
    * @param type Counting method
    * @return the number of UTF-8 bytes in <code>s</code>.
    */
   public static int count(String s, Type type) {
      UTF8ByteCounter counter = new UTF8ByteCounter(type);
      for (int i = 0; i < s.length(); i++) {
         char c = s.charAt(i);
         counter.consume(c);
      }
      return counter.getCount();
   }


   /**
    * Counts the number of UTF-8 bytes using counting methond
    * <code>UTF8ByteCounter.Type.STRING_TO_UTF8</code>. The valued returned is
    * equivalent to <code>s.getBytes("utf-8")</code>.
    *
    * @param s String to be counted
    * @return the number of UTF-8 bytes in <code>s</code>
    */
   public static int count(String s) {
      return count(s, Type.STRING_TO_UTF8);
   }

   private static int countUTF8Bytes(char c) {
      if (c <= 0x7F) {
         return 1;
      } else if (c > 0x7FF) {
         return 3;
      } else {
         return 2;
      }
   }

   /**
    * Returns the number of UTF-8 bytes in the characters consumed. If the last
    * character consumed is a high surrogate, it is assumed that it is an
    * invalid surrogate (not in a surrogate pair). Therefore, this method should
    * be used carefully when counting the number of UTF-8 bytes in a character
    * stream, one character at a time.
    *
    * @return the number of UTF-8 bytes counted so far.
    */
   public int getCount() {
      if (this.seenHighSurrogate) {
         return count + invalidSurrogateBytes;
      } else {
         return count;
      }
   }

   /**
    * Consumes a character. This method count the UTF-8 bytes considering the
    * characters already consumed.
    *
    * @param c character to be consumed.
    */
   public void consume(char c) {
      if (this.seenHighSurrogate) {
         if (Character.isLowSurrogate(c)) {
            //good surrogate pair
            this.count += 4;
         } else {
            //bad high surrogate seen
            //count those bytes and ignore the encoding error
            count += invalidSurrogateBytes;

            //if this char is high surrogate don't count now, it'll be
            //counted in the next itr.
            if (!Character.isHighSurrogate(c)) {
               count += countUTF8Bytes(c);
            }
         }
      } else if (Character.isLowSurrogate(c)) {
         count += invalidSurrogateBytes;
      } else if (!Character.isHighSurrogate(c)) {
         //if this char is high surrogate don't count now, it'll be
         //counted in the next itr.
         count += countUTF8Bytes(c);
      }

      this.seenHighSurrogate = Character.isHighSurrogate(c);
   }
}
