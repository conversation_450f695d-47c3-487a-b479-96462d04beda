/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.buffer;

import java.io.Serializable;

/**
 * A class that represents an item contained in a Buffer.
 * An item has four parts: name, type, status and value.
 * It can be a simple item (having one simple value like String,
 * Integer or Boolean) or it can be a compound one (having a value
 * that contains a Buffer).
 */
public class Item implements Serializable, Cloneable {

   private String name;
   private String type;
   private String status;
   private String identity;
   private Object value;
   private boolean valueSet = false, unchangedValue = false;

   /**
    * Create anonymous (without name) Item.
    */
   public Item() {
   }

   /**
    * Create a named Item.
    * @param name item name
    */
   public Item(String name) {
      this();
      this.name = name;
   }

   /**
    * Create a named Item with the specified value.
    * @param name item name
    * @param value item value
    */
   public Item(String name, Object value) {
      this();
      this.name = name;
      this.value = value;
      valueSet = true;
   }

   /**
    * Create a named Item with the specified value.
    * @param name item name
    * @param value item value
    */
   public Item(String name, int value) {
      this();
      this.name = name;
      this.value = value;
      valueSet = true;
   }

   /**
    * Create a named Item using the specified arguments.
    */
   public Item(String name, String type, String status, Object value) {
      this();
      this.name = name;
      this.type = type;
      this.status = status;
      this.value = value;
      valueSet = true;
   }

   /**
    * Internal "copy" constructor.
    */
   protected Item(String name, String type, String status, String identity,
                  Object value, boolean valueSet, boolean unchangedValue) {
      this();
      this.name = name;
      this.type = type;
      this.status = status;
      this.identity = identity;
      this.value = value;
      this.valueSet = valueSet;
      this.unchangedValue = unchangedValue;
   }

   /**
    * Set the identity of this Item.
    */
   public void setIdentity(String id) {
      this.identity = id;
   }

   /**
    * Get the identity of this Item.
    */
   public String getIdentity() {
      return identity;
   }

   /**
    * Gets the unchanged value flag. This is <b>only</b> set if found in a parsed
    * buffer or explicitly set by a call to
    * {@link #setUnchangedValueFlag(boolean) setUnchangedValueFlag()} method.
    * The flag is also <b>not</b> serialized to buffer with
    * {@link ifs.fnd.buffer.StandardBufferFormatter StandardBufferFormatter}
    * class.
    */
   public boolean isValueUnchanged() {
      return unchangedValue;
   }

   /**
    * Sets the unchanged value flag.
    */
   public void setUnchangedValueFlag(boolean value) {
      this.unchangedValue = value;
   }

   /**
    * Set the name of this Item.
    */
   public void setName(String name) {
      this.name = name;
   }

   /**
    * Set the type of this Item.
    */
   public void setType(String type) {
      this.type = type;
   }

   /**
    * Set the status of this Item.
    */
   public void setStatus(String status) {
      this.status = status;
   }

   /**
    * Return the name of this Item.
    */
   public String getName() {
      return name;
   }

   /**
    * Return the type of this Item.
    */
   public String getType() {
      return type;
   }

   /**
    * Return the status of this Item.
    */
   public String getStatus() {
      return status;
   }

   /**
    * Return true if this Item has a value which is a nested Buffer.
    */
   public boolean isCompound() {
      return value != null && value instanceof Buffer;
   }

   /**
    * Return true if a value has been set for this item. It does not imply that
    * the value is non-null (the value may have been explicitly set to null).
    */
   public boolean isValueSet() {
      return valueSet;
   }

   /**
    * Set the value (String or nested Buffer) of this Item.
    */
   public void setValue(Object value) {
      this.value = value;
      valueSet = true;
   }

   /**
    * Set the int value of this Item.
    */
   public void setValue(int value) {
      this.value = value;
      valueSet = true;
   }

   /**
    * Return the value (String or nested Buffer) of this Item.
    */
   public Object getValue() {
      return value;
   }

   /**
    * Return the String value of this Item.
    */
   public String getString() {
      if (value instanceof Integer || value instanceof Double)
         return value.toString();
      else
         return (String) value;
   }

   /**
    * Return the integer value of this Item.
    */
   public int getInt() {
      if (value instanceof Integer)
         return ((Integer) value);
      else
         return Integer.parseInt((String) value);
   }

   /**
    * Return the double value of this Item.
    */
   public double getDouble() {
      if (value instanceof Double)
         return ((Double) value);
      else
         return Double.parseDouble((String) value);
   }

   /**
    * Return the Buffer value of this Item.
    */
   public Buffer getBuffer() {
      return (Buffer) value;
   }

   /**
    * Clones this Item.
    */
   @Override
   public Object clone() {
      return new Item(name, type, status, identity,
                      value instanceof Buffer ? ((Buffer) value).clone() : value,
                      valueSet, unchangedValue);
   }

   /**
    * Compute the hash code for this Item.
    */
   @Override
   public int hashCode() {
      int hash = 0;

      if (name != null)
         hash ^= name.hashCode();
      if (type != null)
         hash ^= type.hashCode();
      if (status != null)
         hash ^= status.hashCode();
      if (identity != null)
         hash ^= identity.hashCode();
      if (value != null)
         hash ^= value.hashCode();

      return hash;
   }

   /**
    * Returns a string describing this item.
    * The method is used for debugging purposes.
    */
   @Override
   public String toString() {
      return (name == null ? "" : "$" + name)
         + (type == null ? "" : ":" + type)
         + (status == null ? "" : "/" + status)
         + (identity == null ? "" : "/" + identity)
         + (value == null ? "*" : "=" + (isCompound() ? "" : value));
   }

   /**
    * Return true if a specified Object is an Item and has exactly the same
    * contents as this Item.
    */
   @Override
   public boolean equals(Object obj) {
      if (obj != null && obj instanceof Item) {
         Item x = (Item) obj;

         String xname = x.getName();
         if ((name == null && xname != null) || (name != null && !name.equals(xname)))
            return false;

         String xtype = x.getType();
         if ((type == null && xtype != null) || (type != null && !type.equals(xtype)))
            return false;

         String xstatus = x.getStatus();
         if ((status == null && xstatus != null) || (status != null && !status.equals(xstatus)))
            return false;

         String xidentity = x.getIdentity();
         if ((identity == null && xidentity != null) ||(identity != null && !identity.equals(xidentity)))
            return false;

         Object xvalue = x.getValue();
         if ((value == null && xvalue != null) || (value != null && !value.equals(xvalue)))
            return false;

         return true;
      }
      return false;
   }

   /**
   boolean equals2(Object obj) {
      if (obj != null && obj instanceof Item) {
         Item x = (Item) obj;

         return equalStrings(name, x.name) && equalStrings(type, x.type) &&
                equalStrings(status, x.status) && equalStrings(identity, x.identity) &&
                equalObjects(value, x.value);
      }
      return false;
   }
   **/

   /**
   private boolean equalStrings(String a, String b) {
      if (a != null)
         return a.equals(b);
      else
         return b==null; // a is null
   }
   **/

   /**
   private boolean equalObjects(Object a, Object b) {
      if (a != null)
         return a.equals(b);
      else
         return b==null; // a is null
   }
    **/
}
