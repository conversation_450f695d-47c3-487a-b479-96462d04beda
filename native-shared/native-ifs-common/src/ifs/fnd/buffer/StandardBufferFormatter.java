/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.buffer;

import ifs.fnd.service.TraceEvent;
import ifs.fnd.service.TraceEventType;
import ifs.fnd.util.IoUtil;
import ifs.fnd.util.Str;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.Reader;
import java.io.StringReader;

/**
 * <B>Framework internal class:</B> Standard implementation of BufferFormatter interface.
 * This class supports the following string representation of a Buffer:
 *
 * <pre>
 *    Buffer ::= ( [!Header] {Item} )
 *    Item   ::= [$Name] [:Type] [/Status] =Value | * | Buffer
 *    Value  ::= String | Integer | Double | Boolean
 * </pre>
 * where "::=", "[", "]", "{", "}" are meta-symbols
 *
 * This formatter uses the following delimiters
 * (reserved characters):
 * <pre>
 *    (   begin-buffer-marker
 *    )   end-buffer-marker
 *    !   head-marker
 *    $   name-marker
 *    :   type-marker
 *    /   status-marker
 *    =   value-marker
 *    *   null-value-marker
 * </pre>
 *
 * To change the delimiter set, one must recompile this file.
 *
 * Note that this formatter does not preserve the type information
 * of simple items. So for example Integer 12 will be converted to String "12"
 * after format() and parse().
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class StandardBufferFormatter implements BufferFormatter {

   private static TraceEventType parseEventType = new TraceEventType("StandardBufferFormatter.parse");
   private static TraceEventType formatEventType = new TraceEventType("StandardBufferFormatter.format");

   // Note! Codes 28..31 are used by PLSQL, for example in attribute strings.
   static final char BEGIN_BUFFER_MARKER    = (char) 27; //31; //'(';
   static final char END_BUFFER_MARKER      = (char) 26; //30; //')';
   static final char HEAD_MARKER            = (char) 25; //29; //'!';
   static final char NAME_MARKER            = (char) 24; //28; //'$';
   static final char TYPE_MARKER            = (char) 23; //27; //':';
   static final char STATUS_MARKER          = (char) 22; //26; //'/';
   static final char VALUE_MARKER           = (char) 21; //25; //'=';
   static final char NULL_VALUE_MARKER      = (char) 20; //24; //'*';
   static final char NO_VALUE_MARKER        = (char) 19;
   static final char CHANGED_VALUE_MARKER   = (char) 16;
   static final char UNCHANGED_VALUE_MARKER = (char)15;
   static final char IDENTITY_MARKER        = (char)14;

   private boolean lengthPrefixSupported = false;
   
   public StandardBufferFormatter() {
   }

   /**
    * Return a String representation of a specified Buffer
    */
   @Override
   public String format(Buffer buffer) {
      if (buffer == null)
         return null;
      TraceEvent formatEvent = formatEventType.begin();
      StandardTokenWriter stream = new StandardTokenWriter();
      try {
         format(buffer, stream);
      }
      catch (IOException impossible) {
         IoUtil.stderrln("Failed formatting buffer: " + impossible.getMessage());
      }
      String result = stream.getBuffer();
      formatEvent.end();
      return result;
   }

   /**
    * Return UTF-8 encoded representation of a specified Buffer.
    */
   public byte[] formatBytes(Buffer buffer) throws IOException {
      if (buffer == null)
         return null;
      TraceEvent formatEvent = formatEventType.begin();
      ByteArrayOutputStream out = new ByteArrayOutputStream();
      BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(out, "UTF-8"));
      format(buffer, new StandardTokenWriter(writer));
      writer.close();
      byte[] result = out.toByteArray();
      formatEvent.end();
      return result;
   }

   /**
    * Write the String representation of a specified Buffer to an OutputStream
    */
   @Override
   public void format(Buffer buffer, OutputStream output) throws IOException {
      TraceEvent formatEvent = formatEventType.begin();
      StandardTokenWriter stream = new StandardTokenWriter(output);
      format(buffer, stream);
      output.flush();
      formatEvent.end();
      //System.out.println(stream.getCount()+" characters written");
   }

   /**
    * Write the String representation of a specified Buffer to a stream
    */
   private void format(Buffer buffer, StandardTokenWriter stream) throws IOException {
      String head, name, type, status, identity;

      if (buffer == null)
         return;

      stream.write(BEGIN_BUFFER_MARKER);

      if ((head = buffer.getHeader()) != null) {
         stream.write(HEAD_MARKER);
         stream.write(head);
      }

      BufferIterator iter = buffer.iterator();

      while (iter.hasNext()) {
         Item item = iter.next();

         if ((name = item.getName()) != null) {
            stream.write(NAME_MARKER);
            stream.write(name);
         }

         if ((type = item.getType()) != null) {
            stream.write(TYPE_MARKER);
            
            if (isLengthPrefixSupported() && "T".equals(type)) {
               stream.write(BufferUtil.LENGTH_PREFIXED_TEXT);
            } else if (isLengthPrefixSupported() && "A".equals(type)) {
               stream.write(BufferUtil.LENGTH_PREFIXED_ALPHA);
            } else {
               stream.write(type);
            }            
         }

         if ((status = item.getStatus()) != null) {
            stream.write(STATUS_MARKER);
            stream.write(status);
         }

         if ((identity = item.getIdentity()) != null) {
            stream.write(IDENTITY_MARKER);
            stream.write(identity);
         }

         if (item.isValueSet()) {
            Object value = item.getValue();
            if (value == null)
               stream.write(NULL_VALUE_MARKER);
            else if (value instanceof Buffer)
               format((Buffer) value, stream);
            else {
               stream.write(VALUE_MARKER);
               if (BufferUtil.LENGTH_PREFIXED_TEXT.equals(type) 
                   || BufferUtil.LENGTH_PREFIXED_ALPHA.equals(type)) {
                  stream.writePrefixedToken(value.toString());
               } else if (isLengthPrefixSupported()
                          && ("T".equals(type)
                           || "A".equals(type))) {
                  stream.writePrefixedToken(value.toString());
               } else {
                  stream.write(value.toString()); // empty value OK
               }
            }
         }
         else
            stream.write(NO_VALUE_MARKER);
      }

      stream.write(END_BUFFER_MARKER);
   }

   /**
    * Transform a specified String into a Buffer object
    * @param data String object which is compatible to create a Buffer instance
    * @param buffer Buffer instance to get the output result
    * @see #parse(InputStream,Buffer)
    */
   @Override
   public void parse(String data, Buffer buffer) throws BufferFormatException, IOException {
      if (Str.isEmpty(data))
         return;

      TraceEvent parseEvent = parseEventType.begin();

      Reader reader = new BufferedReader(new StringReader(data));
      StandardTokenReader input = new StandardTokenReader(reader);

      input.matchDelimiter(BEGIN_BUFFER_MARKER);
      parse(input, buffer);

      int rest = reader.read();

      if (rest >= 0) {
         //int from = data.length()-rest;
         //rest = rest>40 ? 40 : rest;
         //data = data.substring(from,from+rest);
         throw new BufferFormatException("Unconsumed data!"); //: '"+data+"'...");
      }
      parseEvent.end();
   }

   /**
    * Consume and return one Buffer object from the specified stream.
    * @param input InputStreem instance which is compatible to create a Buffer instance
    * @param buffer Buffer instance to get the output result
    * @see #parse(String,Buffer)
    */
   @Override
   public void parse(InputStream input, Buffer buffer) throws BufferFormatException, IOException {
      TraceEvent parseEvent = parseEventType.begin();
      //StandardTokenReader stream = new StandardTokenReader(input);
      StandardTokenReader stream = newTokenReader(new InputStreamReader(input, "UTF-8"));

      stream.matchDelimiter(BEGIN_BUFFER_MARKER);
      parse(stream, buffer);
      parseEvent.end();
   }

   /**
    * Returns a new instance of StandardTokenReader
    */
   protected StandardTokenReader newTokenReader(Reader reader) {
      return new StandardTokenReader(reader);
   }

   /**
    * Deserialize one Buffer object from the specified stream into the
    * specified Buffer.
    * "(" must have been consumed. This method will consume the matching ")".
    */
   protected void parse(StandardTokenReader stream, Buffer into) throws BufferFormatException, IOException {
      Item item = null;

      char ch = stream.getDelimiter();
      if (ch == HEAD_MARKER) // header
         {
         into.setHeader(stream.getToken());
         ch = stream.getDelimiter();
      }

      while (true) {
         if (ch == END_BUFFER_MARKER) // end of buffer
            {
            into.freeze();
            return;
         }

         if (ch == NAME_MARKER) // name
            {
            item = into.newItem();
            item.setName(stream.getToken());
            ch = stream.getDelimiter();
         }
         else
            item = into.newItem();

         if (ch == TYPE_MARKER) // type
            {
            item.setType(stream.getToken());
            ch = stream.getDelimiter();
         }

         if (ch == STATUS_MARKER) // status
            {
            item.setStatus(stream.getToken());
            ch = stream.getDelimiter();
         }

         if (ch == IDENTITY_MARKER) { // identity
            item.setIdentity(stream.getToken());
            ch = stream.getDelimiter();
         }

         if (ch == CHANGED_VALUE_MARKER) // ignore changedValueMarker
            ch = stream.getDelimiter();

         switch (ch) {
            case NO_VALUE_MARKER : // no value
               into.addItem(item);
               break;

            case NULL_VALUE_MARKER : // null value
               item.setValue(null);
               into.addItem(item);
               break;

            case VALUE_MARKER :
               if (BufferUtil.LENGTH_PREFIXED_TEXT.equals(item.getType()) 
                   || BufferUtil.LENGTH_PREFIXED_ALPHA.equals(item.getType())) {
                  String token = stream.getPrefixedToken();
                  item.setValue(token);
               } else {
                  item.setValue(stream.getToken()); // not null (maybe empty) value
               }
               into.addItem(item);
               break;

            case UNCHANGED_VALUE_MARKER :
               item.setUnchangedValueFlag(true);
               into.addItem(item);
               break;

            case BEGIN_BUFFER_MARKER : // buffer
               Buffer sub = into.newInstance();
               item.setValue(sub);
               into.addItem(item);
               parse(stream, sub);
               break;

            default :
               throw new BufferFormatException("Expecting '*', '=' or '(' but found character code " + (int) ch);
         }

         ch = stream.getDelimiter();
      }
   }
   
   public void setLengthPrefixSupported(boolean lengthPrefixSupported) {
      this.lengthPrefixSupported = lengthPrefixSupported;
   }

   public boolean isLengthPrefixSupported() {
      return lengthPrefixSupported;
   }
}

