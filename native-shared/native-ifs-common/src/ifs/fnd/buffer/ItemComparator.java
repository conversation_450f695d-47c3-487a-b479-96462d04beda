/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.buffer;

/**
 * An instance of this interface can compare two Items.
 */
public interface ItemComparator {

   /**
    * Compare two Items for order.
    * @param i1 first item
    * @param i2 second item
    * @return a negative integer, zero, or a positive integer as the first
    *          argument is less than, equal to, or greater than the second.
    * @throws Exception if the operation fails
    */
   int compare(Item i1, Item i2) throws Exception;
}
