/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.buffer;

import java.io.*;

/**
 * <B>Framework internal class:</B> This class passes characters to a byte or character stream.
 * The type of the stream depends on the used constructor.
 * It performs no byte-to-character conversion if the character output is used.
 * If the characters are collected in a string buffer, then the buffer
 * contents can be fetched by calling the getBuffer() method.
 * The number of characters written is returned by getCount() function.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class StandardTokenWriter {

   private Writer writer;
   private AutoString auto;
   private int count;

   /**
    * Construct an instance that will convert characters to bytes
    * and write them to a specified byte stream.
    */
   public StandardTokenWriter(OutputStream output) {
      this.writer = new BufferedWriter(new OutputStreamWriter(output));
   }

   /**
    * Construct an instance that will write characters to a specified
    * character stream.
    */
   public StandardTokenWriter(Writer output) {
      this.writer = output;
   }

   /**
    * Construct an instance that will write characters to a string buffer
    * with the specified initial size.
    */
   public StandardTokenWriter(int initialSize) {
      this.auto = new AutoString(initialSize);
   }

   /**
    * Construct an instance that will write characters to a string buffer
    * with the default initial size.
    */
   public StandardTokenWriter() {
      this(1024);
   }

   /**
    * Write a String to the output stream.
    */
   public void write(String token) throws IOException {
      if (writer != null)
         writer.write(token);
      else
         auto.append(token);
      count += token.length();
   }

   /**
    * Write a character to the output stream.
    */
   public void write(char delimiter) throws IOException {
      if (writer != null)
         writer.write(delimiter);
      else
         auto.append(delimiter);
      count++;
   }

   /**
    * Return the contents of the string buffer. Throw a RuntimeException
    * if the string buffer is not used by this instance.
    */
   public String getBuffer() {
      if (auto == null)
         throw new RuntimeException("The string buffer is not used by this instance");
      return auto.toString();
   }

   /**
    * Return the number of characters written to the underlying stream.
    */
   public int getCount() {
      return count;
   }
   
   public void writePrefixedToken(String value) throws IOException {
      int len = UTF8ByteCounter.count(value); // == value.getBytes("UTF-8").length
      
      if (writer != null) {
         count += BufferUtil.writeLengthPrefix(writer, len);
      } else {
         count += BufferUtil.writeLengthPrefix(auto, len);
      }
      write(value);
   }
}