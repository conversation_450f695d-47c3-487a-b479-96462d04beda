/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.buffer;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * <B>Framework internal class:</B> Interface for serialization/deserialization of Buffers.
 * A BufferFormatter can transform a Buffer object into a byte or
 * character stream and vice versa using its private syntax.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public interface BufferFormatter {
   /**
    * Write the String representation of a specified Buffer to a byte stream.
    */
   void format(Buffer buffer, OutputStream output) throws IOException;

   /**
    * Return the String representation of a specified Buffer.
    */
   String format(Buffer buffer);

   /**
    * Consume and return one Buffer object from the specified byte stream.
    */
   void parse(InputStream input, Buffer buffer) throws BufferFormatException, IOException;

   /**
    * Trnsform the specified String into a Buffer object.
    */
   void parse(String data, Buffer buffer) throws BufferFormatException, IOException;
}
