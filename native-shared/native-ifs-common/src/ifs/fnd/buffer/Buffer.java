/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.buffer;

import java.io.Serializable;

/**
 *  A Buffer is a collection of items.
 *  It has an optional header that describes its type and/or contents.
 *  Items can be referenced by name or by position.
 *<p>
 *  An <a href=Item.html>Item</a> may have a compound value that
 *  contains another Buffer. Buffers can be nested to arbitrary level.
 *  Leafs of a Buffer tree structure are always simple values like
 *  String or Integer, so Buffers can be easily serialized to a byte
 *  or character stream.
 *<p>
 *  More formally a buffer can represent any data structure
 *  that meets the following BNF definition:
 *<pre>
 *     Buffer ::= [Header] {Item}
 *     Item   ::= [Name] [Type] [Status] Value
 *     Value  ::= String | Integer | Date | Buffer
 *
 *  where
 *
 *     [X] means 0 or 1 occurrences of X, and
 *     {X} means 0, 1 or many occurrences of X
 *</pre>
 *  A Buffer can contain meta-data (name and type of each item in the
 *  buffer structure), data (value of each item), or both.
 *<p>
 *  A Buffer can have many String representations,
 *  which are created by <a href=BufferFormatter.html>BufferFormatter</a>s.
 *<p>
 *<p>
 *  WRITE access to the Buffer:
 *<p>
 *  The following method appends a specified Item to the end of
 *  the item list in this Buffer and returns the position of the newly created item.
 *  This may lead to duplicate item names.
 *<pre>
 *     public int addItem( Item item );
 *</pre>
 *  The following methods create and append a new Item to the end of
 *  the item list in this Buffer and returns the position of the newly created item.
 *  This may lead to duplicate item names.
 *<pre>
 *     public int addItem( String itemName, XXX value );
 *<p>
 *  where XXX is one of:
 *     Buffer, String, Int, ... (a value of an item)
 *</pre>
 *<p>
 *<p>
 *  READ access to the Buffer:
 *<p>
 *  The following methods return the indexed item or the first occurrence
 *  of the named item. They throw an exception or return a default value,
 *  if such item does not exist.
 *<pre>
 *     public XXX getXXX( int position ); // throws RuntimeException
 *     public XXX getXXX( String itemName ) throws ItemNotFoundException;
 *     public XXX getXXX( String itemName, XXX default_value );
 *<p>
 *  where XXX is one of:
 *<p>
 *     Item (an item itself) or
 *     Buffer, String, Int, ... (a value of an item)
 *</pre>
 */
public interface Buffer extends Serializable, Cloneable {
   //==========================================================================
   //  get/setHeader
   //==========================================================================

   /**
    * Set the header of this Buffer.
    * @param header string describing the contents of this buffer
    */
   void setHeader(String header);

   /**
    * Get the header of this Buffer.
    * @return a string describing the contents of this buffer
    */
   String getHeader();

   //==========================================================================
   //  countItems
   //==========================================================================

   /**
    * Return the number of items collected in this Buffer.
    * @return the number of items collected in this Buffer.
    */
   int countItems();

   /**
    * Return the number of items with the specified name.
    * @param name item name
    * @return the number of items with the specified name collected in this Buffer.
    */
   int countItems(String name);

   //==========================================================================
   //  Iteration
   //==========================================================================

   /**
    * Return an iterator over all items in this Buffer.
    * @return an instance of BufferIterator
    */
   BufferIterator iterator();

   //==========================================================================
   //  addItem
   //==========================================================================

   /**
    * Append a specified Item to the end of the item list in this Buffer.
    * The operation may lead to duplicate item names.
    * @param item an item to add to this buffer
    * @return the zero-based index of the added item.
    */
   int addItem(Item item);

   /**
    * Create and append a new Item to this buffer.
    * The item will be placed at the end of the item list in this Buffer,
    * which  may lead to duplicate item names.
    * @param itemName item name
    * @param value item value
    * @return the position of the newly created item (0,1, ...).
    */
   int addItem(String itemName, Object value);

   /**
    * Create and append a new Item to this buffer.
    * The item will be placed at the end of the item list in this Buffer,
    * which  may lead to duplicate item names.
    * @param itemName item name
    * @param value item value
    * @return the position of the newly created item (0,1, ...).
    */
   int addItem(String itemName, Buffer value);

   /**
    * Create and append a new Item to this buffer.
    * The item will be placed at the end of the item list in this Buffer,
    * which  may lead to duplicate item names.
    * @param itemName item name
    * @param value item value
    * @return the position of the newly created item (0,1, ...).
    */
   int addItem(String itemName, String value);

   /**
    * Create and append a new Item to this buffer.
    * The item will be placed at the end of the item list in this Buffer,
    * which  may lead to duplicate item names.
    * @param itemName item name
    * @param value item value
    * @return the position of the newly created item (0,1, ...).
    */
   int addItem(String itemName, int value);

   //==========================================================================
   //  insertItem
   //==========================================================================

   /**
    * Insert a specified Item at the specified position (0,1, ...) in
    * the item list. This may lead to duplicate item names.
    * If the specified position is outside range, then the
    * item will be added at the end of the item list.
    * @param item the item to insert
    * @param position a zero-bassed index
    * @return the position of the inserted item.
    */
   int insertItem(Item item, int position);

   //==========================================================================
   //  setItem
   //==========================================================================

   /**
    * Mofify the first occurence of a named item, or create a new item,
    * if such item does not exist.
    * @param itemName item name
    * @param value item value
    * @return the item's position in the buffer (0,1, ...).
    */
   int setItem(String itemName, String value);

   /**
    * Mofify the first occurence of a named item, or create a new item,
    * if such item does not exist.
    * @param itemName item name
    * @param value item value
    * @return the item's position in the buffer (0,1, ...).
    */
   int setItem(String itemName, int value);

   //==========================================================================
   //  getItemPosition
   //==========================================================================

   /**
    * Get the position of the first occurrence of a named Item.
    * @param itemName item name
    * @return the item position or -1 if such item does not exist.
    */
   int getItemPosition(String itemName);

   //==========================================================================
   //  Get
   //==========================================================================

   /**
    * Get the item at the specified position.
    * @param position a zero-based index of an existing item in the buffer
    * @return the item at given index
    */
   Item getItem(int position);

   /**
    * Get the item with the specified name.
    * @param itemName item name
    * @return the found item
    * @throws ItemNotFoundException if the specified item does not exist
    */
   Item getItem(String itemName) throws ItemNotFoundException;

   /**
    * Get the item with the specified name.
    * @param itemName item name
    * @param defaultValue the default value (possibly null)
    * @return the named item, or the default value if the named item does not exist
    */
   Item getItem(String itemName, Item defaultValue);

   /**
    * Get the value of the item at the specified position.
    * @param position an index to an existing item in the buffer
    * @return the String value of the item at given index
    */
   String getString(int position);

   /**
    * Get the value of the item with the specified name.
    * @param itemName item name
    * @return the String value of the named item
    * @throws ItemNotFoundException if the specified item does not exist
    */
   String getString(String itemName) throws ItemNotFoundException;

   /**
    * Get the value of the item with the specified name.
    * @param itemName item name
    * @param defaultValue the default value (possibly null)
    * @return the String value of the named item, or the default value
    *         if the named item does not exist
    */
   String getString(String itemName, String defaultValue);

   /**
    * Get the value of the item at the specified position.
    * @param position an index to an existing item in the buffer
    * @return the integer value of the item at given index
    */
   int getInt(int position);

   /**
    * Get the value of the item with the specified name.
    * @param itemName item name
    * @return the integer value of the named item
    * @throws ItemNotFoundException if the specified item does not exist
    */
   int getInt(String itemName) throws ItemNotFoundException;

   /**
    * Get the value of the item with the specified name.
    * @param itemName item name
    * @param defaultValue the default value
    * @return the integer value of the named item, or the default value
    *         if the named item does not exist
    */
   int getInt(String itemName, int defaultValue);

   /**
    * Get the value of the item at the specified position.
    * @param position an index of an existing item in the buffer
    * @return the Object being the value of the item at given index
    */
   Object getObject(int position);

   /**
    * Get the value of the item with the specified name.
    * @param itemName item name
    * @return the Object being the value of the named item
    * @throws ItemNotFoundException if the specified item does not exist
    */
   Object getObject(String itemName) throws ItemNotFoundException;

   /**
    * Get the value of the item with the specified name.
    * @param itemName item name
    * @param defaultValue the default value (possibly null)
    * @return the Object being the value of the named item, or the default value
    *         if the named item does not exist
    */
   Object getObject(String itemName, Object defaultValue);

   /**
    * Get the value of the item at the specified position.
    * @param position an index to an existing item in the buffer
    * @return the Buffer being the value of the item at given index
    */
   Buffer getBuffer(int position);

   /**
    * Get the value of the item with the specified name.
    * @param itemName item name
    * @return the Buffer being the value of the named item
    * @throws ItemNotFoundException if the specified item does not exist
    */
   Buffer getBuffer(String itemName) throws ItemNotFoundException;

   /**
    * Get the value of the item with the specified name.
    * @param itemName item name
    * @param defaultValue the default value (possibly null)
    * @return the Buffer being the value of the named item, or the default value
    *         if the named item does not exist
    */
   Buffer getBuffer(String itemName, Buffer defaultValue);

   //==========================================================================
   //  Remove
   //==========================================================================

   /**
    * Remove the named item from the item list in this Buffer.
    * This aplies only to the first occurence, if there are many items with
    * the specified name. If the specified item does not exist
    * the method does nothing.
    * @param name the name of an item
    */
   void removeItem(String name);

   /**
    * Remove the Item at a specified position (0,1, ...) in this Buffer
    * @param position zero-based index of an existing item
    */
   void removeItem(int position);

   /**
    * Remove all items from this Buffer
    */
   void removeItems();

   //==========================================================================
   //  findItem
   //==========================================================================

   /**
    * Find a named Item inthis Buffer.
    * The name can contain "/"-operator, for example "A/B/C" points to
    * item "C" in sub-buffer "B" of sub-buffer "A" of this buffer.
    * @param itemName item name
    * @return the found Item, or null if such Item does not exist.
    */
   Item findItem(String itemName);

   //==========================================================================
   //
   //==========================================================================

   /**
    * Clear the contents of this buffer.
    * The methods removes all items from the buffer, and also clears
    * other instance variables, like for example header.
    */
   void clear();

   /**
    * Return a new (empty) instance of the same class as this Buffer
    * @return a new, empty instance of Buffer interface
    */
   Buffer newInstance();

   /**
    * Compares this buffer to another one.
    * @return true if a specified Object is a Buffer and has exactly the same
    * contents as this Buffer
    */
   @Override
   boolean equals(Object obj);

   /**
    * Clone this Buffer.
    * @return a new instance of Buffer with the same contents as this buffer.
    */
   Object clone();// NOPMD

   /**
    * Compute the hash code for this Buffer
    * @return an int value representing the contents of this buffer
    */
   @Override
   int hashCode();

   /**
    * Return a new instance of Item
    * @return a new instance of Item
    */
   Item newItem();

   //==========================================================================
   //  Freezing the buffer
   //==========================================================================

   /**
    * Indicates if this buffer is frozen.
    * @return true if this buffer is frozen, false otherwise
    */
   boolean isFrozen();

   /**
    * Freezes this buffer.
    */
   void freeze();

   /**
    * Un-freezes this buffer.
    */
   void unfreeze();
}
