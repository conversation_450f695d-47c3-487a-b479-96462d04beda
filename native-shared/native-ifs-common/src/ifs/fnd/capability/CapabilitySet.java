package ifs.fnd.capability;

import ifs.fnd.util.BitSetUtil;
import java.util.AbstractSet;
import java.util.BitSet;
import java.util.EnumSet;
import java.util.Iterator;

public class CapabilitySet<T extends Enum<T> & Capability<T>>
        extends AbstractSet<T> {

    private final EnumSet<T> capabilities;

    private CapabilitySet(Class<T> type) {
        capabilities = EnumSet.noneOf(type);
    }

    public BitSet toBitSet() {
        BitSet bitSet = new BitSet();
        for (Capability<T> c : capabilities) {
            bitSet.set(c.getBitIndex());
        }
        return bitSet;
    }

    public static <T extends Enum<T> & Capability<T>> CapabilitySet<T> fromBitSet(
            BitSet bitSet, Class<T> type) {
        CapabilitySet<T> set = new CapabilitySet<>(type);
        for (int bitIndex = 0; bitIndex < bitSet.length(); bitIndex++) {
            if (bitSet.get(bitIndex)) {

                // XXX: All this mess because interfaces cannot have static methods.
                // Else it could just have been type.lookup(bitIndex)
                T t = type.getEnumConstants()[0].getEnum().lookup(bitIndex);

                if (t != null) {
                    set.add(t);
                }
            }
        }
        return set;
    }

    public static <T extends Enum<T> & Capability<T>> CapabilitySet<T> getIntersection(
            CapabilitySet<T> set1, CapabilitySet<T> set2, Class<T> type) {
        BitSet bitSet1 = set1.toBitSet();
        bitSet1.and(set2.toBitSet());
        return fromBitSet(bitSet1, type);
    }

    public static <T extends Enum<T> & Capability<T>> CapabilitySet<T> of(
            T first, T... rest) {
        CapabilitySet<T> set = CapabilitySet.noneOf(first.getType()); //new CapabilitySet<T>(first.getType());
        set.add(first);
        for (T c : rest) {
            set.add(c.getEnum());
        }
        return set;
    }

    public static <T extends Enum<T> & Capability<T>> CapabilitySet<T> noneOf(Class<T> type) {
        return new CapabilitySet<>(type);
    }

    public byte[] toByteArray() {
        BitSet bitSet = this.toBitSet();
        return BitSetUtil.toByteArray(bitSet);
    }

    public static <T extends Enum<T> & Capability<T>> CapabilitySet<T> fromByteArray(
            byte[] array, Class<T> type) {
        return CapabilitySet.fromBitSet(BitSetUtil.toBitSet(array), type);
    }

    @Override
    public boolean add(T c) {
        return capabilities.add(c);
    }

    @Override
    public Iterator<T> iterator() {
        return capabilities.iterator();
    }

    @Override
    public int size() {
        return capabilities.size();
    }

    @Override
    public String toString() {
       return capabilities.toString();
    }
}
