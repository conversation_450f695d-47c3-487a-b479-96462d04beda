package ifs.fnd.capability;

import java.util.Arrays;
import java.util.Collections;
import java.util.Set;

/**
 * <AUTHOR>
 * 
 */
public abstract class CapabilityManager<T extends Enum<T> & Capability<T>> {

    private final CapabilitySet<T> serverCapabilities;

    public CapabilityManager(Class<T> type) {
        serverCapabilities = CapabilitySet.noneOf(type);
        addSupportedCapabilities(type);
    }

    public Set<T> getServerCapabilities() {
        return Collections.unmodifiableSet(serverCapabilities);
    }

    /* TODO: This method should be modified if it is required to load supported
     *       capabilities through a configuration. */
    private void addSupportedCapabilities(Class<T> type) {
        serverCapabilities.addAll(Arrays.asList(type.getEnumConstants()));
    }

    protected CapabilitySet<T> getCommonCapabilities(
            CapabilitySet<T> clientCapabilies, Class<T> type) {
        return CapabilitySet.getIntersection(serverCapabilities,
                clientCapabilies, type);
    }
}
