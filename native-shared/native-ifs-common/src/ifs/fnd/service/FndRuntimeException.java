/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */


package ifs.fnd.service;

import ifs.fnd.util.Str;

/**
 * IFS encapsulation of standard RuntimeException.
 */
public class FndRuntimeException extends RuntimeException
{
   /**
    * Constructs a new IFS specific runtime exception with the specified cause.
    * @param cause the cause
    */
   public FndRuntimeException(Throwable cause)
   {
      super(cause);
   }

   /**
    * Constructs a new IFS specific runtime exception with the specified detail message
    * and optional parameters referred as &1 to &9.
    * @param message the detail message which may contain references to parameters
    * @param param the parameter values referred in the detail message
    */
   public FndRuntimeException(String message, Object... param)
   {
      super(Str.formatMessage(message, param));
   }
   
   /**
    * Constructs a new IFS specific runtime exception with the specified cause,
    * detail message and optional parameters referred as &1 to &9.
    * @param cause the cause
    * @param message the detail message which may contain references to parameters
    * @param param the parameter values referred in the detail message
    */
   public FndRuntimeException(Throwable cause, String message, Object... param)
   {
      super(Str.formatMessage(message, param), cause);
   }
}
