/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.service;

/**
 * Definition of IFS constants.
 * This class does not depend on any other IFS class, so it can be used anywhere
 * without considering circularity during static initialization.
 */
public class IfsConstants {

   /**
    * UTF-8 character encoding constant
    */
   public static final String UTF8 = "UTF-8";

   /**
    * Path to IFS home directory defined by system property <code>fndext.home</code>
    */
   public static final String IFS_HOME = System.getProperty("fndext.home");

   /**
    * IFS instance name defined by system property <code>fndext.instance</code>.
    */
   public static final String IFS_INSTANCE = System.getProperty("fndext.instance");

   /**
    * MWS server name defined by system property <code>weblogic.Name</code>.
    */
   public static final String IFS_MWS_SERVER = System.getProperty("weblogic.Name", "WildFly");

   /**
    * The Oracle user used to create connections to database.
    */
   public static final String SYSTEM_USER = "IFSSYS";

   /**
    * Path to IFS configuration directory defined by system property <code>fndext.configDir</code>.
    * This constant may be null if the location of IFS configuration directory is defined by
    * properties IFS_HOME and IFS_INSTANCE.
    */
   public static final String IFS_CONFIG_DIR = System.getProperty("fndext.configDir");

   /**
    * OS dependent new-line character sequence.
    */
   public static final String LINE_SEPARATOR = System.getProperty("line.separator");

   /**
    * IFS text separator: '^'.
    */
   public static final char TEXT_SEPARATOR = '^';

   /**
    * IFS field separator (ASCII code = 31).
    */
   public static final char FIELD_SEPARATOR = (char) 31;

   /**
    * IFS record separator (ASCII code = 30).
    */
   public static final char RECORD_SEPARATOR = (char) 30;

   /**
    * IFS group separator (ASCII code = 29).
    */
   public static final char GROUP_SEPARATOR = (char) 29;

   /**
    * IFS file separator (ASCII code = 28).
    */
   public static final char FILE_SEPARATOR = (char) 28;
}
