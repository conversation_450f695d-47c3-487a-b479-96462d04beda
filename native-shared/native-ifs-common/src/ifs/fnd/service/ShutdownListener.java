/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */


package ifs.fnd.service;

import ifs.fnd.log.LogMgr;
import ifs.fnd.log.Logger;
import ifs.fnd.util.IoUtil;
import ifs.fnd.util.Str;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.ServerSocket;
import java.net.Socket;
import java.net.SocketAddress;

/**
 * A class implementing protocol for graceful shutdown of VM in case of running
 * Java as a Windows service through the ifssrv.exe executable.<p>
 * This class implements a socket listener bound to the localhost only and listening
 * for a token. Both the token and the port number are given as environment variables
 * and are set during service startup by the ifssrv.exe executable.<p>
 * On service shutdown ifssrv.exe will send the token to the listener; listener
 * simply makes call to System.exit(), which makes it possible to execute any shutdown hook.<p>
 * The algorithm is a result of Sun Java VM implementation that doesn't listen on any
 * OS signal if started with -Xrs command line modifier.
 */
public class ShutdownListener
{
   //===============================================================================
   //  Constants defining environment variables
   //===============================================================================

   private static final String ENV_PORT_NO = "IFS_SHUTDOWN_PORT";
   private static final String ENV_CMD_STR = "IFS_SHUTDOWN_CMD";
   private static final byte[] LOCALHOST   = {127,0,0,1};


   //===============================================================================
   //  Variable declarations
   //===============================================================================

   private static ShutdownSocketListener shlistener = null;


   // Avoid instantiation
   private ShutdownListener() {}

   /**
    * Starts the listener if not already started. Port number and value of the token
    * the listener is supposed to listen on are taken from environment variables.
    */
   public static synchronized void start()
   {
      if( shlistener!=null )
         return;
         
      Logger log = LogMgr.getFrameworkLogger();

      try
      {
         if(log.debug) log.debug("Reading environment variables:");

         String cmd  = System.getenv(ENV_CMD_STR);
         String port = System.getenv(ENV_PORT_NO);

         if(log.debug)
         {
            log.debug("  &1=&2", ENV_CMD_STR, cmd);
            log.debug("  &1=&2", ENV_PORT_NO, port);
         }

         boolean emptyCmd  = Str.isEmpty(cmd);
         boolean emptyPort = Str.isEmpty(port);

         if( emptyCmd && emptyPort )
         {
            if(log.debug) log.debug("Environment variables not defined - returning");
            return;
         }
         
         shlistener = new ShutdownSocketListener();

         if( emptyCmd || emptyPort )
         {
            error("One of environment variables: '&1', '&2' is empty. Shutdown Listener not started.", ENV_PORT_NO, ENV_CMD_STR);
            return;
         }

         int portNo;
         try
         {
            portNo = Integer.parseInt(port);
            if( portNo<1 || portNo>65535 )
               throw new NumberFormatException("Value must be between 1 and 65535");
         }
         catch( NumberFormatException e )
         {
            error(e, "Invalid port number defined by environment variable: '&1=&2'. Shutdown Listener not started.", ENV_PORT_NO, port);
            return;
         }

         if(log.debug) log.debug("Initializing ShutdownListener with port '&1' and command '&2'", String.valueOf(portNo), cmd);
         shlistener.init(cmd, portNo);
         shlistener.start();
      }
      catch( Throwable t )
      {
         error(t, "Fatal error during ShutdownListener initiation");
         return;
      }
   }

   //===============================================================================
   //  Error and info handling methods
   //===============================================================================

   private static void error( String message )
   {
      error(null, message, null, null);
   }

   private static void error( Throwable e, String message )
   {
      error(e, message, null, null);
   }

   private static void error( Exception e, String message, String p1 )
   {
      error(e, message, p1, null);
   }

   private static void error( String message, String p1, String p2 )
   {
      error(null, message, p1, p2);
   }

   private static void error( Throwable e, String message, String p1, String p2 )
   {
      LogMgr.clearThreadLoggers();
      Logger log = LogMgr.getFrameworkLogger();
      log.error(e, message, p1, p2);
   }

   private static void info( String message )
   {
      LogMgr.clearThreadLoggers();
      Logger log = LogMgr.getFrameworkLogger();
      if(log.info) log.info(message);
   }

   //===============================================================================
   //  Inner class implementing the main loop
   //===============================================================================

   /**
    * Thread class implementing a loop reading from socket.
    */
   private static class ShutdownSocketListener extends Thread
   {
      private String cmd;
      private int    portNo;

      private void init( String cmd, int portNo )
      {
         this.cmd    = cmd;
         this.portNo = portNo;
      }

      /**
       * Starts a thread that will read from socket and perform System.exit()
       * on reception of a predefined string.
       */
      @Override
      public void run()
      {
         ServerSocket listener = null;
         Socket       socket   = null;

         Logger log = LogMgr.getFrameworkLogger();
         if(log.debug) log.debug("Creating listener...");

         try
         {
            //listener = new ServerSocket(portNo);

            //SocketAddress addr = new InetSocketAddress( InetAddress.getLocalHost(), portNo);
            SocketAddress addr = new InetSocketAddress(InetAddress.getByAddress(LOCALHOST), portNo);
            listener = new ServerSocket();
            listener.bind(addr);

         }
         catch( IOException e )
         {
            error(e, "Exception while opening port '&1'. Aborting listening.", String.valueOf(portNo));
            return;
         }
         catch( Throwable t )
         {
            error(t, "Fatal error during ShutdownListener initiation. Exiting");
            return;
         }

         if(log.debug) log.debug("Listener created.");

         try
         {
            while(true)
            {
               if(log.debug) log.debug("Waiting for socket activity...");
               socket = listener.accept();
               if(log.debug) log.debug("Socket activated. Reading...");

               InputStream in = socket.getInputStream();
               BufferedReader reader = IoUtil.newUtf8BufferedReader(in);
               String line = reader.readLine();
               if(log.debug) log.debug("Received line: '&1'", line);

               if( cmd.equals(line) )
               {
                  IoUtil.stdoutln("Exiting gracefully.");
                  info("Shutting down the server.");
                  System.exit(0);
               }
               else
               {
                  error("Wrong input data on socket; continuing...");
                  reader.close();
                  in.close();
                  socket.close();
                  socket = null;
               }
            }
         }
         catch( IOException e )
         {
            error(e, "Exception while reading socket. Aborting listening.");
         }
         catch( Throwable t )
         {
            error(t, "Fatal error during ShutdownListener execution. Exiting");
         }
         if(log.debug) log.debug("Nothing more to do. Finishing thread.");
      }
   }
}

