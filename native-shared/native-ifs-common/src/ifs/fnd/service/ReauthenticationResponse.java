/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.service;

import ifs.fnd.buffer.Buffer;
import ifs.fnd.buffer.Bufferable;
import ifs.fnd.util.Message;

/**
 * Re-authentication response parameters sent from a client to a server.
 */
public class ReauthenticationResponse implements Bufferable {

   private String username;
   private String password;
   private String userComment;
   private String gateId;

   /**
    * Create new instance of ReauthenticationResponse with undefined contents.
    */
   public ReauthenticationResponse() {
   }

   /**
    * Create new instance of ReauthenticationResponse with the specified contents.
    * @param username  value for USERNAME  parameter
    * @param password  value for <PERSON><PERSON><PERSON>ORD  parameter
    * @param uComment  value for USER_COMMENT parameter
    * @param gateId    value for GATE_ID parameter
    */
   public ReauthenticationResponse(String username, String password, String uComment, String gateId) {
      this.username  = username;
      this.password  = password;
      this.userComment  = uComment;
      this.gateId  = gateId;
   }

   /**
    * Get the USERNAME parameter.
    * @return String value of the USERNAME parameter, or null if this value is undefined.
    */
   public String getUsername() {
      return username;
   }

   /**
    * Get the PASSWORD parameter.
    * @return String value of the PASSWORD parameter, or null if this value is undefined.
    */
   public String getPassword() {
      return password;
   }

   /**
    * Get the USER_COMMENT parameter.
    * @return String value of the USER_COMMENT parameter, or null if this value is undefined.
    */
   public String getUserComment() {
      return userComment;
   }

   /**
    * Get the GATE_ID parameter.
    * @return String value of the GATE_ID parameter, or null if this value is undefined.
    */
   public String getGateId() {
      return gateId;
   }

   /**
    * Returns a String representation of this object.
    */
   @Override
   public String toString() {
      return "ReauthenticationResponse{" + "username=" + username + ", password=" + password + ", uComment=" + userComment + ", gateId=" + gateId + '}';
   }

   //==========================================================================
   // Conversion to/from Buffer
   //==========================================================================

   /**
    * Copy the internal state of this Bufferable object into a specified Buffer.
    * @param buf the buffer to store the internal state in
    */
   @Override
   public void save(Buffer buf) {
      if(username != null)
         buf.setItem("USERNAME",  username);
      if(password != null)
         buf.setItem("PASSWORD",  password);
      if(userComment != null)
         buf.setItem("USER_COMMENT", userComment);
      if(gateId != null)
         buf.setItem("GATE_ID",  gateId);
   }

   /**
    * Retrieve the internal state of this Bufferable object from a specified Buffer.
    * @param buf the buffer to read the internal state from
    */
   @Override
   public void load(Buffer buf) {
      username  = buf.getString("USERNAME",  null);
      password  = buf.getString("PASSWORD",  null);
      userComment  = buf.getString("USER_COMMENT", null);
      gateId  = buf.getString("GATE_ID",  null);
   }

   //==========================================================================
   // Conversion to/from IFS-Message
   //==========================================================================

   /**
    * Copy the internal state of this object into an IFS-Message.
    * @param msg the IFS-Message to store the internal state in
    */
   public void save(Message msg) {
      if(username != null)
         msg.setAttribute("USERNAME",  username);
      if(password != null)
         msg.setAttribute("PASSWORD",  password);
      if(userComment != null)
         msg.setAttribute("USER_COMMENT", userComment);
      if(gateId != null)
         msg.setAttribute("GATE_ID",  gateId);
   }

   /**
    * Retrieve the internal state of this object from an IFS-Message.
    * @param msg the IFS-Message to retrieve the internal state from
    */
   public void load(Message msg) {
      username  = msg.findAttribute("USERNAME",  (String)null);
      password  = msg.findAttribute("PASSWORD",  (String)null);
      userComment  = msg.findAttribute("USER_COMMENT", (String)null);
      gateId  = msg.findAttribute("GATE_ID",  (String)null);
   }
}

