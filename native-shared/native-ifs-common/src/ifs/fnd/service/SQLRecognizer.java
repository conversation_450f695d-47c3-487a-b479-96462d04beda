/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.service;

import ifs.fnd.buffer.AutoString;
import ifs.fnd.util.Str;
import ifs.fnd.log.*;
import ifs.fnd.util.IoUtil;
import ifs.fnd.util.SimpleStack;
import java.io.StreamTokenizer;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.Map;
import java.util.Set;

/**
 * <B>Framework internal class:</B> Class used to identify database objects in SQL statements.
 * It can retrieve all database objects (tables, views, PL/SQL procedures
 * and functions) from any SQL or PL/SQL statement.
 * It supports comments, string literals across lines
 * and doubled quotes within a string literal. It pre-appends every database object
 * with the specified prefix.
 *
 * Restrictions/rules:
 *<pre>
 *  o The tokenizer recognizes only PL/SQL packages having names ending with
 *    "_API", "_RPI", "_SYS" or "_CFP".
 *
 *  o Database Schema (owner) may be used explicitly.
 *    If not specified, the tokenizer appends the default prefix (APP_OWNER)
 *    to every recognized database object.
 *
 *  o The identifier DUAL is not considered to be a database table or view
 *</pre>
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class SQLRecognizer {

   //
   //  Token Types
   //
   private static final int EOF = 1; // Atoms
   private static final int OTHER = 3;
   private static final int STRING = 4;
   private static final int ID = 5;

   private static final int SELECT = 10; // Keywords
   private static final int BEGIN_TA = 11;
   private static final int END_TA = 12;
   private static final int TABLE_FUNCTION = 13;
   private static final int JOIN = 14;
   private static final int WITH = 15;

   private static final int TABLE = 20; // Objects
   private static final int PROCEDURE = 21;
   private static final int VARIABLE = 22;

   private static final String HIDDEN_HINT  = String.valueOf((char)4);
   private static final String HINT_START = "/*+";
   private static final String HINT_END   = "*/";
   private static final int    HINT_START_LEN = HINT_START.length();
   private static final int    HINT_END_LEN   = HINT_END.length();

   private static final int NON_SPACE_TOKEN_BUFFER_SIZE = 5;

   private int type;
   private String value;

   private final SQLTokenizer tokenizer;

   private final String dbPrefix;
   private final StringBuilder sql;
   private final ArrayList<String> tables;
   private final ArrayList<String> variables;
   private final ArrayList<Integer> variablePositions;
   private final ArrayList<String> procedures;
   private final ArrayList<SQLProcedure> sqlProcedures; //to strore first level procedures
   private SQLProcedure currentProc; //reference to the active procedure
   private boolean tableArea;
   private int tableAreaParenLevel;  // parenLevel stored when FROM keyword has opened tableArea
   private boolean tablePosition;

   /**
    * Non-space token circular buffer.
    */
   private int    nonSpaceTokenNr;
   private final int    nonSpaceTokenType[]  = new int   [NON_SPACE_TOKEN_BUFFER_SIZE];
   private final String nonSpaceTokenValue[] = new String[NON_SPACE_TOKEN_BUFFER_SIZE];

   private int parenLevel;                 // number of unmatched "("
   private final SimpleStack<TableArea> taStack; // stack with deactivated table areas

   private int extractParenLevel;    // > 0 if inside EXTRACT(...FROM...) function, used to ignore FROM keyword

   private ArrayList<String> hints = null;

   private final Set<String> globalQueryNames;       // currently valid query names defined by WITH clauses
   private final SimpleStack<WithClause> withStack;  // stack with WITH clauses
   private int withParenLevel = -1;            // parenLevel on which current WITH keyword was found
   private WithClause currentWithClause;       // current WITH clause in define mode, only one such clasue may exist

   private final Logger log = LogMgr.getDatabaseLogger();
   private final Logger clsLog = LogMgr.getClassLogger(SQLRecognizer.class);


   public SQLRecognizer(String sqlText, String dbObjectPrefix) throws Exception {
      if(log.debug) log.debug("Rocognizing SQL: '&1'", sqlText);
      dbPrefix = dbObjectPrefix;

      tokenizer = new SQLTokenizer(removeHints(sqlText));

      endTableArea();

      sql = new StringBuilder();
      tables            = Util.newArrayList();
      variables         = Util.newArrayList();
      variablePositions = Util.newArrayList();
      procedures        = Util.newArrayList();
      sqlProcedures     = Util.newArrayList();
      globalQueryNames  = Util.newHashSet();

      parenLevel = 0;
      taStack    = new SimpleStack<>();
      withStack  = new SimpleStack<>();

      currentProc = null;

      parse();

      addHints();
   }

   private String removeHints(String sqlText) {
      int last = 0;
      int start = sqlText.indexOf(HINT_START);
      if(start<0) return sqlText;

      StringBuilder buf = new StringBuilder(sqlText.length());
      while(start>0) {
         int end = sqlText.indexOf(HINT_END, start+HINT_START_LEN);
         if(end<0) break;

         if(isHint(sqlText, start + HINT_START_LEN, end)) {
            buf.append( sqlText.substring(last, start) );
            buf.append(HIDDEN_HINT);
            if(hints==null)
               hints = new ArrayList<>();
            hints.add( sqlText.substring(start, end+HINT_END_LEN) );
            last = end + HINT_END_LEN;
         }
         start = sqlText.indexOf(HINT_START, end+2);
      }
      if(last>0) {
         buf.append( sqlText.substring(last) );
      }
      String s = last==0 ? sqlText : buf.toString();
      if(log.debug) log.debug("String to tokenize: '&1'", s);
      return s;
   }

   /**
    * Checks if a specified text region is a hint.
    *
    * A hint may contain 0 or an even number of quotation marks, for example: "optimizer_features_enable('10.2.0.4')".
    * Otherwise, it is not a hint, but a part of a string literal or part of an identifier.
    */
   private static boolean isHint(String sqlText, int start, int end) {
      return Str.countChar(sqlText, start, end, '\'') % 2 == 0 && Str.countChar(sqlText, start, end, '"') % 2 == 0;
   }

   private void addHints() {
      if(hints==null) return;

      if(log.debug) log.debug("SQL before replacement: '&1'", sql.toString());
      for(String hint : hints) {
         int pos = sql.indexOf(HIDDEN_HINT);
         sql.replace(pos, pos+1, hint);
      }
      if(log.debug) log.debug("SQL after replacement: '&1'", sql.toString());
   }

   /**
    *
    *
    */
   private int nextToken() throws Exception {
      int tt = tokenizer.nextToken();

      switch (tt) {
         case StreamTokenizer.TT_EOF :
            type = EOF;
            value = null;
            break;

         case '(' :
            value = "(";
            type = OTHER;
            parenLevel++; // push "("
            break;

         case ')' :
            value = ")";
            type = tableArea && tableAreaParenLevel == parenLevel ? END_TA : OTHER;

            if (!taStack.empty() && parenLevel == taStack.peek().parenLevel) {
               if(clsLog.debug)
                  clsLog.debug("  Activate previously deactivated table area (paren_level = &1)", parenLevel);
               popTableArea();
               type = OTHER;
            }

            if(parenLevel == extractParenLevel) {
               extractParenLevel = 0; // exit EXTRACT function
            }

            if(parenLevel == withParenLevel) {
               popWithClause();
            }

            parenLevel--; // pop "("
            break;

         case ':' :
            tokenizer.mark();
            tokenizer.nextNonSpaceToken();
            String name = tokenizer.getValue();
            if (IfsNames.isId(name)) {
               value = name;
               type = VARIABLE;
               break;
            }
            else {
               tokenizer.reset();
               value = ":";
               type = OTHER;
               break;
            }

         case '\'' :
            value = tokenizer.getValue();
            type = STRING;
            break;

         case ';' :
            value = ";";
            type = OTHER;
            endTableArea();
            break;

         default :
            if (tt > 0) {
               value = "" + (char) tt;
               type = OTHER;
            }
            else { // ID, because digits are ordinary chars

               value = tokenizer.getValue();
               String valueWithoutDoubleQuotes = removeDoubleQuotes(value);
               type = ID;
               Keyword keyword = (Keyword) KEYWORD_TABLE.get(value.toUpperCase());
               //System.out.println("value="+value+" keyword="+keyword+" keyword.type="+(keyword==null?"":""+keyword.type));
               if (keyword != null) {
                  type = keyword.type;
                  if(type <= 0) {
                     checkForbiddenKeywordSequence();
                     type = ID;
                  }
                  break;
               }
               else if (tableArea && tablePosition) {
                  tokenizer.mark();
                  String owner = valueWithoutDoubleQuotes;
                  if (tokenizer.nextNonSpaceToken() == '.') {
                     if (tokenizer.nextNonSpaceToken() != StreamTokenizer.TT_WORD)
                        throw new FndException("FNDSQLTBID: Expecting table/view name but found: '&1'", tokenizer.toString());
                     value = owner + "." + removeDoubleQuotes(tokenizer.getValue());
                     type = TABLE;
                     break;
                  }
                  tokenizer.reset();
                  if(globalQueryNames.contains(owner.toUpperCase())) {
                     type = OTHER;
                     tablePosition = false;
                     break;
                  }
                  type = TABLE;
                  value = owner;
               }
               else if (!IfsNames.isId(valueWithoutDoubleQuotes)) {
                  type = OTHER;
                  break;
               }
               else if (isIfsPlsqlPackageName(valueWithoutDoubleQuotes)) {
                  tokenizer.mark();
                  String pkg = valueWithoutDoubleQuotes;
                  if (tokenizer.nextNonSpaceToken() == '.') {
                     if (tokenizer.nextNonSpaceToken() != StreamTokenizer.TT_WORD)
                        throw new FndException("FNDSQLSXID: Expecting PL/SQL procedure name but found: '&1'", tokenizer.toString());
                     value = pkg + "." + removeDoubleQuotes(tokenizer.getValue());
                     type = PROCEDURE;
                     break;
                  }
                  tokenizer.reset();
                  type = ID;
                  value = pkg;
               }
            }
            break;
      }

      if (tt > ' ' || tt < 0) {
         addNonSpaceToken();
      }

      if(clsLog.debug)
         clsLog.debug("Token: {&1} &2:\t\"&3\"\t(&4,&5) tableArea=(&6,&7,&8)",
                       parenLevel,
                       tokenTypeName(type),
                       value,
                       tokenizer.getType(),
                       tokenizer.getValue(),
                       tableArea,
                       tableAreaParenLevel,
                       tablePosition);
      return type;
   }

   private static boolean isIfsPlsqlPackageName(String name) {
      int len = name.length();
      if(len < 4)
         return false;
      String suffix = name.substring(len - 4).toUpperCase();
      return "_API".equals(suffix) || "_RPI".equals(suffix) || "_SYS".equals(suffix) || "_CFP".equals(suffix) || "_CLP".equals(suffix) || "_ICP".equals(suffix);
   }

   private static String removeDoubleQuotes(String s) {
      if(s.startsWith("\"") && s.endsWith("\""))
         s = s.substring(1, s.length() - 1);
      return s;
   }

   private void parse() throws Exception {
      while (true) {
         nextToken();

         switch (type) {
            case BEGIN_TA :
               if(extractParenLevel == 0) { // ignore FROM keyword inside EXTRACT function
                  beginTableArea();
               }
               closeParameterlessProcedure();
               break;

            case JOIN :
               if(tableArea)
                  tablePosition = true;
               closeParameterlessProcedure();
               break;

            case END_TA :
               endTableArea();
               break;

            case OTHER :
               if (tableArea && tableAreaParenLevel == parenLevel && ",".equals(value))
                  tablePosition = true;

               closeParameterlessProcedure();

               if (")".equals(value) && currentProc != null) {
                  currentProc.setBrackets(currentProc.getBrackets() - 1);
                  if (currentProc.getBrackets() == 0) {
                     currentProc.setEnd(sql.length() + 1);
                     currentProc = currentProc.parent;
                  }
               }
               if ("(".equals(value) && currentProc != null)
                  currentProc.setBrackets(currentProc.getBrackets() + 1);

               break;

            case ID :
               closeParameterlessProcedure();
               enterExtractFunction();
               break;

            case TABLE :
               if (tableArea && tablePosition) {
                  String uppname = value.toUpperCase();
                  if (!"DUAL".equals(uppname)) {
                     tables.add(uppname);
                     if (uppname.indexOf('.') < 0)
                        appendDbPrefix();
                  }
                  tablePosition = false;
               }
               break;

            case VARIABLE :
               variables.add(value);
               variablePositions.add(sql.length());
               sql.append("?");
               break;

            case PROCEDURE :
               procedures.add(value);
               if (!hasDbPrefix())
                  appendDbPrefix();
               if (currentProc == null) {
                  currentProc = new SQLProcedure(value, !tableArea, null);
                  sqlProcedures.add(currentProc);
               }
               else {
                  currentProc = currentProc.addProcedureParam(value, !tableArea);
               }
               currentProc.setStart(sql.length());

               break;

            case WITH :
               if(isWithClause()) {
                  pushWithClause();
                  if(tableArea) {
                     if(clsLog.debug)
                        clsLog.debug("  Deactivate table area until end of corresponding select statement (paren_level = &1)", parenLevel);
                     pushTableArea();
                  }
               }
               break;

            case TABLE_FUNCTION:
               if (tableArea) {
                  if(clsLog.debug)
                     clsLog.debug("  Deactivate table area until end of nested table statement (paren_level = &1)", parenLevel);
                  pushTableArea();
               }
               break;

            case SELECT :
               if (tableArea) {
                  if(clsLog.debug)
                     clsLog.debug("  Deactivate table area until end of nested select statement (paren_level = &1)", parenLevel);
                  pushTableArea();
               }
               if(parenLevel == withParenLevel) {
                  if(clsLog.debug)
                     clsLog.debug("  Closing define phase of the current WITH clause (paren_level = &1)", parenLevel);
                  currentWithClause = null;
               }
               else if(currentWithClause != null && parenLevel == withParenLevel + 1) {
                  String name = getQueryName();
                  if(name != null) {
                     addQueryName(name);
                  }
               }
               break;

            case EOF :
               closeParameterlessProcedure();
               return;

            default :
               break;
         }

         if (type != VARIABLE)
            sql.append(value);
      }
   }

   /**
    * Checks if it's necessary and eventually closes a parameter-less procedure.
    */
   private void closeParameterlessProcedure() {
      if(currentProc != null && currentProc.bracks == 0 && currentProc.getEnd() == 0 && !"(".equals(value)) {
         currentProc.setEnd(sql.length());
         currentProc = currentProc.parent;
      }
   }

   /**
    * Sets extractParenLevel if the current token is a call to EXTRACT function.
    */
   private void enterExtractFunction() throws Exception {
      if("EXTRACT".equalsIgnoreCase(value)) {
         tokenizer.mark();
         if (tokenizer.nextNonSpaceToken() == '(') {
            extractParenLevel = parenLevel + 1;
         }
         tokenizer.reset();
      }
   }

   /**
    * Returns true if previous non-space token was '.'.
    */
   private boolean hasDbPrefix() {
      String prev = getNonSpaceTokenValue(-1);
      if(clsLog.debug) {
         clsLog.debug("   hasDbPrefix(): prev='&1' &2", prev, showNonSpaceTokenBuffer());
      }
      return ".".equals(prev);
   }

   private void appendDbPrefix() {
      if (Str.isEmpty(dbPrefix))
         return;
      sql.append(dbPrefix);
   }

   private static String tokenTypeName(int type) {
      switch (type) {
         case EOF :
            return "EOF";
         case OTHER :
            return "OTHER";
         case STRING :
            return "STRING";
         case ID :
            return "ID";
         case SELECT :
            return "SELECT";
         case BEGIN_TA :
            return "BEGIN_TA";
         case END_TA :
            return "END_TA";
         case TABLE :
            return "TABLE";
         case PROCEDURE :
            return "PROCEDURE";
         case VARIABLE :
            return "VARIABLE";
         case TABLE_FUNCTION :
            return "TABLE_FUNCTION";
         case JOIN :
            return "JOIN";
         case WITH :
            return "WITH";
         default :
            return "Invalid Token Type: " + type;
      }
   }

   private String[] toStringArray(ArrayList<String> arr) {
      return arr.toArray(new String[arr.size()]);
   }

   public String getSQLText() {
      String sqlText = sql.toString();
      if(log.debug) log.debug("SQL text: &1",sqlText);
      return sqlText;
   }

   public String[] getTables() {
      return toStringArray(tables);
   }

   public String[] getProcedures() {
      return toStringArray(procedures);
   }

   public SQLProcedure[] getSQLProcedure() {
      return sqlProcedures.toArray(new SQLProcedure[sqlProcedures.size()]);
   }

   public String[] getVariables() {
      return toStringArray(variables);
   }

   /**
    * Gets the position of the bind variable at the specified index.
    * Note that all bind variable are replaced with "?" placeholders.
    * @return the position in the SQL text where the specified bind variable has been found
    */
   public int getVariablePosition(int index) {
      return variablePositions.get(index);
   }

   public static void main(String[] arg) throws Exception {
      String text = IoUtil.readFile(arg[0]);
      SQLRecognizer st = new SQLRecognizer(text, "IFSAPP.");
      debug(text);
      debug("\nsql:\n" + st.getSQLText());
      debug("tables", st.getTables());
      debug("procedures", st.getProcedures());
      debug("variables", st.getVariables());
   }

   private static void debug(String line) {
      IoUtil.stdoutln(line);
   }

   private static void debug(String head, String[] arr) {
      debug("\n" + head + ":");
      for (int i = 0; i < arr.length; i++) {
         debug("   " + arr[i]);
      }
   }

   //==========================================================================
   //  Keyword table
   //==========================================================================

   private static class Keyword {
      //String id;
      int type;

      Keyword(String id, int type) {
         //this.id = id;
         this.type = type;
      }
   }

   private static final Map<String, Keyword> KEYWORD_TABLE;

   private static final String[] BEGIN_OF_TABLE_AREA = { "FROM" };

   private static final String[] END_OF_TABLE_AREA =
      { "WHERE", "START", "CONNECT", "GROUP", "UNION", "INTERSECT", "MINUS", "ORDER", "FOR" };

   static {
      KEYWORD_TABLE = new HashMap<>();
      add(BEGIN_OF_TABLE_AREA, BEGIN_TA);
      add(END_OF_TABLE_AREA, END_TA);
      add("SELECT", SELECT);
      add("TABLE", TABLE_FUNCTION);
      add("JOIN", JOIN);
      add("WITH", WITH);
   }

   private static void add(String keyword, int type) {
      if(KEYWORD_TABLE.put(keyword, new Keyword(keyword, type)) != null)
         throw new RuntimeException("Duplicate keyword "+keyword+" in keyword table");
   }

   private static void add(String[] keys, int type) {
      for (int i = 0; i < keys.length; i++) {
         add(keys[i], type);
      }
   }

   //==============================================================
   // Inner Class SQLProcedure
   //==============================================================

   public class SQLProcedure {
      String name;
      ArrayList<SQLProcedure> procedureParams;
      SQLProcedure parent;
      int start, end, bracks;
      boolean inSelect;

      public SQLProcedure(String name, boolean inSelect, SQLProcedure parent) {
         this.name = name;
         procedureParams = Util.newArrayList();
         this.parent = parent;
         this.inSelect = inSelect;
         bracks = 0;
      }

      private SQLProcedure addProcedureParam(String name, boolean bselect) {
         SQLProcedure param = new SQLProcedure(name, bselect, this);
         procedureParams.add(param);
         return param;
      }

      private void setStart(int start) {
         this.start = start;
      }

      private void setEnd(int end) {
         this.end = end;
      }

      private void setBrackets(int bracks) {
         this.bracks = bracks;
      }

      private int getBrackets() {
         return bracks;
      }

      public SQLProcedure[] getProceduralParams() {
         return procedureParams.toArray(new SQLProcedure[procedureParams.size()]);
      }

      public String getName() {
         return name;
      }

      public SQLProcedure getParent() {
         return parent;
      }

      public int getStart() {
         return start;
      }

      public int getEnd() {
         return end;
      }

      public boolean isInSelect() {
         return inSelect;
      }
   }

   //==============================================================
   // Forbidden sequences of tokens
   //==============================================================

   // The first token of every forbidden sequence of tokens is inserted into the keyword table
   // with the type, which is less or equal to 0. This type, after changing its sign,
   // becomes the index to the remaining tokens stored in the forbidden sequence list.
   // If a forbidden sequence of tokens is found then an FndException is thrown.
   // Note that comments and string literals will never match forbidden tokens, because SQLTokenizer
   // ignores comments and returns a string literal as one token inside quotation marks.
   // Instead of remaining tokens an implementation of CustomChecker may be inserted into
   // the forbidden sequence list.

   /**
    * The index that identifies a forbidden sequence. (token type = -forbiddenIndex).
    */
   private static int forbiddenIndex = 0;

   /**
    * The list with forbidden sequences, indexed by forbiddenIndex.
    */
   private static final ArrayList<Object> forbiddenSequence = Util.newArrayList();

   /**
    * Interface used to perform custom check for a forbidden sequence of tokens.
    */
   interface CustomChecker {

      /**
       * Checks if the current token is the first token in a forbidden sequence.
       * The method may consume tokens but it cannot call tokenizer.mark(),
       * which is used by the calling method.
       * @return false to reject current PLSQL statement, true to accept it
       */
      boolean check(SQLRecognizer r) throws Exception;

      /**
       * Gets an error message describing the failed check.
       * @return String that will be used to construct an FndException.
       */
      String getErrorMessage();
   }

   /**
    * Adds a custom check to the keyword table and to the forbidden sequence list.
    */
   private static void addCustomCheck(String firstToken, CustomChecker checker) {

      // add the first token to the keyword table with type <= 0
      add(firstToken, -forbiddenIndex);

      // add CustomChecker instance (instead of remaining tokens) to the forbidden sequence list
      forbiddenSequence.add(checker);

      // prepare the index for the next forbidden sequence
      forbiddenIndex++;
   }

   /**
    * Adds one forbidden sequence to the keyword table and to the forbidden sequence list.
    */
   private static void addForbiddenSequence(String[] seq) {

      // add the first token to the keyword table with type <= 0
      add(seq[0], -forbiddenIndex);

      // add remaining tokens, if any, to the forbidden sequence list
      String[] remainingTokens = new String[seq.length - 1];
       System.arraycopy(seq, 1, remainingTokens, 0, seq.length - 1);
      forbiddenSequence.add(remainingTokens);

      // prepare the index for the next forbidden sequence
      forbiddenIndex++;
   }

   /**
    * Adds all forbidden sequences to the keyword table and to the forbidden sequence list.
    * Null matches any token.
    * A limitation is that the first token must be unique, it must identify the whole sequence.
    * To work-around this limitation a CustomChecker may be used.
    */
   static {
      addForbiddenSequence(new String[]{"DBMS_SESSION", ".", "SET_ROLE"});
      addForbiddenSequence(new String[]{"SET", "ROLE"});
      addForbiddenSequence(new String[]{"DBMS_SQL"});
      addForbiddenSequence(new String[]{"EXECUTE", "IMMEDIATE"});
      addForbiddenSequence(new String[]{"ALTER"});
      addForbiddenSequence(new String[]{"LOGIN_SYS"});

      addForbiddenSequence(new String[]{"DBMS_JAVA_TEST"});
      addForbiddenSequence(new String[]{"DBMS_LOCK"});
      addForbiddenSequence(new String[]{"DBMS_PIPE"});
      addForbiddenSequence(new String[]{"DBMS_RANDOM"});
      addForbiddenSequence(new String[]{"UTL_FILE"});
      addForbiddenSequence(new String[]{"UTL_HTTP"});
      addForbiddenSequence(new String[]{"UTL_SMTP"});
      addForbiddenSequence(new String[]{"UTL_TCP"});

      addForbiddenSequence(new String[]{"DBMS_DEBUG"});
      addForbiddenSequence(new String[]{"DBMS_JOB"});
      addForbiddenSequence(new String[]{"DBMS_RULE"});
      addForbiddenSequence(new String[]{"DBMS_TRANSFORM"});

      addForbiddenSequence(new String[]{"DBMS_DEFER"});
      addForbiddenSequence(new String[]{"DBMS_DEFERGEN"});
      addForbiddenSequence(new String[]{"DBMS_DEFERGEN_AUDIT"});
      addForbiddenSequence(new String[]{"DBMS_DEFERGEN_INTERNAL"});
      addForbiddenSequence(new String[]{"DBMS_DEFERGEN_LOB"});
      addForbiddenSequence(new String[]{"DBMS_DEFERGEN_PRIORITY"});
      addForbiddenSequence(new String[]{"DBMS_DEFERGEN_RESOLUTION"});
      addForbiddenSequence(new String[]{"DBMS_DEFERGEN_UTIL"});
      addForbiddenSequence(new String[]{"DBMS_DEFERGEN_WRAP"});
      addForbiddenSequence(new String[]{"DBMS_DEFER_ENQ_UTL"});
      addForbiddenSequence(new String[]{"DBMS_DEFER_IMPORT_INTERNAL"});
      addForbiddenSequence(new String[]{"DBMS_DEFER_INTERNAL_QUERY"});
      addForbiddenSequence(new String[]{"DBMS_DEFER_INTERNAL_SYS"});
      addForbiddenSequence(new String[]{"DBMS_DEFER_QUERY"});
      addForbiddenSequence(new String[]{"DBMS_DEFER_QUERY_UTL"});
      addForbiddenSequence(new String[]{"DBMS_DEFER_REPCAT"});
      addForbiddenSequence(new String[]{"DBMS_DEFER_SYS"});
      addForbiddenSequence(new String[]{"DBMS_DEFER_SYS_PART1"});

      addCustomCheck("OPEN", new OpenForCustomChecker()); // rejects dynamic versions of OPEN FOR statement
   }

   /**
    * Class rejecting forbidden forms of OPEN FOR statement.
    */
   private static class OpenForCustomChecker implements CustomChecker {

      /**
       * Checks if the current token is the first token in a forbidden sequence.
       * The method may consume tokens but it cannot call tokenizer.mark(),
       * which is used by the calling method.
       * @return false to reject current PLSQL statement, true to accept it
       */
      @Override
      public boolean check(SQLRecognizer r) throws Exception {

         // After "OPEN [:]variable FOR" "SELECT" is accepted, but everything else fails.

         // optional ':'
         int type = r.tokenizer.nextNonSpaceToken();
         if(type == ':')
             type = r.tokenizer.nextNonSpaceToken();

         // variable name
         if(type > 0 || r.tokenizer.getValue() == null)
            return true;

         // FOR
         r.tokenizer.nextNonSpaceToken();
         if(!"FOR".equalsIgnoreCase(r.tokenizer.getValue()))
            return true;

         // SELECT
         r.tokenizer.nextNonSpaceToken();
         return "SELECT".equalsIgnoreCase(r.tokenizer.getValue());
      }

      /**
       * Gets an error message describing the failed check.
       * @return String that will be used to construct an FndException.
       */
      @Override
      public String getErrorMessage() {
         return "FNDSQLFORBIDDEN_OPENFOR: Forbidden version of \"OPEN FOR\" statement rejected";
      }
   }

   /**
    * Checks if the current token is the first token in a forbidden sequence.
    */
   private void checkForbiddenKeywordSequence() throws Exception {
      //System.out.println("checkForbiddenKeywordSequence():");
      int ix = -type;

      tokenizer.mark();
      Object obj = forbiddenSequence.get(ix);

      if(obj instanceof CustomChecker) {
         //
         // Perform custom check
         //
         CustomChecker checker = (CustomChecker) obj;
         if(!checker.check(this))
            throw new FndException(checker.getErrorMessage());
         tokenizer.reset();
         return;
      }

      //
      // Perform standard check
      //
      String[] seq = (String[]) obj;
      for(int i = 0; i < seq.length; i++) {
         String forbiddenToken = seq[i];
         int currentType = tokenizer.nextNonSpaceToken();
         String currentToken = currentType > 0 ? String.valueOf((char)currentType) : tokenizer.getValue();
         //System.out.println(i+": currentType="+currentType+" currentToken="+currentToken+" forbiddenToken="+forbiddenToken);
         if(forbiddenToken != null && !forbiddenToken.equalsIgnoreCase(currentToken)) {
            tokenizer.reset();
            return;
         }
      }
      tokenizer.reset();
      AutoString stmt = new AutoString();
      stmt.append('"');
      stmt.append(value.toUpperCase());
      for(int i = 0; i < seq.length; i++) {
         stmt.append(" ");
         stmt.append(seq[i]);
      }
      stmt.append('"');
      throw new FndException("FNDSQLFORBIDDEN: Forbidden PLSQL code rejected: " + stmt);
   }

   //==============================================================
   // TableArea
   //==============================================================

   /**
    * Class describing an area of SQL/PLSQL text where a reference to a table or view may by found.
    */
   private static class TableArea {
      int parenLevel;          // parenLevel of ")" that activates previously deactivated table area
      int tableAreaParenLevel; // parenLevel stored when FROM keyword has opened tableArea
   }

   /**
    * Clears all instance variables describing current table area.
    */
   private void endTableArea() {
      tableArea = false;
      tableAreaParenLevel = -1;
      tablePosition = false;
   }

   /**
    * Starts a new table area.
    */
   private void beginTableArea() {
      tableArea = true;
      tableAreaParenLevel = parenLevel;
      tablePosition = true;
   }

   /**
    * Stores on a stack and clears the current table area.
    */
   private void pushTableArea() {
      TableArea ta = new TableArea();
      ta.parenLevel = type == SELECT ? parenLevel : parenLevel + 1;
      ta.tableAreaParenLevel = tableAreaParenLevel;
      taStack.push(ta);
      if(clsLog.debug)
         clsLog.debug("Pushed TableArea: parenLevel=&1 tableAreaParenLevel=&2", ta.parenLevel, ta.tableAreaParenLevel);
      endTableArea();
   }

   /**
    * Restores the current table area from a stack.
    */
   private void popTableArea() {
      TableArea ta = taStack.pop();
      tableArea = true;
      tableAreaParenLevel = ta.tableAreaParenLevel;
      tablePosition = false;
   }

   //==============================================================
   // WithClause
   //==============================================================

   /**
    * Class describing a WITH clause (subquery factoring clause) that can define new query names.
    */
   private static class WithClause {
      int parenLevel;          // parenLevel stored when WITH keyword has opened this clause
      Set<String> queryNames;  // set of query names defined by this WITH clause
   }

   /**
    * Checks if the current WITH keyword starts a sub-query factoring clause.
    * @return true if there was no previous non-space token or if the previous non-space token was "("
    */
   private boolean isWithClause() {
      String prev = getNonSpaceTokenValue(-1);
      if(clsLog.debug) {
         clsLog.debug("   isWithClause(): prev='&1' &2", prev, showNonSpaceTokenBuffer());
      }
      return prev == null || "(".equals(prev);
   }

   /**
    * Creates and stores on the stack a new WITH clause.
    */
   private void pushWithClause() throws FndException {
      WithClause wc = new WithClause();
      wc.queryNames = Util.newHashSet();
      wc.parenLevel = parenLevel;
      withParenLevel = parenLevel;
      withStack.push(wc);
      if(currentWithClause != null) {
         throw new FndException("FNDSQLNESTEDWITH: Nested WITH clause is not allowed");
      }
      currentWithClause = wc;
      if(clsLog.debug) {
         clsLog.debug("Pushed WithClause: withParenLevel=&1 globalQueryNames=&2", withParenLevel, globalQueryNames);
      }
   }

   /**
    * Removes current WITH clause from the stack.
    */
   private void popWithClause() {
      WithClause wc = withStack.pop();
      globalQueryNames.removeAll(wc.queryNames);
      withParenLevel = withStack.empty() ? -1 : withStack.peek().parenLevel;
      if(clsLog.debug) {
         clsLog.debug("Poped WithClause: parenLevel=&1 globalQueryNames=&2", wc.parenLevel, globalQueryNames);
      }
   }

   /**
    * Adds a query name both to local and to global set with query names.
    */
   private void addQueryName(String name) {
      name = name.toUpperCase();
      globalQueryNames.add(name);
      withStack.peek().queryNames.add(name);
      if(clsLog.debug) {
         clsLog.debug("Added &1 to globalQueryNames=&2", name, globalQueryNames);
      }
   }

   /**
    * Adds a consumed non-space token to the non-space token buffer.
    */
   private void addNonSpaceToken() {
      int ix = nonSpaceTokenNr % NON_SPACE_TOKEN_BUFFER_SIZE;
      nonSpaceTokenType[ix] = type;
      nonSpaceTokenValue[ix] = value;
      nonSpaceTokenNr++;
   }

   /**
    * Gets the value of a consumed token from the non-space token buffer.
    * @param pos negative position of a the token: -1 = previous, -2 = before previous, ... etc
    * @retuirn token value or null if undefined
    */
   private String getNonSpaceTokenValue(int pos) {
      int nr = nonSpaceTokenNr - 1 + pos;
      return nr < 0 ? null : nonSpaceTokenValue[nr % NON_SPACE_TOKEN_BUFFER_SIZE];
   }

   /**
    * Gets the type of a consumed token from the non-space token buffer.
    * @param pos negative position of a the token: -1 = previous, -2 = before previous, ... etc
    * @retuirn token type or EOF if undefined
    */
   private int getNonSpaceTokenType(int pos) {
      int nr = nonSpaceTokenNr - 1 + pos;
      return nr < 0 ? EOF : nonSpaceTokenType[nr % NON_SPACE_TOKEN_BUFFER_SIZE];
   }

   /**
    * Retrieves query name if the current SELECT keyword starts a named query in a WITH clause.
    * @return query name or null
    */
   private String getQueryName() {
      if("(".equals(getNonSpaceTokenValue(-1)) && "AS".equalsIgnoreCase(getNonSpaceTokenValue(-2)) && getNonSpaceTokenType(-3) == ID) {
         return removeDoubleQuotes(getNonSpaceTokenValue(-3));
      }
      return null;
   }

   private String showNonSpaceTokenBuffer() {
      StringBuilder buf = new StringBuilder();
      buf.append(" NonSpaceTokenBuffer: nr=").append(nonSpaceTokenNr).append(" ix=").append(nonSpaceTokenNr % NON_SPACE_TOKEN_BUFFER_SIZE).append(" {");
      for(int i = 0; i < NON_SPACE_TOKEN_BUFFER_SIZE; i++) {
         if(i > 0) {
            buf.append(", ");
         }
         String t = tokenTypeName(nonSpaceTokenType[i]);
         String v = nonSpaceTokenValue[i];
         buf.append(t).append(':').append(v);
      }
      buf.append("}");
      return buf.toString();

   }
}
