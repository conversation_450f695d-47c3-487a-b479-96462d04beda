/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */


package ifs.fnd.service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * IFS specific replacement for functionality offered by <tt>java.lang.Runtime.addShutdownHook()</tt>.
 * Create your hook classes by extending this class instead of <tt>java.lang.Thread</tt>
 * and implementing the run() method. Use the static method addShutdownHook() from
 * this class to register your hook.<p>
 * The managed shutdown procedure can be started by the Java VM in a shutdown hook operation,
 * but also from other hooks that shouldn't be started before this hook is done, i.e. loggers.
 */
public abstract class IfsShutdownHook
{
   //===============================================================================
   //  Static declarations
   //===============================================================================

   private static final ShutdownHook hook  = new ShutdownHook();
   private static final List<HookWrapper> ifsHooks  = new ArrayList<>();
   private static boolean finished   = false;
   private static boolean inShutdown = false;


   //===============================================================================
   //  Class API
   //===============================================================================

   /**
    * Protected constructor.
    */
   protected IfsShutdownHook()
   {
   }

   /**
    * Implement this method to perform any action that should be taken during the
    * shutdown sequence. Refer to <tt>java.lang.Runtime.addShutdownHook()</tt>
    * for consideration about implementation of the hook process.
    *
    * @see java.lang.Runtime#addShutdownHook
    */
   public abstract void run();


   //===============================================================================
   //  Inner classes
   //===============================================================================

   /**
    * Performs a clean shutdown of the server. An instance of this class
    * is registered as shutdown hook at java.lang.Runtime.
    */
   private static class ShutdownHook extends Thread
   {
      @Override
      public void run()
      {
         startHooks();
      }

      private synchronized void startHooks()
      {
         if( !finished )
         {
            // start all hooks registered in ifsHooks
            for( Iterator i = ifsHooks.iterator(); i.hasNext(); )
            {
               ((HookWrapper)(i.next())).start();
            }

            // ... and wait unfill all have finished
            for( Iterator i = ifsHooks.iterator(); i.hasNext(); )
            {
               try
               {
                  ((HookWrapper)(i.next())).join();
               }
               catch( InterruptedException x )
               {
                  continue;
               }
            }

            finished = true;
         }
      }
   }

   /**
    * Thread encapsulation of IFS specific hooks.
    */
   private static class HookWrapper extends Thread
   {
      private IfsShutdownHook ifsHook;

      private HookWrapper( IfsShutdownHook hook )
      {
         this.ifsHook = hook;
      }

      @Override
      public void run()
      {
         ifsHook.run();
      }
   }

   //===============================================================================
   //  Public static API
   //===============================================================================

   /**
    * Registers a new IFS specific virtual-machine shutdown hook.
    * Refer to <tt>java.lang.Runtime.addShutdownHook()</tt>
    * for consideration about implementation of the hook process.
    *
    * @param hook an instance extending this class and implementing
    *             the <tt>run</tt> method
    *
    * @see java.lang.Runtime#addShutdownHook
    */
   public static void addShutdownHook( IfsShutdownHook hook )
   {
      synchronized(hook)
      {
         ifsHooks.add(new HookWrapper(hook));
      }
   }

   /**
    * Initiates shutdown procedure before VM will do it. To be called from other hooks
    * to guarantee that this hook will finish before the caller.
    * The shutdown sequence is initiated only if the VM's shutdown procedure has already
    * started.
    */
   public static void startHooks()
   {
      synchronized(hook)
      {
         if( shutdownInProgress() )
            hook.startHooks();
      }
   }

   //===============================================================================
   //  Help routines
   //===============================================================================

   /**
    * Checks if the shutdown procedure has already been initiated by VM.
    */
   private static boolean shutdownInProgress()
   {
      if( inShutdown )
         return true;

      Thread dummy = new Thread();
      try
      {
         Runtime runtime = Runtime.getRuntime();
         runtime.addShutdownHook(dummy);
         runtime.removeShutdownHook(dummy);

         return false;
      }
      catch( IllegalStateException e )
      {
         inShutdown = true;
         return true;
      }
   }

   /**
    * Registers the only shutdown hook at VM.
    */
   static
   {
      // Register the only IFS shutdown hook
      Runtime.getRuntime().addShutdownHook(hook);
   }
}

