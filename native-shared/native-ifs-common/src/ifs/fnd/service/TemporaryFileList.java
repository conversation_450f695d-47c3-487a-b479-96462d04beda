/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */


package ifs.fnd.service;

import ifs.fnd.log.LogMgr;
import ifs.fnd.log.Logger;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.RandomAccessFile;
import java.io.Reader;
import java.io.Writer;
import java.text.NumberFormat;
import java.util.ArrayList;

/**
 * Class representing a list with temporary files.
 * A call to delete() deletes all files on the list.
 * The files may be also deleted individually, by calling delete() on an instance
 * of TemporaryFile returned by the add() method.
 * The same file combined with different streams may be added to the same list.
 * <p>
 * If the Java VM is terminated in a way that delete() method is not called then the files
 * will be deleted during the normal shutdown of the Java VM.
 * If the Java VM is aborted, without shutting down cleanly then
 * there is no guarantee that the files will be deleted.
 * <p>
 * Note! Every instance of TemporaryFileList registers a Java VM shutdown hook.
 * The method delete() must be called to de-register this shutdown hook, otherwise
 * the list with shutdown hooks may grow infinitely. It is therefore recommended
 * to call delete() from a finally clause.
 */
public class TemporaryFileList {

   private ArrayList<TemporaryFile> tmpFiles = Util.newArrayList();

   /**
    * Shutdown hook registered by constructor, de-registered by delete().
    */
   private ShutdownHook shutdownHook;

   /**
    * Set by the shutdown hook.
    */
   private volatile boolean shutdownInProgress;

   /**
    * Number formatter shared by all files on the list.
    */
   private NumberFormat intFormat;

   /**
    * Creates a new TemporaryFileList that registers a new Java VM shutdown hook.
    * The method delete() must be called to de-register this shutdown hook.
    * @see #add
    * @see #delete
    */
   public TemporaryFileList() {
      shutdownHook = new ShutdownHook();
      Runtime.getRuntime().addShutdownHook(shutdownHook);
      intFormat = Util.getIntegerFormat();
   }

   NumberFormat getIntegerFormat() {
      return intFormat;
   }

   /**
    * Add a temporary file to this list.
    * @param file a temporary file
    * @param stream the stream to be closed before deleting the file
    * @return an instance of TemporaryFile encapsulating the specified file and stream
    * @see #delete
    */
   public TemporaryFile add(File file, InputStream stream) {
      TemporaryFile t = new TemporaryFile(this, file, stream);
      tmpFiles.add(t);
      return t;
   }

   /**
    * Add a temporary file to this list.
    * @param file a temporary file
    * @param stream the stream to be closed before deleting the file
    * @return an instance of TemporaryFile encapsulating the specified file and stream
    * @see #delete
    */
   public TemporaryFile add(File file, OutputStream stream) {
      TemporaryFile t = new TemporaryFile(this, file, stream);
      tmpFiles.add(t);
      return t;
   }

   /**
    * Add a temporary file to this list.
    * @param file a temporary file
    * @param stream the stream to be closed before deleting the file
    * @return an instance of TemporaryFile encapsulating the specified file and stream
    * @see #delete
    */
   public TemporaryFile add(File file, Reader stream) {
      TemporaryFile t = new TemporaryFile(this, file, stream);
      tmpFiles.add(t);
      return t;
   }

   /**
    * Add a temporary file to this list.
    * @param file a temporary file
    * @param stream the stream to be closed before deleting the file
    * @return an instance of TemporaryFile encapsulating the specified file and stream
    * @see #delete
    */
   public TemporaryFile add(File file, Writer stream) {
      TemporaryFile t = new TemporaryFile(this, file, stream);
      tmpFiles.add(t);
      return t;
   }

   /**
    * Add a temporary file to this list.
    * @param file a temporary file
    * @param stream the stream to be closed before deleting the file
    * @return an instance of TemporaryFile encapsulating the specified file and stream
    * @see #delete
    */
   public TemporaryFile add(File file, RandomAccessFile stream) {
      TemporaryFile t = new TemporaryFile(this, file, stream);
      tmpFiles.add(t);
      return t;
   }

   /**
    * Delete all temporary files on this list.
    */
   public synchronized void delete() {
      Logger log = LogMgr.getFrameworkLogger();
      if(!shutdownInProgress) {
         try {
            Runtime.getRuntime().removeShutdownHook(shutdownHook);
         }
         catch(Throwable any) {
            log.warning(any);
         }
      }

      int count = tmpFiles.size();
      if(log.info)
         log.info("Deleting &1 temporary files from list &2", String.valueOf(count), toString());

      // first close all streams on the list
      for(int i = 0; i < count; i++) {
         TemporaryFile tmpFile = tmpFiles.get(i);
         tmpFile.closeStream();
      }

      // delete all files on the list
      for(int i = 0; i < count; i++) {
         TemporaryFile tmpFile = tmpFiles.get(i);
         tmpFile.deleteFile();
      }

      // and clear the list
      tmpFiles.clear();
   }

   /**
    * Remove a TemporaryFile from the internal list.
    */
   void remove(TemporaryFile file) {
      tmpFiles.remove(file);
   }


   private class ShutdownHook extends Thread {
      @Override
      public void run() {
         shutdownInProgress = true;
         delete();
      }
   }
}
