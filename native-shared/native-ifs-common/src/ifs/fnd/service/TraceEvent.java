/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.service;

/**
 * <B>Framework internal class:</B> Class representing a trace event.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class TraceEvent {
   public static final boolean DEBUG = false;

   public static boolean statisticsEnabled = false;
   static TraceEvent dummy = new TraceEvent(null);

   TraceEvent(TraceEventType type) {
   }

   public void end() {
   }

   public static boolean isEnabled() {
      return false;
   }
}
