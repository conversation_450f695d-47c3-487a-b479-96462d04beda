/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.service;

/**
 * <B>Framework internal class:</B> An interface used for log and trace ouputs.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public interface LogOutput {

   /**
    * Return true if the trace output is turned ON.
    */
   boolean isTraceOn();

   /**
    * Return true if the log output is turned ON.
    */
   boolean isLogOn();

   /**
    * Write a string to the log output, if the log output is turned ON.
    */
   void put(String text);

   /**
    * Write the caller's class name and a specified string to the log output.
    */
   void put(Object caller, String text);

   /**
    * Write a string to the trace output, if the trace output is turned ON.
    */
   void trace(String text);

   /**
    * Write the caller's class name and a specified string to the trace output.
    */
   void trace(Object caller, String text);

   /**
    * Write a stack trace for the specified Exception to the log and trace outputs.
    */
   void error(Throwable any);
}
