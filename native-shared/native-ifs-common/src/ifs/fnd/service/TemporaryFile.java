/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */


package ifs.fnd.service;

import ifs.fnd.log.LogMgr;
import ifs.fnd.log.Logger;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.RandomAccessFile;
import java.io.Reader;
import java.io.Writer;

/**
 * Class representing a file on a TemporaryFileList.
 * The method delete() may be used to delete this file, but it is also
 * possible to delete all files on the list by calling TemporaryFileList.delete().
 * Instances of this class are returned by the add() method on an instance of TemporaryFileList.
 */
public class TemporaryFile {

   private TemporaryFileList list;
   private File file;
   private Object stream; // one of: InputStream, OutputStream, Reader, Writer, RandomAccessFile

   TemporaryFile(TemporaryFileList list, File file, Object stream) {
      this.list = list;
      this.file = file;
      this.stream = stream;
   }

   /**
    * Close the stream and delete the file wrapped by this TemporaryFile.
    * Also remove this TemporaryFile from its TemporaryFileList.
    */
   public void delete() {
      closeStream();
      deleteFile();
      list.remove(this);
   }

   /**
    * Close the stream wrapped by this TemporaryFile.
    */
   void closeStream() {
      Logger log = LogMgr.getFrameworkLogger();
      try {
         if(stream instanceof InputStream)
            ((InputStream) stream).close();
         else if(stream instanceof OutputStream)
            ((OutputStream) stream).close();
         else if(stream instanceof Reader)
            ((Reader) stream).close();
         else if(stream instanceof Writer)
            ((Writer) stream).close();
         else if(stream instanceof RandomAccessFile)
            ((RandomAccessFile) stream).close();
         if(log.info)
            log.info("Closed temporary stream &1 on file &2", stream.toString(), file.getAbsolutePath());
      }
      catch(IOException e) {
         log.warning(e, "Could not close temporary stream &1 on file &2", stream.toString(), file.getAbsolutePath());
      }
   }

   /**
    * Delete the File wrapped by this TemporaryFile.
    */
   void deleteFile() {
      Logger log = LogMgr.getFrameworkLogger();
      String fileName = file.getAbsolutePath();
      if(!file.exists()) {
         log.info("Skipped not existing temporay file &1", fileName);
      }
      else {
         String fileSize = list.getIntegerFormat().format(file.length());
         if(file.delete())
            log.info("Deleted temporay file &1 of size &2 bytes", fileName, fileSize);
         else
            log.warning("Could NOT delete temporay file &1 of size &2 bytes", fileName, fileSize);
      }
   }

}
