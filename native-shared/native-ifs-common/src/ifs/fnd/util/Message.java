package ifs.fnd.util;

import ifs.fnd.log.LogMgr;
import ifs.fnd.log.Logger;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.text.ParseException;
import java.util.Enumeration;
import java.util.StringTokenizer;
import java.util.Vector;

/**
 * A message contains a header (message name) and a set of
 * {@link Attribute}s.
 * An attribute has a name (uppercase identifier, max 30 characters)
 * and a string value (one or many lines of visible characters).
 * A value can contain another message, so messages can be nested.
 * <p>
 * A message can be represented as a text file with the first column
 * containing following meta-characters, which controls the contents of
 * a corresponding line:
 * <pre>
 *     "!" - message header
 *     "$" - attribute name followed by "=" and attribute value
 *     "-" - continuation of attribute value (new-line included)
 *     "+" - continuation of attribute value (new-line excluded)
 *     " " - comment line
 * </pre>
 *
 * For example the following message represents a Command which will send
 * an e-mail to database user MACK:
 * <pre>
 *    !JAVA.COMMAND
 *    $CLASS=ifs.fnd.es.Mail
 *    $TO_USER=MACK
 *    $FROM_USER=FND
 *    $MSG=Hi Mack!
 * </pre>
 */
public class Message {
   private String head;
   private Vector<String> attributeName;
   private Vector<String> attributeValue;

   //-------------------------------------------------------------------------------
   //  constructors
   //-------------------------------------------------------------------------------

   /**
    * Creates an empty Message.
    */
   public Message() {
      head = null;
      attributeName = new Vector<>();
      attributeValue = new Vector<>();
   }

   /**
    * Creates a Message parsing the specified text buffer.
    */
   public Message(String textBuffer) throws InvalidMessageFormatException, IOException {
      this();
      parse(textBuffer);
   }

   /**
    * Transforms the specified text file into a Message object.
    */
   public static Message parseFile(String filename) throws InvalidMessageFormatException, IOException {
      String msg = IoUtil.readFile(filename);
      return new Message(msg);
   }

   //-------------------------------------------------------------------------------
   //  exist methods
   //-------------------------------------------------------------------------------

   /**
    * Returns true if this Message has no header and no attributes.
    */
   public boolean isEmpty() {
      return Str.isEmpty(head) && attributeName.isEmpty();
   }

   /**
    * Returns true if this Message has a named attribute.
    */
   public boolean existAttribute(String name) {
      return attributeName.indexOf(name) >= 0;
   }

   //-------------------------------------------------------------------------------
   //  add methods
   //-------------------------------------------------------------------------------

   /**
    * Add a new String attribute to this Message.
    */
   public void addAttribute(String name, String value) {
      attributeName.addElement(name);
      attributeValue.addElement(value);
   }

   /**
    * Add a new integer attribute to this Message.
    */
   public void addAttribute(String name, int value) {
      addAttribute(name, String.valueOf(value));
   }

   /**
    * Add a new Time attribute to this Message.
    */
   public void addAttribute(String name, Time value) {
      addAttribute(name, value.toString());
   }

   /**
    * Add a new boolean attribute to this Message.
    */
   public void addAttribute(String name, boolean value) {
      addAttribute(name, String.valueOf(value));
   }

   //-------------------------------------------------------------------------------
   //  concat methods, "+"
   //-------------------------------------------------------------------------------

   /**
    * Append a specified value (without new-line) to the named attribute.
    */
   public void concatAttribute(String name, String value) {
      int i = attributeName.indexOf(name);
      if (i < 0)
         addAttribute(name, value);
      else
         attributeValue.setElementAt(attributeValue.elementAt(i) + value, i);
   }

   //-------------------------------------------------------------------------------
   //  append methods, "-"
   //-------------------------------------------------------------------------------

   /**
    * Append a new-line and a specified value to the named attribute.
    */
   public void appendAttribute(String name, String value) {
      int i = attributeName.indexOf(name);
      if (i < 0)
         addAttribute(name, value);
      else
         attributeValue.setElementAt(attributeValue.elementAt(i) + "\n" + value, i);
   }

   /**
    *  set methods - set String/int/boolean/Time value for specified attribute,
    *                if attribute does exist its value is overwritten.
    */

   public void setAttribute(String name, boolean value) {
      setAttribute(name, String.valueOf(value));
   }

   public void setHeader(String head) {
      this.head = head;
   }

   public void setAttribute(String name, String value) {
      int i = attributeName.indexOf(name);
      if (i < 0)
         addAttribute(name, value);
      else
         attributeValue.setElementAt(value, i);
   }

   public void setAttribute(String name, int value) {
      setAttribute(name, String.valueOf(value));
   }

   public void setAttribute(String name, Time value) {
      setAttribute(name, value.toString());
   }

   /**
    *  Returns the String value of the specified attribute.
    */
   public String getStringAttribute(String name) throws AttributeNotFoundException {
      int i = attributeName.indexOf(name);
      if (i < 0)
         throw new AttributeNotFoundException(name);
      return attributeValue.elementAt(i);
   }

   /**
    *  Returns the boolean value of the specified attribute.
    */
   public boolean getBooleanAttribute(String name) throws AttributeNotFoundException {
      return getStringAttribute(name).equals("true");
   }

   /**
    *  Returns the Time value of the specified attribute.
    */
   public Time getTimeAttribute(String name) throws AttributeNotFoundException, ParseException {
      return new Time(getStringAttribute(name));
   }

   /**
    *  Returns the integer value of the specified attribute.
    */
   public int getIntAttribute(String name) throws AttributeNotFoundException {
      return Integer.parseInt(getStringAttribute(name));
   }

   /**
    *   Returns the header of this message.
    */
   public String getHeader() {
      return this.head;
   }

   /**
    *   Returns an array of references to all attributes in this message.
    */
   public Attribute[] getAttributes() {
      int size = attributeName.size();
      Attribute[] a = new Attribute[size];
      for (int i = 0; i < size; i++) {
         a[i] = new Attribute(attributeName.elementAt(i), attributeValue.elementAt(i));
      }
      return a;
   }

   /**
    *  find methods - return String/int/boolean/Time value for specified attribute name
    *  or default_value if the attribute does not exist.
    */
   public boolean findAttribute(String name, boolean defaultValue) {
      try {
         return getBooleanAttribute(name);
      }
      catch (AttributeNotFoundException e) {
         return defaultValue;
      }
   }

   public String findAttribute(String name, String defaultValue) {
      try {
         return getStringAttribute(name);
      }
      catch (AttributeNotFoundException e) {
         return defaultValue;
      }
   }

   public int findAttribute(String name, int defaultValue) {
      try {
         return getIntAttribute(name);
      }
      catch (AttributeNotFoundException e) {
         return defaultValue;
      }
   }

   public Time findAttribute(String name, Time defaultValue) throws ParseException {
      try {
         return getTimeAttribute(name);
      }
      catch (AttributeNotFoundException e) {
         return defaultValue;
      }
   }

   //--------------------------------------------------------------------------------
   //  remove functions
   //--------------------------------------------------------------------------------

   /**
    *  Removes an attribute with the specified name.
    *  If there are more attributes with this name only the first one will be removed.
    *  Returns false if there is no attributes with the specified name.
    */
   public boolean removeAttribute(String name) {
      int i = attributeName.indexOf(name);
      if (i < 0)
         return false;
      attributeName.removeElementAt(i);
      attributeValue.removeElementAt(i);
      return true;
   }

   //--------------------------------------------------------------------------------
   //  parse/format methods
   //--------------------------------------------------------------------------------

   /**
    *  Transforms given text buffer into internal state. The buffer
    *  must be in the format returned by format(), otherwise
    *  the InvalidMessageFormatException is thrown.
    *  @param textBuffer text to parse
    */
   public final void parse(String textBuffer) throws IOException, InvalidMessageFormatException {
      parse(textBuffer, true);
   }

   /**
    *  Transforms given text buffer into internal state. The buffer
    *  must be in the format returned by format(), otherwise
    *  the InvalidMessageFormatException is thrown.
    *  @param textBuffer text to parse
    *  @param logging true to enable logging, false to disable all logging from this method
    */
   public final void parse(String textBuffer, boolean logging) throws IOException, InvalidMessageFormatException {
      String name = null, value = null;

      BufferedReader in = new BufferedReader(new StringReader(textBuffer));
      Logger log = LogMgr.getFrameworkLogger();

      while (true) {
         String line = in.readLine();
         if (line == null)
            break;

         if (logging && log.debug) {
            log.debug(line);
         }
         if (".".equals(line))
            break;

         if (line.length() > 0) // ignore empty lines
            switch (line.charAt(0)) {
               case '$' :
                  //
                  //  New attribute definition. Store previous if exists. Init new one.
                  //
                  if (name != null)
                     addAttribute(name, value);
                  int pos = line.indexOf('=');
                  if (pos < 0)
                     throw error(line, "'=' missing");
                  if (pos == 1)
                     throw error(line, "Missing attribute name");
                  name = line.substring(1, pos);
                  value = line.substring(pos + 1);
                  break;

               case '-' :
                  //
                  //  Append <New-Line> and value.
                  //
                  if (name == null)
                     throw error(line, "Value without attribute name");
                  value = (value == null ? "" : value) + "\r\n" + line.substring(1);
                  break;

               case '+' :
                  //
                  //  Append value.
                  //
                  if (name == null)
                     throw error(line, "Value without attribute name");
                  value = (value == null ? "" : value) + line.substring(1);
                  break;

               case '!' :
                  setHeader(line.substring(1));
                  break;

               case '\t' :
               case ' ' :
                  //
                  //  Comments. Ignore.
                  //
                  break;

               case '#' :
                  //
                  //  New segment. Ignore.
                  //
                  break;

               default :
                  throw error(line, "Invalid first character");
            }
      }
      if (name != null)
         addAttribute(name, value);

      if (logging && log.debug) {
         log.debug("[End-Of-Message]\n");
      }
   }

   /**
    *   Returns internal state in text format.
    */
   public String format() {
      String newLine = "\n";

      if (isEmpty())
         return "";

      StringBuilder buffer = new StringBuilder();
      buffer.append("!");
      if(head != null)
         buffer.append(head);
      buffer.append(newLine);
      if (!attributeName.isEmpty()) {
         Enumeration n = attributeName.elements();
         Enumeration v = attributeValue.elements();
         while (n.hasMoreElements()) {
            buffer.append("$").append(n.nextElement()).append("=");
            String val = Str.nvl((String) v.nextElement(), "");
            if (val.length() == 0)
               buffer.append(newLine);
            else {
               String line = "";
               boolean first = true;
               StringTokenizer t = new StringTokenizer(val, "\r\n", true);
               while (t.hasMoreTokens()) {
                  String token = t.nextToken();
                  switch (token) {
                     case "\r":
                        continue;
                     case "\n":
                        buffer.append(first ? "" : "-").append(line).append(newLine);
                        line = "";
                        first = false;
                        break;
                     default:
                        line = token;
                        break;
                  }
               }
               if (line.length() > 0)
                  buffer.append(first ? "" : "-").append(line).append(newLine);
            }
         }
      }
      return buffer.toString();
   }

   /**
    * Return the text representation of of this Message as a String array.
    * The concatenation of all array elements would give the same result as a call
    * to format() with no arguments.
    * Each element of the array ends with a new-line, which can be useful
    * during parsing.
    */
   public String[] format(int maxLength) throws Exception {
      String nl = "\n";
      Vector<String> v = new Vector<>();
      StringTokenizer big = new StringTokenizer(format(), nl);
      StringBuilder buf = new StringBuilder(maxLength);

      while (big.hasMoreTokens()) {
         String line = big.nextToken();
         if (line.length() > maxLength)
            throw error(line, "Too long line. Cannot format into String array.");

         if (buf.length() + line.length() + nl.length() <= maxLength)
            buf.append(line).append(nl);
         else {
            v.add(buf.toString());
            buf.setLength(0);
            buf.append(line).append(nl);
         }
      }
      if (buf.length() > 0)
         v.add(buf.toString());

      String[] result = new String[v.size()];
      v.copyInto(result);
      return result;
   }

   //-------------------------------------------------------------------------------
   //  other methods
   //-------------------------------------------------------------------------------

   /**
    * Moves the specified attribute into another Message.
    */
   public boolean moveAttribute(String name, Message into) {
      int i = attributeName.indexOf(name);
      if (i < 0)
         return false;
      String value = findAttribute(name, "");
      removeAttribute(name);
      into.addAttribute(name, value);
      return true;
   }

   /**
    * Returns the number of attributes currently stored in this message.
    */
   public int countAttributes() {
      return attributeName.size();
   }

   /**
    * Returns length of this message's string representation which
    * would be created by format() method.
    */
   public int length() {
      int len = 2 + Str.length(head);

      Enumeration n = attributeName.elements();
      Enumeration v = attributeValue.elements();

      while (n.hasMoreElements()) {
         String name = (String) n.nextElement();
         String value = (String) v.nextElement();
         len += Str.length(name) + Str.length(value) + 3;
      }
      return len;
   }

   /**
    * Splits this message into an array of Messages. Each element of the array
    * will have text representation not longer than the specified max length.
    * The header will be copied into every element of the array.
    */
   public Message[] split(int maxLength) throws Exception {
      Vector<Message> vector = new Vector<>(); // of Message
      Message msg = null; // current message
      int len = 0; // length of current message

      int count = this.countAttributes();

      if (isEmpty())
         return new Message[0];

      for (int i = 0; i < count;) {
         if (msg == null) {
            msg = new Message();
            msg.setHeader(this.head);
            len = 2 + Str.length(head);
         }

         String name = attributeName.elementAt(i);
         String value = attributeValue.elementAt(i);

         len += Str.length(name) + Str.length(value) + 3;

         if (len <= maxLength) {
            msg.addAttribute(name, value);
            i++;
         }
         else if (msg.countAttributes() == 0)
            throw new Exception("Message.split(): Too large attribute");
         else {
            vector.add(msg);
            msg = null;
         }
      }

      if (msg != null)
         vector.add(msg);

      if (vector.size() > 0) {
         Message[] msgarr = new Message[vector.size()];
         vector.copyInto(msgarr);
         return msgarr;
      }
      else
         return new Message[0];
   }

   /**
    * In each attribute value of this Message, replace all occurences of '&XXX'
    * with the value of the attribute XXX, using the specified characters
    * as a macro reference 'begin' and 'end' markers. A macro identifier
    * must begin with a letter and can contain only letters and digits.
    * A macro reference begins with the 'begin_char' and ends with the
    * first character which is not a letter nor digit. The 'end_char'
    * used to mark the end of a macro reference will be removed.
    * Double 'begin_char'-character means 'begin_char' itself and not a macro
    * reference.
    */
   public void replaceMacroReferences(char beginChar, char endChar) throws Exception {
      int size = attributeName.size();
      for (int i = 0; i < size; i++) {
         String nam = attributeName.elementAt(i);
         String val = attributeValue.elementAt(i);
         val = replaceMacros(nam, val, beginChar, endChar);
         attributeValue.setElementAt(val, i);
      }
   }

   private String replaceMacros(String name, String value, char beginChar, char endChar) throws Exception {
      StringBuilder buf = new StringBuilder();
      int len = value.length();
      int i = 0;

      while (i < len) {
         int j = value.indexOf(beginChar, i);
         if (j < 0) {
            if (i == 0)
               return value;
            buf.append(value.substring(i));
            i = len;
         }
         else if (j + 1 < len && value.charAt(j + 1) == beginChar) {
            buf.append(value.substring(i, j));
            buf.append(beginChar);
            i = j + 2;
         }
         else {
            j++;

            if (j >= len || !Character.isLetter(value.charAt(j)))
               throw new Exception("Invalid first character of macro reference in '" + value + "' at position " + j);

            int k = j;

            while (k < len && isIdCharacter(value.charAt(k))) {
               k++;
            }

            String macroName = value.substring(j, k);

            if (name.equals(macroName))
               throw new Exception("Recursive macro reference in '" + value + "' at position " + j);

            String macroValue = getStringAttribute(macroName);
            buf.append(value.substring(i, j - 1));
            buf.append(replaceMacros(macroName, macroValue, beginChar, endChar));
            i = k;
            if (i < len && value.charAt(i) == endChar)
               i++;
         }
      }

      Logger log = LogMgr.getFrameworkLogger();
      if (log.debug) {
          log.debug("replaced macros: '&1' -> '&2'", value, buf.toString());
      }

      return buf.toString();
   }

   private boolean isIdCharacter(char ch) {
      return ch == '_' || Character.isLetterOrDigit(ch);
   }

   /**
    * Stores this message in a text file.
    */
   public void save(String filename) throws Exception {
      IoUtil.writeFile(filename, format());
   }

   private InvalidMessageFormatException error(String line, String msg) {
      return new InvalidMessageFormatException(msg + "\nin line:\n" + line);
   }

   @Override
   public String toString() {
      return format();
   }
}
