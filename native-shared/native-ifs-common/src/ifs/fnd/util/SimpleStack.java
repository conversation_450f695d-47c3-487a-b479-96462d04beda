/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.util;

import java.util.ArrayList;
import java.util.EmptyStackException;

/**
 * Simple unsynchronized implementation of stack.
 * @param <E> the type of elements stored on the stack
 */
public class SimpleStack<E> {

   private ArrayList<E> arr;

   /**
    * Creates an empty stack.
    */
   public SimpleStack() {
      arr = new ArrayList<>();
   }

   /**
    * Pushes an item onto the top of this stack.
    *
    * @param   item   the item to be pushed onto this stack.
    * @return  the <code>item</code> argument.
    */
   public E push(E item) {
      arr.add(item);
      return item;
   }

   /**
    * Removes the object at the top of this stack and returns that
    * object as the value of this function.
    *
    * @return The object at the top of this stack
    * @exception EmptyStackException if this stack is empty.
    */
   public E pop() {
      E obj;
      int len = arr.size();
      obj = peek();
      arr.remove(len - 1);
      return obj;
   }

   /**
    * Looks at the object at the top of this stack without removing it
    * from the stack.
    *
    * @return     the object at the top of this stack
    * @exception  EmptyStackException if this stack is empty.
    */
   public E peek() {
      int len = arr.size();
      if(len == 0)
         throw new EmptyStackException();
      return arr.get(len - 1);
   }

   /**
    * Tests if this stack is empty.
    * @return  <code>true</code> if and only if this stack contains
    *          no items; <code>false</code> otherwise.
    */
   public boolean empty() {
      return arr.isEmpty();
   }

   /**
    * Returns the 1-based position where an object is on this stack.
    * If the object <tt>o</tt> occurs as an item in this stack, this
    * method returns the distance from the top of the stack of the
    * occurrence nearest the top of the stack; the topmost item on the
    * stack is considered to be at distance <tt>1</tt>. The <tt>equals</tt>
    * method is used to compare <tt>o</tt> to the
    * items in this stack.
    *
    * @param   o   the desired object.
    * @return  the 1-based position from the top of the stack where
    *          the object is located; the return value <code>-1</code>
    *          indicates that the object is not on the stack.
    */
   public int search(Object o) {
      int i = arr.lastIndexOf(o);
      if(i >= 0) {
         return arr.size() - i;
      }
      return -1;
   }

   /**
    * Returns the number of items on this stack.
    * @return  the number of items in the stack
    */
   public int size() {
      return arr.size();
   }

   /**
    * Returns the item at the specified index.
    * This method is identical in functionality to the get method in the List interface.
    *
    * @param      index an index into this stack
    * @return     the item at the specified index
    * @exception  ArrayIndexOutOfBoundsException if the <tt>index</tt>
    *             is negative or not less than the current size of this stack
    */
   public E get(int index) {
      return arr.get(index);
   }

   @Override
   public String toString() {
      return arr.toString();
   }
}
