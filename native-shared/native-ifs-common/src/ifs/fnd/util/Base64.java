package ifs.fnd.util;

import java.io.*;

/**
 * Class for encoding/decoding binary data to/from Base64 format.
 */
public class Base64 {

   /**
    * OutputStream wrapper with empty close() method.
    */
   private static final class Base64OutputStream extends OutputStream {
      private final OutputStream os;

      private Base64OutputStream(OutputStream os) {
         this.os = os;
      }

      @Override
      public void write(int b) throws IOException {
         os.write(b);
      }

      @Override
      public void write(byte[] b) throws IOException {
         os.write(b);
      }

      @Override
      public void write(byte[] b, int off, int len) throws IOException {
         os.write(b, off, len);
      }

      @Override
      public void flush() throws IOException {
         os.flush();
      }

      @Override
      public void close() {
      }
   }

   public Base64() {
   }

   //--------------------------------------------------------------------------
   //  Encoding
   //--------------------------------------------------------------------------

   /**
    * Encode binary input stream into Base64 character stream.
    * The encoded data is written in max 76 character lines.
    * @param in  the <code>InputStream</code> to encode.
    * @param out the <code>OutputStream</code> to which the encoded data is written.
    * @throws IOException  if an I/O error occurs while encoding.
    */
   public void encode(InputStream in, OutputStream out) throws IOException {
      encode(in, out, true);
   }

   /**
    * Encode binary input stream into Base64 character stream.
    * @param in  the <code>InputStream</code> to encode.
    * @param out the <code>OutputStream</code> to which the encoded data is written.
    * @param newLines if <code>true</code> the encoded data is written in max 76 character lines.
    * @throws IOException  if an I/O error occurs while encoding.
    */
   public void encode(InputStream in, OutputStream out, boolean newLines) throws IOException {
      java.util.Base64.Encoder encoder = newLines ? java.util.Base64.getMimeEncoder() : java.util.Base64.getEncoder();
      /*
       * OutputStream passed to this method should not be closed.
       * Wrapped OutputStream returned from Base64.Encoder must be closed to generate padding characters.
       * The solution is to create a new output stream with empty close() method.
       */
      try (OutputStream wrappedOut = encoder.wrap(new Base64OutputStream(out))) {
         IoUtil.copy(in, wrappedOut);
      }
   }

   /**
    * Encode byte array into Base64 String.
    * @param data the byte array to encode
    * @param newLines if <code>true</code> the encoded data is written in max 76 character lines.
    * @return String containing the resulting Base64 encoded characters
    */
   public String encode(byte[] data, boolean newLines) {
      java.util.Base64.Encoder encoder = newLines ? java.util.Base64.getMimeEncoder() : java.util.Base64.getEncoder();
      return encoder.encodeToString(data);
   }

   //--------------------------------------------------------------------------
   //  Decoding
   //--------------------------------------------------------------------------

   /**
    * Decode Base64 input character stream into binary output stream.
    * @param in  the <code>InputStream</code> to decode.
    * @param out the <code>OutputStream</code> to write the decoded data to.
    * @throws IOException if an I/O error occurs while decoding.
    */
   public void decode(InputStream in, OutputStream out) throws IOException {
      java.util.Base64.Decoder decoder = java.util.Base64.getMimeDecoder();
      /**
       * InputStream passed to this method should not be closed.
       */
      InputStream wrappedIn = decoder.wrap(in);
      try {
         IoUtil.copy(wrappedIn, out);
      }
      catch(IllegalArgumentException e) {
         throw new IOException(e);
      }
   }

   /**
    * Decode Base64 input characters into a String representation of decoded binary stream.
    * @param  in the <code>InputStream</code> to decode.
    * @return String containing decoded binary stream
    */
   public String decode(InputStream in) throws IOException {
      ByteArrayOutputStream out = new ByteArrayOutputStream();
      decode(in, out);
      return new String(out.toByteArray(), "UTF-8");
   }

  /**
   * Decode Base64 encoded String into byte array.
   * @param b64 the string to decode
   * @return byte array containing the decoded bytes.
   */
   public byte[] decode(String b64) throws IOException {
      java.util.Base64.Decoder decoder = java.util.Base64.getMimeDecoder();
      try {
         return decoder.decode(b64);
      }
      catch(IllegalArgumentException e) {
         throw new IOException(e);
      }
   }
}
