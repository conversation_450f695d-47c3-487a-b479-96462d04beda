package ifs.fnd.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * An instance of this class represents a timestamp. This class uses the
 * following format: "yyyy-MM-dd-HH.mm.ss" for conversion between Time
 * objects and String objects.
 */
public class Time {
   private SimpleDateFormat frm = new SimpleDateFormat("yyyy-MM-dd-HH.mm.ss", Locale.US);
   private Date date;

   // YYYY-MM-DD-HH.MI.SS
   // 0   4  7  10 13 16

   /**
    * Create a Time object for the current time.
    */
   public Time() {
      this.date = new Date();
   }

   /**
    * Create a Time object for the specfied timestamp.
    * @param time timestamp in format "yyyy-MM-dd-HH.mm.ss"
    */
   public Time(String time) throws ParseException {
      date = frm.parse(time);
   }

   /**
    * Create a string representation of this Time object.
    * @return timestamp in format "yyyy-MM-dd-HH.mm.ss"
    */
   @Override
   public String toString() {
      return frm.format(date);
   }

   /**
    * Create a Time object for the specfied date.
    * @param date a Date object
    */
   public Time(Date date) {
      this.date = date;
   }

   /**
    * Create a Time object for the specfied time.
    * @param value the number of milliseconds from January 1, 1970 UTC
    */
   public Time(long value) {
      this.date = new Date(value);
   }

   /**
    * Convert this Time instance into a Date.
    * @return the Date corresponding to this Time instance
    */
   public Date toDate() {
      return date;
   }
}
