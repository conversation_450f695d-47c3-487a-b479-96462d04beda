/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.util;

import java.util.*;
import java.lang.ref.*;

//@deprecated Use java.util.WeakHashMap
public class FndSoftHashMap extends AbstractMap {
  //Internal HashMap that will hold the SoftReference
  private final Map<Object, SoftValue> hash = new HashMap<>();
  //Number of hard references to hold internally
  private final int hardSize;
  //FIFO list of hard references, order of last access
  private final LinkedList<Object> hardCache = new LinkedList<>();
  //Reference queue for cleared SoftReference objects
  private final ReferenceQueue<Object> queue = new ReferenceQueue<>();

  public FndSoftHashMap() {
     this(100);
  }

  public FndSoftHashMap(int hardSize) {
     this.hardSize = hardSize;
  }

  public Object get(Object key) {
    Object result = null;
    SoftReference softRef = (SoftReference)hash.get(key);
    if (softRef != null) {
      result = softRef.get();
      if (result == null) {
        //If the value has been garbage collected, remove the entry from the HashMap.
        hash.remove(key);
      }
      else {
        //Add this object to the beginning of the hard reference queue
        hardCache.addFirst(result);
        if (hardCache.size() > this.hardSize) {
          //Remove the last entry if the list is longer than hardSize
          hardCache.removeLast();
        }
      }
    }
    return result;
  }

  /**
   * Go through the ReferenceQueue and remove garbage collected SoftValue
   * objects from the HashMap by looking them up using the SoftValue.key data member.
   */
  private void processQueue() {
    SoftValue softValue;
    while ((softValue = (SoftValue)queue.poll()) != null) {
      hash.remove(softValue.key);
    }
  }

  /**
   * Put the key, value pair into the HashMap using a SoftValue object.
   */
  public Object put(Object key, Object value) {
    processQueue();
    return hash.put(key, new SoftValue(value, key, queue));
  }

  public Object remove(Object key) {
    processQueue(); //throw out garbage collected values first
    return hash.remove(key);
  }

  public void clear() {
    hardCache.clear();
    processQueue();
    hash.clear();
  }

  public int size() {
    processQueue(); //throw out garbage collected values first
    return hash.size();
  }

  public Set entrySet() {
    throw new UnsupportedOperationException();
  }

  /**
   * Subclass of SoftReference which contains not only the value but also
   * the key to make it easier to find the entry in the HashMap after it's
   * been garbage collected.
   */
     private static class SoftValue extends SoftReference<Object>{
    private final Object key;

    private SoftValue(Object k, Object key, ReferenceQueue<Object> q) {
      super(k, q);
      this.key = key;
    }
  }

}