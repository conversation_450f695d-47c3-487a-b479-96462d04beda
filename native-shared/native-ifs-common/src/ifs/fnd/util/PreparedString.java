/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.util;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * A <code>PreparedString</code> is a string (<code>pattern</code>) that contains
 * variables which later on are replaced with values. The resulting string is
 * obtained with the <code>toString()</code> method. A variable may <B>only</B>
 * occur once in the pattern.
 * <BR><BR>
 * Example:
 * <code>
 *    PreparedString ps = new PreparedString("Hello :NAME!");
 * </code>
 * <BR><BR>
 * Here, NAME becomes a variable. It's value is set with:
 * <code>ps.setValue("<PERSON><PERSON><PERSON>", "<PERSON> Doe");</code>
 * <BR><BR>
 * Single instances of ':' are removed from the result. To have an ':' in the
 * result, place two of them in the input pattern.
 * <BR><BR>
 * Valid characters for variable names are letters, digits, '_' & '-'.
 * All valid characters after a ':' makes out the variable's name.
 * <BR><BR>
 * The ':' is the default variable operator. It can be changed by using a
 * constructor accepting </code>variableOp</code>. Do <B>NOT</B> use a letter,
 * a digit or '-' or '_' as a variable operator!
 */
public final class PreparedString {
   /**
    * A String buffer to hold the pattern in.
    */
   private StringBuilder pattern;

   /**
    * A list holding all variables to set values for.
    */
   private List<Variable> variables;

   /**
    * If true, variables with no values set are kept unexpanded.
    */
   private boolean keepVars;

   /**
    * The operator used to identify variables.
    */
   private char variableOperator;

   /**
    * A pattern must be supplied when instantiating a <code>PreparedString</code>
    * object. With this constructor, unset variables are not kept in the resulting
    * string.
    */
   public PreparedString(String pattern) {
      this(pattern, false, ':');
   }

   /**
    * This constructor also sets whether to keep variables with no values in the
    * resulting string.
    */
   public PreparedString(String pattern, boolean keepVariables) {
      this(pattern, keepVariables, ':');
   }

   /**
    * Constructor setting pattern & variable operator.
    */
   public PreparedString(String pattern, char variableOp) {
      this(pattern, false, variableOp);
   }

   /**
    * Constructor setting the whether to keep variables as well as setting the
    * variable operator character.
    */
   public PreparedString(String pattern, boolean keepVariables, char variableOp) {
      variables = new ArrayList<>();
      this.pattern = new StringBuilder(pattern);

      variableOperator = variableOp;
      keepVariables(keepVariables);

      parsePattern();
   }

   /**
    * Returns the variable named <code>name</code>.
    * @returns a <code>Variable</code> with name <code>name</code> if one exists,
    * <code>null</code> otherwise.
    */
   private Variable getVariable(String name) {
      if (name == null || name.equals("")) // null & empty strings have no place here.
         return null;

      Variable v = null;
      Iterator itr = variables.iterator();
      while (itr.hasNext()) // Go through all variables
         {
         v = (Variable) itr.next();
         if (v.name.equals(name)) // return the first one with that name.
            return v;
      }

      return null; // No variable with the name was found.
   }

   /**
    * Returns an array of <code>String</code>s with all the variable names in the
    * pattern.
    */
   public String[] getVariableNames() {
      int i = 0;
      String[] result = new String[variables.size()];
      Iterator itr = variables.iterator();

      while (itr.hasNext()) {
         result[i++] = ((Variable) itr.next()).name;
      }

      return result;
   }

   /**
    * Returns the variable operator set. The variable operator is a character
    * identifying variables in the input pattern.
    */
   public char getVariableOperator() {
      return variableOperator;
   }

   /**
    * Returns whether to keep unset variables or not in the resulting string.
    */
   public boolean keepVariables() {
      return keepVars;
   }

   /**
    * Sets whether to keep variables that have no value set or not.
    * @param on  if <code>true</code> then variables with no values set are kept
    * in the result (from <code>toString()</code>).
    */
   public void keepVariables(boolean on) {
      keepVars = on;
   }

   /**
    * Parses the supplied pattern for variables.
    */
   private void parsePattern() {
      int index, index2;
      String varName;

      // Go through the pattern.
      for (index = 0; index < pattern.length() - 1; index++) {
         // A VARIABLE_OP is found in the pattern.
         if (pattern.charAt(index) == variableOperator) {
            // A double occurence of the VARIABLE_OP is found, remove one and go on.
            if (pattern.charAt(index + 1) == variableOperator)
               pattern.deleteCharAt(index++);
            else {
               pattern.deleteCharAt(index); // Delete the current VARIABLE_OP
               index2 = index;

               // A variable name is as long as there is valid variable characters
               // after a VARIABLE_OP.
               while (index2 < pattern.length() && validVarChar(pattern.charAt(index2))) {
                  index2++;
               }

               // Get the name of the variable
               varName = pattern.substring(index, index2);

               if (varName != null && varName.length() > 0)
                  variables.add(new Variable(varName, null, index)); // Save the variable.

               // Delete the variable name from the pattern.
               pattern.delete(index, index2);
            }
         }
      }

      // The above doesn't handle a single VARIABLE_OP at the end of the string.
      // Remove if one exists.
      if (pattern.length() > 0 && pattern.charAt(pattern.length() - 1) == variableOperator)
         pattern.deleteCharAt(pattern.length() - 1);
   }

   /**
    * Sets the value of an variable.
    * @param name  the name of the variable to set.
    * @param value the value to set.
    */
   public void setValue(String name, String value) {
      Variable v = getVariable(name);
      if (v != null)
         v.value = value;
   }

   /**
    * Returns a <code>String</code> with all variables substituted with values.
    * If a variable don't have a value set and <code>keepVariables()</code>
    * is <code>false</code>, it is removed from the result. Otherwise the variable
    * remains in the pattern.
    */
   @Override
   public String toString() {
      // Make a copy of the pattern so it can be used multiple times
      // (possibly with different values).
      StringBuilder result = new StringBuilder(pattern.toString());

      int offset = 0;
      Variable v;
      Iterator itr = variables.iterator();
      while (itr.hasNext()) {
         v = (Variable) itr.next();
         if (v.value != null) {
            result.insert(v.index + offset, v.value);
            offset += v.value.length();
         }
         else if (keepVars) // The the variable are set to be kept
            {
            // Put back the variable op
            result.insert(v.index + offset++, variableOperator);
            // Put back the name
            result.insert(v.index + offset, v.name);
            offset += v.name.length();
         }
      }

      return result.toString();
   }

   /**
    * Returns true if the character given is valid to use in a variable name.
    * Valid characters are: letters, digits, '_' & '-'
    */
   private boolean validVarChar(char c) {
      return Character.isLetter(c) || Character.isDigit(c) || c == '_' || c == '-';
   }

   /**
    * Class to hold information on variables.
    */
   private static class Variable {
      /**
       * The variable's name.
       */
      public String name;

      /**
       * The index at which the value is to be inserted.
       */
      public int index;

      /**
       * The variable's value.
       */
      public String value;

      /**
       * Constructor setting all values.
       */
      public Variable(String name, String value, int index) {
         this.name = name;
         this.value = value;
         this.index = index;
      }
   }
}