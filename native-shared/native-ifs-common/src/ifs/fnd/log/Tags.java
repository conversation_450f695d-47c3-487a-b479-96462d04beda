/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.log;


import ifs.fnd.service.FndRuntimeException;
import ifs.fnd.util.Str;
import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;

/**
 * A class representing a single tag or a collection of tags that can be used for tagging
 * debug and log output. A tag is just a simple string identifier that can be associated to
 * any object. A tag can be typed or untyped. A tag type is a string identifier that can be used for grouping
 * and filtering tags. Tag collection doesn't allow duplicated tag names, but two tags with the same name
 * and different types are treated as different.
 */
public final class Tags
{
   // The list cannot be empty
   private final List<String> names = new ArrayList<>(1);

   //===============================================================================
   //  Initialization
   //===============================================================================

   /**
    *  A package private constructor.
    *  To create Tags objects.
    */
   Tags()
   {
   }

   /**
    * Creates a new single tag recognized by the given name.
    * The name must not be empty.
    * @param name
    */
   public Tags( String name )
   {
      addTag( getInternalName(name,null) );
   }
   
   /**
    * Creates a new single tag recognized by the given name
    * and optional tag type.
    * The name must not be empty.
    * @param name
    * @param type
    */
   public Tags( String name, String type )
   {
      addTag( getInternalName(name,type) );
   }

   //===============================================================================
   //  Public API
   //===============================================================================

   /**
    * Adds a new single tag recognized by the given name to this tag collection.
    * The name must not be empty. Duplicated tags will be removed.
    * @param name
    */
   public void add( String name )
   {
      addTag( getInternalName(name,null) );
   }

   /**
    * Adds a new single tag recognized by the given name and optional type to this tag collection.
    * The name must not be empty. Duplicated tags will be removed.
    * @param name
    * @param type
    */
   public void add( String name, String type )
   {
      addTag( getInternalName(name,type) );
   }

   /**
    * Adds a collection of tags to this tag collection.
    * Duplicated tags will be removed.
    * @param tags
    */
   public void add( Tags tags )
   {
      if( tags==null )
         throw new FndRuntimeException("TAGNULLTAG: Tag cannot be null");

      for(String name:tags.names)
         addTag(name);
   }

   /**
    * Removes a named tag from this tag collections, if present.
    * @param name 
    * @return true if this collection contained the specified tag
    * @throws IllegalArgumentException if the result would be an empty collection
    */
   public boolean remove( String name ) throws IllegalArgumentException
   {
      if( names.size()==1 && names.contains(name) )
         throw new IllegalArgumentException("Cannot remove the only element from tag collection");
      
      return names.remove(name);
   }
   
   /**
    * Removes a named tag from this tag collections, if present,
    * represented by its name and optional type.
    * @param name 
    * @param type 
    * @return true if this collection contained the specified tag
    * @throws IllegalArgumentException if the result would be an empty collection
    */
   public boolean remove( String name, String type ) throws IllegalArgumentException
   {
      return remove( getInternalName(name,type) );
   }
   
   /**
    * Removes all the tags, if present, denoted by the 'tags' argument
    * from this tag collection.
    * @param tags 
    * @return true if this collection contained at least one of the tags specified as argument
    * @throws IllegalArgumentException if the result would be an empty collection
    */
   public boolean remove( Tags tags ) throws IllegalArgumentException
   {
      if(tags==null) return false;
      
      boolean result = false;
      for(String name:tags.names)
         if(remove(name))
            result = true;
      return result;
   }

   /**
    * Returns name of single tag or a comma separated list of
    * names of all tags in the collection.
    * @return 
    */
   public String getName()
   {
      return getNames(false);
   }
   
   /**
    * String representation of this tag collection.
    * Returns comma separated list of tag names and types.
    * @return 
    */
   @Override
   public String toString()
   {
      return getNames(true);
   }
   
   /**
    * Returns number of single tags in this tag collection.
    * @return 
    */
   public int getSize()
   {
      return names.size();
   }

   /**
    * Returns true if this tag set contains the specified element.
    * @param name
    * @return 
    */
   public boolean contains( String name )
   {
      return names.contains(name);
   }

   /**
    * Returns true if this tag set contains the specified element.
    * @param name
    * @param type
    * @return 
    */
   public boolean contains( String name, String type )
   {
      return contains( getInternalName(name,type) );
   }

   /**
    * Returns true if this tag set contains all of the elements of the specified tag set.
    * @param tags
    * @return 
    */
   public boolean contains( Tags tags )
   {
      if( tags==null ) return false;

      boolean result = false;
      for(String name:tags.names)
         result = contains(name);
      return result;
   }

   //===============================================================================
   //  Help routines
   //===============================================================================

   /**
    * Returns the internal list of names and types.
    * @return 
    */
   List<String> getTagNames()
   {
      return names;
   }
   
   /**
    * Formats this Tags instance to its String representation
    */
   String format()
   {
      StringBuilder buf = new StringBuilder(20);
      buf.append( this.getClass().getName() );
      buf.append(':');

      boolean first = true;
      for(String name:names)
      {
         if(!first)
            buf.append(',');
         buf.append(name);
         first = false;
      }
      return  buf.toString();
   }

   /**
    * Syntactic Analysis of the String representation of the Tags instance
    */
   boolean parse( String str )
   {
      if( Str.isEmpty(str) )
         return false;

      String marker = this.getClass().getName() + ":";

      if( !str.startsWith(marker) )
         return false;

      str = str.substring( marker.length() );

      names.clear();
      StringTokenizer st = new StringTokenizer(str, ",");
      while(st.hasMoreTokens())
         names.add(st.nextToken());

      return true;
   }

   /**
    * Checks if a tag with the given name exist independently of its type.
    * @param name
    * @return 
    */
   boolean containsAny(String name)
   {
      for(String tag:names)
      {
         int ix = tag.indexOf('/');
         if(ix>=0)
            tag = tag.substring(0, ix);
         if(tag.equals(name))
            return true;
      }
      return false;
   }
   
   /**
    * Checks if this collection contains any of the tag send in the argument independently of the type.
    * @param tags
    * @return true if there is at least one tag with the same name, independently of type, in both collections
    */
   boolean containsAny(Tags tags)
   {
      for(String name:tags.names)
      {
         int ix = name.indexOf('/');
         if(ix>=0)
            name = name.substring(0, ix);
         if(containsAny(name))
            return true;
      }
      return false;
   }
   
   /**
    * Checks if this collection contains tags of the given type. Type can be null.
    * @param tagType 
    * @return true if there is at least one tag of the given type
    */
   boolean containsType(String tagType)
   {
      for(String tag:names)
      {
         int ix = tag.indexOf('/');
         String type = ix<0 ? null : tag.substring(ix+1);
         if(type==null && tagType==null || type!=null && type.equals(tagType))
            return true;
      }
      return false;
   }
   
   /**
    * Returns a comma separated list of tag names and optionally
    * types contained by this tag collection.
    * @param includeType if true tag type will be appended to each name
    * @return comma separated list on of tags on form: "<name>[/<type>]"
    */
   private String getNames(boolean includeType)
   {
      StringBuilder buf = new StringBuilder();
      boolean first = true;
      for(String name:names)
      {
         if(!first)
            buf.append(',');
         if(!includeType)
         {
            int ix = name.indexOf('/');
            if(ix>0)
               name = name.substring(0, ix);
         }
         buf.append(name);
         first = false;
      }
      return buf.toString();
   }

   /**
    * Checks if the name is valid and returns the internal representation,
    * which is: "<tag_name>[/<type>]"
    */
   private String getInternalName( String name, String type )
   {
      if(name!=null) name = name.trim();
      if(type!=null) type = type.trim();

      if( Str.isEmpty(name) )
         throw new FndRuntimeException("TAGEMPTYNAME: Tag name cannot be empty");

      //if( !IfsNames.isId(name) )
      //   throw new FndRuntimeException("TAGNAMENOTID: Tag name doesn't conform IFS name");

      if( Str.isEmpty(type) ) return name;
      return name +'/'+ type;
   }

   /**
    * Add a new tag to this collection, if not already exists.
    */
   private void addTag( String name )
   {
      if( !names.contains(name) )
         names.add(name);
   }
}
