/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */


package ifs.fnd.log;

import ifs.fnd.util.Str;
import ifs.fnd.service.FndRuntimeException;
import java.util.logging.LogRecord;

/**
 * A filter implementation that can filter on class name and optionally
 * method name. Filter can be initiated through the logger configuration file.<p>
 * Syntax:<p>
 * <pre>
 * handler.&lt;handler_name&gt;.filter[.&lt;seq_no&gt;]=[!]ifs.fnd.log.ClassFilter,&ltclass_name&gt;[,&lt;method_name&gt;]
 * </pre>
 * Star ('*') and question mark ('?') characters are accepted as wild cards in class and method names.<p>
 * With a preceding exclamation mark ('!') the filter will function in an opposite manner,
 * i.e. it will block records that match the specified class (and method) name
 * and pass through all other records.
 *
 * @see ifs.fnd.log.LoggerConfig
 */
public class ClassFilter extends IfsFilter
{
   private String clsName;
   private String method = null;

   //===============================================================================
   //  Overridden methods
   //===============================================================================

   /**
    * Initialization of the filter.
    */
   @Override
   protected void init()
   {
      int argCnt = getArgumentCount();
      if( argCnt==0 || argCnt >2 )
         throw new FndRuntimeException("CLSFILTERARGNOERR: Invalid number of arguments for Class Filter: &1", String.valueOf(+argCnt));

      clsName = getArgument(0);
      if( argCnt==2 )
         method = getArgument(1);
   }

   /**
    * Returns true if the record is tagged by a class and optionally method
    * that match the filter arguments.
    * @param record Log record to check
    * @return true if the record is tagged
    */
   @Override
   protected boolean isRecLoggable( LogRecord record )
   {
      if( record==null )
         return false;

      String currCls  = record.getSourceClassName();
      if( !like(currCls, this.clsName) )
         return false;

      return this.method==null ? true : like( this.method, record.getSourceMethodName() );
   }

   /**
    * Returns a boolean value of the SQL expression "a like b".
    */
   private boolean like( String a, String b )
   {
      if( a==null || b==null )
         return false;

      return Str.like(a,b);
   }
}

