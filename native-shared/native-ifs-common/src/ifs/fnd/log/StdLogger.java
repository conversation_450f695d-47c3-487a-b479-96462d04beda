/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */


package ifs.fnd.log;

import ifs.fnd.util.*;
import java.util.*;

/**
 * Implementation of the abstract Logger class.
 * @see ifs.fnd.log.Logger
 */
class StdLogger extends Logger
{
   private LoggerImpl impl = null;
   protected final ThreadLoggers loggers;
   private final ThreadLoggers.ThreadLogger threadLogger;
   private Set<LoggerImpl. RemovalDesc> removals = null;
   private Logger forwardTo = null;
   private final SimpleStack<Logger> logStack = new SimpleStack<>();

   //===============================================================================
   //  Constructor
   //===============================================================================

   /**
    * Just delegates to the superclass.
    * @see ifs.fnd.log.Logger
    */
   StdLogger( ThreadLoggers loggers, ThreadLoggers.ThreadLogger threadLogger, String name, boolean warning, boolean info, boolean trace, boolean debug )
   {
      super( name, warning, info, trace, debug );
      this.loggers = loggers;
      this.threadLogger = threadLogger;
   }

   //===============================================================================
   //  Methods for logging messages
   //===============================================================================

   /**
    * Method for logging of errors. Always enabled; use with care.
    * Each error will get an unique generated GUID and will notify registered listeners.
    * @see ifs.fnd.log.Logger.error
    */
   @Override
   public void error(Throwable t, Tags tag, String message, Object... params)
   {
      if(forwardTo!=null)
         forwardTo.error(t, tag, message, params);
      else
      {
         errorId = UUID.randomUUID();
         log(LogMgr.Level.ERROR, t, errorId, tag, message, params);
         loggers.notifyErrorListeners(errorId);
      }
   }

   /**
    * Method for logging of warnings. Use together with the 'warning' flag.
    * @see ifs.fnd.log.Logger.warning
    */
   @Override
   public void warning(Throwable t, Tags tag, String message, Object... params)
   {
      if(forwardTo!=null)
         forwardTo.warning(t, tag, message, params);
      else
         if(warning) log(LogMgr.Level.WARNING, t, null, tag, message, params);
   }

   /**
    * Method for logging of informations. Use together with the 'info' flag.
    * @see ifs.fnd.log.Logger.info
    */
   @Override
   public void info(Throwable t, Tags tag, String message, Object... params)
   {
      if(forwardTo!=null)
         forwardTo.info(t, tag, message, params);
      else
         if(info) log(LogMgr.Level.INFO, t, null, tag, message, params);
   }

   /**
    * Method for tracing and light debugging. Use together with the 'trace' flag.
    * @see ifs.fnd.log.Logger.trace
    */
   @Override
   public void trace(Throwable t, Tags tag, String message, Object... params)
   {
      if(forwardTo!=null)
         forwardTo.trace(t, tag, message, params);
      else
         if(trace) log(LogMgr.Level.TRACE, t, null, tag, message, params);
   }

   /**
    * Method for debugging. Use together with the 'debug' flag.
    * @see ifs.fnd.log.Logger.debug
    */
   @Override
   public void debug(Throwable t, Tags tag, String message, Object... params)
   {
      if(forwardTo!=null)
         forwardTo.debug(t, tag, message, params);
      else
         if(debug) log(LogMgr.Level.DEBUG, t, null, tag, message, params);
   }

   //===============================================================================
   //  Utility routines
   //===============================================================================

   /**
    * Removes the specified method call from stack trace on logging event.
    */
   @Override
   public void removeFromStack( String clsName, String methName )
   {
      if( !Str.isEmpty(clsName) )
      {
         if( impl!=null )
            impl.removeFromStack(clsName, methName);
         else
         {
            if( removals==null )
               removals = new HashSet<>();
            removals.add( new LoggerImpl.RemovalDesc(clsName,methName) );
         }
      }
   }

   /**
    * Enables/disables thread check (enabled by default).
    * @param performCheck
    */
   @Override
   public void performThreadCheck(boolean performCheck)
   {
      init();
      impl.performThreadCheck(performCheck);
   }

   /**
    * Forwards everything to another Logger
    * @param to Logger to forward to
    */
   @Override
   public void forward(Logger to)
   {
      if(to==null)
         throw new IllegalArgumentException("Logger must not be null in call to Logger.forward()");
      if(forwardTo!=null)
         logStack.push(forwardTo);
      forwardTo = to;
   }

   /**
    * Checks if the current Logger is forwarded to another Logger.
    * @return true if the Logger is forwarded
    */
   @Override
   public boolean isForwarded()
   {
      return forwardTo!=null;
   }

   /**
    * Stops Logger forwarding
    */
   @Override
   public void stopForwarding()
   {
      forwardTo = logStack.empty() ? null : logStack.pop();
   }

   //===============================================================================
   //  Private routines
   //===============================================================================

   /**
    * Returns a tag collection as a merge of the application tag, the tags
    * saved on the Logger instance and tags given as argument.
    * @param newTags
    * @return
    */
   private Tags prepareTags(Tags newTags)
   {
      String appName = LogMgr.getApplicationName();
      Tags tags = getTags();
      if(appName == null && tags == null && newTags == null)
      {
         return null;
      }
      Tags clone = new Tags();
      if(appName != null)
      {
         clone.add(appName, "APPNAME");
      }
      if(tags != null)
      {
         clone.add(tags);
      }
      if(newTags != null)
      {
         clone.add(newTags);
      }
      return clone;
   }

   /**
    * The private implementation of the log method used by all the others logging methods
    */
   private void log(LogMgr.Level level, Throwable thrown, UUID guid, Tags tag, String message, Object... params)
   {
      init();
      message = Str.formatMessage(message, params);
      if(guid!=null)
      {
         String guidMsg = "[Error GUID:"+guid+"]";
         message = message==null ? guidMsg : message+" "+guidMsg;
      }
      impl.log(level, thrown, message, prepareTags(tag));
   }

   /**
    * Creates a new instance of LoggerImpl, if not yet created.
    * @see ifs.fnd.log.LoggerImpl
    */
   private void init()
   {
      if( impl==null )
      {
         impl = newLoggerImpl(name, loggers, threadLogger, removals);
         removals = null;
      }
   }

   /**
    * Creates a new instance of LoggerImpl.
    * The method is supposed to be overridden by subclasses.
    */
   protected LoggerImpl newLoggerImpl(String name, ThreadLoggers loggers, ThreadLoggers.ThreadLogger threadLogger, Set<LoggerImpl.RemovalDesc> removals)
   {
      return new LoggerImpl(name, loggers, threadLogger, removals);
   }

   /**
    * Returns instance of java.util.logging.Logger
    * used internally by this Logger.
    */
   @Override
   public java.util.logging.Logger getInternalLogger()
   {
      init();
      return impl.getInternalLogger();
   }
}

