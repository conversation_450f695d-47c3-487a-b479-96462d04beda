/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */


package ifs.fnd.log;

import java.util.logging.LogRecord;

/**
 * Print a brief summary of the LogRecord in a human readable
 * format in 1 line in a way optimized for IFS needs.
 */
class ConsoleFormatter extends IfsFormatter
{
   private final boolean showPkg;

   /**
    * Constructs ConsoleFormatter. The way of showing class names
    * depends on value of the parameter.
    * @param showPkg shows fully qualified class name if 'true'
    */
   ConsoleFormatter( boolean showPkg )
   {
      this.showPkg = showPkg;
   }

   /**
    * Format the given LogRecord.
    * @param record the log record to be formatted.
    * @return a formatted log record
    */
   @Override
   public String format( LogRecord record )
   {
      StringBuilder buf = new StringBuilder();
      formatThreadId(buf, record);
      buf.append(' ');
      formatLevel(buf, record);
      buf.append(' ');
      formatLoggerName(buf, record);
      formatLoggerTag(buf, record);
      buf.append(' ');
      formatSourceMethod(buf, record, showPkg);
      buf.append( formatMessage(record)).append(NL);
      formatThrowable(buf, record);
      return buf.toString().replaceAll("[\\r\\n]+", " ");
   }
}
