/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */


package ifs.fnd.log;

import ifs.fnd.service.FndRuntimeException;
import java.util.logging.LogRecord;

/**
 * A filter implementation that can filter on one or more tags.
 * A record tagged by one or more of the specified tags is passed through.
 * Filter can be initiated through the logger configuration file.<p>
 * Syntax:<p>
 * <pre>
 * handler.&lt;handler_name&gt;.filter[.&lt;seq_no&gt;]=[!]ifs.fnd.log.TagFilter,&lt;tag_name&gt;[,&lt;tag_name&gt;[,...]]
 * </pre>
 * With a preceding exclamation mark ('!') the filter will function in an opposite manner,
 * i.e. it will block records tagged with one (or more) of specified tags
 * and pass through all other records.
 * <p>
 * Note that this filter doesn't support tag types.
 *
 * @see ifs.fnd.log.LoggerConfig
 */
public class TagFilter extends IfsFilter
{
   private Tags[] tags; // tags without type

   //===============================================================================
   //  Overridden methods
   //===============================================================================

   /**
    * Initialization of the filter
    */
   @Override
   protected void init()
   {
      int argCnt = getArgumentCount();

      if( argCnt==0 )
         throw new FndRuntimeException("TAGFILTERARGNOERR: Invalid number of arguments for Tag Filter: &1", String.valueOf(argCnt));

      tags = new Tags[argCnt];
      for( int i=0; i<argCnt; i++ )
      {
         String name = getArgument(i);
         if(name.contains("/"))
            throw new FndRuntimeException("TAGFILTERTYPEERR: Tag Filter doesn't support tag types: '&1'", name);
         tags[i] = new Tags(name, null);
      }
   }

   /**
    * Returns true if any of the tags specified as argument to this filter
    * match the record, i.e. is contained by the records tag set.
    * @param record
    * @return
    */
   @Override
   protected boolean isRecLoggable( LogRecord record )
   {
      if( record==null )
         return false;

      Tags tag = LoggerImpl.getTags(record);
      if(tag==null)
         return false;

      for(Tags t:tags)
      {
         if( tag.containsAny(t) )
            return true;
      }

      return false;
   }
}

