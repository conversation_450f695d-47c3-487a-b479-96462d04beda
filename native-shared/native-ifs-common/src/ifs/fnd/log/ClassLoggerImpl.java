/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.log;

import java.util.Set;

/**
 * Extension of LoggerImpl used for class debugging.
 */
class ClassLoggerImpl extends LoggerImpl {
   /**
    * The level specified at construction time.
    */
   private final LogMgr.Level level;

   /**
    * Creates a new instance of this class.
    */
   ClassLoggerImpl(String name, ThreadLoggers loggers, Set<LoggerImpl.RemovalDesc> removals, LogMgr.Level level) {
      super("ifs.Classdebug."+name, loggers, null, removals);
      this.level = level;
   }

   // Overriden methods controlling how the log() method works

   @Override
   protected boolean checkLevel(LogMgr.Level level) {
      return level.index <= this.level.index;
   }

   @Override
   protected boolean checkClientLogger(LogMgr.Level level) {
      return true;
   }

   @Override
   protected boolean checkserverLogger(LogMgr.Level level) {
      return true;
   }

   @Override
   protected boolean checkSpoolRecord() {
      return false;
   }
}
