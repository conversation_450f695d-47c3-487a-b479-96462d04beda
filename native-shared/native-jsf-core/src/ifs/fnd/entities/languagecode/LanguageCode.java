/*
* ==================================================================================
* File:             LanguageCode.java
* Software Package: LanguageCode
*
* DO NOT EDIT this file. It was generated and will be overwritten at next generation
*
* ==================================================================================
*/

package ifs.fnd.entities.languagecode;

import ifs.fnd.record.FndAbstractArray;
import ifs.fnd.record.FndAbstractRecord;
import ifs.fnd.record.FndRecordMeta;
import ifs.fnd.record.FndText;

/**
 * View representing the <code>LanguageCode</code> entity.
 */
public class LanguageCode extends AbstractLanguageCode {

   public static final FndRecordMeta viewMeta  = new FndRecordMeta(AbstractLanguageCode.viewMeta, "LANGUAGECODE", "LANGUAGE_CODE");

   public final FndText      description         = new FndText(Meta.description);
   public final LanguageCodeStatusEnumeration status              = new LanguageCodeStatusEnumeration(Meta.status);
   public final FndText      nlsLanguage         = new FndText(Meta.nlsLanguage);
   public final FndText      nlsTerritory        = new FndText(Meta.nlsTerritory);
   public final FndText      derivedFromLangCode = new FndText(Meta.derivedFromLangCode);

   public LanguageCode() {
      this(viewMeta);
   }

   protected LanguageCode(FndRecordMeta meta) {
      super(meta);
      add(description);
      add(status);
      add(nlsLanguage);
      add(nlsTerritory);
      add(derivedFromLangCode);
   }

   @Override
   public FndAbstractRecord newInstance() {
      return new LanguageCode();
   }

   @Override
   public FndAbstractArray newArrayInstance() {
      return new LanguageCodeArray();
   }

   /**
    * Meta class for <code>LanguageCode</code> entity.
    */
   public static class Meta extends AbstractLanguageCode.Meta {
   }
}
