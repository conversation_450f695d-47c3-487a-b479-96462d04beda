/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.entities.languagecode;

import ifs.fnd.base.IfsException;
import ifs.fnd.record.FndQueryRecord;
import ifs.fnd.sf.storage.FndEntityHandler;

/**
 * Entity handler for <code>LanguageCode</code> entity.
 */
public class LanguageCodeHandler extends FndEntityHandler {

   /**
    * Package level constructor to force use of factory class
    * <code>LanguageCodeHandlerFactory</code>.
    */
   LanguageCodeHandler() {

   }

   /**
    * Query language codes.
    * @param   record   the <code>FndQueryRecord</code> with conditions for
    * the query.
    * @return  an <code>LanguageCodeArray</code> with the query results.
    * @throws IfsException if there is a problem with executing the query.
    */
   public LanguageCodeArray query(FndQueryRecord record) throws IfsException {
      beforeCall("queryLanguageCode");
      try {
         LanguageCodeArray array = new LanguageCodeArray();
         query(record, array);
         return array;
      }
      finally {
         afterCall();
      }
   }
}