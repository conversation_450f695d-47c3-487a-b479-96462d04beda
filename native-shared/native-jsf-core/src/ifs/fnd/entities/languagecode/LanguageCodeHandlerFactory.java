/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.entities.languagecode;

/**
 * Factory class used to get instances of <code>LanguageCodeHandler</code>.
 */
public final class LanguageCodeHandlerFactory {

   /**
    * Private constructor to disable instantiation.
    */
   private LanguageCodeHandlerFactory() {
   }

   /**
    * Get an instance of a <code>[LanguageCodeHandler]</code>.
    * @return An instance of a <code>[LanguageCodeHandler]</code>.
    */
   public static LanguageCodeHandler getHandler() {
      return new LanguageCodeHandler();
   }
}
