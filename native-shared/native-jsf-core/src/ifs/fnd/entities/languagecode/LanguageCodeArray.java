/*
* ==================================================================================
* File:             LanguageCodeArray.java
* Software Package: LanguageCode
*
* DO NOT EDIT this file. It was generated and will be overwritten at next generation
*
* ==================================================================================
*/

package ifs.fnd.entities.languagecode;

import ifs.fnd.base.ParseException;
import ifs.fnd.base.SystemException;
import ifs.fnd.record.FndAbstractRecord;
import ifs.fnd.record.FndAttribute;
import ifs.fnd.record.FndAttributeMeta;
import ifs.fnd.record.FndCompoundReference;
import ifs.fnd.record.FndDetailCondition;
import ifs.fnd.record.FndQueryReferenceCategory;
import ifs.fnd.record.serialization.FndTokenReader;

/**
 * Array of <code>LanguageCode</code>.
 */
public class LanguageCodeArray extends AbstractLanguageCodeArray {

   public LanguageCodeArray() {
      super();
   }

   public LanguageCodeArray(FndAttributeMeta meta) {
      super(meta);
   }

   public LanguageCodeArray(FndAttributeMeta meta, FndCompoundReference parentKeyInParent) {
      super(meta, parentKeyInParent);
   }

   public boolean add(LanguageCode record) {
      return internalAdd(record);
   }

   public void add(int index, LanguageCode record) {
      internalAdd(index, record);
   }

   public void add(LanguageCodeArray array) {
      internalAdd(array);
   }

   public void assign(LanguageCodeArray from) throws SystemException {
      assign(from, true);
   }

   public boolean contains(LanguageCode record) {
      return internalContains(record);
   }

   public LanguageCode firstElement() {
      return (LanguageCode)internalFirstElement();
   }

   public LanguageCode get(int index) {
      return (LanguageCode)internalGet(index);
   }

   public int indexOf(LanguageCode record) {
      return internalIndexOf(record);
   }

   public LanguageCode lastElement() {
      return (LanguageCode)internalLastElement();
   }

   public int lastIndexOf(LanguageCode record) {
      return internalLastIndexOf(record);
   }

   public LanguageCode remove(int index) {
      return (LanguageCode)internalRemove(index);
   }

   public LanguageCode set(int index, LanguageCode record) {
      return (LanguageCode)internalSet(index, record);
   }

   public FndDetailCondition createDetailCondition(LanguageCode record, FndQueryReferenceCategory category) {
      return createCondition(record, category);
   }

   @Override
   public FndAbstractRecord newRecord() {
      return new LanguageCode();
   }

   @Override
   protected FndAbstractRecord newRecord(FndTokenReader stream) throws ParseException {
      LanguageCode record = new LanguageCode();
      record.parse(stream);
      return record;
   }

   @Override
   protected FndAttribute newAttribute(FndAttributeMeta meta) {
       return new LanguageCodeArray(meta);
   }
}
