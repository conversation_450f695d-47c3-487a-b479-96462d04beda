/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.entities.fnduser;

import ifs.fnd.base.IfsException;
import ifs.fnd.record.*;
import ifs.fnd.sf.storage.FndEntityHandler;

/**
 * Entity handler for <code>FndUser</code> entity.
 */
public class FndUserHandler extends FndEntityHandler {

   FndUserHandler() {
   }

   /**
    * Get an instance of FndUser.
    * @param fndUser user to get
    * @return record from database
    * @throws IfsException if the call fails
    */
   public FndUser getFndUser(FndUser fndUser) throws IfsException {
      beforeCall("getFndUser");
      try {
         return (FndUser)get(fndUser);
      }
      finally {
         afterCall();
      }
   }

   /**
    * Query multiple users.
    * @param record query record with conditions
    * @return array of users matching the conditions
    * @throws IfsException if the call fails
    */
   public FndUserArray queryFndUser(FndQueryRecord record) throws IfsException {
      beforeCall("queryFndUser");
      try {
         FndUserArray array = new FndUserArray();
         query(record, array);
         return array;
      }
      finally {
         afterCall();
      }
   }

}
