/*
 * IFS Research & Development
 *
 * This program is protected by copyright law and by international conventions.
 * All licensing, renting, lending or copying (including for private use), and
 * all other use of the program, which is not expressively permitted by IFS
 * Research & Development (IFS), is a violation of the rights of IFS. Such
 * violations will be reported to the appropriate authorities.
 *
 * VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD TO UP TO TWO
 * YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.entities.fnduser;

import ifs.fnd.util.Str;
import ifs.fnd.base.IfsException;
import ifs.fnd.base.SystemException;
import ifs.fnd.base.FndContext;
import ifs.fnd.log.LogMgr;
import ifs.fnd.log.Logger;
import ifs.fnd.sf.cache.FndRecordCache;
import ifs.fnd.sf.cache.FndRecordCacheItemFactory;
import ifs.fnd.sf.FndServerContext;
import ifs.fnd.record.FndAbstractRecord;
import ifs.fnd.record.FndArray;
import ifs.fnd.record.FndConditionOperator;
import ifs.fnd.record.FndQueryRecord;
import ifs.fnd.record.FndRecord;
import ifs.fnd.record.FndSqlValue;
import ifs.fnd.service.Util;
import ifs.fnd.sf.storage.FndConnection;
import ifs.fnd.sf.storage.FndConnectionManager;
import ifs.fnd.sf.storage.FndFilterCache;
import ifs.fnd.sf.storage.FndPlsqlAccess;
import ifs.fnd.sf.storage.FndPlsqlConfig;
import ifs.fnd.sf.storage.FndResultSet;
import ifs.fnd.sf.storage.FndStatement;
import ifs.fnd.sf.storage.FndSystemConnectionAccess;
import java.sql.SQLException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Collections;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

/**
 * Cache with FndUser views.
 * This class can be used to get an FndUser if the user's directoryId is known.
 */
public final class FndUserCache {

   //Avoid instantiations
   private FndUserCache() {
   }

   //Record cache. Cache item lifespan 60 minutes.
   private static final FndRecordCache CACHE = new FndRecordCache(60, HandlerFactory.FACTORY, 128);

   /**
    * Clear local FndUser cache.
    */
   public static void clearCache() {
      CACHE.clear();
   }

   /**
    * Get an active FndUser from cache
    *
    * @param directoryId
    *           User directory ID
    * @return Active FndUser with matching directoryId (if found)
    * @throws SystemException
    *            if no matching user is found
    */
   public static FndUser getFndUser(String directoryId) throws SystemException {
      try {
         //Return a clone of the user view (since FndUser is mutable)
         return (FndUser) CACHE.get(directoryId.toLowerCase(), true);
      }
      catch (IfsException e) {
         throw new SystemException(e, Texts.GETFNDUSER, directoryId);
      }
   }

   /**
    * Get the identity for an active FndUser from cache.
    *
    * @param directoryId User directory ID
    * @return identity of active FndUser with matching directoryId
    * @throws SystemException
    *            if no matching user is found
    */
   public static String getFndUserIdentity(String directoryId) throws SystemException {
      try {
         FndUser user = (FndUser) CACHE.get(directoryId.toLowerCase(), false);
         return user.identity.getValue();
      }
      catch (IfsException e) {
         throw new SystemException(e, Texts.GETFNDUSER, directoryId);
      }
   }

   /**
    * Get an active FndUser data from cache.
    *
    * @param directoryId User directory ID
    * @return an instance of FndUserData
    * @throws SystemException
    *            if no matching user is found
    */
   public static FndUserData getFndUserData(String directoryId) throws SystemException {
      try {
         FndUser user = (FndUser) CACHE.get(directoryId.toLowerCase(), false);
         return new FndUserData(user);
      }
      catch (IfsException e) {
         throw new SystemException(e, Texts.GETFNDUSER, directoryId);
      }
   }

   /**
    * Find an active FndUser data in cache.
    *
    * @param directoryId User directory ID
    * @return an instance of FndUserData, or null id not found in cache
    */
   public static FndUserData findFndUserData(String directoryId) {
      FndUser user = (FndUser) CACHE.find(directoryId.toLowerCase());
      return user == null ? null : new FndUserData(user);
   }

   /**
    * Find the identity for an active FndUser in cache.
    * This method does not update the cache.
    *
    * @param directoryId User directory ID
    * @return identity of active FndUser with matching directoryId or null if not found in cache
    */
   public static String findFndUserIdentity(String directoryId) {
      FndUser user = (FndUser) CACHE.find(directoryId.toLowerCase());
      return user == null ? null : user.identity.getValue();
   }

   /**
    * Checks if the current user has been granted access to given activity.
    *
    * @param directoryId
    *           User directory ID
    * @param activityName
    *           The name of activity to check grants for
    * @return true if the activity has been granted to the user, false otherwise
    * @throws IfsException
    *            if the checking procedure fails for some reason
    */
   public static boolean isActivityGranted(String directoryId, String activityName) throws IfsException {
      return getFiltersForActivity(directoryId, activityName) != null;
   }

   /**
    * Checks if the current user has been granted access to given activity.
    *
    * @param directoryId
    *           User directory ID
    * @param activityName
    *           The name of activity to check grants for
    * @return an iterator over filters that should be active during the lifetime of call to the the activity,
    *         or null if the user has not been granted assess to the activity
    * @throws IfsException
    *            if the checking procedure fails for some reason
    */
   public static Iterator<FndFilterCache.Entry> getFiltersForActivity(String directoryId, String activityName) throws IfsException {
      //Do not clone the user view (the cached instance may need to be be updated)
      CachedUser user = (CachedUser) CACHE.get(directoryId.toLowerCase(), false);
      String identity = user.identity.getValue();

      if (FndPlsqlConfig.getApplicationOwner().equalsIgnoreCase(user.oracleUser.getValue())) {
         //Application owner is implicitly granted everything (no filters will be applied)
         return Collections.emptyIterator();
      }

      Set<FndFilterCache.Entry> filters = (Set<FndFilterCache.Entry>) user.grantedActivities.get(activityName);
      if(filters == null) {

         // fetch from the database the filters configured for the activity and the current user
         filters = Util.newHashSet();
         if(!queryGrantedActivityFilters(identity, activityName, filters))
            return null; // the activity is not granted

         // update the cached user instance
         user.grantedActivities.put(activityName, filters);
      }
      return filters.iterator();
   }

   /**
    * Check if the current user has been granted a given system privilege.
    * Note that this method will NOT consider system privileges CONNECT or IMPERSONATE USER,
    * those privileges may be granted from some external registry (like LDAP).
    * Privileges CONNECT and IMPERSONATE USER are managed by the J2EE container, not by IFSAPP.
    * @param systemPrivilege The name of the system privilege to check grants for.
    * @return true if the given system privilege has been granted to the user, false otherwise.
    * @throws IfsException if the checking method fails for some reason
    */
   public static boolean isSystemPrivilegeGranted(String systemPrivilege) throws IfsException {
      CachedUser user = (CachedUser) CACHE.get(FndContext.getCurrentContext().getApplicationUser().toLowerCase(), false);
      if (user.grantedSystemPrivileges == null) {
         Set<String> set = new HashSet<>(4);
         queryGrantedSystemPrivileges(set);

         //update the cached user instance
         user.grantedSystemPrivileges = set;
      }
      return user.grantedSystemPrivileges.contains(systemPrivilege);
   }

   /**
    * When using J2EE connection pool an uninitialized connection (system connection) is used to check activity grants.
    * User identity is specified as a bind variable.
    */
   private static final String SELECT_GRANTED_ACTIVITY_FILTERS = createSelectGrantedActivityFilters();

   private static String createSelectGrantedActivityFilters() {
      String appOwner = FndPlsqlConfig.getApplicationOwner();
      String sql =
         " select distinct F.filter_id, F.granted_filter_type_db \n" +
         " from &AO.activity_grant_filter F, &AO.activity_grant A, &AO.fnd_user_role_runtime R \n" +
         " where R.identity = ? \n" +
         " and R.role = A.role \n" +
         " and A.activity_name = ? \n" +
         " and A.role = F.permission_set_id(+) \n" +
         " and A.activity_name = F.activity_name(+)";
      sql = Str.replace(sql, "&AO", appOwner);
      return sql;
   }

   private static final String SELECT_GRANTED_SYSTEM_PRIVILEGES = createSelectGrantedSystemPrivileges();

   private static String createSelectGrantedSystemPrivileges() {
      String appowner = FndPlsqlConfig.getApplicationOwner();
      return "select " + appowner + ".Current_Oracle_User_SYS.My_System_Privileges__ from dual";
   }

   /**
    * Retrieve from the database filters defined for an activity granted to the user.
    * @param set the set to add filters to
    * @return true if the activity is granted to the user, false otherwise
    */
   private static boolean queryGrantedActivityFilters(String identity, String activityName, Set<FndFilterCache.Entry> set) throws IfsException
   {
      Logger log = LogMgr.getSecurityLogger();
      FndConnection c = null;
      FndStatement stmt = null;

      try {
         // get system connection to the database
         DbAccess dbAccess = new DbAccess();
         FndConnectionManager mgr = FndServerContext.getCurrentServerContext().getConnectionManager();
         mgr.getSystemConnection(dbAccess, null);
         c = dbAccess.getConnection();

         stmt = c.createStatement();
         stmt.defineParameter(new FndSqlValue("IDENTITY", identity));
         stmt.defineParameter(new FndSqlValue("ACTIVITY", activityName));
         stmt.prepare(SELECT_GRANTED_ACTIVITY_FILTERS);
         stmt.executeQuery();

         FndResultSet rs = stmt.getFndResult();
         boolean rowFound = false;

         while(rs.next()) {
            rowFound = true;

            String filterId = stmt.getText(1);

            // skip outer-joined null filter ID
            if(rs.wasNull()) {
               continue;
            }

            // retrive from FndFilterCache the entry corresponding to the filter ID and type
            String filterType = stmt.getText(2);
            FndFilterCache.Entry entry = FndFilterCache.getFilterEntry(c, filterId, parseFilterType(filterType));

            // add the entry to the set
            set.add(entry);
         }

         if(!rowFound) {
            return false; // no row found, the activity is not granted to the user
         }

         if(log.debug) {
            log.debug("Filters for activity &1 granted to user &2: &3", activityName, identity, set.toString());
         }
         return true;
      }
      catch(SQLException e) {
         throw new SystemException(e, Texts.QRYACTIVITYFILTERS, e.getMessage());
      }
      finally {
         try {
            if(stmt != null) {
               stmt.close(); // unregister FndAbortableProcess
            }
         }
         finally {
            if(c != null) {
               c.close();
            }
         }
      }
   }

   /**
    * Convert string value of ActivityGrantFilterTypeEnumeration to integer type defined in FndFilterCache.
    */
   private static int parseFilterType(String type) throws SystemException {
      switch (type) {
         case "STANDARD_FILTER":
            return FndFilterCache.STANDARD_FILTER;
         case "PERMISSION_SET_FILTER":
            return FndFilterCache.PERMISSION_SET_FILTER;
         default:
            throw new SystemException(Texts.FILTERTYPE, type);
      }
   }

   private static void queryGrantedSystemPrivileges(final Set<String> set) throws IfsException {
      Logger log = LogMgr.getSecurityLogger();
      FndPlsqlAccess access = new FndPlsqlAccess();
      FndArray result = new FndArray();
      access.executePLSQLSelect(SELECT_GRANTED_SYSTEM_PRIVILEGES, new FndRecord(), result, 1, 0);
      String resultStr = result.get(0).getAttribute(0).toString();
      String[] privileges = resultStr == null ? new String[]{} : resultStr.split(ifs.fnd.base.FndConstants.FIELD_SEPARATOR);
      for (String privilege : privileges) {
         set.add(privilege.intern());
      }
      if(log.debug) {
         log.debug("System privileges granted to &1: &2", FndServerContext.getCurrentServerContext().getApplicationUser(), set.toString());
      }
   }

   /**
    * Extension of FndUser containing granted activities and privileges.
    * An instance of this class is stored in FndRecordCache.
    * The map with granted activities and the set with granted system privileges
    * are removed from the cache together with the user instance.
    */
   private static final class CachedUser extends FndUser {

      // set concurrency level to 1, because we expect only one thread to add entries to the map (the user himself)
      final Map<String,Set<FndFilterCache.Entry>> grantedActivities = new ConcurrentHashMap<>(16, 0.75f, 1);

      private volatile Set<String> grantedSystemPrivileges;

      public CachedUser() {
         super();
      }

      /**
       * Create a new instance.
       */
      @Override
      public FndAbstractRecord newInstance() {
         return new CachedUser();
      }
   }

   private static final class HandlerFactory implements FndRecordCacheItemFactory {

      private HandlerFactory() {
      }

      /**
       * Find an active FndUser with a specific directory_id
       */
      @Override
      public FndAbstractRecord getRecord(String key) throws IfsException {
         FndUserHandler handler = FndUserHandlerFactory.getHandler();
         FndUser user = new CachedUser();
         user.addCondition(user.directoryId.createEqualCondition(key.toUpperCase()));
         user.addCondition(user.active.createEqualCondition(true), FndConditionOperator.AND);
         FndQueryRecord qry = new FndQueryRecord(user);
         qry.maxRows.setValue(1);
         FndUserArray result = handler.queryFndUser(qry);
         if (result.size() == 1) {
            return result.get(0);
         }
         else {
            throw new SystemException(Texts.NOFNDUSER, key.toUpperCase());
         }
      }

      public static final HandlerFactory FACTORY = new HandlerFactory();
   }

   /**
    * <B>Framework internal class:</B>
    * Class used internally to get a system database connection for querying granted activities and privileges.
    * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
    */
   public static final class DbAccess implements FndSystemConnectionAccess {

      private FndConnection connection;

      /**
       * Private constructor guarantees that only FndUserCache class can instantiate this nested class.
       */
      private DbAccess() {
      }

      /**
       * The caller must guarantee that the connection will be closed.
       */
      FndConnection getConnection() {
         return connection;
      }

      //=============================================================================
      // Implementation of FndSystemConnectionAccess interface
      //=============================================================================

      /**
       * Sets an open database connection owned by system user (IFSSYS).
       * It is a callback method called by FndConnectionManager on a trusted class from java server framework.
       * @param connection an open database connection
       * @param param a parameter passed to getSystemConnection
       * @throws IfsException - if access to database fails for some reason
       */
      @Override
      public void setSystemConnection(FndConnection connection, Object param) throws IfsException {
         this.connection = connection;
      }
   }
}