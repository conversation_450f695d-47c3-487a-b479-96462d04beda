package ifs.fnd.entities.fnduser;

import ifs.fnd.record.*;
import ifs.fnd.base.IfsException;


/**
 * Entity representing a Foundation1 user.
 */
public class FndUser extends FndLUEntityView {
   /**
    * Meta view for the entity.
    */
   public static final FndRecordMeta viewMeta  = new FndRecordMeta("FNDEXT", "FND_USER", "FND_USER","FND_USER","Fnd_User","FND_USER_API");

   /**
    * User identity.
    */
   public final FndText                        identity     = new FndText(Meta.identity);
   /**
    * User description.
    */
   public final FndText                        description  = new FndText(Meta.description);
   /**
    * Oracle user for the user.
    */
   public final FndText                        oracleUser   = new FndText(Meta.oracleUser);
   /**
    * Directory Id for the user.
    */
   public final FndText                        directoryId  = new FndText(Meta.directoryId);
   /**
    * Indicates if the user is active.
    */
   public final FndBoolean              active       = new FndBooleanString(Meta.active, "TRUE", "FALSE");

   /**
    * Primary key attribute.
    */
   public final Reference primaryKey  = new Reference(Meta.primaryKey, identity);

   /**
    * Create a new instance of FndUser.
    */
   public FndUser() {
      super(viewMeta);
      add(identity);
      add(description);
      add(oracleUser);
      add(directoryId);
      add(active);
      add(primaryKey);
   }

   /**
    * Create a new instance.
    */
   @Override
   public FndAbstractRecord newInstance() {
      return new FndUser();
   }

   /**
    * Class representing the primary key for <code>FndUser</code> entity.
    */
   public static class Reference extends FndCompoundReference    {

      public Reference(FndCompoundReferenceMeta ref, FndText identity) {
         super(ref);
         add(identity);
      }

      public void assign(Reference ref)  throws IfsException {
         protectedAssign(ref);
      }

   }

   /**
    * Meta class (contains meta attributes) for <code>FndUser</code> entity.
    */
   public static class Meta   {

      private static final FndAttributeMeta identity     = new FndAttributeMeta(viewMeta, "IDENTITY", "IDENTITY", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 120);
      private static final FndAttributeMeta description  = new FndAttributeMeta(viewMeta, "DESCRIPTION", "DESCRIPTION", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 4000);
      private static final FndAttributeMeta oracleUser   = new FndAttributeMeta(viewMeta, "ORACLE_USER", "ORACLE_USER", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 120);
      private static final FndAttributeMeta directoryId  = new FndAttributeMeta(viewMeta, "DIRECTORY_ID", "WEB_USER", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 4000);
      public  static final FndAttributeMeta active       = new FndAttributeMeta(viewMeta, "ACTIVE", "ACTIVE", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);

      public static final FndCompoundReferenceMeta primaryKey  = new FndCompoundReferenceMeta(viewMeta, "Fnd_User_Key", new FndAttributeMeta[] {identity},viewMeta,true);

   }

}
