/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.entities.fnduser;

/**
 * POJO class representing an FND user.
 */
public class FndUserData {

   private final String identity;

   private final String oracleUser;

   private final String directoryId;

   /**
    * Constructs an immutable instance based on the specified FND user.
    * @param user FND user to copy data from
    */
   FndUserData(FndUser user) {
      this.identity = user.identity.getValue();
      this.oracleUser = user.oracleUser.getValue();
      this.directoryId = user.directoryId.getValue();
   }

   /**
    * Gets the identity of the FND user.
    * @return not null FND user identity
    */
   public String getIdentity() {
      return identity;
   }

   /**
    * Gets the Oracle user name.
    * @return database user name or null if undefined
    */
   public String getOracleUser() {
      return oracleUser;
   }

   /**
    * Gets the directory ID.
    * @return not null directory ID
    */
   public String getDirectoryId() {
      return directoryId;
   }
}
