/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.remote.j2ee;

import ifs.fnd.record.*;

/**
 * View representing an EJB operation.
 */
public class FndEjbOperation extends FndView {
   private static final FndRecordMeta viewMeta = new FndRecordMeta("FND", "FNDEJBOPERATION");

   private static final FndAttributeMeta nameMeta = new FndAttributeMeta(viewMeta, "NAME");

   public final FndAlpha name = new FndAlpha(nameMeta);

   public FndEjbOperation() {
      super(viewMeta);
      add(name);
   }

   @Override
   public FndAbstractRecord newInstance() {
      return new FndEjbOperation();
   }
}
