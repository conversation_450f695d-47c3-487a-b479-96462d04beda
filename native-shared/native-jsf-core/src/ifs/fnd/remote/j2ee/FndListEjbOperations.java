/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.remote.j2ee;

import ifs.fnd.record.*;

/**
 * View representing a result from listEjbOperations() operation.
 */
public class FndListEjbOperations extends FndView {
   private static final FndRecordMeta viewMeta = new FndRecordMeta("FND", "FND_LIST_EJB_OPERATIONS");

   private static final FndAttributeMeta operationsMeta = new FndAttributeMeta(viewMeta, "OPERATIONS");

   public final FndEjbOperationArray operations = new FndEjbOperationArray(operationsMeta);

   public FndListEjbOperations() {
      super(viewMeta);
      add(operations);
   }

   @Override
   public FndAbstractRecord newInstance() {
      return new FndListEjbOperations();
   }
}
