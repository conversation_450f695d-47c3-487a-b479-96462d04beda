/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.remote.j2ee;

import ifs.fnd.record.FndAbstractRecord;
import ifs.fnd.base.FndContext;

/**
 * Result wrapper for in out records.
 */
public class FndInOutRecordResultWrapper extends FndResultWrapper {

    private FndAbstractRecord inRecord;
    private FndAbstractRecord outRecord;

    /** Creates a new instance of FndInOutRecordResultWrapper */
    public FndInOutRecordResultWrapper(FndAbstractRecord inRecord, FndAbstractRecord outRecord, FndContext ctx) {
        super(ctx);
        this.inRecord = inRecord;
        this.outRecord = outRecord;
    }

    public FndAbstractRecord getResult() {
        return outRecord;
    }

}
