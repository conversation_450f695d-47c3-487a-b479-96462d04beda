/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.remote.j2ee;

import ifs.fnd.base.FndContext;

/**
 * Abstract base class for result wrappers.
 */
public abstract class FndResultWrapper implements java.io.Serializable {

    /** Context returned from remote call
     */
    private FndContext ctx;

    /** Creates a new instance of FndResultWrapper */
    public FndResultWrapper(FndContext ctx) {
        this.ctx = ctx;
    }

    /** Get the context as returned from a remote call
     */
    public FndContext getContext() {
        return ctx;
    }

}
