/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.remote;

import ifs.fnd.base.FndContext;
import ifs.fnd.log.*;

/**
 * Client-side extension of FndContext.
 */
public class FndRemoteContext extends FndContext {

   public FndRemoteContext() {
      Logger log = getClassLogger();
      if(log.debug) {
         log.debug("New FndRemoteContext created (&1)", this.hashCode());
      }
   }
}