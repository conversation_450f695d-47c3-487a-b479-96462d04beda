/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.internal;

import ifs.fnd.record.*;
import ifs.fnd.record.serialization.*;
import ifs.fnd.base.IfsException;
import ifs.fnd.base.ParseException;
import ifs.fnd.base.EncryptionException;
import java.util.List;

/**
 * <B>Framework internal class:</B> Used for accessing internal attribute methods. <BR><BR>
 * <B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public final class FndAttributeInternals {

   static {
      // force loading of FndInternals class, which will initialize the static state of this class
      ifs.fnd.service.Util.initClass(FndInternals.class);
   }

   private static FndInternals internals;

   /**
    * Set an instance of FndInternals.
    * The method is called by static code in class FndInternals.
    * @param internals an instance of FndInternals
    */
   public static void setFndInternals(FndInternals internals) {
      FndAttributeInternals.internals = internals;
   }

   /**
    * Avoid instantiations.
    */
   private FndAttributeInternals() {
   }

   /**
    * Set the internal record on an aggregate.
    * @param agg Aggregate to set record on.
    * @param rec Record to set.
    */
   public static void internalSetRecord(FndAbstractAggregate agg, FndAbstractRecord rec) {
      internals.internalSetRecord(agg, rec);
   }

   /**
    * Get the internal record set on an aggregate.
    * @param agg Aggregate.
    * @return The record set on the aggregate.
    */
   public static FndAbstractRecord internalGetRecord(FndAbstractAggregate agg) {
      return internals.internalGetRecord(agg);
   }

   /**
    * Add a record to an array.
    * @param arr Array to add record to.
    * @param rec Record to add.
    * @return true
    */
   public static boolean internalAdd(FndAbstractArray arr, FndAbstractRecord rec) {
      return internals.internalAdd(arr, rec);
   }

   /**
    * Add a record to an array.
    * @param arr Array to add record to.
    * @param rec Record to add.
    * @param setDirty true if the array should be marked as dirty, false otherwise
    * @return true
    */
   public static boolean internalAdd(FndAbstractArray arr, FndAbstractRecord rec, boolean setDirty) {
      return internals.internalAdd(arr, rec, setDirty);
   }

   /**
    * Get a record from an array.
    * @param arr Array to get record from.
    * @param i Position in array.
    * @return The record on position i in arr.
    */
   public static FndAbstractRecord internalGet(FndAbstractArray arr, int i) {
      return internals.internalGet(arr, i);
   }

   /**
    * Removes and returns a record from an array.
    * @param arr Array to remove a record from.
    * @param i Array index
    * @return The removed record
    */
   public static FndAbstractRecord internalRemove(FndAbstractArray arr, int i) {
      return internals.internalRemove(arr, i);
   }

   /**
    * Get the internal records of an array as a List.
    * @param arr Array.
    * @return List of records in array.
    */
   public static List<FndAbstractRecord> getInternalRecords(FndAbstractArray arr) {
      return internals.getInternalRecords(arr);
   }

   /**
    * Add a list of records to an array.
    * @param arr Array to add records to.
    * @param records List of records to add.
    */
   public static void load(FndAbstractArray arr, List<FndAbstractRecord> records) {
      internals.load(arr, records);
   }
   
   /**
    * Sets the <code>FndAttribute.SET</code> flag on an attribute.
    * @param attr Attribute to set the flag on.
    */
   public static void internalSet(FndAttribute attr) {
      internals.internalSet(attr);
   }

   /**
    * Set the internal value of an attribute. (Does not work for FndBinary.)
    * @param attr Attribute to set value on.
    * @param value Value to set on attribute.
    */
   public static void internalSetValue(FndAttribute attr, Object value) {
      internals.internalSetValue(attr, value);
   }

   /**
    * Get the internal value of an attribute. (Does not work for FndBinary.)
    * @param attr Attribute to get value from.
    * @return Value of the attribute.
    */
   public static Object internalGetValue(FndAttribute attr) {
      return internals.internalGetValue(attr);
   }

   /**
    * Set the internal counted value on attribute.
    * @param attr Attribute to set counted value on.
    * @param count Counted value.
    */
   public static void setCount(FndAttribute attr, int count) {
      internals.setCount(attr, count);
   }

   /**
    * Assign attribute values included in a compound reference.
    * @param ref1 compund reference to set attribute values in
    * @param ref2 compund reference to get attribute values from
    * @throws IfsException if there is a failure
    */
   public static void internalAssign(FndCompoundReference ref1, FndCompoundReference ref2) throws IfsException {
      internals.internalAssign(ref1, ref2);
   }

   /**
    * Return a FndSqlValue based on the value of an attribute
    * @param attr attribute to set database value on
    * @return the SQL value
    */
   public static FndSqlValue toFndSqlValue(FndAttribute attr) {
      return internals.toFndSqlValue(attr);
   }

   /**
    * Get value from database and set on the specified attribute
    * @param attr attribute to set database value on
    * @param data an instance of FndSqlData interface to get value from
    * @param colNr column/parameter index number
    * @throws IfsException if there is a failure
    */
   public static void setSqlValue(FndAttribute attr, FndSqlData data, int colNr) throws IfsException {
      internals.setSqlValue(attr, data, colNr);
   }

   /**
    * Get SQL type for an attribute.
    * @param  attr attribute to get SQL type for.
    * @return An instance of FndSqlType representing SQL type of the attribute.
    */
   public static FndSqlType getSqlType(FndAttribute attr) {
      return internals.getSqlType(attr);
   }


   /**
    * Create an attribute with specified name.
    * @param name attribute name
    * @return new attribute instance
    */
   public static FndAttribute newAttribute(String name) {
      return internals.newAttribute(name);
   }

   /**
    * Create an attribute with specified type and ame.
    * @param type attribute type
    * @param name attribute name
    * @return new attribute instance
    */
   public static FndAttribute newAttribute(String type, String name) {
      return internals.newAttribute(type, name);
   }

   /**
    * Create an attribute with specified type, name and value.
    * @param type attribute types
    * @param name attribute name
    * @param value initial attribute value
    * @return new instance
    */
   public static FndAttribute newAttribute(FndAttributeType type, String name, Object value) {
      return internals.newAttribute(type, name, value);
   }

   /**
    * Formats the whole attribute into an Fnd Buffer.
    * @param attr attribute to format
    * @param stream Stream to which to write the resulting buffer
    * @throws ParseException Attribute cannot be serialized into a buffer
    */
   public static void format(FndAttribute attr, FndTokenWriter stream) throws ParseException {
      internals.format(attr, stream);
   }

   /**
    * Parses an Fnd Buffer to get the attribute.
    * @param attr attribute to parse into
    * @param stream Stream from which to read the buffer
    * @throws ParseException Syntax error in buffer
    */
   public static void parse(FndAttribute attr, FndTokenReader stream) throws ParseException {
      internals.parse(attr, stream);
   }

   /**
    * Gets database string representation of an attribute's value.
    * @param attr attribute to get database string representation for
    * @return string used to store the value of the attribute in the database,
    *         or null if the attribute has no value
    */
   public static String toDatabaseString(FndAttribute attr) {
      return internals.toDatabaseString(attr);
   }

   /**
    * Parses a database string to get an attribute's value.
    * @param attr attribute to parse into
    * @param dbValue database representation of the attribute value
    * @throws ParseException if the specified string contains an illegal value
    */
   public static void parseDatabaseString(FndAttribute attr, String dbValue) throws ParseException {
      internals.parseDatabaseString(attr, dbValue);
   }

   /**
    * Sets NOT_INSTALLED flag on an attribute.
    * @param attr attribute to set the flag on
    */
   public static void setNotInstalled(FndAttribute attr) {
      internals.setNotInstalled(attr);
   }

   /**
    * Gets the template record for a
    * {@link ifs.fnd.record.FndAbstractAggregate FndAbstractAggregate} object.
    */
   public static FndAbstractRecord getTemplateRecord(FndAbstractAggregate agg) {
      return internals.getTemplateRecord(agg);
   }

   /**
    * Encrypt given string value if global encryption is enabled.
    * @param value to be encrypted
    */
   public static String encryptString(String value) throws EncryptionException{
      return internals.encryptString(value);
   }
}
