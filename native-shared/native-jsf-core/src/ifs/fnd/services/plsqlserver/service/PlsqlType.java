/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.services.plsqlserver.service;

/**
 * <B>Framework internal class:</B>
 * Class that repesents data types supported by PLSQL Gateway.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public final class PlsqlType {

   /**
    * The name of this PlsqlType
    */
   private final String name;

   /**
    * Private constructor used to create all valid static instances of PlsqlType
    */
   private PlsqlType(String name) {
      this.name = name;
   }

   /**
    * Returns the name of this PlsqlType. The name equals the name of
    * the corresponding FndSqlType with one exception: ROWID.
    */
   public String getName() {
      return name;
   }

   /**
    * Constant representing PLSQL type TEXT
    */
   public static final PlsqlType TEXT      = new PlsqlType("TEXT");

   /**
    * Constant representing PLSQL type LONG TEXT
    */
   public static final PlsqlType LONG_TEXT = new PlsqlType("LONG_TEXT");

   /**
    * Constant representing PLSQL type NUMBER
    */
   public static final PlsqlType NUMBER    = new PlsqlType("NUMBER");

   /**
    * Constant representing PLSQL type TIMESTAMP
    */
   public static final PlsqlType TIMESTAMP = new PlsqlType("TIMESTAMP");

   /**
    * Constant representing PLSQL type ROWID
    */
   public static final PlsqlType ROWID     = new PlsqlType("ROWID");

   /**
    * Constant representing PLSQL type BINARY
    */
   public static final PlsqlType BINARY    = new PlsqlType("BINARY");

}

