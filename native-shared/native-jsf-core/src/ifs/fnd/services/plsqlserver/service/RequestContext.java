/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.services.plsqlserver.service;

import ifs.fnd.base.SystemException;
import ifs.fnd.buffer.*;
import ifs.fnd.log.LogMgr;
import ifs.fnd.log.Logger;

import ifs.fnd.sf.storage.*;
import ifs.fnd.sf.FndServerContext;

/**
 * <B>Framework internal class:</B>
 * The abstract super class that encapsulates request and transaction context.
 * It contains common functionality for both Web Client requests and
 * PLSQL invocation requests.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public abstract class RequestContext {

   private final String fndUser;
   private final String appOwner;
   private final String dbPrefix;  // database schema (app owner) and "."


   /**
    * The only database connection used during the current request
    */
   private final FndConnection connection;

   /**
    * Processor that supports buffer interface for database access.
    * It uses WebRequestBufferTypeMap to map item types to/from PLSQL types.
    * It performs no security checks.
    */
   private PlsqlProcessor bufferProcessor;

   private Logger log;

   /**
    * Constructs a new RequestContext.
    */
   public RequestContext(String user, String language) throws Exception {
      log = LogMgr.getDatabaseLogger();

      FndServerContext ctx = FndServerContext.getCurrentServerContext();
      ctx.setLanguageAndLocale(language);
      if(log.trace) {
         log.trace("Called FndServerContext.setLanguageAndLocale(): language=&1 locale=&2", language, ctx.getLocale());
      }

      appOwner = FndPlsqlConfig.getApplicationOwner();
      dbPrefix = PlsqlUtil.isEmpty(appOwner) ? "" : appOwner + ".";

      FndConnectionManager mgr = ctx.getConnectionManager();
      connection = mgr.getPlsqlGatewayConnection(user);
      fndUser = connection.getFndUser();
   }

   private PlsqlProcessor getBufferProcessor() {
      if(bufferProcessor == null) {
         bufferProcessor = new PlsqlProcessor(connection, new WebRequestBufferTypeMap());
      }
      return bufferProcessor;
   }

   //==========================================================================
   // Buffer interface for database access
   //==========================================================================

   /**
    * Execute a stored procedure using buffer interface without any security checks.
    */
   public void call(String sql, Buffer vars) throws Exception {
      BufferCommandInstance cmd = new BufferCommandInstance(sql, vars);
      getBufferProcessor().call(cmd, false);
   }

   //==========================================================================
   //
   //==========================================================================

   /**
    * Release all resources used by this RequestContext
    */
   public void close() {
      try {
         if(connection != null) {
            connection.close();
         }
      }
      catch(SystemException | RuntimeException any) {
         log.error(any, "Ignored error when closing request context: &1", any.getMessage());
      }
      log = null;
   }

   //==========================================================================
   // Current values
   //==========================================================================


   /**
    * Returns the application owner used for execution of the current request
    * @return name of the database user that owns IFSAPP database objects
    */
   public String getApplicationOwner() {
      return appOwner;
   }

   /**
    * Return the Fnd User for the current request
    */
   public String getFndUser() {
      return fndUser;
   }


   /**
    * Return the database prefix (schema) valid for the current request
    */
   public String getDbPrefix() {
      return dbPrefix;
   }

   //==========================================================================
   //
   //==========================================================================


   /**
    * Returns a new instance of Buffer interface
    */
   public Buffer createNewBuffer() {
      return PlsqlUtil.createNewBuffer();
   }

   Logger getLogger() {
      return log;
   }

   //==========================================================================
   // Database connection/session
   //==========================================================================

   /**
    * Returns the (only) database connection used during the current request
    */
   public FndConnection getDatabaseConnection() {
      return connection;
   }
}
