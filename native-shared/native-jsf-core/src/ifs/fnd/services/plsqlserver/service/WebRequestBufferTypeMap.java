/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.services.plsqlserver.service;

import ifs.fnd.base.SystemException;
import ifs.fnd.record.FndSqlType;

/**
 * <B>Framework internal class:</B>
 * Implementation of BufferTypeMap that defines the mapping used by IFS Web Client.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class WebRequestBufferTypeMap implements BufferTypeMap {

   /**
    * Maps Item type to PlsqlType
    */
   @Override
   public PlsqlType itemTypeToPlsqlType(String type) throws SystemException {

      if(type==null || type.length()==0 || type.equals("S"))
         return PlsqlType.TEXT;
      else if("N".equals(type))
         return PlsqlType.NUMBER;
      else if("D".equals(type))
         return PlsqlType.TIMESTAMP;
      else if("R".equals(type))
         return PlsqlType.BINARY;
      else if("LT".equals(type))
         return PlsqlType.LONG_TEXT;
      else
         throw new SystemException(Texts.BADITEMTYPE, type);
   }

   /**
    * Maps PlsqlType to Item type
    */
   @Override
   public String plsqlTypeToItemType(PlsqlType type) throws SystemException {

      if(type == PlsqlType.TEXT)
         return "S";
      else if(type == PlsqlType.NUMBER)
         return "N";
      else if(type == PlsqlType.TIMESTAMP)
         return "D";
      else if(type == PlsqlType.ROWID)
         return "S";
      else if(type == PlsqlType.BINARY)
         return "R";
      else if (type == PlsqlType.LONG_TEXT)
         return "LT";
      else
         throw new SystemException(Texts.BADCONVERTPLTYPE, type.getName());
   }

   /**
    * Maps Item type to FndSqlType.
    * @param type the string value of an Item type
    * @return the corresponding FndSqlType
    * @throws SystemException if a conversion error occurs
    */
   @Override
   public FndSqlType itemTypeToFndSqlType(String type) throws SystemException {
      if(type == null || type.length() == 0 || type.equals("S"))
         return FndSqlType.TEXT;
      else if("N".equals(type))
         return FndSqlType.NUMBER;
      else if("D".equals(type))
         return FndSqlType.TIMESTAMP;
      else if("R".equals(type))
         return FndSqlType.BINARY;
      else if("LT".equals(type))
         return FndSqlType.LONG_TEXT;
      else
         throw new SystemException(Texts.BADITEMTYPE2, type);
   }
}

