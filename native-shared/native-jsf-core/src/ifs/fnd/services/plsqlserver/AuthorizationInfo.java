/*
 * ==================================================================================
 * File:         AuthorizationInfo.java
 * Package:      Plsql_Server
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 * ==================================================================================
 */

package ifs.fnd.services.plsqlserver;

import ifs.fnd.record.*;

/**
 * View representing a parameter passed to PlsqlGateway.getAuthorizationInfo() operation.
 */
public class AuthorizationInfo extends FndView {
   public static final FndRecordMeta viewMeta  = new FndRecordMeta("PLSQL_SERVER", "AUTHORIZATION_INFO");

   private static final FndAttributeMeta systemNameMeta   = new FndAttributeMeta(viewMeta, "SYSTEM_NAME", null, 0, 50);
   private static final FndAttributeMeta userSessionMeta  = new FndAttributeMeta(viewMeta, "USER_SESSION");

   private static final FndAttributeMeta authorizationsMeta  = new FndAttributeMeta(viewMeta, "AUTHORIZATIONS");

   public final FndAlpha                       systemName   = new FndAlpha(systemNameMeta);
   public final FndBoolean                     userSession  = new FndBoolean(userSessionMeta);

   public final AuthorizationObjectArray       authorizations  = new AuthorizationObjectArray(authorizationsMeta);

   public AuthorizationInfo() {
      super(viewMeta);
      add(systemName);
      add(userSession);
      add(authorizations);
   }

   @Override
   public FndAbstractRecord newInstance() {
      return new AuthorizationInfo();
   }

   public static AuthorizationInfo newRecord() {
      return new AuthorizationInfo();
   }

}
