/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 * ----------------------------------------------------------------------------
 * File        : RefusedRequestException.java
 * Notes       :
 * ----------------------------------------------------------------------------
 * Modified    :
 *    2016-Dec-02  <PERSON>k D - Created (TEJSL-1282)
 * ----------------------------------------------------------------------------
 */
package ifs.fnd.http;

import ifs.fnd.base.SystemException;

/**
 * Subclass of SystemException that signals refused HTTP request.
 */
public class RefusedRequestException extends SystemException
{
   public RefusedRequestException(String msg, String... param)
   {
      super(msg, param);
   }
}
