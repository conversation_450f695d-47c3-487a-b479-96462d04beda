/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.record;

import java.util.Date;
import java.util.ArrayList;
import ifs.fnd.base.SystemException;
import ifs.fnd.base.IfsException;

/**
 * Base class for date attributes.
 * Its value is stored as java Date object.
 */
public abstract class FndAbstractDate extends FndQueryableAttribute {

   protected FndAbstractDate(FndAttributeType type) {
      super(type);
   }

   protected FndAbstractDate(FndAttributeType type, String name) {
      super(type, name);
   }

   protected FndAbstractDate(FndAttributeType type, String name, Date value) {
      super(type, name, value);
   }

   protected FndAbstractDate(FndAttributeMeta meta) {
      super(meta);
   }

   protected FndAbstractDate(FndAttributeMeta meta, Date value) {
      super(meta, value);
   }

   /**
    * Sets the value of the attribute from another FndAbstractDate.
    * @param field Attribute from which to copy the value
    * @throws IfsException if the type of the specified attribute does not match
    *                      the type of this attribute
    */
   public void setValue(FndAbstractDate field) throws IfsException {
      if (field.getClass() != getClass())
         throw new SystemException("ABSDATESETVAL: Attribute type mismatch in FndAbstractDate.setValue()");
      internalSetValue(field.internalGetValue());
   }

   /**
    * Create a "attribute like value" condition on this attribute.
    * The condition will be converted to SQL expression "TO_CHAR(column, :formatMask) LIKE :value".
    * @param value Value to compare attribute to. Use wildcards '%' and '_'.
    * @param formatMask Oracle specific format mask for TO_CHAR function
    * @return Created condition.
    */
   public FndSimpleCondition createLikeCondition(String value, String formatMask) {
      ArrayList<String> list = new ArrayList<>(2);
      list.add(value);
      list.add(formatMask);
      return new FndSimpleCondition(new FndText(getName()), FndQueryOperator.LIKE, list);
   }

   /**
    * Create a "attribute not like value" condition on this attribute.
    * The condition will be converted to SQL expression "TO_CHAR(column, :formatMask) NOT LIKE :value".
    * @param value Value to compare attribute to. Use wildcards '%' and '_'.
    * @param formatMask Oracle specific format mask for TO_CHAR function
    * @return Created condition.
    */
   public FndSimpleCondition createNotLikeCondition(String value, String formatMask) {
      ArrayList<String> list = new ArrayList<>(2);
      list.add(value);
      list.add(formatMask);
      return new FndSimpleCondition(new FndText(getName()), FndQueryOperator.NOT_LIKE, list);
   }
}
