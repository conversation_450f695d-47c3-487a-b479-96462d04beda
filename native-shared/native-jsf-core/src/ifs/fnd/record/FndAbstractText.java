/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.record;

import ifs.fnd.base.IfsException;
import ifs.fnd.base.SystemException;

/**
 * Base class for text attributes.
 * Its value is stored as java String object or an {@link ifs.fnd.record.FndEnumeration FndEnumeration} value.
 */
public abstract class FndAbstractText extends FndQueryableAttribute {

   protected FndAbstractText(FndAttributeType type) {
      super(type);
   }

   protected FndAbstractText(FndAttributeType type, String name) {
      super(type, name);
   }

   protected FndAbstractText(FndAttributeType type, String name, String value) {
      super(type, name, value);
   }

   protected FndAbstractText(FndAttributeMeta meta) {
      super(meta);
   }

   protected FndAbstractText(FndAttributeMeta meta, String value) {
      super(meta, value);
   }

   /**
    * Sets the value of the attribute from another FndAbstractText.
    * @param field Attribute from which to copy the value
    * @throws IfsException if the type of the specified attribute does not match
                            the type of this attribute or if the value is out of range
    */
   public void setValue(FndAbstractText field) throws IfsException {
      if (this instanceof FndEnumeration) {
         FndEnumeration enumeration = (FndEnumeration) this;
         enumeration.parseString(field.toString()); // validate and set value
         setDirty();
         setQuery(false);
      }
      else {
         if (field.getClass() != getClass()) {
            throw new SystemException("ABSTEXTSETVAL: Attribute type mismatch in FndAbstractText.setValue()");
         }
         internalSetValue(field.internalGetValue());
      }
   }
}
