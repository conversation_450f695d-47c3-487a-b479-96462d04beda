/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;

import ifs.fnd.base.ParseException;
import ifs.fnd.base.SystemException;
import ifs.fnd.record.serialization.*;

/**
 * Abstract super-class for aggregates.
 * Subclasses of FndAbstractAggregate are generated from a model created in IFS Developer Studio.
 *
 * @see ifs.fnd.record.FndAggregate
 */
public abstract class FndAbstractAggregate extends FndCompoundAttribute implements Cloneable {

   /**
    * Constructor
    */
   public FndAbstractAggregate() {
      super(FndAttributeType.AGGREGATE);
   }

   /**
    * Constructor
    * @param name attribute name
    */
   public FndAbstractAggregate(String name) {
      super(FndAttributeType.AGGREGATE, name);
   }

   /**
    * Constructor
    * @param meta attribute meta data
    */
   public FndAbstractAggregate(FndAttributeMeta meta) {
      super(meta);
      setType(FndAttributeType.AGGREGATE);
   }

   /**
    * Constructor
    * @param meta attribute meta data
    * @param parentKeyInParent reference to parent
    */
   public FndAbstractAggregate(FndAttributeMeta meta, FndCompoundReference parentKeyInParent) {
      super(meta, parentKeyInParent, FndAttributeType.AGGREGATE);
   }

   /**
    * Get a new record of the type associated with this aggregate.
    * @return a new record of the type associated with this aggregate
    */
   protected abstract FndAbstractRecord getTemplateRecord();

   /**
    * Check if the aggregate use framework or custom query functionality.
    * @return true if the aggregate use custom query implementation.
    */
   public boolean customQuery() {
      return false;
   }

   /**
    * Sets the record that is the value of the aggregate.
    * @param rec A record contained by the aggregate
    */
   protected final void internalSetRecord(FndAbstractRecord rec) {
      internalSetValue(rec);
      if (rec != null)
         connectElement(rec);
   }

   /**
    * Set record state on the record that is the value of the aggregate.
    * The method does nothing if the value of the aggregate is null.
    * @param state record state
    */
   protected final void markStateRecursively(FndRecordState state) {
      if (internalGetValue() != null) { //only set the internal record state if it has been created - this will avoid problems with recursive aggregates
         FndAbstractRecord record = internalGetRecord();
         if (record != null) {
            record.setState(state);
         }
      }
   }

   /**
    * Sets the nonexistent flag on the aggregate.
    */
   @Override
   public final void setNonExistent() {
      if (internalGetValue() != null) {
         //only set the internal record to non existent if it has been created - this will avoid problems with recursive aggregates
         FndAbstractRecord r = internalGetRecord();
         if (r != null)
            r.setNonExistent();
      }
      super.setNonExistent();
   }

   /**
    * Returns a record that is the value of the aggregate
    * @return value of the aggregate
    */
   protected final FndAbstractRecord internalGetRecord() {
      FndAbstractRecord r = (FndAbstractRecord) internalGetValue();
      if (r == null) {
         r = getTemplateRecord();
         if (r != null) {
            super.value = r;
            //setQuery(false); ???
            connectElement(r);
            setNonExistent();

            //internalSetRecord(rec);
            //setDirty(false);
            //setNonExistent();
            //unset();
         }
      }
      return r;
   }

   /**
    * Formats the aggregate into an Fnd buffer
    * @param stream Where to output the resulting buffer
    * @param releaseMemory true if after formatting the attribute value should be cleared releasing the memory, false otherwise
    * @throws ParseException Error in buffer formatting
    */
   @Override
   protected final void formatValue(FndTokenWriter stream, boolean releaseMemory) throws ParseException {
      stream.write(FndSerializeConstants.BEGIN_BUFFER_MARKER);
      internalGetRecord().formatItem(stream);
      stream.write(FndSerializeConstants.END_BUFFER_MARKER);
      if(releaseMemory)
         value = null;
   }

   /**
    * Approximates the size of the serialized value of this attribute.
    * @param changedValueMode current value of ChangedValueMode that controls formatting process
    * @return the number of bytes needed for the serialized value of this attribute
    * @see #formatValue
    */
   @Override
   int serializedValueSize(FndAbstractRecord.ChangedValueMode changedValueMode) {
      int size = 2;
      size += internalGetRecord().serializedItemSize(changedValueMode);
      return size;
   }

   /**
    * Parses an FND buffer and sets the value accordingly
    * @param stream Stream from which to read the buffer
    * @throws ParseException Errors in the input buffer
    */
   @Override
   protected final void parseBuffer(FndTokenReader stream) throws ParseException {
      String name = null;

      char ch = stream.getDelimiter();

      if (ch == FndSerializeConstants.BEGIN_BUFFER_MARKER)
         ch = stream.getDelimiter();

      while (ch != FndSerializeConstants.END_BUFFER_MARKER) {
         FndAbstractRecord record = internalGetRecord();

         if (ch == FndSerializeConstants.NAME_MARKER)
            name = stream.getToken(); //DATA

         if (record != null)
            record = record.newInstance();
         else
            record = new FndRecord("NONAME");

         record.setState(FndRecordState.QUERY_RECORD);
         connectElement(record);
         record.parse(stream);
         this.value = record;
         set();
         setExistent();
         ch = stream.getDelimiter();
      }
   }

   /**
    * Formats this object into the XML stream.
    * @param s XML serializer to which to append the attribute contents
    * @param customAttr top level attribute (tested with "==" operator) which will be formatted by the custom formatter
    * @param customFormatter formatter that will format the custom attribute value
    */
   @Override
   protected final void formatAttributeToXml(FndXmlSerializer s, FndAttribute customAttr, FndAttributeXmlFormatter customFormatter) throws ParseException {
      if (!exist()) // If the attribute is non-existent or is a key, don't do anything.
         return;

      FndXmlElementAttributeList attributes = new FndXmlElementAttributeList();
      if(s.isFlagSet(FndXmlSerializer.FLAG_DATATYPE_INFO))
         attributes.add("ifsrecord:datatype", meta.getType().getName());

      if (isDirty() && s.isFlagSet(FndXmlSerializer.FLAG_DIRTY_INFO))
            attributes.add("ifsrecord:dirty", "true");

      if(isExcluded() && s.isFlagSet(FndXmlSerializer.FLAG_EXCLUDE_INFO))
         attributes.add("ifsrecord:exclude", "1");

      String name = getName();

      if (isNull()) {
         attributes.add("xsi:nil", "1");
         s.addElement(name, attributes);
         s.newLine();
      }
      else {
         // If the name of this array is the same as it's records, append "_LIST" to the name.
         //if (!isNull() && name != null && name.equalsIgnoreCase(internalGetRecord().getType()))
         //   name = name + "_LIST";
         s.startElement(name, attributes);
         s.newLine();
         internalGetRecord().formatToXml(s, null, null);
         s.endElement(name);
         s.newLine();

         if(s.destroyFormattedRecords())
            value = null;
      }
   }

   /**
    * Formats this attribute's value to the xml stream.
    * @param s XML serializer to which to append the value
    */
   @Override
   protected final void formatValueToXml(FndXmlSerializer s) throws ParseException{
   }

   /**
    * Copy the value of an attribut to this attribute.
    * Argument must be an instance of FndAbstractAggregate.
    * @param from Attribute to copy value from.
    * @throws SystemException
    */
   @Override
   protected final void assign(FndAttribute from) throws SystemException {
      if (!(from instanceof FndAbstractAggregate))
         throw new SystemException("AGGASSIGN:Could not assign &1 to &2 since &1 is not an aggregate.", from.getName(), getName());

      try {
         FndAbstractAggregate agg = (FndAbstractAggregate) from;
         if (from.exist() && !from.isNull())
            internalSetRecord((FndAbstractRecord) agg.internalGetRecord().clone());
      }
      catch (CloneNotSupportedException e) {
         throw new SystemException(e, "AGGASSIGNCLONE:Could not assign &1 to &2.", from.getName(), getName());
      }
   }

   /**
    * Copy the attribute. Internal record will be cloned.
    * @param target Attribute to copy to (must be an instance of FndAbstractAggregate).
    * @param cloning true if value should be cloned
    */
   @Override
   protected final void copy(FndAttribute target, boolean cloning) throws CloneNotSupportedException, SystemException {
      super.copy(target, cloning);

      FndAbstractAggregate targetAggregate = (FndAbstractAggregate) target;
      if (!this.isNull()) {
         FndAbstractRecord r = targetAggregate.internalGetRecord();
         if (r instanceof FndView) {
            FndAbstractRecord template = targetAggregate.getTemplateRecord();
            if (template != null && !template.getClass().isInstance(r)) { //Check if we need to do a transform
                ((FndView) r).transformView((FndView) template);
               targetAggregate.internalSetRecord(template);
               targetAggregate.connectElement(template);
            }
            else {
               targetAggregate.connectElement(r);
            }
         }
         else {
            targetAggregate.connectElement(r);
         }
      }
   }

   /**
    * Clone the internal (record) value.
    */
   @Override
   protected final Object cloneValue() throws CloneNotSupportedException {
      if (!this.isNull())
         return this.internalGetRecord().clone();
      else
         return null;
   }

   /**
    * Clones this attribute
    * @return A copy of the attribute.
    * @throws CloneNotSupportedException if cloning is not supported
    */
   @Override
   public final Object clone() throws CloneNotSupportedException {
      FndAbstractAggregate aggregate = (FndAbstractAggregate) super.clone();
      if(!isNull()) {
         FndAbstractRecord record = aggregate.internalGetRecord();
         if (record != null) {
            aggregate.connectElement(record);
         }
      }
      return aggregate;
   }

   /**
    * Create a new attribute of the same type.
    * @param meta attribute meta data
    * @return New attribute instance.
    */
   @Override
   protected abstract FndAttribute newAttribute(FndAttributeMeta meta);
}
