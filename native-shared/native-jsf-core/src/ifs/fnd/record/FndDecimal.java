/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.record;

import java.math.BigDecimal;
import ifs.fnd.base.*;

/** FndDecimal datatype for attributes.
 */
public final class FndDecimal extends FndAbstractNumber {

   /**
    * Create a new instance.
    */
   public FndDecimal() {
      super(FndAttributeType.DECIMAL);
   }

   /**
    * Create a new instance, also specifying attribute name.
    * @param name attribute name
    */
   public FndDecimal(String name) {
      super(FndAttributeType.DECIMAL, name);
   }

   /**
    * Create a new instance, also specifying attribute name and initial value.
    * @param name attribute name
    * @param value initial attribute value
    */
   public FndDecimal(String name, BigDecimal value) {
      super(FndAttributeType.DECIMAL, name, value);
   }

   /**
    * Create a new instance based on specified meta data.
    * @param meta attribute meta data
    */
   public FndDecimal(FndAttributeMeta meta) {
      super(meta);
      setType(FndAttributeType.DECIMAL);
   }

   /**
    * Create a new instance based on specified meta data. Also set initial attribute value.
    * @param meta meta data
    * @param value initial value
    */
   public FndDecimal(FndAttributeMeta meta, BigDecimal value) {
      super(meta, value);
      setType(FndAttributeType.DECIMAL);
   }

   /**
    * Creates a new attribute based on meta-data.
    * @param meta Meta-data to base the attribute on
    * @return The created attribute
    */
   @Override
   protected FndAttribute newAttribute(FndAttributeMeta meta) {
      return new FndDecimal(meta);
   }

   /** Gets the value of the attribute.
    * @return Value
    */
   public BigDecimal getValue() {
      return (BigDecimal)internalGetValue();
   }

   /** Return the value of the attribute. If value is null, return defaultValue.
    *  @param defaultValue Value to return if attribute value is null.
    *  @return Attribute value.
    */
   public BigDecimal getValue(BigDecimal defaultValue) {
      return isNull() ? defaultValue : getValue();
   }

   /** Sets the value of the attribute.
    * @param value Value
    */
   public void setValue(BigDecimal value) {
      internalSetValue(value);
   }

   /** Copies the value of another FndDecimal
    * @param field Attribute to copy the value from
    */
   public void setValue(FndDecimal field) {
      setValue(field.getValue());
   }

   /** Sets the value of the attribute from another attribute.
    *  The main difference between assign and setValue is that the value will be cloned
    *  in assign (if necessary). The attribute value will never be cloned in setValue.
    * @param from Attribute to copy value from
    * @throws SystemException Thrown if cloning fails
    */
   public void assign(FndDecimal from) throws SystemException {
      super.assign(from);
   }

   /** Sets the value of the attribute from a string containing a decimal number.
    * @param value A string containing a decimal number
    * @throws ParseException if <code>value</code> is of illegal format.
    */
   @Override
   public void parseString(String value) throws ParseException {
      try {
         if (value == null || value.length() == 0) {
            this.value = null;
         }
         else {
            this.value = new BigDecimal(value);
         }
         set();
         setExistent();
      }
      catch (NumberFormatException ex) {
         throw new ParseException(ex, "ILLEGALDECVALUE: Illegal decimal value (&1) for attribute &2.&3", value, getParentRecord().getName(), getName());
      }
   }

   /** Compares two attributes. Used for sorting.
    * @param attr Attribute to compare with
    * @return 0 if attribute values are equal, a value less than 0 if the value of this
    * attribute is less than the value of the specified attribute. A value greater than 0
    * is returned if the value of this attribute is greater than the value of the specified attribute.
    */
   @Override
   public int compareTo(FndAttribute attr) {
      if (this == attr)
         return 0;

      else if (attr instanceof FndDecimal) {
         if (this.isNull() && attr.isNull())
            return 0;
         else if (!this.isNull())
            return this.getValue().compareTo(((FndDecimal)attr).getValue());
      }
      return 1;
   }

   /** Convert attribute value to a FndSqlValue
    *  @return new FndSqlValue
    */
   @Override
   protected FndSqlValue toFndSqlValue() {
      if (this.isNull()) {
         FndSqlValue val = new FndSqlValue(this.getName(), this.getSqlType());
         val.setNull();
         return val;
      }
      else {
         return new FndSqlValue(this.getName(), this.getValue());
      }
   }

   /**
    * Get value from database and set on this attribute
    * @param data an instance of FndSqlData interface to get value from
    * @param colNr column/parameter index number
    */
   @Override
   protected void setSqlValue(FndSqlData data, int colNr) throws IfsException {
      this.setValue(data.getBigDecimal(colNr));
   }

   /** Get SQL type
    */
   @Override
   protected FndSqlType getSqlType() {
      return FndSqlType.DECIMAL;
   }

   /**
    * See {@link FndAttribute#createBetweenCondition(Object,Object) createBetweenCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createBetweenCondition(BigDecimal value1, BigDecimal value2) {
      return super.createBetweenCondition(value1, value2);
   }

   /**
    * See {@link FndAttribute#createEqualCondition(Object) createEqualCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createEqualCondition(BigDecimal value) {
      return super.createEqualCondition(value);
   }

   /**
    * See {@link FndAttribute#createGreaterThanCondition(Object) createGreaterThanCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createGreaterThanCondition(BigDecimal value) {
      return super.createGreaterThanCondition(value);
   }

   /**
    * See {@link FndAttribute#createGreaterThanOrEqualCondition(Object) createGreaterThanOrEqualCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createGreaterThanOrEqualCondition(BigDecimal value) {
      return super.createGreaterThanOrEqualCondition(value);
   }

   /**
    * See {@link FndAttribute#createLessThanCondition(Object) createLessThanCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createLessThanCondition(BigDecimal value) {
      return super.createLessThanCondition(value);
   }

   /**
    * See {@link FndAttribute#createLessThanOrEqualCondition(Object) createLessThanOrEqualCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createLessThanOrEqualCondition(BigDecimal value) {
      return super.createLessThanOrEqualCondition(value);
   }

   /**
    * See {@link FndAttribute#createNotEqualCondition(Object) createNotEqualCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createNotEqualCondition(BigDecimal value) {
      return super.createNotEqualCondition(value);
   }

   /**
    * Returns an FndDecimal attribute holding the value represented by the specified String.
    * @param value the string to be parsed
    * @return a new instance of FndDecimal with the specified value
    * @throws ParseException if the specified String has invalid format
    */
   public static FndDecimal valueOf(String value) throws ParseException {
      FndDecimal attr = new FndDecimal();
      attr.parseString(value);
      return attr;
   }
}
