/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;

/**
 * Enumeration of condition operators used to build compound conditions.
 *
 * @see ifs.fnd.record.FndCondition FndCondition
 */
public final class FndConditionOperator {
    private String name;

    private FndConditionOperator(String name) {
        this.name = name;
    }

   @Override
    public String toString() {
        return name;
    }

    public static final FndConditionOperator AND = new FndConditionOperator("AND");
    public static final FndConditionOperator OR = new FndConditionOperator("OR");
}
