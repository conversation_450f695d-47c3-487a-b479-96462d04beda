/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;


/**
 * Immutable meta-object describing a validation object.
 */
public final class FndValidationAttributeMeta {

   private FndAttributeMeta attribute;
   private boolean          mandotory = false;
   private boolean          uppercase = false;

   /**
    * Constructor used to create attribute validation.
    */
   public FndValidationAttributeMeta(FndAttributeMeta attribute, boolean mandotory, boolean uppercase) {
      this.attribute = attribute;
      this.mandotory = mandotory;
      this.uppercase = uppercase;
   }


   /**
    * Returns the mapped attribute of this validation.
    */
   public FndAttributeMeta getAttribute() {
      return attribute;
   }

   /**
    * Returns the attribute's Mandatory flag
    */
   public boolean isMandatory() {
      return mandotory;
   }

   /**
    * Returns the attribute's Uppercase flag
    */
   public boolean isUppercase() {
      return uppercase;
   }
}
