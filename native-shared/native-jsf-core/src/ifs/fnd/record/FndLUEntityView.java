/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;

import ifs.fnd.base.FndContext;
import ifs.fnd.record.serialization.*;
import java.util.List;

/**
 * Base class for entities built with IFS Foundation1.
 */
public class FndLUEntityView extends FndBaseEntityView {
   private static final FndAttributeMeta OBJVERSION_META = new FndAttributeMeta(true, "OBJ_VERSION", "OBJVERSION", 2000);
   private static final FndAttributeMeta OBJID_META = new FndAttributeMeta(true, "OBJ_ID", "OBJID", 50);

   /**
    * Objversion attribute.
    */
   public final FndAlpha objVersion = new FndAlpha(OBJVERSION_META);
   /**
    * Objid attribute.
    */
   public final FndAlpha objId = new FndAlpha(OBJID_META);

   /**
    * Framework internal constructor required by Externalizable interface.
    */
   public FndLUEntityView() {
      assert(false);
   }

   /**
    * Create a new instance.
    * @param name record name
    * @param table database table/view associated with the entity
    * @param entity entity name
    */
   public FndLUEntityView(String name, String table, String entity) {
      super(new FndRecordMeta(name, table, entity));
      addEntityAttribute(objId);
      addEntityAttribute(objVersion);
   }

   /**
    * Create a new instance based on a specified meta-record.
    * @param meta meta-record
    */
   public FndLUEntityView(FndRecordMeta meta) {
      super(meta);
      addEntityAttribute(objId);
      addEntityAttribute(objVersion);
      IFndCustomFieldProvider hook = FndContext.getCurrentContext().getCustomFieldProvider();
      if(hook!=null) {
          List<FndAttribute> customFields = hook.getCustomFields(meta);
          if(customFields!=null) {
             for(FndAttribute attr : customFields) {
                 add(attr);
             }
          }
      }
   }

   /**
    * Create a new instance.
    * @return new instance
    */
   @Override
   public FndAbstractRecord newInstance() {
      return new FndLUEntityView(meta);
   }

   /**
    * Controls if the parent record (if there is a parent) must be made dirty.
    * Records with no own objVersion attribute must propagate the dirty flag to it's parent.
    * @return false
    */
   @Override
   final boolean makeParentDirty() {
      return false;
   }

   /**
    * Format entity attributes to XML stream.
    * @param s XML serializer to format entity attributes to
    * @param attributes attribute list to add entity attributes to
    */
   @Override
   protected void formatEntityAttributesToXml(FndXmlSerializer s, FndXmlElementAttributeList attributes) {
      if (!objId.isNull())
         attributes.add("ifsrecord:OBJ_ID", objId.toString());

      if (!objVersion.isNull()){
          attributes.add("ifsrecord:OBJ_VERSION", s.encode(objVersion.toString()));
      }
   }

   /**
    * Check if an attribute is an entity attribute (like OBJ_ID or OBJ_VERSION).
    * @param attr attribute to check
    */
   @Override
   protected boolean isEntityAttribute(FndAttribute attr) {
      return attr == objId || attr == objVersion;
   }

   /**
    * Gets the table where the LOB columns are to be stored.
    * @return Table name
    */
   public final String getLobTable() {
      return meta.getLobTable();
   }

   public boolean isCustomLU() { return false;}
}
