/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.record;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface IFndCustomFieldProvider {
    /**
     * Fetch custom fields for a given database object (as specified by the meta data argument)
     * @param meta 
     */
    List<FndAttribute> getCustomFields(FndRecordMeta meta);
    
    /**
     * Get the actual view name to use for selects against the database. For LU's with custom fields this will be different from the standard view
     * @param entity
     * @param defaultView
     * @return The name of the view to use for database selects
     */
    String getView(String entity, String defaultView);
    
    /**
     * Get the actual package name to use for database updates. For LU's with custom fields this will be different from the standard package
     * @param view Name of the view that the document reads data from
     * @param defaultPkg
     * @return The name of the database package to use for database updates.
     */
    String getPlsqlPackage(String view, String defaultPkg);
}
