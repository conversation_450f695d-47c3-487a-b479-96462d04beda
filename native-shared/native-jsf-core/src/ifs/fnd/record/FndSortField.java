/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 * ----------------------------------------------------------------------------
 */

package ifs.fnd.record;

/**
 * Interface that represents a group of attributes that can act as a sort key.
 * Instances of FndSortField are used to define ordering of records in
 * a compile-time type-safe manner.
 * Both {@link FndAttribute} and {@link FndCompoundReference} implement this interface.
 * Interface FndSortField is used as type of arguments to
 * {@link FndQueryRecord#setOrderBy(FndSortField...) FndQueryRecord.setOrderBy} and
 * {@link FndAbstractArray#sort(FndSortField...) FndAbstractArray.sort}
 */
public interface FndSortField {

   /**
    * Returns an iterator over attributes contained in this sort field.
    */
   FndAttribute.Iterator sortFieldIterator();

   /**
    * Creates a new descending FndSortField.
    * The new sort field will contain the same attributes as this sort field
    * but the ordering of sort keys will be marked as descending.
    */
   FndSortField descending();

   /**
    * Returns the sort direction for this sort field.
    */
   FndSort.Direction getSortDirection();

}
