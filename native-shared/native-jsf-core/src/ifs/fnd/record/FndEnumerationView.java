package ifs.fnd.record;

import ifs.fnd.base.*;
import ifs.fnd.record.serialization.*;

/**
 * A view that represents all possible values of a specific subclass of FndEnumeration attribute.
 */
public class FndEnumerationView extends FndView {
   public static final FndRecordMeta VIEW_META = new FndRecordMeta("ENUMERATION");
   private static final FndAttributeMeta NAME_META = new FndAttributeMeta(VIEW_META, "NAME");
   private static final FndAttributeMeta VALUES_META = new FndAttributeMeta(VIEW_META, "VALUES");

   private static final FndRecordMeta VALUE_VIEW_META = new FndRecordMeta("ENUMERATION_VALUE");
   private static final FndAttributeMeta VALUE_ID_META = new FndAttributeMeta(VALUE_VIEW_META, "ID");
   private static final FndAttributeMeta VALUE_DESCRIPTION_META = new FndAttributeMeta(VALUE_VIEW_META, "DESCRIPTION");
   private static final FndAttributeMeta PROPERTIES_VIEW_META = new FndAttributeMeta(VALUE_VIEW_META, "PROPERTIES");

   private static final FndRecordMeta PROPERTY_VIEW_META = new FndRecordMeta("PROPERTY");
   private static final FndAttributeMeta PROPERTY_NAME_META = new FndAttributeMeta(PROPERTY_VIEW_META, "NAME");
   private static final FndAttributeMeta PROPERTY_VALUE_META = new FndAttributeMeta(PROPERTY_VIEW_META, "VALUE");

   public final FndText name = new FndText(NAME_META);
   public final EnumerationValueArray values = new EnumerationValueArray(VALUES_META);

   public FndEnumerationView() {
      super(VIEW_META);
      add(name);
      add(values);
   }

   @Override
   public FndAbstractRecord newInstance() {
      return new FndEnumerationView();
   }

   public static FndEnumerationView newRecord() {
      return new FndEnumerationView();
   }

   public Value addValue(String id, String description) {
      Value value = new Value();
      try {
         value.id.setValue(id);
         value.description.setValue(description);
      }
      catch (ApplicationException e) {
         throw new IfsRuntimeException(e, "");
      }
      values.add(value);
      return value;
   }

   public static final class EnumerationValueArray extends FndAbstractArray {
      public EnumerationValueArray() {
         super();
      }

      public EnumerationValueArray(FndAttributeMeta meta) {
         super(meta);
      }

      @Override
      protected FndAttribute newAttribute(FndAttributeMeta meta) {
         return new EnumerationValueArray(meta);
      }

      public boolean add(Value rec) {
         return internalAdd(rec);
      }

      public Value get(int i) {
         return (Value)internalGet(i);
      }

      @Override
      public FndAbstractRecord newRecord() {
         return new Value();
      }

      @Override
      protected FndAbstractRecord newRecord(FndTokenReader stream) throws ParseException {
         Value record = new Value();
         record.parse(stream);
         return record;
      }
   }

   public static final class Value extends FndView {
      public final FndText id = new FndText(VALUE_ID_META);
      public final FndAlpha description = new FndAlpha(VALUE_DESCRIPTION_META);
      public final PropertiesArray properties = new PropertiesArray(PROPERTIES_VIEW_META);

      public Value() {
         super(VALUE_VIEW_META);
         add(id);
         add(description);
         add(properties);
      }

      @Override
      public FndAbstractRecord newInstance() {
         return new Value();
      }
   }

   public static final class PropertiesArray extends FndAbstractArray {
      public PropertiesArray() {
         super();
      }

      public PropertiesArray(FndAttributeMeta meta) {
         super(meta);
      }

      @Override
      protected FndAttribute newAttribute(FndAttributeMeta meta) {
         return new PropertiesArray(meta);
      }

      public boolean add(FndProperty rec) {
         return internalAdd(rec);
      }

      public FndProperty get(int i) {
         return (FndProperty)internalGet(i);
      }

      @Override
      public FndAbstractRecord newRecord() {
         return new FndProperty();
      }

      @Override
      protected FndAbstractRecord newRecord(FndTokenReader stream) throws ParseException {
         FndProperty r = new FndProperty();
         r.parse(stream);
         return r;
      }
   }

   public static final class FndProperty extends FndView {

      public final FndText name = new FndText(PROPERTY_NAME_META);
      public final FndAlpha value = new FndAlpha(PROPERTY_VALUE_META);

      public FndProperty() {
         super(PROPERTY_VIEW_META);
         add(name);
         add(value);
      }

      @Override
      public FndAbstractRecord newInstance() {
         return new FndProperty();
      }
   }

   public FndProperty addProperty(Value valueNode, String name, String value) {
      FndProperty property = new FndProperty();
      try {
         property.name.setValue(name);
         property.value.setValue(value);
      }
      catch (ApplicationException e) {
         throw new IfsRuntimeException(e, "");
      }
      valueNode.properties.add(property);
      return property;
   }

}