/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.record;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.sql.Time;
import ifs.fnd.base.*;
import ifs.fnd.record.serialization.*;

/** FndTime data type for attributes.
 */
public final class FndTime extends FndAbstractDate {

   /**
    * Create a new instance.
    */
   public FndTime() {
      super(FndAttributeType.TIME);
   }

   /**
    * Create a new instance, also specifying attribute name.
    * @param name attribute name
    */
   public FndTime(String name) {
      super(FndAttributeType.TIME, name);
   }

   /**
    * Create a new instance, also specifying attribute name and initial value.
    * @param name attribute name
    * @param value initial attribute value
    */
   public FndTime(String name, Date value) {
      super(FndAttributeType.TIME, name, value);
   }

   /**
    * Create a new instance based on specified meta data.
    * @param meta attribute meta data
    */
   public FndTime(FndAttributeMeta meta) {
      super(meta);
      setType(FndAttributeType.TIME);
   }

   /**
    * Create a new instance based on specified meta data. Also set initial attribute value.
    * @param meta meta data
    * @param value initial value
    */
   public FndTime(FndAttributeMeta meta, Time value) {
      super(meta, value);
      setType(FndAttributeType.TIME);
   }

   /**
    * Creates a new attribute based on meta-data.
    * @param meta Meta-data to base the attribute on
    * @return The created attribute
    */
   @Override
   protected FndAttribute newAttribute(FndAttributeMeta meta) {
      return new FndTime(meta);
   }

   /** Gets the value of the attribute.
    * @return Value
    */
   public Date getValue() {
      return (Date)internalGetValue();
   }

   /** Return the value of the attribute. If value is null, return defaultValue.
    *  @param defaultValue Value to return if attribute value is null.
    *  @return Attribute value.
    */
   public Date getValue(Date defaultValue) {
      return isNull() ? defaultValue : getValue();
   }

   /** Sets the value of the attribute.
    * @param value Value
    */
   public void setValue(Date value) {
      internalSetValue(value);
   }

   /** Copies the value from another FndTime.
    * @param field Attribute to copy value from
    */
   public void setValue(FndTime field) {
      setValue(field.getValue());
   }

   /** Sets the value of the attribute from another attribute.
    *  The main difference between assign and setValue is that the value will be cloned
    *  in assign (if necessary). The attribute value will never be cloned in setValue.
    * @param from Attribute to copy value from
    * @throws SystemException Thrown if cloning fails
    */
   public void assign(FndTime from) throws SystemException {
      super.assign(from);
   }

   /** Returns string representation (as used in Fnd buffers) of the time.
    * @return Time in string format HH.mm.ss
    */
   @Override
   public String toString() {
      if (value != null) {
         SimpleDateFormat timeFormat = FndContext.getCurrentTimeFormat();
         return timeFormat.format(getValue());
      }

      return null;
   }

   /** Parses a string containing a time and sets the value of the attribute to that time.
    * @param value time value to parse.
    * throws ParseException  if <code>value</code> is of illegal format.
    */
   @Override
   public void parseString(String value) throws ifs.fnd.base.ParseException {
      try {
         parseString(value, FndContext.getCurrentTimeFormat());
      }
      catch(ifs.fnd.base.ParseException e) {
         parseString(value, FndContext.getCurrentXMLTimeFormat());
      }
   }

   /** Parses a string containing a time and sets the value of the attribute to that time.
    * @param value time value to parse.
    * @param timeFormat Object used to parse time value
    * throws ParseException  if <code>value</code> is of illegal format.
    */
   private void parseString(String value, SimpleDateFormat timeFormat) throws ifs.fnd.base.ParseException {
      try {
         if(value == null || value.length()==0) {
            this.value = null;
         }
         else {
            this.value = timeFormat.parseObject(value);
         }
         set();
         setExistent();
      }
      catch(ParseException e) {
         throw new ifs.fnd.base.ParseException(e, "ILLEGALTIMEVALUE: Illegal time value (&1) for attribute &2.&3",
         value, getParentRecord().getName(), getName());
      }
   }

   /** Formats this attribute into an XML stream
    * @param s XML serializer to which to add the value to
    */
   @Override
   protected void formatValueToXml(FndXmlSerializer s)  throws ifs.fnd.base.ParseException{
      SimpleDateFormat xmlDateFormatter = FndContext.getCurrentXMLTimeFormat();
      s.append(xmlDateFormatter.format(getValue()));
   }

   /** Compares two attributes. Used for sorting.
    * @param attr Attribute to compare with
    * @return 0 if equal
    */
   @Override
   public int compareTo(FndAttribute attr) {
      if (this == attr)
         return 0;

      if (attr instanceof FndTime) {
         if (this.isNull() && attr.isNull())
            return 0;
         else if (!this.isNull())
            return this.getValue().compareTo(((FndTime)attr).getValue());
      }
      return 1;
   }

   /** Convert attribute value to a FndSqlValue
    *  @return a new FndSqlValue
    */
   @Override
   protected FndSqlValue toFndSqlValue() {
      if (this.isNull()) {
         FndSqlValue val = new FndSqlValue(this.getName(), this.getSqlType());
         val.setNull();
         return val;
      }
      else {
         return new FndSqlValue(this.getName(), new Time(this.getValue().getTime()));
      }
   }

   /**
    * Get value from database and set on this attribute
    * @param data an instance of FndSqlData interface to get value from
    * @param colNr column index number
    */
   @Override
   protected void setSqlValue(FndSqlData data, int colNr) throws IfsException {
      Time time = data.getTime(colNr);
      if (time != null)
         this.setValue(new Date(time.getTime()));
      else
         this.setNull();
   }

   /** Get SQL type
    */
   @Override
   protected FndSqlType getSqlType() {
      return FndSqlType.TIME;
   }

   /**
    * See {@link FndAttribute#createBetweenCondition(Object,Object) createBetweenCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createBetweenCondition(Time value1, Time value2) {
      return super.createBetweenCondition(value1, value2);
   }

   /**
    * See {@link FndAttribute#createEqualCondition(Object) createEqualCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createEqualCondition(Time value) {
      return super.createEqualCondition(value);
   }

   /**
    * See {@link FndAttribute#createGreaterThanCondition(Object) createGreaterThanCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createGreaterThanCondition(Time value) {
      return super.createGreaterThanCondition(value);
   }

   /**
    * See {@link FndAttribute#createGreaterThanOrEqualCondition(Object) createGreaterThanOrEqualCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createGreaterThanOrEqualCondition(Time value) {
      return super.createGreaterThanOrEqualCondition(value);
   }

   /**
    * See {@link FndAttribute#createLessThanCondition(Object) createLessThanCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createLessThanCondition(Time value) {
      return super.createLessThanCondition(value);
   }

   /**
    * See {@link FndAttribute#createLessThanOrEqualCondition(Object) createLessThanOrEqualCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createLessThanOrEqualCondition(Time value) {
      return super.createLessThanOrEqualCondition(value);
   }

   /**
    * See {@link FndAttribute#createNotEqualCondition(Object) createNotEqualCondition} method description.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    */
   public FndSimpleCondition createNotEqualCondition(Time value) {
      return super.createNotEqualCondition(value);
   }

   /**
    * Create a "attribute between attribute1 and attribute2" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr1 the first attribute to compare this attribute to
    * @param attr2 the second attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createBetweenCondition(FndTime attr1, FndTime attr2) {
      return new FndSimpleCondition(this, FndQueryOperator.BETWEEN, attr1, attr2);
   }

   /**
    * Create a 'attribute = attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createEqualCondition(FndTime attr) {
      return new FndSimpleCondition(this, FndQueryOperator.EQUAL, attr, null);
   }

   /**
    * Create a 'attribute > attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createGreaterThanCondition(FndTime attr) {
      return new FndSimpleCondition(this, FndQueryOperator.GREATER_THAN, attr, null);
   }

   /**
    * Create a 'attribute >= attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createGreaterThanOrEqualCondition(FndTime attr) {
      return new FndSimpleCondition(this, FndQueryOperator.GREATER_THAN_OR_EQUAL, attr, null);
   }

   /**
    * Create a 'attribute < attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createLessThanCondition(FndTime attr) {
      return new FndSimpleCondition(this, FndQueryOperator.LESS_THAN, attr, null);
   }

   /**
    * Create a 'attribute <= attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createLessThanOrEqualCondition(FndTime attr) {
      return new FndSimpleCondition(this, FndQueryOperator.LESS_THAN_OR_EQUAL, attr, null);
   }

   /**
    * Create a 'attribute <> attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createNotEqualCondition(FndTime attr) {
      return new FndSimpleCondition(this, FndQueryOperator.NOT_EQUAL, attr, null);
   }

   /** Clone attribute value.
    *  @return A copy of the internal Date value.
    */
   @Override
   protected Object cloneValue() throws CloneNotSupportedException {
      if (!this.isNull())
         return this.getValue().clone();
      else
         return null;
   }

   /**
    * Returns an FndTime attribute holding the value represented by the specified String.
    * @param value the string to be parsed
    * @return a new instance of FndTime with the specified value
    * @throws ParseException if the specified String has invalid format
    */
   public static FndTime valueOf(String value) throws ifs.fnd.base.ParseException {
      FndTime attr = new FndTime();
      attr.parseString(value);
      return attr;
   }
}
