/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.record;

import ifs.fnd.base.IfsException;
import ifs.fnd.base.SystemException;

import java.util.ArrayList;

/**
 * Base class for numeric attributes.
 * Its value is stored as a subclass of java.lang.Number (Long, Double, BigDecimal).
 */
public abstract class FndAbstractNumber extends FndQueryableAttribute {

   protected FndAbstractNumber(FndAttributeType type) {
      super(type);
   }

   protected FndAbstractNumber(FndAttributeType type, String name) {
      super(type, name);
   }

   protected FndAbstractNumber(FndAttributeType type, String name, Number value) {
      super(type, name, value);
   }

   protected FndAbstractNumber(FndAttributeMeta meta) {
      super(meta);
   }

   protected FndAbstractNumber(FndAttributeMeta meta, Number value) {
      super(meta, value);
   }

   /** Sets the value of the attribute from another FndAbstractNumber.
    *  @param field Attribute from which to copy the value
    *  @throws IfsException - if the type of the specified attribute does not match
    *                         the type of this attribute
    */
   public void setValue(FndAbstractNumber field) throws IfsException {
      if (field.getClass() != getClass())
         throw new SystemException("ABSNUMSETVAL: Attribute type mismatch in FndAbstractNumber.setValue()");
      internalSetValue(field.internalGetValue());
   }

   /**
    * Create a "attribute between attribute1 and attribute2" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr1 the first attribute to compare this attribute to
    * @param attr2 the second attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createBetweenCondition(FndAbstractNumber attr1, FndAbstractNumber attr2) {
      return new FndSimpleCondition(this, FndQueryOperator.BETWEEN, attr1, attr2);
   }

   /**
    * Create a 'attribute = attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createEqualCondition(FndAbstractNumber attr) {
      return new FndSimpleCondition(this, FndQueryOperator.EQUAL, attr, null);
   }

   /**
    * Create a 'attribute > attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createGreaterThanCondition(FndAbstractNumber attr) {
      return new FndSimpleCondition(this, FndQueryOperator.GREATER_THAN, attr, null);
   }

   /**
    * Create a 'attribute >= attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createGreaterThanOrEqualCondition(FndAbstractNumber attr) {
      return new FndSimpleCondition(this, FndQueryOperator.GREATER_THAN_OR_EQUAL, attr, null);
   }

   /**
    * Create a 'attribute < attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createLessThanCondition(FndAbstractNumber attr) {
      return new FndSimpleCondition(this, FndQueryOperator.LESS_THAN, attr, null);
   }

   /**
    * Create a 'attribute <= attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createLessThanOrEqualCondition(FndAbstractNumber attr) {
      return new FndSimpleCondition(this, FndQueryOperator.LESS_THAN_OR_EQUAL, attr, null);
   }

   /**
    * Create a 'attribute <> attribute" condition on this attribute.
    * <P>See class {@link ifs.fnd.record.FndSimpleCondition FndSimpleCondition} for example of usage.
    * @param attr the attribute to compare this attribute to
    * @return Created condition.
    */
   public FndSimpleCondition createNotEqualCondition(FndAbstractNumber attr) {
      return new FndSimpleCondition(this, FndQueryOperator.NOT_EQUAL, attr, null);
   }

   /**
    * Create a "attribute like value" condition on this attribute.
    * The condition will be converted to SQL expression "TO_CHAR(column, :formatMask) like :value".
    * @param value Value to compare attribute to. Use wildcards '%' and '_'.
    * @param formatMask Oracle specific format mask for TO_CHAR function
    * @return Created condition.
    */
   public FndSimpleCondition createLikeCondition(String value, String formatMask) {
      ArrayList<String> list = new ArrayList<>(2);
      list.add(value);
      list.add(formatMask);
      return new FndSimpleCondition(new FndText(getName()), FndQueryOperator.LIKE, list);
   }

   /**
    * Create a "attribute not like value" condition on this attribute.
    * The condition will be converted to SQL expression "TO_CHAR(column, :formatMask) NOT LIKE :value".
    * @param value Value to compare attribute to. Use wildcards '%' and '_'.
    * @param formatMask Oracle specific format mask for TO_CHAR function
    * @return Created condition.
    */
   public FndSimpleCondition createNotLikeCondition(String value, String formatMask) {
      ArrayList<String> list = new ArrayList<>(2);
      list.add(value);
      list.add(formatMask);
      return new FndSimpleCondition(new FndText(getName()), FndQueryOperator.NOT_LIKE, list);
   }
}
