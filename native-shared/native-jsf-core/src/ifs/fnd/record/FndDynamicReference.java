/*
 *                IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.record;

import ifs.fnd.base.*;

/**
 * Class that represents a dynamic reference in a generated view.
 */
public class FndDynamicReference extends FndCompoundReference {

   /**
    * Constructs an FndDynamicReference with the specified contents.
    */
   public FndDynamicReference(FndCompoundReferenceMeta ref, FndAttribute[] attributeList) {
      super(ref);
      for(int i=0; i<attributeList.length; i++) {
         add(attributeList[i]);
      }
   }

   /**
    * Assigns value to all attributes included in this reference.
    * @param ref the reference to get the attribute values from.
    */
   public void assign(FndDynamicReference ref) throws IfsException {
      protectedAssign(ref);
   }

   /**
    * Indicates whether some other reference is "equal to" this one.
    * @param ref the reference to compare this reference to.
    */
   public boolean isEqualTo(FndDynamicReference ref) {
      return protectedIsEqualTo(ref);
   }

}
