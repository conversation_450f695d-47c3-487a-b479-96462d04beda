/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;

/**
 * Validation rules for a specific view type.
 * @param <V> a subclass of FndView
 */
public abstract class FndValidation<V extends FndView> {

   /**
    * Gets the class of the view this validation rules should be applied on.
    * @return the runtime class corresponding to generic parameter V
    */
   public abstract Class<?> getViewClass();

   /**
    * Apply the validation rules on a specified view.
    * @param view a view to apply the validation rules on
    */
   public abstract void applyRules(V view);

   /**
    * Returns a string representation of this object.
    * @return a short description of the runtime type of this validation rules
    */
   @Override
   public String toString() {
      return "FndValidation<" + getClass().getName() + ">";
   }
}
