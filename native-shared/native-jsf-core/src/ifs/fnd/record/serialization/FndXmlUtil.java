/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record.serialization;

import ifs.fnd.base.SystemException;
import ifs.fnd.base.IfsRuntimeException;
import ifs.fnd.base.IfsException;
import ifs.fnd.internal.FndAttributeInternals;
import ifs.fnd.record.*;
import ifs.fnd.sf.storage.FndTermDefinitionStorage;
import ifs.fnd.util.Str;
import ifs.fnd.service.IfsProperties;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * Utility class for serializing to/from XML.
 */
public final class FndXmlUtil {

   /**
    * Initial size for temporary byte buffer used for formatting XML.
    */
   private static final int INITIAL_STAX_BUFFER_SIZE = 512;

   //Avoid instantiation
   private FndXmlUtil() {
   }

   //==============
   // formatRecord
   //==============

   /**
    * Formats a record into a utf-8 encoded xml string.
    * @param   record   the record to format.
    * @throws  ParseException if the record for some reason cannot be formatted
    * into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   public static byte[] formatRecord(FndAbstractRecord record) throws ifs.fnd.base.ParseException {
      return formatRecord(record, true, null, null);
   }

   /**
    * Formats a record into a utf-8 encoded xml string.
    * @param   record   the record to format.
    * @param   addRecordAttributes  if <code>true</code>, IFS record attributs
    * are used in the XML produced. These attributes are in the <code>ifsrecord</code>
    * namespace.
    * @throws  ParseException if the record for some reason cannot be formatted into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   public static byte[] formatRecord(FndAbstractRecord record, boolean addRecordAttributes) throws ifs.fnd.base.ParseException {
      return formatRecord(record, addRecordAttributes, null, null);
   }

   /**
    * Formats a record into a utf-8 encoded xml string.
    * @param   record   the record to format.
    * @param   flags the XML serialization flags to use. See class
    * {@link FndXmlSerializer FndXmlSerializer} for more information on the flags.
    * @throws  ParseException if the record for some reason cannot be formatted into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   public static byte[] formatRecord(FndAbstractRecord record, int flags) throws ifs.fnd.base.ParseException {
      return privateFormatRecord(record, flags, null, null, false);
   }

   /**
    * Formats a record into a utf-8 encoded xml string.
    * @param   record   the record to format.
    * @param   addRecordAttributes  if <code>true</code>, IFS record attributs
    * are used in the XML produced. These attributes are in the <code>ifsrecord</code>
    * namespace.
    * @param direction the direction (REQUEST/RESPONSE) of the formatted record.
    *        If the direction is not null and if the specified record is a generated parameter-list view
    *        then attributes with direction not matching the direction of the view will be omitted.
    * @throws  ParseException if the record for some reason cannot be formatted
    * into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   public static byte[] formatRecord(FndAbstractRecord record, boolean addRecordAttributes, FndRecordFormat.Direction direction) throws ifs.fnd.base.ParseException {
      return formatRecord(record, addRecordAttributes, null, direction);
   }

   /**
    * Formats a record into a utf-8 encoded xml string.
    * @param   record   the record to format.
    * @param   flags the XML serialization flags to use. See class
    * {@link FndXmlSerializer FndXmlSerializer} for more information on the flags.
    * @param direction the direction (REQUEST/RESPONSE) of the formatted record.
    *        If the direction is not null and if the specified record is a generated parameter-list view
    *        then attributes with direction not matching the direction of the view will be omitted.
    * @throws  ParseException if the record for some reason cannot be formatted
    * into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   public static byte[] formatRecord(FndAbstractRecord record, int flags, FndRecordFormat.Direction direction) throws ifs.fnd.base.ParseException {
      return privateFormatRecord(record, flags, null, direction, false);
   }

   /**
    * Formats a record into a utf-8 encoded xml string.
    * @param   record   the record to format.
    * @param   namespace  Will be used in the xml namepace. If null then created from the metadata view
    * <code>null</code> value for this parameter indicates a that a Business API
    * is not in use. (When a namspace is used, IFS record attributes are not added).
    * @param direction the direction (REQUEST/RESPONSE) of the formatted record.
    *        If the direction is not null and if the specified record is a generated parameter-list view
    *        then attributes with direction not matching the direction of the view will be omitted.
    * @throws  ParseException if the record for some reason cannot be formatted
    * into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   public static byte[] formatRecord(FndAbstractRecord record, String namespace, FndRecordFormat.Direction direction) throws ifs.fnd.base.ParseException {
      return formatRecord(record, true, namespace, direction);
      // true here is a default used when namespace==null.
   }

   /**
    * Formats a record into a utf-8 encoded xml string.
    * @param   record   the record to format.
    * @param   namespace  Will be used in the xml namepace. If null then created from the metadata view
    * <code>null</code> value for this parameter indicates a that a Business API
    * is not in use. (When a namespace is used, IFS record attributes are not added).
    * @throws  ParseException if the record for some reason cannot be formatted
    * into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   public static byte[] formatRecord(FndAbstractRecord record, String namespace) throws ifs.fnd.base.ParseException {
      return formatRecord(record, true, namespace, null);
      // true here is a default used when namespace==null.
   }

   /**
    * Formats a record into a utf-8 encoded xml string.
    * @param record   the record to format.
    * @param direction the direction (REQUEST/RESPONSE) of the formatted record.
    *        If the direction is not null and if the specified record is a generated parameter-list view
    *        then attributes with direction not matching the direction of the view will be omitted.
    * @throws ParseException if the record for some reason cannot be formatted into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   public static byte[] formatRecord(FndAbstractRecord record, FndRecordFormat.Direction direction) throws ifs.fnd.base.ParseException {
      return formatRecord(record, true, null, direction);
   }

   /**
    * Formats a record into a utf-8 encoded xml string.
    * @param   record   the record to format.
    * @param   addRecordAttributes  wheter IFS record attributes are to be
    * used or not. Not used when <code>namespace</code> is non-null.
    * @param   namespace  Will be used in the xml namepace. If null then created from the metadata view
    * <code>null</code> value for this parameter indicates a that a Business API
    * is not in use.
    * @param direction the direction (REQUEST/RESPONSE) of the formatted record.
    *        If the direction is not null and if the specified record is a generated parameter-list view
    *        then attributes with direction not matching the direction of the view will be omitted.
    * @throws  ParseException if the record for some reason cannot be formatted
    * into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   public static byte[] formatRecord(FndAbstractRecord record, boolean addRecordAttributes, String namespace, FndRecordFormat.Direction direction) throws ifs.fnd.base.ParseException {
      return privateFormatRecord(record, (addRecordAttributes ? FndXmlSerializer.FLAG_ALL_INFO : 0), namespace, direction, false);
   }

   /**
    * Formats a record into a utf-8 encoded xml string.
    * @param   record   the record to format.
    * @param   flags the XML serialization flags to use. See class
    * {@link FndXmlSerializer FndXmlSerializer} for more information on the flags. This
    * value is ignored if <code>namespace</code> parameter is non-null.
    * @param   namespace  Will be used in the xml namepace. If null then created from the metadata view
    * @param direction the direction (REQUEST/RESPONSE) of the formatted record.
    *        If the direction is not null and if the specified record is a generated parameter-list view
    *        then attributes with direction not matching the direction of the view will be omitted.
    * @param destroyFormattedRecords flag indicating if the formatted records and attributes
    *        should be destroyed to release memory
    *
    * @throws  ParseException if the record for some reason cannot be formatted
    * into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   private static byte[] privateFormatRecord(FndAbstractRecord record, int flags, String namespace, FndRecordFormat.Direction direction, boolean destroyFormattedRecords) throws ifs.fnd.base.ParseException {
      return privateFormatRecordStax(record, flags, namespace, direction, destroyFormattedRecords);
   }

   private static byte[] privateFormatRecordStax(FndAbstractRecord record, int flags, String namespace, FndRecordFormat.Direction direction, boolean destroyFormattedRecords) throws ifs.fnd.base.ParseException {
      FndByteBufferOutputStream out = new FndByteBufferOutputStream(INITIAL_STAX_BUFFER_SIZE);
      FndXmlWriter writer = new FndXmlWriter(out, FndXmlStreamUtil.UTF8);
      FndXmlRecordFormatter formatter = new FndXmlRecordFormatter(writer);
      formatter.setFlags(flags);
      formatter.setDefaultNamespace(namespace);
      formatter.setDirection(direction);
      formatter.setDestroyFormattedRecords(destroyFormattedRecords);
      formatter.formatRecord(record);
      writer.close();
      return out.getBytes();
   }

   //===================
   // formatRecordArray
   //===================

   /**
    * Formats an array of records into a utf-8 encoded xml string.
    * @param   array array to format.
    * @param   addRecordAttributes  wheter IFS record attributes are to be
    * @param   namespace  Will be used in the xml namepace. If null then created from the metadata view
    * is not in use.
    * @param direction the direction (REQUEST/RESPONSE) of the formatted array.
    *        If the direction is not null then the name of the root XML element will be set to
    *        the direction name ("REQUEST" or "RESPONSE"), otherwise a default name will be created.
    * @throws  ParseException if the array for some reason cannot be formatted into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   public static byte[] formatRecordArray(FndAbstractArray array, boolean addRecordAttributes, String namespace, FndRecordFormat.Direction direction) throws ifs.fnd.base.ParseException {
      return formatRecordArray(array, (addRecordAttributes ? FndXmlSerializer.FLAG_ALL_INFO : 0), namespace, direction);
   }

   /**
    * Formats an array of records into a utf-8 encoded xml string.
    * @param   array array to format.
    * @param   flags the XML serialization flags to use. See class
    * {@link FndXmlSerializer FndXmlSerializer} for more information on the flags. This

    * @param   namespace  the name of the Business API in use (if any).  A
    * <code>null</code> value for this parameter indicates a that a Business API
    * is not in use.
    * @param direction the direction (REQUEST/RESPONSE) of the formatted array.
    *        If the direction is not null then the name of the root XML element will be set to
    *        the direction name ("REQUEST" or "RESPONSE"), otherwise a default name will be created.
    * @throws  ParseException if the array for some reason cannot be formatted into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   public static byte[] formatRecordArray(FndAbstractArray array, int flags, String namespace, FndRecordFormat.Direction direction) throws ifs.fnd.base.ParseException {
      return privateFormatRecordArray(array, flags, namespace, direction, false);
   }

   /**
    * Formats an array of records into a utf-8 encoded xml string.
    * @param   array array to format.
    * @param   flags the XML serialization flags to use. See class
    * {@link FndXmlSerializer FndXmlSerializer} for more information on the flags. This

    * @param   namespace  the name of the Business API in use (if any).  A
    * <code>null</code> value for this parameter indicates a that a Business API
    * is not in use.
    * @param direction the direction (REQUEST/RESPONSE) of the formatted array.
    *        If the direction is not null then the name of the root XML element will be set to
    *        the direction name ("REQUEST" or "RESPONSE"), otherwise a default name will be created.
    * @param destroyFormattedRecords flag indicating if the formatted records and attributes
    *        should be destroyed to release memory
    *
    * @throws  ParseException if the array for some reason cannot be formatted into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   private static byte[] privateFormatRecordArray(FndAbstractArray array, int flags, String namespace, FndRecordFormat.Direction direction, boolean destroyFormattedRecords) throws ifs.fnd.base.ParseException {
      return privateFormatRecordArrayStax(array, flags, namespace, direction, destroyFormattedRecords);
   }

   private static byte[] privateFormatRecordArrayStax(FndAbstractArray array, int flags, String namespace, FndRecordFormat.Direction direction, boolean destroyFormattedRecords) throws ifs.fnd.base.ParseException {
      FndByteBufferOutputStream out = new FndByteBufferOutputStream(INITIAL_STAX_BUFFER_SIZE);
      FndXmlWriter writer = new FndXmlWriter(out, FndXmlStreamUtil.UTF8);
      FndXmlRecordFormatter formatter = new FndXmlRecordFormatter(writer);
      formatter.setFlags(flags);
      formatter.setDefaultNamespace(namespace);
      formatter.setDirection(direction);
      formatter.setDestroyFormattedRecords(destroyFormattedRecords);
      formatter.formatArray(array);
      writer.close();
      return out.getBytes();
   }

   //==================
   // parseRecordArray
   //==================

   /**
    * Parses an (utf-8 encoded) xml string into an array of records.
    * @param   data  the InputStream with xml data.
    * @param   array the array to parse the xml data into.
    * @param   allowNewAttributes the boolean flag indicating if attributes that don't match the target record should be allowed
    * @param   direction the direction (REQUEST/RESPONSE) of the formatted array. If the direction is
    *          not null then the name of the root XML element must match the direction name ("REQUEST" or "RESPONSE").
    * @throws  ParseException if the data cannot be parsed into the record.
    * @throws  SystemException if the data cannot be parsed into the record.
    */
   public static void parseRecordArray(InputStream data, FndAbstractArray array, boolean allowNewAttributes, FndRecordFormat.Direction direction) throws ifs.fnd.base.ParseException, SystemException {
      parseRecordArrayStax(data, array, allowNewAttributes, direction);
   }

   private static void parseRecordArrayStax(InputStream data, FndAbstractArray array, boolean allowNewAttributes, FndRecordFormat.Direction direction) throws ifs.fnd.base.ParseException, SystemException {
      FndXmlReader reader = new FndXmlReader(data);
      FndXmlRecordParser parser = new FndXmlRecordParser(reader);
      parser.setDirection(direction);
      parser.setAllowNewAttributes(allowNewAttributes);
      parser.parseArray(array);
      reader.close();
   }

   /**
    * Parses an (utf-8 encoded) xml string into an array of records.
    * @param   data  the raw xml data.
    * @param   array the array to parse the xml data into.
    * @param   allowNewAttributes the boolean flag indicating if attributes that don't match the target record should be allowed
    * @param   direction the direction (REQUEST/RESPONSE) of the formatted array. If the direction is
    *          not null then the name of the root XML element must match the direction name ("REQUEST" or "RESPONSE").
    * @throws  ParseException if the data cannot be parsed into the record.
    * @throws  SystemException if the data cannot be parsed into the record.
    */
   public static void parseRecordArray(byte[] data, FndAbstractArray array, boolean allowNewAttributes, FndRecordFormat.Direction direction) throws ifs.fnd.base.ParseException, SystemException {

      parseRecordArray(new ByteArrayInputStream(data), array, allowNewAttributes, direction);
   }

   //=============
   // parseRecord
   //=============

   /**
    * Parses an (utf-8 encoded) xml string into a record.
    * @param   data  the raw xml data.
    * @param   record   the record to parse the xml data into.
    * @throws  ParseException if the data cannot be parsed into the record.
    */
   public static void parseRecord(byte[] data, FndAbstractRecord record) throws ifs.fnd.base.ParseException, SystemException {
      parseRecord(new ByteArrayInputStream(data), record, true, false);
   }

    /**
    * Parses an (utf-8 encoded) xml string into a record.
    * @param   data  the raw xml data.
    * @param   record   the record to parse the xml data into.
    * @param   direction the direction (REQUEST/RESPONSE) of the formatted array. If the direction is
    *          not null then the name of the root XML element must match the direction name ("REQUEST" or "RESPONSE").
    * @throws  ParseException if the data cannot be parsed into the record.
    */
   public static void parseRecord(byte[] data, FndAbstractRecord record, FndRecordFormat.Direction direction) throws ifs.fnd.base.ParseException, SystemException {
      parseRecord(new ByteArrayInputStream(data), record, true, false, false, direction);
   }

   /**
    * Parses an (utf-8 encoded) xml string into a record.
    * @param   data  the raw xml data.
    * @param   record   the record to parse the xml data into.
    * @param   allowNewAttributes the boolean flag indicating if attributes that don't match the target record should be allowed
    * @throws  ParseException if the data cannot be parsed into the record.
    */
   public static void parseRecord(byte[] data, FndAbstractRecord record, boolean allowNewAttributes) throws ifs.fnd.base.ParseException, SystemException {
      parseRecord(new ByteArrayInputStream(data), record, true, allowNewAttributes);
   }

   /**
    * Parses an (utf-8 encoded) xml string into a record.
    * @param   data  the InputStream with xml data.
    * @param   record   the record to parse the xml data into.
    * @param   caseSensitive the boolean flag indicating whether parsed view/attribute names are case sensitive.
    * @throws  ParseException if the data cannot be parsed into the record.
    */
   public static void parseRecord(InputStream data, FndAbstractRecord record, boolean caseSensitive) throws ifs.fnd.base.ParseException, SystemException {
      parseRecord(data, record, caseSensitive, false);
   }

   /**
    * Parses an (utf-8 encoded) xml string into a record.
    * @param   data  the InputStream with xml data.
    * @param   record   the record to parse the xml data into.
    * @param   caseSensitive the boolean flag indicating whether parsed view/attribute names are case sensitive.
    * @param   allowNewAttributes the boolean flag indicating if attributes that don't match the target record should be allowed
    * @throws  ParseException if the data cannot be parsed into the record.
    */
   public static void parseRecord(InputStream data, FndAbstractRecord record, boolean caseSensitive, boolean allowNewAttributes) throws ifs.fnd.base.ParseException, SystemException {
      parseRecord(data, record, caseSensitive, allowNewAttributes, false, null);
   }
   /**
    * Parses an (utf-8 encoded) xml string into a record.
    * @param   data  the InputStream with xml data.
    * @param   record   the record to parse the xml data into.
    * @param   caseSensitive the boolean flag indicating whether parsed view/attribute names are case sensitive.
    * @param   allowNewAttributes the boolean flag indicating if attributes that don't match the target record should be allowed
    * @param   ignoreUnderscores the boolean flag indicating if object names should be also matched by ignoring underscores in the record and attribute names
    * @throws  ParseException if the data cannot be parsed into the record.
    */
   public static void parseRecord(InputStream data, FndAbstractRecord record, boolean caseSensitive, boolean allowNewAttributes, boolean ignoreUnderscores, FndRecordFormat.Direction direction) throws ifs.fnd.base.ParseException, SystemException {
      parseRecordStax(data, record, caseSensitive, allowNewAttributes, ignoreUnderscores, direction);
   }

   private static void parseRecordStax(InputStream data, FndAbstractRecord record, boolean caseSensitive, boolean allowNewAttributes, boolean ignoreUnderscores, FndRecordFormat.Direction direction) throws ifs.fnd.base.ParseException, SystemException {
      FndXmlReader reader = new FndXmlReader(data);
      FndXmlRecordParser parser = new FndXmlRecordParser(reader);
      parser.setCaseSensitive(caseSensitive);
      parser.setDirection(direction);
      parser.setIgnoreUnderscores(ignoreUnderscores);
      parser.setAllowNewAttributes(allowNewAttributes);
      parser.parseRecord(record);
      reader.close();
   }

   /**
    * Generates an XML example file for a record.
    * @param   record   the record to generate the example for.
    * @param   namespace the target XML namespace
    * @return  a byte-array with UTF-8 encoded <code>String</code> containing
    * the example XML.
    */
   public static byte[] generateXmlExample(FndAbstractRecord record, String namespace) throws IfsException {
      populateExampleData(record, new Stack<>());
      return formatRecord(record, false, namespace, null);
   }

   /**
    * Generates an XML example file for a query condition.
    * @param   record   the record to generate the example for.
    * @param   namespace the target XML namespace
    * @return  a byte-array with UTF-8 encoded <code>String</code> containing
    * the example XML.
    */
   public static byte[] generateXmlExampleForQuery(FndAbstractRecord record, String namespace) throws IfsException {
      populateExampleData(record, new Stack<>());
      FndAbstractArray result = (FndAbstractArray) record.getAttribute("RESULT");
      result.setNonExistent();
      return formatRecord(record, false, namespace, null);
   }

    /**
    * Generates an XML example file for an array
    * @param   array   the array to generate the example for.
     *@param   record  the record that the array consists of.
    * @param   namespace the target XML namespace
    * @return  a byte-array with UTF-8 encoded <code>String</code> containing
    * the example XML.
    */
   public static byte[] generateXmlExampleForArray(FndAbstractArray array, FndAbstractRecord record, String namespace) throws IfsException {
      populateExampleData(record, new Stack<>());
      FndAttributeInternals.internalAdd(array, record);
      FndAttributeInternals.internalAdd(array, record);
      return formatRecordArray(array, false, namespace, null);
   }

   /**
    * Generates an XML example file for a record.
    * @param   record   the record to generate the example for.
    * @param   namespace the target XML namespace
    * @param  direction the direction (REQUEST/RESPONSE) of a parameter-list view, for other views the argument is ignored
    * @return  a byte-array with UTF-8 encoded <code>String</code> containing
    * the example XML.
    */
   public static byte[] generateXmlExample(FndAbstractRecord record, String namespace, FndRecordFormat.Direction direction) throws IfsException {
      populateExampleData(record, new Stack<>());
      return formatRecord(record, false, namespace, direction);
   }

   /**
    * Populates a record's attributes with example data.
    * @param   record   the record to generate the example for.
    */
   public static void populateExampleData(FndAbstractRecord record) throws IfsException {
      populateExampleData(record, new Stack<>());
   }

   /**
    * Populates a record's attributes with example data.
    */
   private static void populateExampleData(FndAbstractRecord rec, Stack<String> stack) throws IfsException {
      int attrCount = rec.getAttributeCount();
      for(int i=0; i<attrCount; i++) {
         FndAttribute attr = rec.getAttribute(i);
         if(attr instanceof FndAbstractDate) {
            try {
               FndAttributeInternals.internalSetValue(attr, new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss", Locale.US).parse("2005-09-18-09:01:01"));
            }
            catch (ParseException ex) {
               FndAttributeInternals.internalSetValue(attr, null);   // this should never happen
            }
         }
         else
         if(attr instanceof FndEnumeration) {
            // Call class' the toEnumerationView() method using reflection.
            // Since it's static, it's not possible to re-write using inheritance
            // (unless using non-static method). If an error occurs, set value
            // to null.
            try {
               Method method = attr.getClass().getMethod("toEnumerationView", new Class[]{});
               FndEnumerationView enumView = (FndEnumerationView)method.invoke(null, new Object[]{});
               if(enumView != null && enumView.values != null && enumView.values.size() > 0) {
                  ((FndEnumeration)attr).parseString(enumView.values.get(0).id.getValue());
               }
               else
                  FndAttributeInternals.internalSetValue(attr, null);
            }
            catch (NoSuchMethodException | ifs.fnd.base.ParseException | InvocationTargetException | IllegalAccessException ex) {
               FndAttributeInternals.internalSetValue(attr, null);
            }
         }
         else
         if(attr instanceof FndGuid)
            FndAttributeInternals.internalSetValue(attr, "491ef749131f0611bab32fa60950b06098e1");
         else
         if(attr instanceof FndAbstractText) {
            int len = attr.getMeta().getLength();
            if(len < 0 || len > 3)
               len = 3;
            FndAttributeInternals.internalSetValue(attr, "ABC".substring(0, len));
         }
         else
         if(attr instanceof FndDecimal)
            FndAttributeInternals.internalSetValue(attr, BigDecimal.valueOf(3.14));
         else
         if(attr instanceof FndInteger)
            FndAttributeInternals.internalSetValue(attr, 42);
         else
         if(attr instanceof FndNumber)
            FndAttributeInternals.internalSetValue(attr, 3.14);
         else
         if(attr instanceof FndSimpleArray) {
            FndSimpleArray arr = (FndSimpleArray)attr;
            FndSimpleArray.ElementType elemType = arr.getElementType();
            if(elemType==FndSimpleArray.LONG)
               arr.setLongArray(new long[] {1, 2, 3123, 44, 44, -4, 0});
            else if(elemType==FndSimpleArray.DOUBLE)
               arr.setDoubleArray(new double[]{1.5, 2.5, 3.5});
            else if(elemType==FndSimpleArray.STRING)
               arr.setStringArray(new String[] {"abc", "def", null, "xyz"});
            else if(elemType==FndSimpleArray.DATE)
               arr.setDateArray(new Date[]{new Date(), new Date(), null});
            else if(elemType==FndSimpleArray.BINARY)
               arr.setBinaryArray(new byte[][] {{1, 2, 3, 5, 5, 5, 5}, {0, 0, 0}, null});
            else if(elemType==FndSimpleArray.BOOLEAN)
               arr.setBooleanArray(new boolean[] {true, false, false});
         }
         else
         if(attr instanceof FndBinary)
            FndAttributeInternals.internalSetValue(attr, new byte[]{0x43,0x41,0x46,0x45,0x42,0x41,0x42,0x45});
         else
         if(attr instanceof FndBoolean)
            FndAttributeInternals.internalSetValue(attr, Boolean.TRUE);
         else
         if(attr instanceof FndAbstractArray || attr instanceof FndAbstractAggregate) {
            FndRecordMeta meta = rec.getMeta();
            String viewName = ((meta.getPackage() != null) ? meta.getPackage() + "." : "") + meta.getType();
            // check for circularity
            int loopCount = 0;
            for(Iterator itr2 = stack.iterator(); itr2.hasNext(); ) {
               String s = (String)itr2.next();
               if(s != null && s.equals(viewName)) {
                  loopCount++;
               }
            }
            if(loopCount < 2) {  // Allow three levels of circularity
               FndAbstractRecord subRec = null;
               if(attr instanceof FndAbstractArray) {
                  FndAbstractArray array = (FndAbstractArray)attr;
                  subRec = array.newRecord();
                  FndAttributeInternals.internalAdd(array, subRec);
               }
               if(attr instanceof FndAbstractAggregate) {
                  FndAbstractAggregate aggr = (FndAbstractAggregate)attr;
                  subRec = FndAttributeInternals.getTemplateRecord(aggr);
                  FndAttributeInternals.internalSetRecord(aggr, subRec);
               }
               if(subRec != null) { // Just a safety check
                  stack.push(viewName);
                  populateExampleData(subRec, stack);
                  stack.pop();
               }
            }
            else
               FndAttributeInternals.internalSetValue(attr, null);
         }
      }
   }

   /**
    * Generates an XSD schema for a view.
    * @param   rec  the
    * {@link ifs.fnd.record.FndAbstractRecord FndAbstractRecord} object to
    * generate the schema for.
    * @param   validation  Referred view validation meta object.
    * @param   omitXmlDeclaration   if <code>true</code> the XML declaration
    * (first line) is omitted from the result.
    * @param   namespace the target XML namespace
    * @param  direction the direction (REQUEST/RESPONSE) of a parameter-list view, for other views the argument is ignored
    * @return  a byte-array with a UTF-8 encoded <code>String</code> containing
    * the XSD schema.
    * @throws  UnsupportedEncodingException  if the current Java VM doesn't support UTF-8 encodings.
    */
   public static byte[] generateXsdSchema(FndAbstractRecord rec, FndValidationMeta validation, boolean omitXmlDeclaration, String namespace, FndRecordFormat.Direction direction) throws IfsException, UnsupportedEncodingException {
      return privateGenerateXsdSchema(rec, validation, omitXmlDeclaration, namespace, direction, false);
   }

   /**
    * Generates an XSD schema for a view.
    * @param   rec  the
    * {@link ifs.fnd.record.FndAbstractRecord FndAbstractRecord} object to
    * generate the schema for.
    * @param   validation  Referred view validation meta object.
    * @param   omitXmlDeclaration   if <code>true</code> the XML declaration
    * (first line) is omitted from the result.
    * @param   namespace the target XML namespace
    * @param  direction the direction (REQUEST/RESPONSE) of a parameter-list view, for other views the argument is ignored
    * @return  a byte-array with a UTF-8 encoded <code>String</code> containing
    * the XSD schema.
    */
   public static byte[] generateXsdSchema(FndAbstractRecord rec, FndValidationMeta validation, boolean omitXmlDeclaration, String namespace, FndRecordFormat.Direction direction, boolean query) throws IfsException {
      return privateGenerateXsdSchema(rec, validation, omitXmlDeclaration, namespace, direction, query);
   }

   /**
    * Generates an XSD schema for a query view.
    * @param   rec  the
    * {@link ifs.fnd.record.FndAbstractRecord FndAbstractRecord} object to
    * generate the schema for.
    * @param   validation  Referred view validation meta object.
    * @param   omitXmlDeclaration   if <code>true</code> the XML declaration
    * (first line) is omitted from the result.
    * @param   namespace the target XML namespace
    * @param  direction the direction (REQUEST/RESPONSE) of a parameter-list view, for other views the argument is ignored
    * @return  a byte-array with a UTF-8 encoded <code>String</code> containing
    * the XSD schema.
    * @throws  UnsupportedEncodingException  if the current Java VM doesn't support UTF-8 encodings.
    */
   public static byte[] generateXsdSchemaForQuery(FndAbstractRecord rec, FndValidationMeta validation, boolean omitXmlDeclaration, String namespace, FndRecordFormat.Direction direction) throws IfsException, UnsupportedEncodingException {
      return privateGenerateXsdSchema(rec, validation, omitXmlDeclaration, namespace, direction, true);
   }
    /**
    * Generates an XSD schema for an array.
    * @param   rec  the
    * {@link ifs.fnd.record.FndAbstractRecord FndAbstractRecord} object to
    * generate the schema for.
    * @param   validation  Referred view validation meta object.
    * @param   omitXmlDeclaration   if <code>true</code> the XML declaration
    * (first line) is omitted from the result.
    * @param   namespace the target XML namespace
    * @param  direction the direction (REQUEST/RESPONSE) of a parameter-list view, for other views the argument is ignored
    * @return  a byte-array with a UTF-8 encoded <code>String</code> containing
    * the XSD schema.
    * @throws  UnsupportedEncodingException  if the current Java VM doesn't support UTF-8 encodings.
    */
   public static byte[] generateXsdSchemaForArray(FndAbstractRecord rec, FndValidationMeta validation, boolean omitXmlDeclaration, String namespace, FndRecordFormat.Direction direction) throws IfsException, UnsupportedEncodingException {
      return privateGenerateXsdSchemaForArray(rec, validation, omitXmlDeclaration, namespace, direction);
   }

   /**
    * Generates an XSD schema for a view.
    * @deprecated This method converts byte[] to String. Use the method that returns byte[].
    * @param   rec  the
    * {@link ifs.fnd.record.FndAbstractRecord FndAbstractRecord} object to
    * generate the schema for.
    * @param   validation  Referred view validation meta object.
    * @param   omitXmlDeclaration   if <code>true</code> the XML declaration
    * (first line) is omitted from the result.
    * @param   namespace the target XML namespace
    * @param  direction the direction (REQUEST/RESPONSE) of a parameter-list view, for other views the argument is ignored
    * @param  query true for a query view, false for other views
    * @return  a String containing the XSD schema.
    */
   public static String generateXsdSchemaAsString(FndAbstractRecord rec, FndValidationMeta validation, boolean omitXmlDeclaration, String namespace, FndRecordFormat.Direction direction, boolean query) throws IfsException {
      try {
         byte[] doc = privateGenerateXsdSchema(rec, validation, omitXmlDeclaration, namespace, direction, query);
         return new String(doc, FndSerializeConstants.BUFFER_CHARSET);
      }
      catch (UnsupportedEncodingException e) {
         throw new SystemException(e, "XSDVIEW:Cannot generate XSD schema for view: &1", e.getMessage());
      }
   }

   /**
    * Generates an XSD schema for an array.
    * @deprecated This method converts byte[] to String. Use the method that returns byte[].
    * @param   rec  the
    * {@link ifs.fnd.record.FndAbstractRecord FndAbstractRecord} object to
    * generate the schema for.
    * @param   validation  Referred view validation meta object.
    * @param   omitXmlDeclaration   if <code>true</code> the XML declaration
    * (first line) is omitted from the result.
    * @param   namespace the target XML namespace
    * @param  direction the direction (REQUEST/RESPONSE) of a parameter-list view, for other views the argument is ignored
    * @return  a String containing the XSD schema.
    */
   public static String generateXsdSchemaForArrayAsString(FndAbstractRecord rec, FndValidationMeta validation, boolean omitXmlDeclaration, String namespace, FndRecordFormat.Direction direction) throws IfsException {
      try {
         byte[] doc = privateGenerateXsdSchemaForArray(rec, validation, omitXmlDeclaration, namespace, direction);
         return new String(doc, FndSerializeConstants.BUFFER_CHARSET);
      }
      catch (UnsupportedEncodingException e) {
         throw new SystemException(e, "XSDARRAY:Cannot generate XSD schema for array: &1", e.getMessage());
      }
   }

   /**
    * Generates an XSD schema for a view.
    * @param   rec  the
    * {@link ifs.fnd.record.FndAbstractRecord FndAbstractRecord} object to
    * generate the schema for.
    * @param   validation  Referred view validation meta object.
    * @param   omitXmlDeclaration   if <code>true</code> the XML declaration
    * (first line) is omitted from the result.
    * @param   namespace the target XML namespace
    * @param  direction the direction (REQUEST/RESPONSE) of a parameter-list view, for other views the argument is ignored
    * @param  query true for a query view, false for other views
    * @return  a byte-array with a UTF-8 encoded <code>String</code> containing
    * the XSD schema.
    */
   private static byte[] privateGenerateXsdSchema(FndAbstractRecord rec, FndValidationMeta validation, boolean omitXmlDeclaration, String namespace, FndRecordFormat.Direction direction, boolean query) throws IfsException {
      FndByteBufferOutputStream out = null; // used only if STAX == true
      FndXmlSerializer xml;
      FndXmlWriter writer;

      try {
         out = new FndByteBufferOutputStream(INITIAL_STAX_BUFFER_SIZE);
         writer = new FndXmlWriter(out, FndXmlStreamUtil.UTF8, omitXmlDeclaration);
         FndXmlRecordFormatter formatter = new FndXmlRecordFormatter(writer);
         formatter.setAddDefaultNamespaces(false);
         formatter.setDirection(direction);
         xml = new FndXmlStreamSerializer(writer, formatter);

         String schemaName = namespace == null ? getSchemaNameForView(rec, direction) : "urn:ifsworld-com:schemas:" + namespace;
         FndXmlElementAttributeList attrs = new FndXmlElementAttributeList();
         attrs.add("targetNamespace", schemaName);
         attrs.add("xmlns:xs", "http://www.w3.org/2001/XMLSchema");
         attrs.add("xmlns", schemaName);
         attrs.add("elementFormDefault", "qualified");
         attrs.add("attributeFormDefault", "unqualified");
         xml.startElement("xs:schema", attrs);
         attrs.clear();
         xml.newLine();
         generateXsdSchemaForView(rec, validation, xml, attrs, new Stack<>(), query);
         xml.endElement("xs:schema");
         writer.close();
      } catch(ifs.fnd.base.ParseException e) {
         throw new SystemException(e, "XSDSCHEMAVIEW:Cannot generate XSD schema for view: &1", e.getMessage());
      }

      return out.getBytes();
   }

   /**
    * Generates an XSD schema for an array.
    * @param   rec  the
    * {@link ifs.fnd.record.FndAbstractRecord FndAbstractRecord} object to
    * generate the schema for.
    * @param   validation  Referred view validation meta object.
    * @param   omitXmlDeclaration   if <code>true</code> the XML declaration
    * (first line) is omitted from the result.
    * @param   namespace the target XML namespace
    * @param  direction the direction (REQUEST/RESPONSE) of a parameter-list view, for other views the argument is ignored
    * @return  a String containing the XSD schema.
    */
   private static byte[] privateGenerateXsdSchemaForArray(FndAbstractRecord rec, FndValidationMeta validation, boolean omitXmlDeclaration, String namespace, FndRecordFormat.Direction direction) throws IfsException {
      FndByteBufferOutputStream out = null; // used only if STAX == true
      FndXmlSerializer xml;

      try {
         out = new FndByteBufferOutputStream(INITIAL_STAX_BUFFER_SIZE);
         FndXmlWriter writer = new FndXmlWriter(out, FndXmlStreamUtil.UTF8, omitXmlDeclaration);
         FndXmlRecordFormatter formatter = new FndXmlRecordFormatter(writer);
         formatter.setAddDefaultNamespaces(false);
         formatter.setDirection(direction);
         xml = new FndXmlStreamSerializer(writer, formatter);

         String schemaName = namespace == null ? getSchemaNameForView(rec, direction) : "urn:ifsworld-com:schemas:" + namespace;
         FndXmlElementAttributeList attrs = new FndXmlElementAttributeList();
         attrs.add("targetNamespace", schemaName);
         attrs.add("xmlns:xs", "http://www.w3.org/2001/XMLSchema");
         attrs.add("xmlns", schemaName);
         attrs.add("elementFormDefault", "qualified");
         attrs.add("attributeFormDefault", "unqualified");
         xml.startElement("xs:schema", attrs);
         attrs.clear();
         xml.newLine();
         attrs.add("name", rec.getName() + "_LIST");
         xml.startElement("xs:element", attrs);
         attrs.clear();
         xml.startElement("xs:complexType");
         attrs.add("minOccurs", "0");
         attrs.add("maxOccurs", "unbounded");
         xml.startElement("xs:sequence", attrs);
         attrs.clear();
         generateXsdSchemaForView(rec, validation, xml, attrs, new Stack<>(), false);
         xml.endElement("xs:sequence");
         xml.endElement("xs:complexType");
         xml.endElement("xs:element");
         xml.endElement("xs:schema");
         writer.close();
      } catch(ifs.fnd.base.ParseException e) {
         throw new SystemException(e, "XSDSCHEMAARRAY:Cannot generate XSD schema for array: &1", e.getMessage());
      }
      return out.getBytes();
   }

   /**
    * Gets the name of a view used in XML and XSD documents.
    * @param  view a record to get the name for
    * @param  direction the direction (REQUEST/RESPONSE) of a parameter-list view, for other types of views the argument is ignored
    * @return a String containing the view name
    */
   public static String getViewName(FndAbstractRecord view, FndRecordFormat.Direction direction) {
      FndRecordMeta meta = view.getMeta();
      if(meta == null)
         throw new IfsRuntimeException("FndXmlUtil.getViewName(): Missing meta view for record '&1'", view.getClass().getName());
      String dirStr = direction!=null ? "_" + direction.toString() : "";
      return meta.isParameterListView() ? meta.getType() + dirStr : meta.getType();
   }

   /**
    * Gets the default name of a XSD schema for a view.
    * @param  view a record to get the schema name for
    * @param  direction the direction (REQUEST/RESPONSE) of a parameter-list view, for other types of views the argument is ignored
    * @return a String containing the name of XSD schema.
    */
   static String getSchemaNameForView(FndAbstractRecord view, FndRecordFormat.Direction direction) {
      FndRecordMeta meta = view.getMeta();
      //System.out.println("##" + getViewName(view, direction));
      // Type of a parameter-list view already contains the package prefix
      // One parameter-list view represents two schemas (request and response)
      String ns;
      if(meta == null)
         ns = view.getName();
      else if(meta.getPackage()==null)
         ns = meta.getType();
      else
         ns = meta.getPackage() + "_" + meta.getType();

      String schemaName = (meta != null && meta.isParameterListView()) ? getViewName(view, direction) : ns;

      schemaName = FndUtil.toUpperCaseName(schemaName).toLowerCase();
      return "urn:ifsworld-com:schemas:" + schemaName;
   }

   /**
    * Generates the view part of an XSD schema.
    * @param   rec  the FndAbstractRecord to generate for.
    * @param   validation  Referred view validation meta object.
    * @param   xml   the FndXmlSerializer for the XML docment being generated.
    * @param   attrs a temp FndXmlElementAttributeList object to reduce object
    * creation.
    * @param   stack a <code>java.util.Stack</code> to keep track of circularity.
    * @throws  SystemException   if there is an error generating the schema.
    */
   private static void generateXsdSchemaForView(FndAbstractRecord rec, FndValidationMeta validation, FndXmlSerializer xml, FndXmlElementAttributeList attrs, Stack<String> stack, boolean query) throws IfsException {

      boolean wsdlxsDouble = IfsProperties.getSnapshot().getProperty("ifs.wsdlxsDouble", false); // Dynamic IFS property

      attrs.add("name", getViewName(rec, xml.getDirection()));

      FndRecordMeta metaRec = rec.getMeta();

      FndTermDefinitionStorage.PathTermSet termSet = new FndTermDefinitionStorage.PathTermSet();
      List<String> recTermPaths = metaRec.getTermPaths();
      for(String termPath : recTermPaths)
         termSet.addTerm(termPath);

      List<FndRecordFieldMeta> fields = metaRec.getFields();
      if(fields!=null)
         for(FndRecordFieldMeta fldMeta : fields) {
            String termPath = fldMeta.getTermPath();
            if( !Str.isEmpty(termPath) )
               termSet.addTerm(termPath);
         }

      FndTermDefinitionStorage.getTermDefinitions(termSet);

      xml.startElement("xs:element", attrs);
      xml.newLine();

      String term = null;
      for(String path : recTermPaths) {
         String text = termSet.getTermDefinition(path);
         if( !Str.isEmpty(text) ) {
            term = text;
            break;
         }
      }
      xml.addXsdDocumentation(term, metaRec.getDefinition());

      attrs.clear();
      xml.startElement("xs:complexType");
      xml.newLine();
      xml.startElement("xs:all");
      xml.newLine();

      int attrCount = rec.getAttributeCount();
      for(int a=0; a<attrCount; a++) {
         FndAttribute attr = rec.getAttribute(a);
         FndValidationAttributeMeta attrValidation = validation!=null ? validation.getAttributeValidation(attr.getName()) : null;
         if(!directionMatches(xml, attr))
            continue;

         String attrName = attr.getName();
         if("OBJ_VERSION".equals(attrName)) continue;
         if("OBJ_ID".equals(attrName)) continue;
         if("CREATED_BY".equals(attrName)) continue;
         if("CREATED_DATE".equals(attrName)) continue;
         if(query && "RESULT".equals(attrName)) continue;

         attrs.add("name", attr.getName());

         if(attr instanceof FndTimestamp)
            attrs.add("type", "xs:dateTime");
         else
         if(attr instanceof FndTime)
            attrs.add("type", "xs:time");
         else
         if(attr instanceof FndDate)
            attrs.add("type", "xs:date");
         else
         if(attr instanceof FndAbstractText && attr.isLong())
            attrs.add("type", "xs:string"); //base64Binary");
         else
         if(attr instanceof FndSimpleArray)
            // this must be here since FndSimpleArray is a sub-class of FndBinary
            // but handled differently in XML.
            assert true;
         else
         if(attr instanceof FndBinary && attr.isLong())
            attrs.add("type", "xs:base64Binary");
         else
         if(attr instanceof FndNumber)
         {
            if(wsdlxsDouble)
               attrs.add("type", "xs:double");
            else
               attrs.add("type", "xs:float");
         }
         else
         if(attr instanceof FndInteger)
            attrs.add("type", "xs:int");
         else
         if(attr instanceof FndBoolean)
            attrs.add("type", "xs:boolean");

         if(attrValidation!=null && attrValidation.isMandatory()) {
            assert true;
         }
         else{
            attrs.add("nillable", "true");
            attrs.add("minOccurs", "0");
         }
         xml.startElement("xs:element", attrs);
         attrs.clear();
         xml.newLine();

         FndRecordFieldMeta fldMeta = metaRec.findField(attrName);
         xml.addXsdDocumentation(fldMeta==null ? null : termSet.getTermDefinition(fldMeta.getTermPath()), attr.getMeta().getDefinition());

         if(attr instanceof FndDecimal)
         {
            xml.startElement("xs:simpleType");
            xml.newLine();
            attrs.add("base", "xs:decimal");
            xml.startElement("xs:restriction", attrs);
            attrs.clear();
            xml.newLine();
            attrs.add("value", "dummy");
            xml.addElement("xs:totalDigits", attrs);
            attrs.clear();
            xml.newLine();
            attrs.add("value", "dummy");
            xml.addElement("xs:fractionDigits", attrs);
            attrs.clear();
            xml.newLine();
            xml.endElement("xs:restriction");
            xml.newLine();
            xml.endElement("xs:simpleType");
            xml.newLine();
         }
         else
         if(attr instanceof FndEnumeration) {
            xml.startElement("xs:simpleType");
            xml.newLine();
            attrs.add("base", "xs:string");
            xml.startElement("xs:restriction", attrs);
            attrs.clear();
            xml.newLine();

            // Call class' the toEnumerationView() method using reflection.
            // Since it's static, it's not possible to re-write using inheritance
            // (unless using non-static method). If an error occurs, throw
            // exception.
            try {
               Method method = attr.getClass().getMethod("toEnumerationView", new Class[]{});
               FndEnumerationView enumView = (FndEnumerationView)method.invoke(null, new Object[]{});
               if(enumView != null && enumView.values != null && enumView.values.size() > 0) {
                  for(int i=0; i<enumView.values.size(); i++) {
                     attrs.add("value", enumView.values.get(i).id.getValue());
                     xml.addElement("xs:enumeration", attrs);
                     attrs.clear();
                     xml.newLine();
                  }
               }
            }
            catch (NoSuchMethodException ex) {
               throw new SystemException(ex, "NO_TO_ENUM_VIEW_METHOD: No toEnumerationView method in view '&1'", rec.getName());
            }
            catch (IllegalAccessException ex) {
               throw new SystemException(ex, "ILLEGAL_ACCESS_EX:Error accessing toEnumerationView method in view '&1: &2'", rec.getName(), ex.getMessage());
            }
            catch (InvocationTargetException ex) {
               throw new SystemException(ex, "INVOKATION_EX:Error invoking toEnumerationView method in view '&1': &2", rec.getName(), ex.getMessage());
            }
            xml.endElement("xs:restriction");
            xml.newLine();
            xml.endElement("xs:simpleType");
            xml.newLine();
         }
         else
         if(attr instanceof FndAbstractText && !attr.isLong()) {
            xml.startElement("xs:simpleType");
            xml.newLine();
            attrs.add("base", "xs:string");
            xml.startElement("xs:restriction", attrs);
            attrs.clear();
            xml.newLine();
            if(attr.getMeta().getLength() > 0) {
               attrs.add("value", Integer.toString(attr.getMeta().getLength()));
               xml.addElement("xs:maxLength", attrs);
               attrs.clear();
               xml.newLine();
            }
            xml.endElement("xs:restriction");
            xml.newLine();
            xml.endElement("xs:simpleType");
            xml.newLine();
         }
         else
         if(attr instanceof FndSimpleArray) {
            xml.startElement("xs:complexType");
            xml.newLine();
            attrs.add("minOccurs", "0");
            attrs.add("maxOccurs", "unbounded");
            xml.startElement("xs:sequence", attrs);
            xml.newLine();
            attrs.clear();

            attrs.add("name", "VALUE");
            attrs.add("nillable", "true");
            attrs.add("minOccurs", "0");
            xml.startElement("xs:element", attrs);
            xml.newLine();
            attrs.clear();

            xml.startElement("xs:simpleType");
            xml.newLine();

            attrs.add("base", "xs:string");
            xml.startElement("xs:restriction", attrs);
            xml.newLine();
            attrs.clear();

/*
            attrs.add("value", "2000");
            xml.addElement("xs:maxLength", attrs);
            xml.newLine();
            attrs.clear();
*/

            xml.endElement("xs:restriction");
            xml.newLine();

            xml.endElement("xs:simpleType");
            xml.newLine();

            xml.endElement("xs:element");
            xml.newLine();

            xml.endElement("xs:sequence");
            xml.newLine();

            attrs.add("name", "elementType");
            attrs.add("type", "xs:string");
            attrs.add("use", "required");
            xml.addElement("xs:attribute", attrs);
            attrs.clear();
            xml.newLine();

            attrs.add("name", "elementCount");
            attrs.add("type", "xs:int");
            attrs.add("use", "required");
            xml.addElement("xs:attribute", attrs);
            attrs.clear();
            xml.newLine();

            xml.endElement("xs:complexType");
            xml.newLine();
         }
         else
         if(attr instanceof FndBinary && !attr.isLong()) {
            xml.startElement("xs:simpleType");
            xml.newLine();
            attrs.add("base", "xs:base64Binary");
            xml.startElement("xs:restriction", attrs);
            xml.newLine();
            attrs.clear();
            if(attr.getMeta().getLength() > 0) {
               attrs.add("value", Integer.toString(attr.getMeta().getLength()));
               xml.addElement("xs:maxLength", attrs);
               attrs.clear();
               xml.newLine();
            }
            xml.endElement("xs:restriction");
            xml.newLine();
            xml.endElement("xs:simpleType");
            xml.newLine();
         }
         else
         if(attr instanceof FndAbstractArray || attr instanceof FndAbstractAggregate) {
            String viewName = ((rec.getMeta().getPackage() != null) ? rec.getMeta().getPackage() + "." : "") + rec.getMeta().getType();
            FndValidationMeta detailValidation = validation!=null ? validation.getDetailValidation(attr.getName()) : null;

            // check for circularity
            int loopCount = 0;
            for(Iterator itr2 = stack.iterator(); itr2.hasNext(); ) {
               String s = (String)itr2.next();
               if(s != null && s.equals(viewName)) {
                  loopCount++;
               }
            }
            if(loopCount < 2) {  // Allow three levels of circularity
               xml.startElement("xs:complexType");
               xml.newLine();
               attrs.add("minOccurs", "0");
               FndAbstractRecord subRec = null;
               if(attr instanceof FndAbstractArray) {
                  attrs.add("maxOccurs", "unbounded");
                  if(attr instanceof FndArray && ((FndArray)attr).size() > 0) {
                      subRec = FndAttributeInternals.internalGet(((FndAbstractArray)attr),0);
                  }
                  else subRec = ((FndAbstractArray)attr).newRecord();
               }
               else if(attr instanceof FndAbstractAggregate) {
                  attrs.add("maxOccurs", "1");
                  if(attr instanceof FndAggregate){
                     subRec = FndAttributeInternals.internalGetRecord((FndAbstractAggregate)attr);
                  }
                  else subRec = FndAttributeInternals.getTemplateRecord((FndAbstractAggregate)attr);
               }
               xml.startElement("xs:sequence", attrs);
               attrs.clear();
               xml.newLine();

               stack.push(viewName);
               generateXsdSchemaForView(subRec, detailValidation, xml, attrs, stack, false);
               stack.pop();

               xml.endElement("xs:sequence");
               xml.newLine();
               xml.endElement("xs:complexType");
               xml.newLine();
            }
            else {
               xml.appendComment(" And so on... ");
               xml.newLine();
            }
         }

         xml.endElement("xs:element");
         xml.newLine();
      }

      xml.endElement("xs:all");
      xml.newLine();
      xml.endElement("xs:complexType");
      xml.newLine();
      xml.endElement("xs:element");
      xml.newLine();
   }

   /**
    * Checks if the direction of an attribute (parameter) matches the direction of the formatted record.
    * @param   xml  the XML serializer used to generate the XML stream
    * @param   attr an attribute to check the direction for
    * @return true if the attribute should be included in the generated XML document, false otherwise
    */
   public static boolean directionMatches(FndXmlSerializer xml, FndAttribute attr) {
      FndAttributeMeta meta = attr.getMeta();
      if(meta == null)
         return true;

      FndRecordFormat.Direction recordDirection = xml.getDirection();
      if(recordDirection == null)
         return true;

      FndAttributeMeta.Direction paramDirection = meta.getDirection();
      if(paramDirection == null)
         return true;

      if(paramDirection == FndAttributeMeta.IN)
         return recordDirection == FndRecordFormat.REQUEST;
      else if(paramDirection == FndAttributeMeta.OUT)
         return recordDirection == FndRecordFormat.RESPONSE;
      else if(paramDirection == FndAttributeMeta.RETURN_VALUE)
         return recordDirection == FndRecordFormat.RESPONSE;
      else
         return true;
   }

   //=======================
   // formatAndDestroyRecord
   //=======================

   /**
    * Formats a record into a utf-8 encoded xml string.
    * The formatted record and its components will be destroyed to release memory.
    * @param   record   the record to format.
    * @throws  ParseException if the record for some reason cannot be formatted
    * into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   public static byte[] formatAndDestroyRecord(FndAbstractRecord record) throws ifs.fnd.base.ParseException {
      return formatAndDestroyRecord(record, true, null, null);
   }

   /**
    * Formats a record into a utf-8 encoded xml string.
    * The formatted record and its components will be destroyed to release memory.
    * @param   record   the record to format.
    * @param   addRecordAttributes  if <code>true</code>, IFS record attributs
    * are used in the XML produced. These attributes are in the <code>ifsrecord</code>
    * namespace.
    * @throws  ParseException if the record for some reason cannot be formatted into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   public static byte[] formatAndDestroyRecord(FndAbstractRecord record, boolean addRecordAttributes) throws ifs.fnd.base.ParseException {
      return formatAndDestroyRecord(record, addRecordAttributes, null, null);
   }

   /**
    * Formats a record into a utf-8 encoded xml string.
    * The formatted record and its components will be destroyed to release memory.
    * @param   record   the record to format.
    * @param   flags the XML serialization flags to use. See class
    * {@link FndXmlSerializer FndXmlSerializer} for more information on the flags.
    * @throws  ParseException if the record for some reason cannot be formatted into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   public static byte[] formatAndDestroyRecord(FndAbstractRecord record, int flags) throws ifs.fnd.base.ParseException {
      return privateFormatRecord(record, flags, null, null, true);
   }

   /**
    * Formats a record into a utf-8 encoded xml string.
    * The formatted record and its components will be destroyed to release memory.
    * @param   record   the record to format.
    * @param   addRecordAttributes  if <code>true</code>, IFS record attributs
    * are used in the XML produced. These attributes are in the <code>ifsrecord</code>
    * namespace.
    * @param direction the direction (REQUEST/RESPONSE) of the formatted record.
    *        If the direction is not null and if the specified record is a generated parameter-list view
    *        then attributes with direction not matching the direction of the view will be omitted.
    * @throws  ParseException if the record for some reason cannot be formatted
    * into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   public static byte[] formatAndDestroyRecord(FndAbstractRecord record, boolean addRecordAttributes, FndRecordFormat.Direction direction) throws ifs.fnd.base.ParseException {
      return formatAndDestroyRecord(record, addRecordAttributes, null, direction);
   }

   /**
    * Formats a record into a utf-8 encoded xml string.
    * The formatted record and its components will be destroyed to release memory.
    * @param   record   the record to format.
    * @param   flags the XML serialization flags to use. See class
    * {@link FndXmlSerializer FndXmlSerializer} for more information on the flags.
    * @param direction the direction (REQUEST/RESPONSE) of the formatted record.
    *        If the direction is not null and if the specified record is a generated parameter-list view
    *        then attributes with direction not matching the direction of the view will be omitted.
    * @throws  ParseException if the record for some reason cannot be formatted
    * into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   public static byte[] formatAndDestroyRecord(FndAbstractRecord record, int flags, FndRecordFormat.Direction direction) throws ifs.fnd.base.ParseException {
      return privateFormatRecord(record, flags, null, direction, true);
   }

   /**
    * Formats a record into a utf-8 encoded xml string.
    * The formatted record and its components will be destroyed to release memory.
    * @param   record   the record to format.
    * @param   namespace  Will be used in the xml namepace. If null then created from the metadata view
    * <code>null</code> value for this parameter indicates a that a Business API
    * is not in use. (When a namspace is used, IFS record attributes are not added).
    * @param direction the direction (REQUEST/RESPONSE) of the formatted record.
    *        If the direction is not null and if the specified record is a generated parameter-list view
    *        then attributes with direction not matching the direction of the view will be omitted.
    * @throws  ParseException if the record for some reason cannot be formatted
    * into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   public static byte[] formatAndDestroyRecord(FndAbstractRecord record, String namespace, FndRecordFormat.Direction direction) throws ifs.fnd.base.ParseException {
      return formatAndDestroyRecord(record, true, namespace, direction);
      // true here is a default used when namespace==null.
   }

   /**
    * Formats a record into a utf-8 encoded xml string.
    * The formatted record and its components will be destroyed to release memory.
    * @param   record   the record to format.
    * @param   namespace  Will be used in the xml namepace. If null then created from the metadata view
    * <code>null</code> value for this parameter indicates a that a Business API
    * is not in use. (When a namespace is used, IFS record attributes are not added).
    * @throws  ParseException if the record for some reason cannot be formatted
    * into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   public static byte[] formatAndDestroyRecord(FndAbstractRecord record, String namespace) throws ifs.fnd.base.ParseException {
      return formatAndDestroyRecord(record, true, namespace, null);
      // true here is a default used when namespace==null.
   }

   /**
    * Formats a record into a utf-8 encoded xml string.
    * The formatted record and its components will be destroyed to release memory.
    * @param record   the record to format.
    * @param direction the direction (REQUEST/RESPONSE) of the formatted record.
    *        If the direction is not null and if the specified record is a generated parameter-list view
    *        then attributes with direction not matching the direction of the view will be omitted.
    * @throws ParseException if the record for some reason cannot be formatted into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   public static byte[] formatAndDestroyRecord(FndAbstractRecord record, FndRecordFormat.Direction direction) throws ifs.fnd.base.ParseException {
      return formatAndDestroyRecord(record, true, null, direction);
   }

   /**
    * Formats a record into a utf-8 encoded xml string.
    * The formatted record and its components will be destroyed to release memory.
    * @param   record   the record to format.
    * @param   addRecordAttributes  wheter IFS record attributes are to be
    * used or not. Not used when <code>namespace</code> is non-null.
    * @param   namespace  Will be used in the xml namepace. If null then created from the metadata view
    * <code>null</code> value for this parameter indicates a that a Business API
    * is not in use.
    * @param direction the direction (REQUEST/RESPONSE) of the formatted record.
    *        If the direction is not null and if the specified record is a generated parameter-list view
    *        then attributes with direction not matching the direction of the view will be omitted.
    * @throws  ParseException if the record for some reason cannot be formatted
    * into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   public static byte[] formatAndDestroyRecord(FndAbstractRecord record, boolean addRecordAttributes, String namespace, FndRecordFormat.Direction direction) throws ifs.fnd.base.ParseException {
      return privateFormatRecord(record, (addRecordAttributes ? FndXmlSerializer.FLAG_ALL_INFO : 0), namespace, direction, true);
   }

   //============================
   // formatAndDestroyRecordArray
   //============================

   /**
    * Formats an array of records into a utf-8 encoded xml string.
    * The formatted array and its components will be destroyed to release memory.
    * @param   array array to format.
    * @param   addRecordAttributes  wheter IFS record attributes are to be
    * @param   namespace  Will be used in the xml namepace. If null then created from the metadata view
    * is not in use.
    * @param direction the direction (REQUEST/RESPONSE) of the formatted array.
    *        If the direction is not null then the name of the root XML element will be set to
    *        the direction name ("REQUEST" or "RESPONSE"), otherwise a default name will be created.
    * @throws  ParseException if the array for some reason cannot be formatted into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   public static byte[] formatAndDestroyRecordArray(FndAbstractArray array, boolean addRecordAttributes, String namespace, FndRecordFormat.Direction direction) throws ifs.fnd.base.ParseException {
      return formatAndDestroyRecordArray(array, (addRecordAttributes ? FndXmlSerializer.FLAG_ALL_INFO : 0), namespace, direction);
   }

   /**
    * Formats an array of records into a utf-8 encoded xml string.
    * The formatted array and its components will be destroyed to release memory.
    * @param   array array to format.
    * @param   flags the XML serialization flags to use. See class
    * {@link FndXmlSerializer FndXmlSerializer} for more information on the flags. This

    * @param   namespace  the name of the Business API in use (if any).  A
    * <code>null</code> value for this parameter indicates a that a Business API
    * is not in use.
    * @param direction the direction (REQUEST/RESPONSE) of the formatted array.
    *        If the direction is not null then the name of the root XML element will be set to
    *        the direction name ("REQUEST" or "RESPONSE"), otherwise a default name will be created.
    * @throws  ParseException if the array for some reason cannot be formatted into xml.
    * @return a byte array with the xml string (utf-8 encoded).
    */
   public static byte[] formatAndDestroyRecordArray(FndAbstractArray array, int flags, String namespace, FndRecordFormat.Direction direction) throws ifs.fnd.base.ParseException {
      return privateFormatRecordArray(array, flags, namespace, direction, true);
   }

}
