/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record.serialization;

import ifs.fnd.base.ParseException;
import ifs.fnd.util.XmlUtil;

import ifs.fnd.util.Str;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import javax.xml.stream.XMLOutputFactory;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamWriter;

/**
 * Class that writes data to an XML stream.
 */
public class FndXmlWriter {

   /**
    * The output stream for encoded XML document.
    */
   private OutputStream out;
   private OutputStream outOrg;

   /**
    * XML writer writing to the output stream above.
    */
   private XMLStreamWriter writer;

   /**
    * The xml encoding used in the document.
    */
   private String xmlEncoding;

   /**
    * Flag determining if the produced XML should be properly indented or not.
    */
   private boolean isIndentOn = true;

   /**
    * The current indentation level when isIndentOn == true.
    */
   private int indentLevel = 0;

   /**
    * Internal flag keeping track of indentation.
    */
   private boolean indentNext = false;

   /**
    * If true the XML declaration (first line) is not written to the XML stream.
    * Otherwise START_DOCUMENT and END_DOCUMENT events are written to the XML stream.
    */
   private boolean omitXmlDeclaration;

   /**
    * Denotes if the document has been initialized (START_DOCUMENT event
    * has been written).
    */
   private boolean xmlDeclarationWritten;

   /**
    * Denotes if the document has been written directly to the underlying stream
    * (using the writeDocument() method).
    */
   private boolean docWrittenToStream = false;

   /**
    * Line seperator for xml document.
    */
   private static final String NL = "\n";

   /**
    * The indentation string pre-padding each line when indentLevel > 0 and isIndentOn == true.
    */
   private static final char[] INDENT_STRING = createIndentString();

   private static char[] createIndentString() {
      char[] arr = new char[256];
      for(int i = 0; i < arr.length; i++)
         arr[i] = ' ';
      return arr;
   }

   /**
    * Constructs a FndXmlWriter instance with specified stream and UTF-8 encoding.
    * @param out the stream to write to
    * @throws ifs.fnd.base.ParseException
    *
    * @see #setIndent
    */
   public FndXmlWriter(OutputStream out) throws ParseException {
      this(out, FndXmlStreamUtil.UTF8, false);
   }

   /**
    * Constructs a FndXmlWriter instance with specified parameters.
    * @param out the stream to write to
    * @param xmlEncoding the character set encoding to be used on the XML
    * @throws ifs.fnd.base.ParseException
    *
    * @see #setIndent
    */
   public FndXmlWriter(OutputStream out, String xmlEncoding) throws ParseException {
      this(out, xmlEncoding, false);
   }

   /**
    * Constructs a FndXmlWriter instance with specified parameters.
    * @param outSt the stream to write to
    * @param xmlEncoding the character set encoding to be used on the XML
    * @param omitXmlDeclaration if true the XML declaration (first line) is not written to the XML stream
    * @throws ifs.fnd.base.ParseException
    *
    * @see #setIndent
    */
   public FndXmlWriter(OutputStream outSt, String xmlEncoding, boolean omitXmlDeclaration) throws ParseException {
      try {
         ifs.fnd.log.Logger log = ifs.fnd.log.LogMgr.getClassLogger(this.getClass());
         if(log.debug) printStackTrace(log);

         this.outOrg = outSt;
         this.out = log.trace && !(outSt instanceof FndByteBufferOutputStream) ? new ByteArrayOutputStream() : outSt;

         this.xmlEncoding = xmlEncoding;
         this.omitXmlDeclaration = omitXmlDeclaration;
         XMLOutputFactory outFactory = XmlUtil.newXMLOutputFactory();//XMLOutputFactory.newInstance();
         writer = outFactory.createXMLStreamWriter(this.out, xmlEncoding);
         if(log.trace) log.trace("Class implementing XMLStreamWriter: &1", writer.getClass().getName());
      }
      catch(XMLStreamException e) {
         throw new ParseException(e, "XMLWRITER_CREATE:Cannot create FndXmlWriter: &1", e.getMessage());
      }
   }

   /**
    * Late initialization of the document.
    */
   void init() throws XMLStreamException, ParseException {
      if(!xmlDeclarationWritten) {
         xmlDeclarationWritten = true;
         if(!omitXmlDeclaration) {
            if(docWrittenToStream) {
               throw new ParseException("Document already written to the undelying stream.");
            }
            writer.writeStartDocument(xmlEncoding, "1.0");
            newLine();
         }
      }
   }

   /**
    * Returns the encapsulated instance of XMLStreamWriter
    */
   XMLStreamWriter getXMLStreamWriter() {
      return writer;
   }

   /**
    * Write properly encoded XML text to this document.
    * @param data array of bytes to write
    * @throws ifs.fnd.base.ParseException
    */
   public void writeXml(byte[] data) throws ParseException {
      printStackTrace(null);
      try {
         if(!xmlDeclarationWritten) init();
         writer.writeCharacters("");
         writer.flush();
      }
      catch (XMLStreamException e) {
         throw new ParseException(e, "XMLWRITER_FLUSH_XML:Cannot flush data to XML stream: &1", e.getMessage());
      }
      try {
         out.write(data, 0, data.length);
      }
      catch (IOException e) {
         throw new ParseException(e, "XMLWRITER_WRITE_XML:Cannot write data to XML stream: &1", e.getMessage());
      }
   }

   /**
    * Write properly encoded entire XML document to this writer.
    * @param data array of bytes to write
    * @throws ifs.fnd.base.ParseException
    */
   public void writeDocument(byte[] data) throws ParseException {
      printStackTrace(null);
      if(xmlDeclarationWritten) {
         throw new ParseException("Data has already been written to the writer");
      }
      try {
         out.write(data, 0, data.length);
         xmlDeclarationWritten = true;
         docWrittenToStream = true;
      }
      catch (IOException e) {
         throw new ParseException(e, "XMLWRITER_WRITE_XML:Cannot write data to XML stream: &1", e.getMessage());
      }
   }

   /**
    * Perform a BASE64 encoding of an array of bytes and write the result to the stream.
    * @param data array of bytes to encode and write
    * @throws ifs.fnd.base.ParseException
    */
   public void writeBase64(byte[] data) throws ParseException {
      printStackTrace(null);
      try {
         if(!xmlDeclarationWritten) init();
         writer.writeCharacters("");
         writer.flush();
      }
      catch (XMLStreamException e) {
         throw new ParseException(e, "XMLWRITER_FLUSH_B64:Cannot flush XML stream: &1", e.getMessage());
      }
      try {
         ByteArrayInputStream in = new ByteArrayInputStream(data);
         (new Base64()).encode(in, out);
      }
      catch (IOException e) {
         throw new ParseException(e, "XMLWRITER_WRITE_B64:Cannot write BASE64 data to XML stream: &1", e.getMessage());
      }
   }

   /**
    * Writes a simple element to the output stream.
    * @param name  the name of the element.
    * @throws ifs.fnd.base.ParseException
    */
   public void writeEmptyElement(String name) throws ParseException {
      try {
         writeIndent();
         writer.writeEmptyElement(name);
      }
      catch(XMLStreamException x) {
         throw new ParseException(x, "XMLSTREAM_EMPTYELEM:Cannot write empty ELEMENT to XML stream: &1", x.getMessage());
      }
   }

   /**
    * Writes an END_ELEMENT to the output stream.
    * @throws ifs.fnd.base.ParseException
    */
   public void writeEndElement() throws ParseException {
      writeEndElement(null);
   }

   /**
    * Writes an END_ELEMENT to the output stream.
    * @param name the name of the element (this parameter is ignored)
    * @throws ifs.fnd.base.ParseException
    * @see #writeEndElement()
    */
   public void writeEndElement(String name) throws ParseException {
      indentLevel--;
      try {
         writeIndent();
         writer.writeEndElement();
      }
      catch(XMLStreamException x) {
         throw new ParseException(x, "XMLWRITER_ENDELEM:Cannot write END_ELEMENT to XML stream: &1", x.getMessage());
      }
   }

   /**
    * Write text to the output stream.
    * @param text the text to write
    * @throws ifs.fnd.base.ParseException
    */
   public void writeCharacters(String text) throws ParseException {
      try {
         if(!xmlDeclarationWritten) init();
         writer.writeCharacters(text);
      }
      catch(XMLStreamException e) {
         throw new ParseException(e, "XMLWRITER_CHAR:Cannot write characters to XML stream: &1", e.getMessage());
      }
   }

   /**
    * Write CData to the output stream.
    * @param data the CData to write
    */
   void writeCData(String data) throws ParseException {
      try {
         if(!xmlDeclarationWritten) init();
         writer.writeCData(data);
      }
      catch(XMLStreamException e) {
         throw new ParseException(e, "XMLWRITER_CDATA:Cannot write CData to XML stream: &1", e.getMessage());
      }
   }

   /**
    * Write a new-line sequence to the output stream.
    * @throws ifs.fnd.base.ParseException
    */
   public void newLine() throws ParseException {
      writeCharacters(NL);
      indentNext = true;
   }

   /**
    * Writes an XML comment with the data enclosed.
    * @param data the data contained in the comment, may be null
    * @throws ifs.fnd.base.ParseException
    */
   public void writeComment(String data) throws ParseException {
      try {
         writeIndent();
         writer.writeComment(data);
      }
      catch(XMLStreamException x) {
         throw new ParseException(x, "XMLWRITER_COMMENT:Cannot write COMMENT to XML stream: &1", x.getMessage());
      }
   }

   /**
    * Sets wheter to use proper indentation of the XML produced or not. Either
    * way, well-formed XML is produced. The default value for this flag is true.
    * @param on if true, the XML produced is indented.
    */
   public void setIndent(boolean on) {
      isIndentOn = on;
   }

   /**
    * Adds a start element to the document.
    * @param name the name of the element (may contain a prefix)
    * @throws ifs.fnd.base.ParseException
    */
   public void writeStartElement(String name) throws ParseException {
      try {
         writeIndent();
         writer.writeStartElement(name);
         indentLevel++;
      }
      catch(XMLStreamException e) {
         throw new ParseException(e, "XMLWRITER_STARTELEM:Cannot write start element to XML stream: &1", e.getMessage());
      }
   }

   /**
    * Adds a start element to the document.
    * @param prefix the prefix that will be pre-appended to the element name
    * @param name   the name of the element.
    * @throws ifs.fnd.base.ParseException
    */
   public void writeStartElement(String prefix, String name) throws ParseException {
      writeStartElement(addPrefix(prefix,name));
   }

   /**
    * Writes a namespace to the output stream.
    * @param prefix       the prefix to bind this namespace to
    * @param namespaceURI the uri to bind the prefix to
    * @throws ifs.fnd.base.ParseException
    */
   public void writeNamespace(String prefix, String namespaceURI) throws ParseException {
      try {
         if(!xmlDeclarationWritten) init();
         writer.writeNamespace(prefix, namespaceURI);
      }
      catch(XMLStreamException e) {
         throw new ParseException(e, "XMLWRITER_NAMESPACE:Cannot write namespace to XML stream: &1", e.getMessage());
      }
   }

   /**
    * Writes an attribute to the output stream.
    * @param name  the name of the attribute (may contain a prefix)
    * @param value the value of the attribute
    * @throws ifs.fnd.base.ParseException
    */
   public void writeAttribute(String name, String value) throws ParseException {
      try {
         if(!xmlDeclarationWritten) init();
         writer.writeAttribute(name, value);
      }
      catch(XMLStreamException e) {
         throw new ParseException(e, "XMLWRITER_ATTRIBUTE:Cannot write attribute to XML stream: &1", e.getMessage());
      }
   }

   /**
    * Writes an attribute to the output stream.
    * @param prefix    the prefix that will be pre-appended to to the local name
    * @param localName the local name of the attribute
    * @param value     the value of the attribute
    * @throws ifs.fnd.base.ParseException
    */
   public void writeAttribute(String prefix, String localName, String value) throws ParseException {
      writeAttribute(addPrefix(prefix,localName), value);
   }

   private void writeIndent() throws XMLStreamException, ParseException {
      if(!xmlDeclarationWritten) init();
      if(isIndentOn && indentNext) {
         if(indentLevel <= INDENT_STRING.length) {
            writer.writeCharacters(INDENT_STRING, 0, indentLevel);
         }
         else {
            int n = indentLevel;
            int max = INDENT_STRING.length;
            while(n > 0) {
               writer.writeCharacters(INDENT_STRING, 0, Math.min(n, max));
               n -= max;
            }
         }
         indentNext = false;
      }
   }

   /**
    * Writes the default namespace to the output stream.
    * @param namespaceURI the uri to bind the default namespace to
    * @throws ifs.fnd.base.ParseException
    */
   public void writeDefaultNamespace(String namespaceURI) throws ParseException {
      try {
         if(!xmlDeclarationWritten) init();
         writer.writeDefaultNamespace(namespaceURI);
      }
      catch(XMLStreamException e) {
         throw new ParseException(e, "XMLWRITER_DEFNAMESPACE:Cannot write default namespace to XML stream: &1", e.getMessage());
      }
   }

   /**
    * Closes this writer and its underlying XML writer.
    * This method does not close the underlying output stream.
    * @throws ifs.fnd.base.ParseException
    */
   public void close() throws ParseException {
      try {
         if(!xmlDeclarationWritten) init();
         if(!omitXmlDeclaration && !docWrittenToStream)
            writer.writeEndDocument();
         writer.flush();
         if(docWrittenToStream)
            out.close();
         else
            writer.close();
         // debugging
         ifs.fnd.log.Logger log = ifs.fnd.log.LogMgr.getClassLogger(this.getClass());
         if(log.trace) {
            if(this.out instanceof ByteArrayOutputStream ) {
               ByteArrayOutputStream bos = (ByteArrayOutputStream)this.out;
               log.trace("XML written to OutputStream:\n&1\n.", Str.bytesToString(bos.toByteArray()));
               try {
                  bos.writeTo(this.outOrg);
                  this.outOrg.flush();
               } catch(IOException e) {
                  throw new ParseException(e, "IOException '&1' while writing to the output stream", e.toString());
               }
            } else if(this.out instanceof FndByteBufferOutputStream) {
               FndByteBufferOutputStream bos = (FndByteBufferOutputStream)this.out;
               byte[] buf = bos.getBytes();
               log.trace("XML written to OutputStream:\n&1\n.", Str.bytesUtf8ToString(buf));
            }
      }
         if(log.debug) printStackTrace(log);
      }
      catch(XMLStreamException | IOException e) {
         throw new ParseException(e, "XMLWRITER_CLOSE:Cannot close XML stream: &1", e.getMessage());
      }
   }

   /**
    * Adds prefix to an attribute or element name only if not empty.
    */
   private String addPrefix(String prefix, String name) {
      if(prefix != null && prefix.length() > 0)
         return prefix+":"+name;
      else
         return name;
   }

   private void printStackTrace(ifs.fnd.log.Logger log) {
      if( log==null )
         log = ifs.fnd.log.LogMgr.getClassLogger(this.getClass());
      if(log.debug) log.debug("Current stack trace:\n&1\n.", ifs.fnd.service.Util.getStackTrace(new Exception()));
   }
   
   void flush() throws ParseException {
      try {
         this.writer.flush();
      } catch (XMLStreamException e) {
         throw new ParseException(e, "XMLWRITER_FLUSH:Cannot flush XML stream: &1", e.getMessage());
      }
   }
}