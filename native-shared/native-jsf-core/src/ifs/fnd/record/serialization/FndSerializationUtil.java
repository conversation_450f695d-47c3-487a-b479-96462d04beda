/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record.serialization;

import ifs.fnd.base.*;
import ifs.fnd.record.*;

/**
 * <B>Framework internal class:</B> Utility class for serializing from input/output to/from views.
 * Handles input/output formats:
 * </P>
 * <UL>
 * <LI>Buffer</LI>
 * <LI>XML</LI>
 * </UL>
 * <P>
 * When parsing/formatting, the raw data format in what is set in the current
 * context.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public final class FndSerializationUtil {

   /**
    * Avoid instantiations.
    */
   private FndSerializationUtil() {
   }

   /**
    * Parses raw data into a record.
    * @param   data  the raw data to parse.
    * @param   record   the record to parse into.
    * @exception  IfsException   if the data for some reason cannot be parsed
    * into the record.
    */
   public static void parseRecord(byte[] data, FndAbstractRecord record) throws IfsException {
      if(FndContext.getCurrentExternalBodyType() == FndSerializeConstants.BODY_TYPE_XML)
         FndXmlUtil.parseRecord(data, record);
      else
         FndBufferUtil.parseRecord(data, record);
   }

   public static void parseRecord(String data, FndAbstractRecord record) throws IfsException {
      parseRecord(FndBufferUtil.toByteArray(data), record);
   }

   /**
    * Parses raw data into a record.
    * @param   data  the raw data to parse.
    * @param   inRecord   the first record to parse into.
    * @param   outRecord   the second record to parse into.
    * @exception  IfsException   if the data for some reason cannot be parsed
    * into the record.
    */
   public static void parseRecord(byte[] data, FndAbstractRecord inRecord, FndAbstractRecord outRecord) throws IfsException {
      if(FndContext.getCurrentExternalBodyType() == FndSerializeConstants.BODY_TYPE_XML)
         FndXmlUtil.parseRecord(data, inRecord);
      else
         FndBufferUtil.parseRecord(data, inRecord, outRecord);
   }

   public static void parseRecord(String data, FndAbstractRecord inRecord, FndAbstractRecord outRecord) throws IfsException {
      parseRecord(FndBufferUtil.toByteArray(data), inRecord, outRecord);
   }

   /**
    * Formats a record into raw data.
    * @param   record   the record to format.
    * @return the formatted record.
    * @exception  IfsException   if the record for some reason cannot be formatted
    * into the raw data format.
    */
   public static byte[] formatRecord(FndAbstractRecord record) throws IfsException {
      if(FndContext.getCurrentExternalBodyType() == FndSerializeConstants.BODY_TYPE_XML)
         return FndXmlUtil.formatRecord(record);
      else
         return FndBufferUtil.formatRecord(record);
   }

   /**
    * Formats a record into raw data.
    * @param inRecord the record to format.
    * @param outRecord the output record to format
    * @return the formatted record.
    * @exception IfsException   if the record for some reason cannot be formatted
    * into the raw data format.
    */
   public static byte[] formatRecord(FndAbstractRecord inRecord, FndAbstractRecord outRecord) throws IfsException {
      if(FndContext.getCurrentExternalBodyType() == FndSerializeConstants.BODY_TYPE_XML)
         return FndXmlUtil.formatRecord(outRecord); //XML => do NOT return in view
      else
         return FndBufferUtil.formatRecord(inRecord, outRecord);
   }

   /**
    * Converts an IFS Record in buffer format (usually *.rec) into the
    * equavilent XML format.
    * @param   buf   an <code>byte</code>-array with the serialized buffer.
    * @return  the XML document as a UTF-8 encoded string.
    * @throws  ParseException if there is a problem parsing the input or
    * formatting the output.
    */
   public static byte[] bufferToXml(byte[] buf) throws ParseException {
      FndRecord rec = new FndRecord();
      FndBufferUtil.parseRecord(buf, rec);
      return FndXmlUtil.formatRecord(rec);
   }

   /**
    * Converts an IFS Record in XML format into the equavilent IFS buffer format.
    * @param   xml   an <code>byte</code>-array with the serialized XML.
    * @return  the <code>byte</code>-array with the serialized IFS buffer.
    * @throws  ParseException if there is a problem parsing the input or
    * formatting the output.
    * @throws  SystemException   if there are any other problems with the
    * conversion.
    */
   public static byte[] xmlToBuffer(byte[] xml) throws ParseException, SystemException {
      FndRecord rec = new FndRecord();
      FndXmlUtil.parseRecord(xml, rec, true);
      return FndBufferUtil.formatRecord(rec);
   }
}
