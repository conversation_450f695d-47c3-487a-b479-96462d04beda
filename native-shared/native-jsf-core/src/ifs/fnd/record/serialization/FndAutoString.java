/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record.serialization;

/**
 * This class can be used instead of java.lang.StringBuffer.
 * It has better performance but is not safe for use by multiple threads.
 */
public final class FndAutoString implements Cloneable {
   private char[] value;
   private int length;

   /**
    * Construct a new AutoString allocating a buffer of the specified size.
    */
   public FndAutoString(int initialSize) {
      value = new char[initialSize];
      length = 0;
   }

   /**
    * Construct a new AutoString allocating a buffer of the default size.
    */
   public FndAutoString() {
      this(256);
   }

   /**
    * Append a string to this string buffer.
    */
   public FndAutoString append(String str) {
      if (str == null)
         return this;
      int len = str.length();
      if (length + len > value.length)
         reallocate(length + len);

      str.getChars(0, len, value, length);
      length += len;
      return this;
   }

   /**
    * Append a sequence of two strings to this string buffer.
    */
   public FndAutoString append(String str1, String str2) {
      append(str1);
      append(str2);
      return this;
   }

   /**
    * Append a sequence of three strings to this string buffer.
    */
   public FndAutoString append(String str1, String str2, String str3) {
      append(str1);
      append(str2);
      append(str3);
      return this;
   }

   /**
    * Append a sequence of four strings to this string buffer.
    */
   public FndAutoString append(String str1, String str2, String str3, String str4) {
      append(str1);
      append(str2);
      append(str3);
      append(str4);
      return this;
   }

   /**
    * Append a sequence of five strings to this string buffer.
    */
   public FndAutoString append(String str1, String str2, String str3, String str4, String str5) {
      append(str1);
      append(str2);
      append(str3);
      append(str4);
      append(str5);
      return this;
   }

   /**
    * Append a sequence of six strings to this string buffer.
    */
   public FndAutoString append(String str1, String str2, String str3, String str4, String str5, String str6) {
      append(str1);
      append(str2);
      append(str3);
      append(str4);
      append(str5);
      append(str6);
      return this;
   }

   /**
    * Append a portion of an array of characters to this string buffer.
    */
   public FndAutoString append(char[] cbuf, int off, int len) {
      if (len == 0)
         return this;
      if (length + len > value.length)
         reallocate(length + len);

      System.arraycopy(cbuf, off, value, length, len);
      length += len;
      return this;
   }

   /**
    * Append to this buffer the contents of another AutoString
    */
   public FndAutoString append(FndAutoString buf) {
      append(buf.value, 0, buf.length);
      return this;
   }

   /**
    * Append one character to this string buffer.
    */
   public FndAutoString append(char ch) {
      if (length + 1 > value.length)
         reallocate(length + 1);

      value[length++] = ch;
      return this;
   }

   /**
    * Append to this buffer the string representation of an integer
    */
   public FndAutoString appendInt(int i) {
      if (length + 12 > value.length)
         reallocate(length + 12);

      if (i < 0) {
         value[length++] = '-';
         i = -i;
      }

      int first = length;

      do {
         value[length++] = (char) ('0' + i % 10);
         i = i / 10;
      }
      while (i > 0);

      int last = length - 1;

      while (first < last) {
         char tmp = value[first];
         value[first] = value[last];
         value[last] = tmp;
         first++;
         last--;
      }
      return this;
   }

   /**
    * Append to this buffer the string representation of boolean
    */
   public FndAutoString appendBoolean(boolean b) {
      append(b ? "true" : "false");
      return this;
   }

   /**
    * Clear the characters from the buffer but keep the allocated space
    */
   public void clear() {
      length = 0;
   }

   /**
    * Return the length (character count) of this string buffer.
    */
   public int length() {
      return length;
   }

   private void reallocate(int needSize) {
      int extsize = 2 * value.length;
      int newsize = needSize > extsize ? needSize : extsize;

      char[] newvalue = new char[newsize];

      System.arraycopy(value, 0, newvalue, 0, value.length);
      value = newvalue;
   }

   /**
    * Convert to a string representing the data in this string buffer
    */
   @Override
   public String toString() {
      return new String(value, 0, length);
   }

   /**
    * Return a hashcode for this AutoString
    */
   @Override
   public int hashCode() {
      int h = 0;
      int off = 0;
      char val[] = value;
      int len = length;

      if (len < 16) {
         for (int i = len; i > 0; i--) {
              h = (h * 37) + val[off++];
          }
      }
      else {
         // only sample some characters
         int skip = len / 8;
         for (int i = len; i > 0; i -= skip, off += skip) {
              h = (h * 39) + val[off];
          }
      }
      return h;
   }

   /**
    * Return true if a specified Object is an AutoString and has exactly the same
    * contents as this AutoString
    */
   @Override
   public boolean equals(Object obj) {
      if (obj != null && obj instanceof FndAutoString) {
         FndAutoString another = (FndAutoString) obj;
         int n = length;
         if (n == another.length) {
            char v1[] = value;
            char v2[] = another.value;
            int i = 0;
            int j = 0;
            while (n-- != 0) {
               if (v1[i++] != v2[j++])
                  return false;
            }
            return true;
         }
      }
      return false;
   }

   /**
    * Clones this AutoString.
    * @return copy of this FndAutoString
    */
   @Override
   public Object clone() {
      FndAutoString buf = new FndAutoString(length);
      buf.append(value, 0, length);
      return buf;
   }

   /**
    * Sets the length of this string buffer to a value lower than the current length of the buffer.
    * @param   newLength the new length
    * @throws  IndexOutOfBoundsException if the specified length is negative or is greater than the current length of the buffer.
    */
   public void setLength(int newLength) {
      if(newLength < 0 || newLength > length)
         throw new StringIndexOutOfBoundsException(newLength);
      length = newLength;
   }
   
    /**
     * Tests if this string buffer ends with the specified suffix.
     *
     * @param   suffix   the suffix.
     * @return  <code>true</code> if the character sequence represented by the
     *          argument is a suffix of the character sequence represented by
     *          this buffer, <code>false</code> otherwise. Note that the
     *          result will be <code>true</code> if the argument is the empty string.
     */
   public boolean endsWith(String suffix) {
      int suffixLen = suffix.length();
      if(suffixLen > length)
         return false;
      int start = length - suffixLen;
      for(int i = 0; i < suffixLen; i++) {
         if(value[start + i] != suffix.charAt(i))
            return false;
      }
      return true;
   }
}
