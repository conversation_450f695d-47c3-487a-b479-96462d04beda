/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record.serialization;

import ifs.fnd.base.ParseException;
import ifs.fnd.internal.FndAttributeInternals;
import ifs.fnd.internal.FndRecordInternals;
import ifs.fnd.record.FndAbstractArray;
import ifs.fnd.record.FndAbstractRecord;
import ifs.fnd.record.FndAttribute;

/**
 * Class that uses XML Streaming API to format a record or array.
 */
public class FndXmlRecordFormatter
{
   /**
    * XML stream to write data to.
    */
   private FndXmlWriter writer;

   /**
    * Holds the xml document's default namespace. The namespace is set on the
    * root element.
    */
   private String defaultNamespace;

   /**
    * The serialization flags set.
    */
   private int flags;

   /**
    * Sets whether any namespace attributes added to the root element by default.
    */
   private boolean addDefaultNamespaces = true;

   /**
    * The direction (REQUEST/RESPONSE) of the formatted record or array.
    */
   private FndRecordFormat.Direction direction;

   /**
    * The flag indicating if the records and attributes
    * should be destroyed after formatting.
    */
   private boolean destroyFormattedRecords;

   /**
    * Constructs a new instance of FndXmlRecordFormatter with the default values for all properties.
    * Before calling formatRecord() or formatArray() the formatter may be configured using
    * one or more of its set-methods.
    *
    * @param writer the XML stream to write to
    *
    * @see #setDefaultNamespace
    * @see #setAddDefaultNamespaces
    * @see #setFlags
    * @see #setDirection
    * @see #setDestroyFormattedRecords
    */
   public FndXmlRecordFormatter(FndXmlWriter writer) throws ParseException {
      this.writer = writer;
   }

   /**
    * Sets the direction (REQUEST/RESPONSE) property of this formatter.
    * The default value for direction is null.
    * @param  direction the direction (REQUEST/RESPONSE) of a parameter-list view,
    *                   for other types of views the direction is ignored
    */
   public void setDirection(FndRecordFormat.Direction direction) {
      this.direction = direction;
   }

   /**
    * Returns the direction (REQUEST/RESPONSE) of the formatted record or array.
    * @return an instance of FndRecordFormat.Direction, or null if the direction is unspecified.
    */
   public FndRecordFormat.Direction getDirection() {
      return direction;
   }

   /**
    * Sets the flag indicating if the formatted records and attributes should be destroyed.
    * The default value for this flag is false.
    * @param flag true to destroy formatted records and attributes
    */
   public void setDestroyFormattedRecords(boolean flag) {
      destroyFormattedRecords = flag;
   }

   /**
    * Gets the flag indicating if the formatted records and attributes should be destroyed.
    * @return true if the formatted records and attributes should be destroyed, false otherwise
    */
   public boolean destroyFormattedRecords() {
      return destroyFormattedRecords;
   }

   /**
    * Sets the flag indicating if default namespace attributes should be added to the root element.
    * The default value is true.
    *
    * @param on if true then some default namespace attributes are automatically added to the root element.
    */
   public void setAddDefaultNamespaces(boolean on) {
      this.addDefaultNamespaces = on;
   }

   /**
    * Returns true if some default namespaces should be added to the root element.
    * @return true if the XML document's root element should have
    *         some namespace attributes added automatically.
    */
   public boolean addDefaultNamespaces() {
      return addDefaultNamespaces;
   }

   /**
    * Sets the default namespace.
    * The default value for namespace is null.
    *
    * @param namespace the namespace to add to the root element
    *
    * @see #getDefaultNamespace
    */
   public void setDefaultNamespace(String namespace) {
      defaultNamespace = namespace;
   }

   /**
    * Gets the default namespace.
    *
    * @return
    * @see #setDefaultNamespace
    */
   public String getDefaultNamespace() {
      return defaultNamespace;
   }

   /**
    * Returns <code>true</code> if the XML file in production should contain
    * IFS specific record attributes (in namespace <code>ifsrecord</code>).
    * @return <code>true</code> if the XML document should contain any of the
    * record attributes (that is, if any serializaton flag is set).
    */
   public boolean addRecordAttributes() {
      return flags > 0;
   }

   /**
    * Checks if a serialization flag is set.
    * @param   flag  the flags to check. May be any of the <code>FLAG_</code>*
    * constants of this class (except FLAG_ALL_INFO).
    * @return  <code>true</code> if the flag is set, <code>false</code>
    * otherwise.
    */
   public boolean isFlagSet(int flag) {
      return (flags & flag) != 0;
   }

   /**
    * Sets the serialization flags defined in interface FndXmlSerializer.
    * The default value for falgs is 0.
    *
    * @param   flags the flags to set. Can be any of the <code>FLAG_*</code>
    * contants defined in interface FndXmlSerializer or a bitwise or (<code>|</code>) combination of
    * them (except for FLAG_ALL_INFO).
    *
    * @see FndXmlSerializer
    * @see #isFlagSet
    */
   public void setFlags(int flags) {
      this.flags = flags;
   }

   /**
    * Formats a record into XML output stream.
    *
    * @param record the record to format.
    *
    * @throws ParseException if the record for some reason cannot be formatted into xml.
    */
   public void formatRecord(FndAbstractRecord record) throws ParseException {
      formatRecord(record, null, null);
   }

   /**
    * Formats a record into XML output stream.
    *
    * @param record          the record to format.
    * @param customAttr      top level attribute (tested with "==" operator) which will be formatted by the custom formatter
    * @param customFormatter formatter that will format the custom attribute
    *
    * @throws ParseException if the record for some reason cannot be formatted into xml.
    */
   public void formatRecord(FndAbstractRecord record,
                            FndAttribute customAttr,
                            FndAttributeXmlFormatter customFormatter) throws ParseException {

      if(defaultNamespace != null) {
         flags = 0;
         defaultNamespace = "urn:ifsworld-com:schemas:" + defaultNamespace;
      }
      else
         defaultNamespace = FndXmlUtil.getSchemaNameForView(record, direction);

      FndXmlStreamSerializer serializer = new FndXmlStreamSerializer(writer, this);
      FndRecordInternals.formatToXml(record, serializer, customAttr, customFormatter);
   }

   /**
    * Formats an array into XML output stream.
    *
    * @param array the array to format
    * @throws ParseException if the array for some reason cannot be formatted into xml.
    */
   public void formatArray(FndAbstractArray array) throws ParseException {

      String name = direction==null ? array.newRecord().getType() + "_LIST" : direction.toString();

      if(defaultNamespace != null) {
         flags = 0;
         defaultNamespace = "urn:ifsworld-com:schemas:" + defaultNamespace;
      }
      else {
         defaultNamespace = FndXmlUtil.getSchemaNameForView(array.newRecord(), direction) + "_list";
      }
      defaultNamespace = defaultNamespace.toLowerCase(); // why only for array but not for record?

      FndXmlStreamSerializer serializer = new FndXmlStreamSerializer(writer, this);

      serializer.startElement(name);
      serializer.newLine();

      for(int i=0; i<array.size(); i++)
         FndRecordInternals.formatToXml(FndAttributeInternals.internalGet(array, i), serializer, null, null);

      serializer.endElement(name);
      serializer.newLine();
   }
}
