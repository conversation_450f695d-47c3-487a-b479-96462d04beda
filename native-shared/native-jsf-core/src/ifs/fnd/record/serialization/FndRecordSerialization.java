/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record.serialization;

import ifs.fnd.base.*;

/**
 * <B>Framework internal class:</B> Record buffer parse/serialization methods.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public interface FndRecordSerialization {
   void parseBuffer(FndTokenReader stream) throws ParseException;
   void formatBuffer(FndTokenWriter stream) throws ParseException;
   void parse(FndTokenReader stream) throws ParseException;
   void format(FndTokenWriter stream) throws ParseException;
   void parseItem(FndTokenReader stream) throws ParseException;
   void formatItem(FndTokenWriter stream) throws ParseException;
}
