/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;

/**
 * Class for generating unique keys.
 * @deprecated Use java.util.UUID
 */
public final class FndKeyGen {

    //Disable instantiations
    private FndKeyGen() {
    }

    /**
     * Return a
     * <code>String</code> that contains globally unique id.
     *
     * @return a new randomly generated UUID.
     */
    public static String newId() {
        return java.util.UUID.randomUUID().toString();
    }

    /**
     * Return a
     * <code>String</code> that contains globally unique id.
     *
     * @return a new randomly generated UUID.
     */
    public static String newRandomId() {
        return java.util.UUID.randomUUID().toString();
    }
}
