/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;

import ifs.fnd.base.FndEncryption;
import ifs.fnd.base.EncryptionException;
import ifs.fnd.base.FndContext;
import java.io.IOException;

final class FndEncrypter
{
   /*
    * HEDJSE: Changed this class to use ifs.fnd.base.FndEncryption to do the
    *         actual encryption/decryption. That class requires no configuration
    *         of encryption key. This class still checks the global encryption
    *         enabled flag in context.
    */

   //Avoid instantiations
   private FndEncrypter() {
   }

   static String encrypt(String unencryptedString) throws EncryptionException {
      try {
         return (FndContext.getCurrentContext().isEncryptionEnabled() ?
                  FndEncryption.encrypt(unencryptedString) : unencryptedString);
      }
      catch (IOException ex) {
         throw new EncryptionException(ex, "I/O error during encryption: &1.", ex.getMessage());
      }
   }
   static String decrypt(String encryptedString) throws EncryptionException {
      try {
         return (FndContext.getCurrentContext().isEncryptionEnabled() ?
            FndEncryption.decrypt(encryptedString) : encryptedString);
      }
      catch (IOException ex) {
         throw new EncryptionException(ex, "I/O error during decryption: &1.", ex.getMessage());
      }
   }
}
