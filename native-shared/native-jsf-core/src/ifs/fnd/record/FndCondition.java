/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.record;

import ifs.fnd.base.ApplicationException;
import ifs.fnd.base.IfsRuntimeException;
import ifs.fnd.record.serialization.FndSerializeConstants;

/**
 * A query condition, can be simple or compound.
 * Simple conditions are combined into compound conditions using AND and OR operators.
 * <p>
 * The following example combines three simple conditions into one compound condition
 * by calling {@link #and and} and {@link #or or} methods.
 * <pre>
 *    PersonInfo person = new PersonInfo();
 *    person.excludeQueryResults();
 *    person.firstName.include();
 *    person.lastName.include();
 *
 *    FndSimpleCondition country = person.country.createEqualIgnoreCaseCondition("US");
 *    FndSimpleCondition first = person.firstName.createLikeCondition("Al%");
 *    FndSimpleCondition last  = person.lastName.createLikeCondition("Al%");
 *    FndCondition c = country.<B>and</B>(first.<B>or</B>(last));
 *    person.addCondition(c);
 *
 *    PersonInfoHandler handler = PersonInfoHandlerFactory.getHandler();
 *    FndQueryRecord qry = new FndQueryRecord(person);
 *    PersonInfoArray arr = (PersonInfoArray) handler.query(qry); </pre>
 * The query results in the following server debug output:
 * <pre>
 *    SELECT A.FIRST_NAME, A.LAST_NAME
 *    FROM ifsapp.PERSON_INFO_PUBLIC A
 *    WHERE (LOWER(A.COUNTRY_DB) = LOWER(:1)
 *    <B>AND</B> (A.FIRST_NAME LIKE :2
 *    <B>OR</B> A.LAST_NAME LIKE :3))
 *
 *    FndStatement: Binding parameters:
 *       1: (TEXT) = US
 *       2: (TEXT) = Al%
 *       3: (TEXT) = Al%
 * </pre>
 *
 * @see ifs.fnd.record.FndSimpleCondition FndSimpleCondition
 * @see ifs.fnd.record.FndDetailCondition FndDetailCondition
 * @see ifs.fnd.record.FndQueryResultCategory FndQueryResultCategory
 * @see ifs.fnd.record.FndQueryRecord FndQueryRecord
 */
public class FndCondition extends FndView {

   private static final FndRecordMeta VIEW_META = new FndRecordMeta(FndSerializeConstants.COMPOUND_CONDITION_TAG);
   private static final FndAttributeMeta CATEGORY_META = new FndAttributeMeta("CATEGORY");
   private static final FndAttributeMeta LEFT_META = new FndAttributeMeta("LEFT");
   private static final FndAttributeMeta RIGHT_META = new FndAttributeMeta("RIGHT");
   private static final String CATEGORY_OR  = "OR";
   private static final String CATEGORY_AND = "AND";

   public final FndAlpha category     = new FndAlpha(CATEGORY_META);
   public final FndCompoundItem left  = new FndCompoundItem(LEFT_META);
   public final FndCompoundItem right = new FndCompoundItem(RIGHT_META);

   /**
    * Outer (compound) condition or the record owning this condition.
    * The variable is used to recreate the exact type of an attribute/record
    * during parsing a simple/detail condition.
    */
   private transient FndAbstractRecord owner;

   /**
    * Framework internal constructor required by Externalizable interface.
    */
   public FndCondition() {
      super(VIEW_META);
      add(category);
      add(left);
      add(right);
      setState(FndRecordState.QUERY_RECORD);
   }

   protected FndCondition(FndRecordMeta meta) {
      super(meta);
      add(category);
      add(left);
      add(right);
      setState(FndRecordState.QUERY_RECORD);
   }

   //Used only from FndSimpleCondition
   protected FndCondition(boolean init, FndRecordMeta meta) {
      super(meta);
      setState(FndRecordState.QUERY_RECORD);
   }

   /**
    * Sets the record beeing the owner (parent) of this query condition.
    * @param owner an outer (compound) condition or the record owning this condition.
    */
   final void setOwner(FndAbstractRecord owner) {
      this.owner = owner;
   }

   /** Used for cloning.
    *  @return A new instance with meta-data copied.
    */
   @Override
   public FndAbstractRecord newInstance() {
      return new FndCondition();
   }

   private FndCondition combine(FndCondition condition, String categoryValue) {
      FndCondition newcond = new FndCondition();
      try {
         newcond.left.setRecord(this);
         newcond.right.setRecord(condition);
         newcond.category.setValue(categoryValue);
      }
      catch (ApplicationException e) {
         throw new IfsRuntimeException(e, "CONDCOMBINE:Error combining conditions");
      }
      return newcond;
   }

   /** Combines two condition with "and"
    * @param condition Condition to combine with
    * @return Resulting condition
    */
   public FndCondition and(FndCondition condition) {
      return combine(condition, CATEGORY_AND);
   }

   /** Combines two conditions with "or"
    * @param condition Condition to combine with
    * @return Resulting condition
    */
   public FndCondition or(FndCondition condition) {
      return combine(condition, CATEGORY_OR);
   }

   /**
    * Returns the the record owning the attribute beeing the target of this query condition.
    */
   final FndAbstractRecord findOwnerRecord() {
      if(owner instanceof FndCondition)
         return ((FndCondition)owner).findOwnerRecord();
      else
         return owner;
   }

}