/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.sta.management;

import ifs.fnd.base.FndEncryption;
import ifs.fnd.util.IoUtil;
import java.io.File;
import java.io.IOException;
import java.util.Map;
import java.util.Iterator;
import java.util.SortedSet;
import java.util.TreeSet;
import javax.management.Attribute;
import javax.management.AttributeChangeNotification;
import javax.management.AttributeList;
import javax.management.AttributeNotFoundException;
import javax.management.DynamicMBean;
import javax.management.InvalidAttributeValueException;
import javax.management.MBeanAttributeInfo;
import javax.management.MBeanException;
import javax.management.MBeanInfo;
import javax.management.MBeanNotificationInfo;
import javax.management.MBeanOperationInfo;
import javax.management.Notification;
import javax.management.NotificationBroadcasterSupport;
import javax.management.ReflectionException;

/**
 * This service is used to initialize applications by reading properties stored
 * in their xml based configuration files. Additionally, it provides a complete management
 * interface to handle configurations on the fly.
 */

public class PropertyManagementService
        extends NotificationBroadcasterSupport implements DynamicMBean
{
   /** Properties file which contains the configuration data */
   private final File propertyFile;
   
   /** Handler which handles properties stored in the <code>propertyFile</code>. */
   private PropertyFileHandler fileHandler;
   
   /** Map of all the properties stored the configuration file */
   private Map<String,Property> properties;
   
   /** Notification sequence id */
   private long sequenceId = 0;
   
   /**
    * Creates an instance.
    *
    * @param   filename    Name of the configuration file.
    * @throws  <code>IOException</code> if properties cannot be read from the configuration file
    *          due to an I/O error.
    */
   public PropertyManagementService(String filename) throws IOException
   {
      this.propertyFile = new File(filename);
      loadProperties();
   }
   
   @Override
   public synchronized String getAttribute(String name) throws AttributeNotFoundException, MBeanException
   {
      Property p;
      if(properties.containsKey(name) && (p = properties.get(name)) != null)
      {
         if(p.isEncrypted())
         {
            try
            {
               return FndEncryption.decrypt(p.getValue());
            }
            catch (Exception e)
            {
               throw new MBeanException(e, "Value decryption failed.");
            }
            
         }
         else
         {
            return p.getValue();
         }
      }
      else
      {
         throw new AttributeNotFoundException("No such property: " + name);
      }
   }
   
   @Override
   public synchronized AttributeList getAttributes(String[] names)
   {
      AttributeList list = new AttributeList();
      Property p;
      for (String name : names)
      {
         if(properties.containsKey(name) && (p = properties.get(name)) != null)
         {
            String value;
            if(p.isEncrypted())
            {
               try
               {
                  String tmp = FndEncryption.encrypt("manager");
                  value = FndEncryption.decrypt(p.getValue());
               }
               catch (Exception e)
               {
                  e.printStackTrace();
                  continue;
               }
            }
            else
            {
               value = p.getValue();
            }
            list.add(new Attribute(name, value));
         }
      }
      return list;
   }
   
   @Override
   public synchronized void setAttribute(Attribute attribute)
   throws InvalidAttributeValueException, MBeanException, AttributeNotFoundException
   {
      String name = attribute.getName();
      Property p;
      String oldVal;
      
      if(!properties.containsKey(name) || (p = properties.get(name)) == null)
         throw new AttributeNotFoundException(name);
      
      Object value = attribute.getValue();
      if (!(value instanceof String))
         throw new InvalidAttributeValueException("Attribute value is not a string: " + value);
      
      oldVal = p.getValue();
      
      if(p.isEncrypted())
      {
         try
         {
            p.setValue(FndEncryption.encrypt((String)value));
         }
         catch (Exception e)
         {
            throw new MBeanException(e, "Value encryption failed.");
         }
      }
      else
      {
         p.setValue((String)value);
      }
      
      /* set system property */
      System.setProperty(name, p.getValue());
      
      /* send notification */
      try
      {
         sendAttributeChageNotification(name, p.getValue(), oldVal, p.isEncrypted());
      }
      catch (Exception e)
      {
         IoUtil.stderrln("Sending AttributeChangeNotification failed. Cause: " + e.getMessage());
      }
   }
   
   @Override
   public synchronized AttributeList setAttributes(AttributeList list)
   {
      Attribute[] attributes = (Attribute[]) list.toArray(new Attribute[0]);
      AttributeList returnList = new AttributeList();
      Property p;
      for (Attribute attr : attributes)
      {
         String name = attr.getName();
         Object newVal = attr.getValue();
         
         if (properties.containsKey(name) && (p = properties.get(name)) != null
                 && newVal instanceof String)
         {
            Object oldVal = p.getValue();
            
            if(p.isEncrypted())
            {
               try
               {
                  p.setValue(FndEncryption.encrypt((String)newVal));
               }
               catch (Exception e)
               {
                  continue;
               }
            }
            else
            {
               p.setValue((String)newVal);
            }
            /* set system property */
            System.setProperty(name, p.getValue());
            
            returnList.add(new Attribute(name, newVal));
            
            /* send notification */
            try
            {
               sendAttributeChageNotification(name, p.getValue(), oldVal, p.isEncrypted());
            }
            catch (Exception e)
            {
               IoUtil.stderrln("Sending AttributeChangeNotification failed. Cause: " + e.getMessage());
            }
         }
      }
      return returnList;
   }
   
   @Override
   public Object invoke(String name, Object[] args, String[] sig) throws MBeanException, ReflectionException
   {
      if ("reloadProperties".equals(name) &&
              (args == null || args.length == 0) &&
              (sig == null || sig.length == 0))
      {
         try
         {
            return loadProperties();
         }
         catch (IOException e)
         {
            throw new MBeanException(e);
         }
      }
      else if ("saveProperties".equals(name) &&
              (args == null || args.length == 0) &&
              (sig == null || sig.length == 0))
      {
         try
         {
            return saveProperties();
         }
         catch (IOException e)
         {
            throw new MBeanException(e);
         }
      }
      throw new ReflectionException(new NoSuchMethodException(name));
   }
   
   @Override
   public synchronized MBeanInfo getMBeanInfo()
   {
      SortedSet<String> sortedPropNames = new TreeSet<>();
      for (Property p : properties.values())
         sortedPropNames.add(p.getName());
      MBeanAttributeInfo[] attrs = new MBeanAttributeInfo[sortedPropNames.size()];
      Iterator<String> it = sortedPropNames.iterator();
      for (int i = 0; i < attrs.length; i++)
      {
         String name = it.next();
         Property p = properties.get(name);
         attrs[i] = new MBeanAttributeInfo(
                 p.getName(),
                 "java.lang.String",
                 p.getDescription(),
                 p.isReadable(),
                 p.isWritable(),
                 false);
      }
      MBeanOperationInfo[] opers = {
         new MBeanOperationInfo(
                 "reloadProperties",
                 "Reload properties from file",
                 null,
                 "java.lang.String",
                 MBeanOperationInfo.ACTION_INFO),
         new MBeanOperationInfo(
                 "saveProperties",
                 "Save properties to persistant storage",
                 null,
                 "java.lang.String",
                 MBeanOperationInfo.ACTION_INFO)
      };
      MBeanNotificationInfo[] notifs = {
         new MBeanNotificationInfo(
                 new String[] { AttributeChangeNotification.ATTRIBUTE_CHANGE },
                 AttributeChangeNotification.class.getName(),
                 "Attribute value change notification")
      };
      return new MBeanInfo(
              this.getClass().getName(),
              "Property Manager MBean",
              attrs,
              null,
              opers,
              notifs);
   }
   
   /**
    * Loads all properties from the configuration file and updates the environment.
    *
    * @return  Status of the operation.
    * @throws  <code>IOException</code> if an error occurs while reading properties
    *          from the configuration file.
    */
   private String loadProperties() throws IOException
   {
      fileHandler = new PropertyFileHandler(propertyFile);
      properties = fileHandler.getProperties();
      
      /* updating the environment */
      for(Property p : properties.values())
      {
         System.setProperty(p.getName(), p.getValue());
      }
      return "Properties re-loaded successfully.";
   }
   
   /**
    * Write the properties to the configuration file.
    *
    * @return  Status of the operation.
    * @throws  <code>IOException</code> if an error occurs while writing properties
    *          to the configuration file.
    */
   private String saveProperties() throws IOException
   {
      if(fileHandler.setProperties(properties.values()))
         return "Properties written to the persistant storage successfully.";
      else
         return "Problem occured while saving the modifications. " +
                 "Changes not written to the persistant storage";
   }
   
   /**
    * Broadcasts an AttributeChangeNotification to all the interested listeners.
    *
    * @param   attribName  Name of the attribute subjected to the change.
    * @param   newVal      New value assigned to the attribute.
    * @param   oldVal      Value of the attribute before assigning the new value.
    * @param   isEncrypted Whether the value of the attribute is encrypted or not.
    * @throws  <code>Exception</code> if an error occurs while preparing or sending the notification.
    */
   private void sendAttributeChageNotification(String attribName, Object newValEnc, Object oldValEnc, boolean isEncrypted)
   throws Exception
   {
      String newVal = (String)newValEnc;
      String oldVal = (String)oldValEnc;
      
      if(isEncrypted)
      {
         newVal = FndEncryption.decrypt((String)newValEnc);
         oldVal = FndEncryption.decrypt((String)oldValEnc);
      }
      
      if(!newVal.equals(oldVal))
      {
         Notification n =
                 new AttributeChangeNotification(
                 this,
                 sequenceId++,
                 System.currentTimeMillis(),
                 "Attribute value of '" + attribName + "' has changed",
                 attribName,
                 "java.lang.String",
                 oldValEnc,
                 newValEnc);
         
         sendNotification(n);
      }
   }
}
