/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.storage;

import ifs.fnd.base.IfsException;
import ifs.fnd.base.SystemException;
import ifs.fnd.buffer.Buffer;
import ifs.fnd.buffer.Item;
import ifs.fnd.internal.FndAttributeInternals;
import ifs.fnd.record.FndAttribute;
import ifs.fnd.record.FndOutputStreamManager;
import ifs.fnd.record.FndRecord;
import ifs.fnd.services.plsqlserver.service.PlsqlConverter;
import ifs.fnd.services.plsqlserver.service.PlsqlInvocationBufferTypeMap;
import ifs.fnd.services.plsqlserver.service.PlsqlInvocationProcessor;
import ifs.fnd.services.plsqlserver.service.PlsqlUtil;
import java.io.IOException;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;

/**
 * Class representing a cursor over a database result set.
 * The result set is created by a call to PLSQL function returning a REF CURSOR.
 */
public class FndRefCursor implements AutoCloseable {

   private FndResultSet resultSet;    // used by nextRow() and getRecord()

   private FndStatement stmt;      // FndStatement functionality is reused in getXxx() methods

   private FndStatement mainStmt;  // The statemnent that returned the result set (null if it has been already closed)

   private PlsqlConverter plsqlConverter;                            // used by getRecord()

   private PlsqlInvocationProcessor.BufferConverter bufferConverter; // used by getRecord()

   /**
    * <B>Framework internal method:</B> Create a new instance of FndRefCursor over the specified JDBC ResultSet.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    *
    * @param resultSet JDBC result set
    * @param connection opened database connection
    * @throws ifs.fnd.base.SystemException
    */
   public FndRefCursor(FndResultSet resultSet, FndConnection connection) throws SystemException {
      this.resultSet = resultSet;
      stmt = connection.createStatement();
      stmt.setCursorResultSet(resultSet);
   }

   /**
    * <B>Framework internal method:</B> Create a new instance of FndRefCursor over the specified JDBC ResultSet.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    *
    * @param resultSet JDBC result set
    * @param connection opened database connection
    * @throws ifs.fnd.base.SystemException
    * @deprecated Use constructor taking FndResultSet as the first parameter.
    */
   public FndRefCursor(java.sql.ResultSet resultSet, FndConnection connection) throws SystemException {
      this(new FndResultSet(resultSet, null), connection);
   }

   /**
    * <B>Framework internal method:</B> Create a new instance of FndRefCursor over the specified JDBC ResultSet.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    *
    * @param resultSet JDBC result set
    * @param connection opened database connection
    * @param mainStmt the statemnent that returned the result set
    * @throws ifs.fnd.base.SystemException
    */
   public FndRefCursor(FndResultSet resultSet, FndConnection connection, FndStatement mainStmt) throws SystemException {
      this(resultSet, connection);
      this.mainStmt = mainStmt;
   }

   /**
    * <B>Framework internal method:</B> Create a new instance of FndRefCursor over the specified JDBC ResultSet.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    *
    * @param resultSet JDBC result set
    * @param connection opened database connection
    * @param mainStmt the statemnent that returned the result set
    * @throws ifs.fnd.base.SystemException
    * @deprecated Use constructor taking FndResultSet as the first parameter.
    */
   public FndRefCursor(java.sql.ResultSet resultSet, FndConnection connection, FndStatement mainStmt) throws SystemException {
      this(new FndResultSet(resultSet, null), connection, mainStmt);
   }

   /**
    * Moves the cursor to the next row in the result set.
    * @return true if the new current row is valid, false if there are no more rows
    * @throws SystemException
    */
   public boolean nextRow() throws SystemException {
      try {
         boolean valid = resultSet.next();
         if(!valid)
            close();
         return valid;
      }
      catch (SQLException e) {
         throw new SystemException(e, "REFCURSORNEXT:Unable to move cursor to next record." + " Error is: &1", e.getMessage().trim());
      }
   }

   /**
    * Close this cursor and its database connecion.
    * @throws SystemException
    */
   @Override
   public void close() throws SystemException {
      try {
         if(mainStmt != null)
            mainStmt.close(); // unregister FndAbortableProcess
      }
      finally {
         stmt.getFndConnection().close();
      }
   }

   /**
    * Retrieve a typed value from the current row in the result set into an attribute.
    * @param attr the attribute to retrieve value into
    * @param colNr column position in the result set
    * @throws IfsException
    */
   public void getValue(FndAttribute attr, int colNr) throws IfsException {
      FndAttributeInternals.setSqlValue(attr, stmt, colNr);
      if (stmt.resultWasNull())
         attr.setNull();
   }

   /**
    * Get string value from the current row in the result set.
    * @param colNr column position in the result set
    * @return the column value
    * @throws IfsException
    */
   public String getString(int colNr) throws IfsException {
      return stmt.getString(colNr);
   }

   /**
    * Get string value from the current row in the result set.
    * @param colNr column position in the result set
    * @return the column value
    * @throws IfsException
    */
   public String getText(int colNr) throws IfsException {
      return stmt.getText(colNr);
   }

   /**
    * Get string value from the current row in the result set.
    * @param colNr column position in the result set
    * @return the column value
    * @throws IfsException
    */
   public String getLongText(int colNr) throws IfsException {
      return stmt.getLongText(colNr);
   }

   /**
    * Get binary value from the current row in the result set.
    * @param colNr column position in the result set
    * @return the column value
    * @throws IfsException
    */
   public byte[] getBinary(int colNr) throws IfsException {
      return stmt.getBinary(colNr);
   }

   /**
    * Get column (binary) value from database into a stream.
    * @param colNr column position in the result set
    * @param outputMgr an output stream manager taking care of data fetched from the database
    * @return true if not null binaray data has been written to the stream, false otherwise
    * @throws IfsException
    */
   public boolean getBinary(int colNr, FndOutputStreamManager outputMgr) throws IfsException {
      return stmt.getBinary(colNr, outputMgr);
   }

   /**
    * Get binary value from the current row in the result set.
    * @param colNr column position in the result set
    * @return the column value
    * @throws IfsException
    */
   public byte[] getLongRaw(int colNr) throws IfsException {
      return stmt.getLongRaw(colNr);
   }


   /**
    * Get boolean value from the current row in the result set.
    * @param colNr column position in the result set
    * @return the column value
    * @throws IfsException
    */
   public boolean getBoolean(int colNr) throws IfsException {
      return stmt.getBoolean(colNr);
   }


   /**
    * Get Timestamp value from the current row in the result set.
    * @param colNr column position in the result set
    * @return the column value
    * @throws IfsException
    */
   public java.sql.Timestamp getTimestamp(int colNr) throws IfsException {
      return stmt.getTimestamp(colNr);
   }

   /**
    * Get Date value from the current row in the result set.
    * @param colNr column position in the result set
    * @return the column value
    * @throws IfsException
    */
   public java.sql.Date getDate(int colNr) throws IfsException {
      return stmt.getDate(colNr);
   }

   /**
    * Get Time value from the current row in the result set.
    * @param colNr column position in the result set
    * @return the column value
    * @throws IfsException
    */
   public java.sql.Time getTime(int colNr) throws IfsException {
      return stmt.getTime(colNr);
   }

   /**
    * Get double value from the current row in the result set.
    * @param colNr column position in the result set
    * @return the column value
    * @throws IfsException
    */
   public double getDouble(int colNr) throws IfsException {
      return stmt.getDouble(colNr);
   }

   /**
    * Get long value from the current row in the result set.
    * @param colNr column position in the result set
    * @return the column value
    * @throws IfsException
    */
   public long getLong(int colNr) throws IfsException {
      return stmt.getLong(colNr);
   }

   /**
    * Get BigDecimal value from the current row in the result set.
    * @param colNr column position in the result set
    * @return the column value
    * @throws IfsException
    */
   public java.math.BigDecimal getBigDecimal(int colNr) throws IfsException {
      return stmt.getBigDecimal(colNr);
   }

   /**
    * Check if result of last fetched value from the result set was null.
    * @return true if result was null, false otherwise.
    * @throws IfsException
    */
   public boolean resultWasNull() throws IfsException {
      return stmt.resultWasNull();
   }

   /**
    * Get the current row from the result set.
    * @return an FndRecord representing one database row
    * @throws IfsException if SQL error occurs while fetching data
    */
   public FndRecord getRecord() throws IfsException {
      if(plsqlConverter == null) {
         plsqlConverter = new PlsqlConverter(new PlsqlInvocationBufferTypeMap());
         bufferConverter = new PlsqlInvocationProcessor.BufferConverter();
      }

      Buffer row = PlsqlUtil.createNewBuffer();
      try {
         ResultSetMetaData metadata = resultSet.getMetaData();
         int columnCount = metadata.getColumnCount();
         for (int i = 0; i < columnCount; i++) {
            Item item = plsqlConverter.columnToItem(stmt, metadata, i + 1, null);
            row.addItem(item);
         }
      }
      catch (SQLException | IOException e) {
         throw new SystemException(e, "REFCURSOR_GETREC:Failed to get row from REF cursor: &1", e.getMessage());
      }

      return bufferConverter.convertBufferToFndRecord(row);
   }
}
