/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.storage;

import ifs.fnd.base.*;
import ifs.fnd.log.*;
import ifs.fnd.record.*;
import ifs.fnd.entities.fnduser.FndUserCache;
import ifs.fnd.service.IfsProperties;
import ifs.fnd.sf.FndServerContext;
import ifs.fnd.util.SimpleStack;

import java.util.Iterator;

/**
 * <B>Framework internal class:</B> Common ancestor for all handlers.
 * The class publishes methods for activation and deactivation of filters.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public abstract class FndAbstractHandler {

   /**
    * Constant used to tag log messages tracing flow of invoked handler methods.
    */
   public static final Tags FND_FLOW = new Tags("FndFlow");

   private static final boolean SKIP_ACTIVITY_GRANTS = IfsProperties.getSnapshot().getProperty("fnd.skipActivityGrants", false); // Static IFS property

   //The license cache singleton to be notified about activity calls.
   //private static final FndLicenseCache LICENSE_CACHE = FndLicenseCache.getInstance();

   /**
    * Class representing the state of an operation being called on this handler.
    */
   private static class OperationCall {
      private FndFilterMap filterMap;                     // filter map in current context
      private FndFilterMap.State filterMapState;          // state of the filter map stored on entry to the operation
      private FndValidationMap validationMap;             // validation map in current context
      private FndValidationMap.State validationMapState;  // state of the validation map stored on entry to the operation
      private String operationName;                       // name of the current operation
      private Logger log;                                 // current logger for the operation
      private boolean topLevelActivity;                   // true if beforeCall() has set insideActivity flag in context
   }

   /**
    * Stack with the state of currently running operations.
    */
   private final SimpleStack<OperationCall> calls = new SimpleStack<>();

   /**
    * Returns the state of the current operation.
    */
   private OperationCall currentCall() {
      return calls.peek();
   }

   /**
    * Constant used to mark activityName in old version of generated Wrapper classes.
    */
   private static final String NO_ACTIVITY_NAME = String.valueOf((char)0);

   /**
    * The name of activity for activity and service handlers, null for all other tye of handlers.
    */
   private String activityName = NO_ACTIVITY_NAME;

   /**
    * Sets the activity name for this handler.
    * The method is called from the constructor of generated wrapper classes
    * for activity and service handlers.
    */
   protected void setActivityName(String activityName) {
      this.activityName = activityName;
   }

   /**
    * Performs before-call activities.
    * The method checks security and stores the current state of the current filter map in context.
    */
   protected void beforeCall() throws IfsException {
      beforeCall(null);
   }

   /**
    * Performs before-call activities.
    * The method checks security and stores the current state of the current filter map in context.
    */
   protected void beforeCall(String operationName) throws IfsException {
      FndServerContext ctx = FndServerContext.getCurrentServerContext();
      ctx.setEncryptionEnabled(true);
      OperationCall call = new OperationCall();

      call.log = LogMgr.getCallSequenceLogger();
      if(call.log.trace)
         call.log.trace("Entering &1.&2", getClass().getName(), operationName);

      // filters configured for the activity and roles granted to the current user
      Iterator<FndFilterCache.Entry> filters = null;

      if(!SKIP_ACTIVITY_GRANTS) {
         if(this instanceof FndEntityHandler) {
            assert true; // by default there is no security check for entity handlers
         }
         else if (NO_ACTIVITY_NAME.equals(activityName)) {
            // abort because of old generated code
            throw new SystemException(Texts.NOACTIVITYNAME, getClass().getName(), operationName);
         }
         else if(activityName == null) {
            assert true; // no security check for implementation handlers and user-message handlers
         }
         else {
            if("".equals(activityName))
               // handler operation not attached to the security system
               throw new ifs.fnd.base.SecurityException(Texts.NOACTIVITY, getClass().getName(), operationName);
            // check activity security and fetch configured filters
            String directoryId = FndContext.getCurrentContext().getApplicationUser();
            filters = FndUserCache.getFiltersForActivity(directoryId, activityName);
            if(filters == null) {
               throw new ifs.fnd.base.SecurityException(Texts.NOACTIVITYGRANT, directoryId, activityName);
            }

            if(!ctx.isInsideActivity()) {
               ctx.setInsideActivity(true);
               call.topLevelActivity = true;
            }
         }
      }
      else if(activityName != null && !activityName.equals(NO_ACTIVITY_NAME) && !activityName.equals("")) {
         if(!ctx.isInsideActivity()) {
            ctx.setInsideActivity(true);
            call.topLevelActivity = true;
         }
      }

      // update the stack after succesfull security check
      call.filterMap = ctx.getFilterMap();
      call.filterMapState = call.filterMap.mark();
      call.validationMap = ctx.getValidationMap();
      call.validationMapState = call.validationMap.mark();
      call.operationName = operationName;
      calls.push(call);

      // activate filters configured for the activity and current user
      if(filters != null) {
         while(filters.hasNext()) {
            FndFilterCache.Entry entry = (FndFilterCache.Entry) filters.next();
            currentCall().filterMap.addFilter(entry); // activateFilter(entry.getRecordMeta(), entry.getFndFilter());
         }
      }

      if(call.topLevelActivity) {
         if(call.log.trace)
            call.log.trace(FND_FLOW, "Enter activity [&1]", activityName);
         ctx.setActivityOperation(activityName, operationName);
      }
   }

   /**
    * Performs after-call activities.
    * The method restores the state of the current filter map in context.
    */
   protected void afterCall() throws SystemException {
      FndServerContext ctx = FndServerContext.getCurrentServerContext();
      ctx.setEncryptionEnabled(false);
      OperationCall call = calls.pop();
      if(call.log.trace)
         call.log.trace("Exiting &1.&2", getClass().getName(), call.operationName);
      if(call.filterMapState != null)
         call.filterMap.reset(call.filterMapState);
      if(call.validationMapState != null)
         call.validationMap.reset(call.validationMapState);
      if(call.topLevelActivity) {
         ctx.setInsideActivity(false);
         ctx.setActivityOperation(null, null);
         if(call.log.trace) {
            call.log.trace("   Login_SYS.Init_Fnd_Session_ calls = &1(&2)", ctx.getInitFndSessionDbCount(), ctx.getInitFndSessionCount());
         }
      }
   }

   /**
    * Adds a meta-view-to-filter mapping to the current filter map.
    * @param metaView the view to attach a filter to
    * @param filter filter to be attached to the above view
    */
   protected void activateFilter(FndRecordMeta metaView, FndFilter filter) {
      currentCall().filterMap.addFilter(metaView, filter);
   }

   /**
    * Adds a meta-attribute-to-filter mapping to the current filter map.
    * The filter will be used for elements of the specified meta-array/aggregate.
    * @param metaAttr the compound attribute to attach a filter to
    * @param filter filter to be attached to the above attribute
    */
   protected void activateFilter(FndCompoundAttributeMeta metaAttr, FndFilter filter) {
      currentCall().filterMap.addFilter(metaAttr, filter);
   }

   /**
    * Adds a meta-reference-to-filter mapping to the current filter map.
    * The filter will be used to validate references during save operation.
    * @param metaRef the meta-reference to attach a filter to
    * @param filter filter to be attached to the above reference
    */
   protected void activateFilter(FndCompoundReferenceMeta metaRef, FndFilter filter) {
      currentCall().filterMap.addFilter(metaRef, filter);
   }

   /**
    * Adds validation rules to the current validation map.
    * @param <V> a subclass of VndView
    * @param rules validation rules for view of type V
    */
   protected <V extends FndView> void activateValidation(FndValidation<V> rules) {
      currentCall().validationMap.addRules(rules);
   }

   /**
    * Verifies that a current user has been granted access to an activity.
    *
    * @param directoryId
    *           User directory ID
    * @param activityName
    *           The name of activity to check grants for
    * @throws SecurityException
    *            if the user has not been granted assess to the activity
    * @throws IfsException
    *            if the checking procedure fails for some reason
    */
   public static void checkActivityGranted(String directoryId, String activityName) throws IfsException {

      Logger log = LogMgr.getSecurityLogger();
      if(SKIP_ACTIVITY_GRANTS) {
         if (log.warning)
            log.warning("Activity grant security disabled!");
         return;
      }
      boolean ok = FndUserCache.isActivityGranted(directoryId, activityName);
      if(log.trace)
         log.trace("FndAbstractHandler: Checking if activity " + activityName + " is granted to user " + directoryId + ": "+ok);
      if(!ok) {
         throw new ifs.fnd.base.SecurityException(Texts.NOACTIVITYGRANT, directoryId, activityName);
      }
   }

   /**
    * Indicates if checking of activity grants is activated.
    * @return true if the framework will perform checks that clients have been granted access to
    *         called activities and services, false otherwise
    */
   public static boolean isCheckingActivityGrantsOn() {
      return !SKIP_ACTIVITY_GRANTS;
   }
}
