/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.sf.storage;

import ifs.fnd.base.ApplicationException;
import ifs.fnd.base.DatabaseException;
import ifs.fnd.base.FndContext;
import ifs.fnd.base.FndDebug;
import ifs.fnd.base.IfsException;
import ifs.fnd.base.IfsRuntimeException;
import ifs.fnd.base.SystemException;
import ifs.fnd.base.ValidationException;
import ifs.fnd.internal.FndAttributeInternals;
import ifs.fnd.internal.FndRecordInternals;
import ifs.fnd.log.LogMgr;
import ifs.fnd.log.Logger;
import ifs.fnd.record.FndAbstractAggregate;
import ifs.fnd.record.FndAbstractArray;
import ifs.fnd.record.FndAbstractRecord;
import ifs.fnd.record.FndAdvancedQueryView;
import ifs.fnd.record.FndArray;
import ifs.fnd.record.FndAttribute;
import ifs.fnd.record.FndAttributeMeta;
import ifs.fnd.record.FndBaseEntityView;
import ifs.fnd.record.FndBinary;
import ifs.fnd.record.FndCompoundAttribute;
import ifs.fnd.record.FndCompoundAttributeMeta;
import ifs.fnd.record.FndCompoundReference;
import ifs.fnd.record.FndCompoundReferenceMeta;
import ifs.fnd.record.FndEntityState;
import ifs.fnd.record.FndEntityView;
import ifs.fnd.record.FndGenericAggregate;
import ifs.fnd.record.FndImportStorage;
import ifs.fnd.record.FndInputStreamManager;
import ifs.fnd.record.FndLUEntityView;
import ifs.fnd.record.FndOutputStreamManager;
import ifs.fnd.record.FndPersistentView;
import ifs.fnd.record.FndPrepareStorage;
import ifs.fnd.record.FndQueryRecord;
import ifs.fnd.record.FndQueryResultCategory;
import ifs.fnd.record.FndQueryStorage;
import ifs.fnd.record.FndRecordMeta;
import ifs.fnd.record.FndRecordState;
import ifs.fnd.record.FndSaveStorage;
import ifs.fnd.record.FndSort;
import ifs.fnd.record.FndSqlType;
import ifs.fnd.record.FndSqlValue;
import ifs.fnd.record.FndText;
import ifs.fnd.record.IFndCustomFieldProvider;
import ifs.fnd.record.serialization.FndAutoString;
import ifs.fnd.service.IfsProperties;
import ifs.fnd.service.Util;
import ifs.fnd.services.plsqlserver.AttributeString;
import ifs.fnd.services.plsqlserver.service.PlsqlUtil;
import ifs.fnd.sf.FndServerContext;
import ifs.fnd.util.SimpleStack;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.IdentityHashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <B>Framework internal class:</B> Implementation of FndStorage which assumes simple one-to-one mapping between
 * database columns and FndAttributes in an FndPersistentView. Relations between
 * views (mapped to tables) are defined by instances of FndCompoundReference representing
 * groups of FndAttributes (columns).
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class FndSqlStorage implements FndStorage {

   /**
    * Database logger used for debug output.
    * The same instance of Logger is used by FnfSqlStorageUtil and by nested class Context.
    */
   private Logger log;

   /**
    * Utility class used to split code into two files, instantiated in FndSqlStorage constructor.
    */
   private FndSqlStorageUtil util;

   /**
    * Current execution context, created/closed by beforeCall/afterCall
    * in a top level storage operation. The context is accessed from FndSqlStorageUtil.
    */
   Context context;

   /**
    * True if this instance of FndSqlStorage is the creator of the current execution Context.
    */
   private boolean contextCreator;

   /**
    * Array of states for dirty records. Used when saving detail records.
    * The ordering of states in this array is the ordering that will be used
    * when saving detail records.
    */
   private static final FndRecordState[] DIRTY_STATES = { FndRecordState.REMOVED_RECORD, FndRecordState.MODIFIED_RECORD, FndRecordState.NEW_RECORD };

   /**
    * <B>Framework internal class:</B> Class representing execution context of a standard storage operation.
    * The context is created (and destroyed) by the top level procedure.
    * The context is reused during recursive calls to storage operations.
    *
    * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
    */
   public static final class Context {

      /**
       * Map with aliases used in a statement. IdentityHashMap uses reference-equality to compare keys.
       */
      private IdentityHashMap<Object, String> tableAliases;

      /**
       * Application owner for the base PL/SQL system.
       */
      private String appOwner;

      /**
       * Open database connection.
       */
      private FndConnection dbConnection;

      /**
       * Cache with prepared select statements for detail queries.
       */
      private StatementCache stmtCache;

      /**
       * Key used to identify cached statements.
       */
      private SimpleStack<String> stmtCacheKey; //NOPMD - the field is used outside Context class

      /**
       * Current server context.
       */
      private FndServerContext serverContext;

      /**
       * True inside a call to get operation (top level call, or recursive call from save).
       * queryDetailView called from get operation ignores query conditions.
       */
      private boolean isGetOperation;

      /**
       * The flag is set to true by populate() before each direct call to queryDetailView().
       * The flag is cleared after the call.
       * In populate-mode queryDetailView() works in the follwing way:
       * <pre>
       *  (1) it acts like get() and ignores query conditions
       *  (2) it appends parent key condition and/or primary key condition (null keys are ignored)
       *  (3) the statement cache is not used, because different detail records with the same
       *      path (from the top level master record) may have different include-flags thus
       *      resulting in different select statements.
       * </pre>
       * The flag is false during the first (and only) call to get(), which calls
       * queryDetailView() in standard (non-populate) mode.
       */
      private boolean populateMode;

      /**
       * True inside a call to populate() in a "dirty" part of master-detail structure.
       * Note that a dirty-part (of master-detail structure) cannot contain a non-dirty part.
       * In dirty-mode all filters are applied.
       * In non-dirty-mode, which is the default mode for get() and queryDetailView(), filters
       * on ref-by-value aggregates are skipped. It is safe to do so, because in such a case all
       * master-detail connections are fetched from (or checked against) the database.
       * This flag is set (and cleared) by populate() if the passed view is in state NEW_RECORD.
       * Also, this flag is set (and cleared) for each dirty compound attribute
       * in populateExcludedCompoundAttributes().
       */
      private boolean dirtyPopulateMode;

      /**
       * Flag controlling the import-mode during save() operation.
       * The flag is set to true by top-level import() operation before calling save().
       * The flag is never cleared, because the storage context disappears after
       * the call to a top-level operation.
       * In import-mode save() works in the following way:
       * <pre>
       *  (1) only importable details are accessed (dependent non-entity details
       *      and elements of arrays/aggregates that implement FndImportStorage)
       *  (2) state of top level record and all importable details is set to NEW
       *  (3) entity state set on an entity record is used to update database
       *  (4) before validation UPDATE_ALLOWED flag is cleared (ok to modify simple attributes,
       *      ok to insert elements of arrays and aggregates)
       * </pre>
       */
      private boolean importMode;

      /**
       * The compound reference being parent-key in the parent for a detail view passed to recursive query.
       */
      private FndCompoundReference parentKeyInParent;

      /**
       * The compound meta-reference being parent-key in a detail view passed to recursive query.
       */
      private FndCompoundReferenceMeta metaParentKeyInElement;

      /**
       * The compound attribute being the container for a detail view passed to recursive query.
       */
      private FndCompoundAttribute conditionContainer;

      /**
       * Recursion level (1, 2 ...).
       */
      private int recursionLevel;

      /**
       * The main record of a SQL statement. It is used to build the path of record
       * and attribute names to a detail record in a master-detail tree.
       */
      private FndAbstractRecord stmtRoot;

      /**
       * Default fetch size used when fetching results from database.
       */
      final int defaultFetchSize;

      /**
       * Maximum fetch size.
       */
      final int maxFetchSize;

      /**
       * Context is created by beforeCall in a top level storage operation.
       */
      private Context(FndServerContext serverContext) throws IfsException {
         this.serverContext = serverContext;
         tableAliases = new IdentityHashMap<>();
         stmtCache = new StatementCache();
         stmtCacheKey = new SimpleStack<>();
         recursionLevel = 0;
         appOwner = FndPlsqlConfig.getApplicationOwner();
         IfsProperties p = IfsProperties.getSnapshot(); // Dynamic IFS property
         defaultFetchSize = p.getDefaultFetchSize();
         maxFetchSize = p.getMaxFetchSize();
      }

      /**
       * Context is closed by afterCall in a top level storage operation.
       */
      private void close() {
         stmtCache.close();

         // Return (close) database connection
         if(dbConnection != null) {
            try {
               returnConnection(dbConnection);
            }
            catch (Exception any) { //TODO:Why catching Exception?
               Logger tmpLog = LogMgr.getDatabaseLogger();
               if (tmpLog.debug) {
                  tmpLog.debug("Exception while returning database connection ignored:\n &1", any.toString());
               }
            }
         }
      }

      String appOwner() {
         return appOwner;
      }

      Map<Object, String> tableAliases() {
         return tableAliases;
      }

      FndFilterMap getFilterMap() {
         return serverContext.getFilterMap();
      }

      FndAbstractRecord stmtRoot() {
         return stmtRoot;
      }

      FndServerContext getFndServerContext() {
         return serverContext;
      }

      /**
       * Gets a database connection for an entity.
       * @param record the entity to get connection for.
       * @return  A database connection for the entity.
       * @throws  IfsException   throw to indicate that a connection can't be obtained.
       */
      FndConnection getConnection(FndBaseEntityView record) throws IfsException {
         if (dbConnection == null) {
            dbConnection = serverContext.getConnectionManager().getPlsqlConnection();
         }
         return dbConnection;
      }

      /**
       * Return a connection with this method. This class calls this method to
       * indicate that it is done with the connection.
       * @param c the connection to return.
       * @throws IfsException  throw to indicate that the connection couldn't
       * be returned properly.
       */
      private void returnConnection(FndConnection c) throws IfsException {
         if (c != null) {
            serverContext.getConnectionManager().returnConnection(c);
         }
      }
   }

   Logger getLogger() {
      return log;
   }

   /**
    * The method is called in the beginning of every public storage operation.
    */
   private void beforeCall() throws IfsException {
      if (context == null) {
         FndServerContext srvctx = FndServerContext.getCurrentServerContext();
         context = srvctx.getStorageContext();
         if (context == null) {
            contextCreator = true;
            context = new Context(srvctx);
            srvctx.setStorageContext(context);
         }
      }
      context.recursionLevel++;
   }

   /**
    * The method is called at the end every public storage operation.
    */
   protected void afterCall() {
      // Do nothing if beforeCall() failed to create storage context
      if(context == null) {
         return;
      }

      context.tableAliases.clear(); // alias table is needed only at current recursion level
      context.stmtRoot = null;
      context.recursionLevel--;

      if (contextCreator) {
         contextCreator = false;
         Context c = context;
         context = null; // Context will be recreated by next top level operation
         c.serverContext.setStorageContext(null);
         c.close();
      }
   }

   /**
    * Default constructor.
    * Reference to the new instance is passed to FndSqlStorageUtil, which
    * gives access to storage Context.
    */
   public FndSqlStorage() {
      log = LogMgr.getDatabaseLogger();
      util = new FndSqlStorageUtil(this, log);
      if(log.debug) {
         FndSqlStorage me = this;
         log.debug("New instance of FndSqlStorage created: &1", me);
      }
   }

   /**
    * Create a new statement.
    * @param c database connection
    * @param stmtRoot the main record for the statement, which will be stored in Context and used
    *                 for generation of table aliases
    */
   FndStatement createStatement(FndConnection c, FndAbstractRecord stmtRoot) throws SystemException {
      context.tableAliases.clear();
      context.stmtRoot = stmtRoot;
      return c.createStatement();
   }

   /**
    * Populates an entity with data.
    * The method retrieves from database included parts of a view structure.
    * Also, it verifies that parent keys corresponding to non-dirty compound attributes
    * and to excluded compound attributes match the database.
    * Filters are used for dirty compound attributes.
    * No filters are used for non-dirty compound attributes.
    * <p>
    * Note! As a side-effect of this operation, the input record
    * may be modified.
    * <p>
    * @param view record to populate
    * @return result record with some parts fetched form database and other parts moved from
    *         the specified record
    * @throws IfsException if there is a problem populating the entity
    */
   @Override
   public FndBaseEntityView populate(FndBaseEntityView view) throws IfsException {
      try {
         beforeCall();

         if (log.trace) {
            log.trace("view=&1", view.getName());
         }

         FndConnection c = context.getConnection(view);

         return (FndBaseEntityView) populate(c, view, null);
      }
      finally {
         afterCall();
      }
   }

   /**
    * Populates an entity or a detail record with data.
    * This method is called from public populate() for the top level view (an entity)
    * and recursively for detail views from populateExcludedCompoundAttributes().
    *
    * @param c database connection
    * @param conditionView record to populate
    * @param condContainer array/aggregate containing the record above, null if the record is a top level entity
    * @throws IfsException if there is a problem populating the view
    */
   private FndPersistentView populate(
      FndConnection c,
      FndPersistentView conditionView,
      FndCompoundAttribute condContainer)
   throws IfsException {

      if (log.debug) {
         log.debug("connection=&1 view=&2 condContainer=&3",
                   String.valueOf(c),
                   conditionView.getName(),
                   condContainer==null ? "null" : condContainer.getName());
      }

      boolean clearDirtyPopulateMode = false;

      try {
         FndPersistentView resultView;

         if(conditionView.getState() == FndRecordState.NEW_RECORD) {

            // enter dirty-mode
            if(context.dirtyPopulateMode == false) {
               context.dirtyPopulateMode = true;
               clearDirtyPopulateMode = true;
               if (log.debug) {
                  log.debug("   dirtyPopulateMode = true (before NEW record &1)", conditionView.getName());
               }
            }

            resultView = (FndPersistentView) conditionView.newInstance();
            copySimpleAttributes(conditionView, resultView);
            FndRecordInternals.setIdentity(resultView, FndRecordInternals.getIdentity(conditionView));
         }
         else {
            resultView = populateRecord(c, conditionView, condContainer);
         }

         populateExcludedCompoundAttributes(c, conditionView, resultView);

         return resultView;
      }
      finally {
         if(clearDirtyPopulateMode) {
            context.dirtyPopulateMode = false;
            if (log.debug) {
               log.debug("   dirtyPopulateMode = false (after NEW record &1)", conditionView.getName());
            }
         }
      }
   }

   /**
    * Populates a record with data.
    * This method is called on a record found somewhere in the master-detail structure
    * passed to public populate().
    * It calls get() or queryDetailView(), depending on passed container attribute.
    * Note that statement cache for detail queries cannot be used in populate-mode.
    * Different detail records with the same path (from the top level master record) may
    * have different include-flags thus resulting in different select statements.
    * Depending on the flag dirtyPopulateMode queryDetailView() will apply filters or not.
    *
    * @param c database connection
    * @param conditionView record to populate
    * @param condContainer array/aggregate containing the record above, null if the record is a top level entity
    * @throws DatabaseException if the record instance could not be found in the database
    * @throws IfsException if there is a problem populating the view
    */
   private FndPersistentView populateRecord(
      FndConnection c,
      FndPersistentView conditionView,
      FndCompoundAttribute condContainer)
   throws IfsException {

      if (log.debug) {
         log.debug("connection=&1 conditionView=&2", String.valueOf(c), conditionView.getName());
      }

      util.includeParentKeys(conditionView);

      FndPersistentView resultView = null;

      if(condContainer == null) { // call get() for top level entity
         resultView = get((FndBaseEntityView) conditionView, c);
      }
      else { // call queryDetailView() for a detail view

         boolean clearPopulateMode = false;

         try {

            // enter populate-mode
            if(context.populateMode == false) {
               context.populateMode = true;
               clearPopulateMode = true;
               if (log.debug) {
                  log.debug("   populateMode = true (before call to queryDetailView)");
               }
            }

            // verify that the parent key in detail matches the parent key in master.
            util.checkParentKeyInDetail(condContainer, conditionView);

            FndQueryStorage detailStorage = condContainer instanceof FndQueryStorage ? (FndQueryStorage) condContainer : null;
            FndCompoundAttributeMeta metaContainer = condContainer.getCompoundMeta();
            FndCompoundReferenceMeta metaParentKeyInElement = metaContainer.getParentKeyInElement();

            if (detailStorage != null && conditionView instanceof FndBaseEntityView) {
               context.conditionContainer     = condContainer;
               context.metaParentKeyInElement = metaParentKeyInElement;
               context.parentKeyInParent      = condContainer.getParentKeyInParent();

               FndAbstractArray outArr = detailStorage.query(new FndQueryRecord(conditionView));

               context.conditionContainer     = null;
               context.metaParentKeyInElement = null;
               context.parentKeyInParent      = null;

               if (outArr.size() > 0) {
                  resultView = (FndPersistentView) FndAttributeInternals.internalGet(outArr, 0);
               }
            }
            else {
               FndArray resultArr = new FndArray();
               Container resultContainer = new Container(resultArr);
               queryDetailView(c, conditionView, condContainer, metaParentKeyInElement, condContainer.getParentKeyInParent(), resultContainer);
               if (resultContainer.size() > 0) {
                  resultView = resultContainer.get(0);
               }
            }

            if (resultView == null) {
               throw new SystemException(Texts.FNDPOPULATENOTFOUND, conditionView.getName(), util.getPrimaryKeyImage(conditionView));
            }
         }
         finally {
            if(clearPopulateMode) {
               context.populateMode = false;
               if (log.debug) {
                  log.debug("   populateMode = false (after call to queryDetailView)");
               }
            }
         }
      }

      util.checkParentKeys(conditionView, resultView);

      FndRecordInternals.setIdentity(resultView, FndRecordInternals.getIdentity(conditionView));
      return resultView;
   }

   /**
    * Populates excluded compound attributes with data.
    * The method moves existent excluded compound attributes from condition view to result view
    * and then calls populate() on every moved detail record.
    * @param c database connection
    * @param conditionView condition view
    * @param resultView result view to populate
    * @throws IfsException if there is a problem with populating the view
    */
   private void populateExcludedCompoundAttributes(
      FndConnection c,
      FndPersistentView conditionView,
      FndPersistentView resultView)
   throws IfsException {

      if (log.debug) {
         log.debug("conditionView=&1", conditionView.getName());
      }

      FndAttribute.Iterator condDetails = conditionView.details();
      FndAttribute.Iterator resultDetails = resultView.details();
      while (condDetails.hasNext() && resultDetails.hasNext()) {
         FndCompoundAttribute condAttr   = (FndCompoundAttribute) condDetails.next();
         FndCompoundAttribute resultAttr = (FndCompoundAttribute) resultDetails.next();

         if (!condAttr.exist() || !condAttr.isSet() || !condAttr.isExcluded()) {
            continue;
         }

         boolean clearDirtyPopulateMode = false;

         try {

            // enter dirty-mode if the condition attribute is dirty
            if(context.dirtyPopulateMode == false && condAttr.isDirty()) {
               context.dirtyPopulateMode = true;
               clearDirtyPopulateMode = true;
               if (log.debug) {
                  log.debug("   dirtyPopulateMode = true (before dirty attribute &1)", condAttr.getMeta().getFullName());
               }
            }

            if (condAttr.isVector()) {
               FndAbstractArray condArr = (FndAbstractArray) condAttr;
               FndAbstractArray resultArr = (FndAbstractArray) resultAttr;
               int arrSize = condArr.size();
               for (int i = 0; i < arrSize; i++) {
                  FndPersistentView condDetail = (FndPersistentView) FndAttributeInternals.internalGet(condArr, i);
                  FndPersistentView resultDetail = populate(c, condDetail, resultAttr);
                  FndAttributeInternals.internalAdd(resultArr, resultDetail);
               }
            }
            else {
               FndAbstractAggregate condAggr = (FndAbstractAggregate) condAttr;
               FndAbstractAggregate resultAggr = (FndAbstractAggregate) resultAttr;
               FndPersistentView condDetail = (FndPersistentView) FndAttributeInternals.internalGetRecord(condAggr);
               FndPersistentView resultDetail = populate(c, condDetail, resultAttr);
               FndAttributeInternals.internalSetRecord(resultAggr, resultDetail);
            }
         }
         finally {
            if(clearDirtyPopulateMode) {
               context.dirtyPopulateMode = false;
               if (log.debug) {
                  log.debug("   dirtyPopulateMode = false (after dirty attribute &1)", condAttr.getMeta().getFullName());
               }
            }
         }
      }
   }

   /**
    * Copy contents of simple attributes from one view to another.
    * Both views must be instances of the same view type.
    */
   private void copySimpleAttributes(FndPersistentView fromView, FndPersistentView toView) {
      int count = Math.min(fromView.getAttributeCount(), toView.getAttributeCount());
      for(int i=0; i<count; i++) {
         FndAttribute from = fromView.getAttribute(i);
         FndAttribute to = toView.getAttribute(i);
         if(from.isSet()) {
            FndAttributeInternals.internalSetValue(to, FndAttributeInternals.internalGetValue(from));
         }
         if(!from.exist()) {
            to.setNonExistent();
         }
         to.include(from.getResultCategory());
      }
   }

   /**
    * Standard entity import method stores in the database the current state of an entity.
    * @param view record to import
    * @throws IfsException if there is a problem importing the entity
    */
   @Override
   public void importEntity(FndBaseEntityView view) throws IfsException {
      try {
         beforeCall();

         if (log.trace) {
            log.trace("view=&1, contextCreator=&2", view.getName(), String.valueOf(contextCreator));
         }

         if (contextCreator) {
            context.importMode = true;

            // change state of importable records to NEW, clear OBJID/OBJVERSION, clear UPDATE_ALLOWED flag...
            prepareForImport(view);

            // validate view before importing
            view.validate(false);
            view.abortIfInvalid();
         }

         FndEntityState state = view.entityState();
         save(null, view, util.isIndependentEntity(view), state == null ? null : state.getStateValue());
      }
      finally {
         afterCall();
      }
   }

   /**
    * Prepares a record and its importable details for import.
    * Importable details are non-entity details and arrays/aggregates that implement FndImportStorage.
    * The method changes the record state to NEW, clears OBJID and OBJVERSION,
    * clears UPDATE_ALLOWED flags... etc.
    */
   private void prepareForImport(FndPersistentView view) throws SystemException {

      // set state on master record only (recursively = false)
      view.setState(FndRecordState.NEW_RECORD, false);

      // mark the record with IMPORTING flag to skip some validation steps
      FndRecordInternals.setImporting(view, true);

      // clear OBJID, OBJVERSION
      if(view instanceof FndLUEntityView) {
         FndLUEntityView entity = (FndLUEntityView) view;
         entity.objId.setNonExistent();
         entity.objVersion.setNonExistent();
      }
      else if(view instanceof FndEntityView) {
         FndEntityView entity = (FndEntityView) view;
         entity.objVersion.setNonExistent();
      }

      // prepare simple attributes
      int count = view.getAttributeCount();
      for (int i = 0; i < count; i++) {
         FndAttribute attr = view.getAttribute(i);
         if (attr.exist() && !attr.isCompound() && attr.isSet() && attr.getMeta().isPersistent()) {

            // Force validation. FndAttribute.validate() validates only dirty attributes.
            attr.setDirty(true);

            // Allow insert of simple attributes
            attr.setUpdateAllowed(true);
         }
      }

      // prepare persistent (dependent and independent) compound attributes
      FndCompoundAttribute.Iterator details = view.persistentDetails();
      while (details.hasNext()) {
         FndCompoundAttribute attr = details.next();
         if (!attr.exist()) {
            continue;
         }

         // Force validation. FndAttribute.validate() validates only dirty attributes.
         attr.setDirty(true);

         // Allow insert of detail records
         attr.setUpdateAllowed(true);

         boolean importAllowed = true;
         if (attr.isVector()) {
            FndAbstractArray arr = (FndAbstractArray) attr;
            int arrSize = arr.size();
            for (int j = 0; j < arrSize; j++) {
               FndPersistentView detail = (FndPersistentView) FndAttributeInternals.internalGet(arr, j);
               if(importAllowed) //ToDo: Temporary solution: Allow importEntity() on all documents
                  prepareForImport(detail);
               else if (detail.getState() != FndRecordState.QUERY_RECORD) {
                  importNotAllowed(detail);
               }
            }
         }
         else if (!attr.isNull()) {
            FndAbstractAggregate aggr = (FndAbstractAggregate) attr;
            FndPersistentView detail = (FndPersistentView) FndAttributeInternals.internalGetRecord(aggr);
            if (detail != null) {
               if(importAllowed) { //ToDo: Temporary solution: Allow importEntity() on all documents
                  prepareForImport(detail);
               }
               else if (detail.getState() != FndRecordState.QUERY_RECORD) {
                  importNotAllowed(detail);
               }
            }
         }
      }
   }

   private void importNotAllowed(FndPersistentView detail) throws SystemException {
      throw new SystemException(Texts.IMPORTNOTALLOWED, detail.getName());
   }

   /**
    * Standard entity save method will do different things depending on record state.
    * REMOVED_RECORD: Remove existing record from database
    * NEW_RECORD: Store a new instance in database
    * MODIFIED_RECORD: Modify an existing record
    * QUERIED_RECORD: Save dirty independent details
    * @param view record to save
    * @throws IfsException if there is a problem saving the entity
    */
   @Override
   public void save(FndBaseEntityView view) throws IfsException {
      save(view, null);
   }

   /**
    * Standard entity save method will do different things depending on record state.
    * REMOVED_RECORD: Remove existing record from database
    * NEW_RECORD: Store a new instance in database
    * MODIFIED_RECORD: Modify an existing record
    * QUERIED_RECORD: Save dirty independent details
    * @param view record to save
    * @param targetState not used (entity state of LU entities is modified by PLSQL code)
    * @throws IfsException if there is a problem saving the entity
    */
   @Override
   public void save(FndBaseEntityView view, FndEntityState.Enum targetState) throws IfsException {
      try {
         beforeCall();

         if (log.trace) {
            log.trace("view=&1, contextCreator=&2", view.getName(), String.valueOf(contextCreator));
         }

         //Validate view before saving
         if (contextCreator) {
            markRecordsToRemove(view, false);
            view.validate(false);
            view.abortIfInvalid();
         }

         save(null, view, util.isIndependentEntity(view), null);
      }
      finally {
         afterCall();
      }
   }

   /**
    * Update a set of entities with the same attribute values.
    * The method performs a query based on the condition record and then
    * updates every entity in the result set with the attribute values defined in the value record.
    * <p>
    * The method will do different things depending on state of the value record (and optionally its details).
    * <pre>
    *    REMOVED_RECORD:
    *       Remove entities from database.
    *
    *    NEW_RECORD, MODIFIED_RECORD:
    *       Modify existing entities by setting the values of persistent,
    *       dirty attributes specified in the value record.
    *
    *    QUERY_RECORD:
    *       Only OBJVERSION will be updated.
    * </pre>
    * The type of the condition record must match the type of the value record.
    * Also, the structure of the condition record must match the structure of the value record,
    * which means that if a value record contains a detail record then the condition record must contain
    * the corresponding (possibly empty) detail record of the same type.
    * In case of arrays in value-record only the first element is important; it is used to
    * update/remove all corresponding element records returned by the query.
    * <p>
    * Include query result flags on condition record are ignored, all attributes necessary to
    * properly perform the operation (OBJID, OBJVERSION, primary and parent keys) are included automatically.
    *
    * @param value record defining attribute values to set
    * @param condition record with query condition defining the entity set to be updated
    * @param targetState not used (entity state of LU entities is modified by PLSQL code)
    * @throws IfsException if the operation fails for any reason
    */
   @Override
   public void batchSave(FndBaseEntityView value, FndBaseEntityView condition, FndEntityState.Enum targetState) throws IfsException {
      try {
         beforeCall();
         if (log.trace) {
            log.trace("value=&1 condition=&2 targetState=&3", value.getName(), condition.getName(), String.valueOf(targetState));
         }
         FndSqlBatchProcessor batch = new FndSqlBatchProcessor(this, util, log);
         batch.batchSave(value, condition, null);
      }
      finally {
         afterCall();
      }
   }

   /**
    * Set state to REMOVED_RECORD on records that will be removed by save() operation.
    * The method recursively visits the whole structure of records. When a record marked as
    * REMOVED is found then all its cascade-delete details are also marked as REMOVED.
    *
    * @param view a view in a structure of records passed to save() operation
    * @param setRemoved true if the state of the specified record should be set to REMOVED, false otherwise
    */
   void markRecordsToRemove(FndPersistentView view, boolean setRemoved) {
      FndRecordState state = view.getState();

      if(setRemoved && state != FndRecordState.REMOVED_RECORD) {
         view.setState(FndRecordState.REMOVED_RECORD, false);
         state = view.getState();
         if (log.debug) {
            log.debug("Changing state to REMOVED_RECORD on &1", view.getName());
         }
      }

      // visit all persistent (dependent and independent) compound attributes
      FndCompoundAttribute.Iterator details = view.persistentDetails();
      while (details.hasNext()) {
         FndCompoundAttribute attr = details.next();
         if (!attr.exist() || !attr.isDirty()) {
            continue;
         }

         boolean setRemovedOnDetails = attr.cascadeDelete() && state == FndRecordState.REMOVED_RECORD;

         if (attr.isVector()) {
            FndAbstractArray arr = (FndAbstractArray) attr;
            int arrSize = arr.size();
            for (int j = 0; j < arrSize; j++) {
               FndPersistentView detail = (FndPersistentView) FndAttributeInternals.internalGet(arr, j);
               markRecordsToRemove(detail, setRemovedOnDetails);
            }
         }
         else if (!attr.isNull()) {
            FndAbstractAggregate aggr = (FndAbstractAggregate) attr;
            FndPersistentView detail = (FndPersistentView) FndAttributeInternals.internalGetRecord(aggr);
            if (detail != null) {
               markRecordsToRemove(detail, setRemovedOnDetails);
            }
         }
      }
   }

   /**
    * Standard method to call for getting default values (aka "prepare").
    * If the argument view is based on a Logical Unit, the prepare method in that
    * LU is called.
    * Views must be in state NEW_RECORD and have the "defaulting" value set.
    * @param view view to get default values for
    * @throws IfsException
    */
   @Override
   public void getDefaults(FndBaseEntityView view) throws IfsException {
      if (view instanceof FndLUEntityView) {
         try {
            beforeCall();

            if (log.trace) {
               log.trace("view=&1, contextCreator=&2", view.getName(), String.valueOf(contextCreator));
            }

            FndConnection c = context.getConnection(view);

            if(!FndStorageInstallationCache.isTableInstalled(view, c)) {
               throw new SystemException(Texts.PREPARENOTINST, view.getName());
            }

            prepare(c, view, util.isIndependentEntity(view));
         }
         finally {
            afterCall();
         }
      }
   }

   /**
    * Save a persistent view (non-LU or LU-view) using an already opened connection.
    * This operation delegates work to different subroutines depending on
    * record state (REMOVED_RECORD, NEW_RECORD, MODIFIED_RECORD) and record type
    * (FndEntityView, FndLUEntityView).
    * @param c database connection, ignored if the specified view is an FndBaseEntityView
    * @param view record to save
    * @param independent true if the specified view is an independent entity to be saved,
    *                    false otherwise. See FndSqlStorageUtil.isIndependentEntity() comments
    *                    for details.
    * @param targetState not used (entity state of LU entities is modified by PLSQL code)
    */
   void save(FndConnection c, FndPersistentView view, boolean independent, FndEntityState.Enum targetState) throws IfsException {

      if (log.debug) {
         log.debug("connection=&1 view=&2 independent=&3", String.valueOf(c), view.getName(), String.valueOf(independent));
      }

      if(view instanceof FndBaseEntityView) {
         c = context.getConnection((FndBaseEntityView)view);
      }

      FndRecordState state = view.getState();
      FndBaseEntityView entity = view instanceof FndBaseEntityView ? (FndBaseEntityView)view : null;
      FndLUEntityView luView = view instanceof FndLUEntityView ? (FndLUEntityView) view : null;

      // get primary key from the parent to dependent detail or to independent-new detail
      // (if the container attribute is existent and dirty)
      FndCompoundAttribute container = view.getContainer();
      boolean refToRef = util.isReferenceToReferenceRelationship(container) && !(container instanceof FndGenericAggregate);
      if (container != null && container.exist() && container.isDirty() && !refToRef) {
         if (!independent || (state == FndRecordState.NEW_RECORD)) {
            util.copyParentKeyFromParent(view);
         }
      }

      // save independent details
      List detailsToRemove = null;
      if(context.importMode) {
         importCompoundAttributes(c, view, true);
      }
      else {
         detailsToRemove = saveCompoundAttributes(c, view, true);
      }

      // save the record itself
      boolean savedInDb = false;
      if (view.isPersistent()) {
         assert(luView != null);
         if (state == FndRecordState.REMOVED_RECORD) {
            checkReadOnlyView(view);
            // Will also remove compound attributes
            removeLUView(c, luView);

            // remove independent details, if any
            if(detailsToRemove != null) {
               removeIndependentDetails(c, detailsToRemove);
            }

            view.clear();
            savedInDb = true;
         }
         else if (state == FndRecordState.NEW_RECORD) {
            checkReadOnlyView(view);
            if(view instanceof FndLUEntityView && ((FndLUEntityView) view).isCustomLU()){
               plsqlNewCustom(c, luView, "DO");
            }
            else{
               plsqlNew(c, luView, "DO");
            }
            savedInDb = true;
            resetUpdateAllowedFlags(view);
         }
         else if (state == FndRecordState.MODIFIED_RECORD && view.isDirty()) {
            checkReadOnlyView(view);
            //Modify only if record is MODIFIED and dirty
            if(luView.isCustomLU()){
               plsqlModifyCustom(c, luView);
            }
            else{
               plsqlModify(c, luView);
            }
            savedInDb = true;
         }
      }

      clearNotReferredIndependentDetails(view);

      // copy parent key to the parent, if any (nullify if view has been removed)
      if (independent && !refToRef && savedInDb) {
         util.copyParentKeyToParent(view);
      }

      // save dependent details of a persistent or non-persistent record (ignored if view has been removed)
      if(context.importMode) {
         importCompoundAttributes(c, view, false);
      }
      else {
         saveCompoundAttributes(c, view, false);
      }

      view.setState(FndRecordState.QUERY_RECORD);
      FndRecordInternals.setImporting(view, false); // clear IMPORTING flag if save was called by importEntity
   }

   /**
    * Checks that read-only view is not saved in database.
    * @param view a view that is supposed to be saved in the database
    */
   private void checkReadOnlyView(FndPersistentView view) throws SystemException {
      if (view.getMeta().isReadOnly() || view instanceof FndAdvancedQueryView) {
         throw new SystemException(Texts.SAVEREADONLY, view.getName());
      }
   }

   /**
    * Set UPDATE_ALLOWED flag for simple attributes to the value defined in meta-data.
    * In some situations UPDATE_ALLOWED flag sent from the client may not correspond
    * to the meta-data definition, see for example FndSessionBean.afterActivity().
    * In such cases the flag should be re-set to its correct value.
    * Note that the same flag may be modified by FndEntityHandler by applying entity
    * state rules after the save operation is completed.
    */
   private void resetUpdateAllowedFlags(FndPersistentView view) {
      int count = view.getAttributeCount();
      for (int i = 0; i < count; i++) {
         FndAttribute attr = view.getAttribute(i);
         FndAttributeMeta meta = attr.getMeta();
         if (attr.exist() && !attr.isCompound() && meta.isPersistent()) {
            if(attr.isUpdateAllowed() != meta.isUpdateAllowed()) {
               //System.out.println("   Setting UPDATE_ALLOWED="+ meta.isUpdateAllowed() +" on "+meta.getFullName());
               attr.setUpdateAllowed(meta.isUpdateAllowed());
            }
         }
      }
   }

   private void prepare(FndConnection c, FndPersistentView view, boolean independent) throws IfsException {
      assert(view instanceof FndLUEntityView);
      if (log.debug) {
         log.debug("connection=&1 view=&2 independent=&3", c.toString(), view.getName(), String.valueOf(independent));
      }

      FndRecordState state = view.getState();
      FndLUEntityView luView = (FndLUEntityView) view;

      FndCompoundAttribute container = view.getContainer();
      boolean refToRef = util.isReferenceToReferenceRelationship(container);
      if (container != null && container.exist() && container.isDirty() && !refToRef) {
         if (!independent || (state == FndRecordState.NEW_RECORD)) {
            util.copyParentKeyFromParent(view);
         }
      }

      //prepare independent details
      prepareCompoundAttributes(c, view, true);

      if (view.isDefaulting() && state == FndRecordState.NEW_RECORD && view.isPersistent()) {
         plsqlNew(c, luView, "PREPARE");

         //At this point all set simple-attributes should be marked dirty
         int attrCount = luView.getAttributeCount();
         for(int i=0; i<attrCount; i++) {
            FndAttribute attr = luView.getAttribute(i);
            if (!attr.isCompound() && attr.isSet()) {
               attr.setDirty();
            }
         }
      }

      // copy parent key to the parent
      if (independent && !refToRef) {
         util.copyParentKeyToParent(view);
      }

      //prepare dependent details of a persistent or non-persistent record
      prepareCompoundAttributes(c, view, false);

      //Defaulting flag should no longer be set
      view.defaulting(false);
   }

   private void prepareCompoundAttributes(FndConnection c, FndPersistentView view, boolean independent) throws IfsException {
      FndCompoundAttribute.Iterator details = independent ? view.persistentIndependentDetails() : view.persistentDependentDetails();
      while (details.hasNext()) {
         FndCompoundAttribute attr = details.next();

         // check if array/aggregate is installed
         FndRecordMeta metaElem = attr.getCompoundMeta().getElement();
         if(metaElem != null && !FndStorageInstallationCache.isTableInstalled(metaElem, c)) {
            util.markNotInstalledCompoundAttribute(attr, metaElem, "prepare");
            if (attr instanceof FndAbstractAggregate) {
               attr.setNull(); // remove element record created by markNotInstalledCompoundAttribute()
               attr.setDirty(false);
            }
            continue;
         }

         if (!attr.exist()) {
            continue;
         }

         // call prepare for every element in array/aggregate
         FndPrepareStorage detailStorage = attr instanceof FndPrepareStorage ? (FndPrepareStorage) attr : null;

         if (attr.isVector()) {
            FndAbstractArray arr = (FndAbstractArray) attr;
            int arrSize = arr.size();
            for (int i = 0; i < arrSize; i++) {
               FndPersistentView detail = (FndPersistentView) FndAttributeInternals.internalGet(arr, i);
               if (detail instanceof FndLUEntityView) {
                  if(!FndStorageInstallationCache.isTableInstalled(detail, c)) {
                     util.markNotInstalledCompoundAttribute(arr, detail, "prepare");
                  }
                  else {
                     if (detailStorage != null)
                        detailStorage.getDefaults((FndLUEntityView) detail);
                     else
                        prepare(c, detail, independent);
                  }
               }
            }
         }
         else {
            FndAbstractAggregate aggr = (FndAbstractAggregate) attr;
            FndPersistentView detail = (FndPersistentView) FndAttributeInternals.internalGetRecord(aggr);
            if (detail instanceof FndLUEntityView) {
               if(!FndStorageInstallationCache.isTableInstalled(detail, c)) {
                  util.markNotInstalledCompoundAttribute(aggr, detail, "prepare");
               }
               else {
                  if (detailStorage != null)
                     detailStorage.getDefaults((FndLUEntityView) detail);
                  else
                     prepare(c, detail, independent);
               }
            }
         }
      }
   }

   /**
    * Clear/remove referents that are no longer referred to by parent-key-in-parent reference.
    */
   private void clearNotReferredIndependentDetails(FndPersistentView view) throws IfsException {
      FndCompoundAttribute.Iterator details = view.persistentIndependentDetails();

      while (details.hasNext()) {
         FndCompoundAttribute attr = details.next();
         if (!attr.exist()) {
            continue;
         }

         if (attr.isVector()) {
            FndAbstractArray arr = (FndAbstractArray) attr;
            int arrSize = arr.size();
            for (int j = 0; j < arrSize;) {
               FndPersistentView detail = (FndPersistentView) FndAttributeInternals.internalGet(arr, j);

               switch (util.copyParentKeyFromParent(detail)) {
                  case FndSqlStorageUtil.DEST_MODIFIED :
                  case FndSqlStorageUtil.DEST_MODIFIED_TO_NULL :
                     // Remove the element that does not match the parent key
                     FndAttributeInternals.internalRemove(arr, j);
                     arrSize = arr.size();
                     break;

                  case FndSqlStorageUtil.DEST_UNMODIFIED :
                     j++;
                     break;

                  default :
               }
            }
         }
         else if(!attr.isNull()) {
            FndAbstractAggregate aggr = (FndAbstractAggregate) attr;
            FndPersistentView detail = (FndPersistentView) FndAttributeInternals.internalGetRecord(aggr);
            if (detail == null) {
               continue;
            }

            switch (util.copyParentKeyFromParent(detail)) {
               case FndSqlStorageUtil.DEST_MODIFIED :
               case FndSqlStorageUtil.DEST_MODIFIED_TO_NULL :
                  aggr.setNonExistent(); // same behaviour as in query/get
                  break;

               case FndSqlStorageUtil.DEST_UNMODIFIED :
                  break;
            }
         }
      }
   }

   /**
    * Save compound attributes (arrays/aggregates) for a persistent view.
    * This method is common for both LU and non-LU views.
    * If the master view is in state REMOVED_RECORD then the method returns a list of independent
    * details that are in state REMOVED_RECORD (but not marked for cascade delete),
    * otherwise the returned list is empty.
    * @param c database connection
    * @param view persistent view
    * @param independent true if independent details should be saved,
    *        false if dependent details should be saved
    * @return a list of independent details that should be removed after removing the master record.
    */
   private List saveCompoundAttributes(FndConnection c, FndPersistentView view, boolean independent) throws IfsException {
      boolean gatherDetailsToRemove = independent && view.getState() == FndRecordState.REMOVED_RECORD;
      List<FndPersistentView> detailsToRemove = gatherDetailsToRemove ? new ArrayList<>() : null;

      // Create a list with dirty compound attributes
      List<FndCompoundAttribute> attrToSave = null;
      FndCompoundAttribute.Iterator details = independent ? view.persistentIndependentDetails() : view.persistentDependentDetails();
      while (details.hasNext()) {
         FndCompoundAttribute attr = details.next();
         if (!attr.exist() || !attr.isDirty()) {
            continue;
         }
         if(attrToSave == null) {
            attrToSave = new ArrayList<>();
         }
         attrToSave.add(attr);
      }
      int attrToSaveSize = attrToSave == null ? 0 : attrToSave.size();

      // Visit the list in reversed order and remove all REMOVED detail records
      for(int i = attrToSaveSize - 1; i >= 0; i--) {
         FndCompoundAttribute attr = (FndCompoundAttribute) attrToSave.get(i);
         FndSaveStorage detailStorage = attr instanceof FndSaveStorage ? (FndSaveStorage) attr : null;

         if (attr.isVector()) {
            FndAbstractArray arr = (FndAbstractArray) attr;
            int arrSize = arr.size();
            for (int j = 0; j < arrSize;) {
               FndPersistentView detail = (FndPersistentView) FndAttributeInternals.internalGet(arr, j);
               FndRecordState state = detail.getState();
               if (state == FndRecordState.REMOVED_RECORD) {
                  if (gatherDetailsToRemove && !attr.cascadeDelete()) {
                     detailsToRemove.add(detail);
                  }
                  else if (detailStorage != null && detail instanceof FndBaseEntityView) {
                     detailStorage.save((FndBaseEntityView) detail);
                  }
                  else {
                     save(c, detail, independent, null);
                  }

                  // Remove from the array the element record removed from database
                  FndAttributeInternals.internalRemove(arr, j);
                  arrSize = arr.size();
               }
               else {
                  j++;
               }
            }
         }
         else {
            FndAbstractAggregate aggr = (FndAbstractAggregate) attr;
            FndPersistentView detail = (FndPersistentView) FndAttributeInternals.internalGetRecord(aggr);
            if (detail != null) {
               FndRecordState state = detail.getState();
               if(state == FndRecordState.REMOVED_RECORD) {
                  if (gatherDetailsToRemove && !attr.cascadeDelete()) {
                     detailsToRemove.add(detail);
                  }
                  else if (detailStorage != null && detail instanceof FndBaseEntityView) {
                     detailStorage.save((FndBaseEntityView) detail);
                     aggr.setNonExistent();
                  }
                  else {
                     save(c, detail, independent, null);
                     aggr.setNonExistent();
                  }
               }
            }
         }
      }

      // Visit the list in normal order and update all MODIFIED and NEW detail records
      for(int i = 0; i< attrToSaveSize; i++) {
         FndCompoundAttribute attr = (FndCompoundAttribute) attrToSave.get(i);
         FndSaveStorage detailStorage = attr instanceof FndSaveStorage ? (FndSaveStorage) attr : null;

         if (attr.isVector()) {
            FndAbstractArray arr = (FndAbstractArray) attr;
            int arrSize = arr.size();
            /** Detail records must be saved in order depending on record state.
             *  1. Removed (already removed during the reversed scan)
             *  2. Modified
             *  3. New
             *  Loop two times, each time handling different record state:
             */
            for (int states = 1; states < DIRTY_STATES.length; states++) {
               for (int j = 0; j < arrSize; j++) {
                  FndPersistentView detail = (FndPersistentView) FndAttributeInternals.internalGet(arr, j);
                  FndRecordState state = detail.getState();
                  if (state == DIRTY_STATES[states]) {
                     if (detailStorage != null && detail instanceof FndBaseEntityView) {
                        detailStorage.save((FndBaseEntityView) detail);
                     }
                     else {
                        save(c, detail, independent, null);
                     }
                  }
               }
            }
         }
         else {
            FndAbstractAggregate aggr = (FndAbstractAggregate) attr;
            FndPersistentView detail = (FndPersistentView) FndAttributeInternals.internalGetRecord(aggr);
            if (detail != null) {
               FndRecordState state = detail.getState();
               if(state == FndRecordState.NEW_RECORD || state == FndRecordState.MODIFIED_RECORD) {
                  if (detailStorage != null && detail instanceof FndBaseEntityView) {
                     detailStorage.save((FndBaseEntityView) detail);
                  }
                  else {
                     save(c, detail, independent, null);
                  }
               }
            }
         }
      }
      return detailsToRemove;
   }

   /**
    * Import compound attributes (arrays/aggregates) of a persistent view.
    * This method is common for both LU and non-LU views.
    * It loops over persistent details in state NEW_RECORD.
    * @param c database connection
    * @param view persistent view
    * @param independent true if independent details should be saved,
    *        false if dependent details should be saved
    */
   private void importCompoundAttributes(FndConnection c, FndPersistentView view, boolean independent) throws IfsException {
      FndCompoundAttribute.Iterator details = independent ? view.persistentIndependentDetails() : view.persistentDependentDetails();
      while (details.hasNext()) {
         FndCompoundAttribute attr = details.next();
         if (!attr.exist() || !attr.isDirty()) {
            continue;
         }

         FndImportStorage detailStorage = attr instanceof FndImportStorage ? (FndImportStorage) attr : null;

         if (attr.isVector()) {
            FndAbstractArray arr = (FndAbstractArray) attr;
            int arrSize = arr.size();
            for (int i = 0; i < arrSize; i++) {
               FndPersistentView detail = (FndPersistentView) FndAttributeInternals.internalGet(arr, i);
               FndRecordState state = detail.getState();
               if (state == FndRecordState.NEW_RECORD) {
                  if (detailStorage != null && detail instanceof FndBaseEntityView) {
                     detailStorage.importEntity((FndBaseEntityView) detail);
                  }
                  else {
                     save(c, detail, independent, null);
                  }
               }
            }
         }
         else {
            FndAbstractAggregate aggr = (FndAbstractAggregate) attr;
            FndPersistentView detail = (FndPersistentView) FndAttributeInternals.internalGetRecord(aggr);
            if (detail != null) {
               FndRecordState state = detail.getState();
               if (state == FndRecordState.NEW_RECORD) {
                  if (detailStorage != null && detail instanceof FndBaseEntityView) {
                     detailStorage.importEntity((FndBaseEntityView) detail);
                  }
                  else {
                     save(c, detail, independent, null);
                  }
               }
            }
         }
      }
   }

   /**
    * Remove independent details on the specified list.
    * @param c database connection
    * @param details a list of independent details to be removed
    */
   private void removeIndependentDetails(FndConnection c, List details) throws IfsException {
      // Visit attributes in reversed order.
      for(int i = details.size() - 1; i >= 0; i--) {
         FndPersistentView detail = (FndPersistentView)details.get(i);
         FndCompoundAttribute attr = detail.getContainer();
         FndSaveStorage detailStorage = attr instanceof FndSaveStorage ? (FndSaveStorage) attr : null;
         if (detailStorage != null && detail instanceof FndBaseEntityView) {
            detailStorage.save((FndBaseEntityView) detail);
         }
         else {
            save(c, detail, true, null);
         }
         if(attr instanceof FndAbstractAggregate) {
            attr.setNonExistent();
         }
      }
   }

   /**
    * Remove an existing LU-entity instance and all its details from database.
    * (Called by save when record state is REMOVED_RECORD).
    * The following attributes must have values set:
    *    Obj_Id,
    *    Obj_Version,
    *    primary key attributes
    * @param c database connection
    * @param view record to remove
    */
   private void removeLUView(FndConnection c, FndLUEntityView view) throws IfsException {
      if (log.debug) {
         log.debug("view=&1", view.getName());
      }
      // skip this check for old native details converted to LU entities
      // view.objVersion.checkValuePresent(Texts.NOOBJVERSIONREMOVE);
      util.preparePrimaryKey(view, true);
      FndLUEntityView r = (FndLUEntityView) view.newInstance();
      prepareGetForRemove(r);
      FndAttributeInternals.internalAssign(r.getPrimaryKey(), view.getPrimaryKey());
      r = (FndLUEntityView) get(r, c);
      if(!view.objVersion.isNull()) {
         r.objVersion.setValue(view.objVersion); // Use ObjVersion sent by the client
      }

      if(!view.isCustomLU()){
         plsqlLock(c, r); // lock the master record which will be removed as the last record
      }
      removeLUTree(c, r);
   }

   /**
    * Prepare an LU-view for complete get() from database in order to be removed.
    * Only details marked for cascade-delete are removed automatically.
    * Read-only detail views are excluded (read-only flag overrides cascade-delete flag).
    * @param view the root of master-detail structure to be fetched from database
    */
   private void prepareGetForRemove(FndLUEntityView view) throws IfsException {
      view.setNonExistent();
      view.excludeQueryResults();
      view.objId.include();
      view.objVersion.include();

      FndCompoundAttribute.Iterator details = view.persistentCascadeDeleteDetails();
      while (details.hasNext()) {
         FndCompoundAttribute attr = details.next();

         // Skip read-only details and native (non-LU) details.
         // Meta-element points to the abstract class, but isReadOnly and isLU are common
         // to the whole meta inheritance tree.
         FndRecordMeta elemMeta = attr.getCompoundMeta().getElement();
         if(elemMeta.isReadOnly() || !elemMeta.isLU()) {
            continue;
         }

         attr.include();
         if (attr.isVector()) { //Array
            FndAbstractArray arr = (FndAbstractArray) attr;
            FndLUEntityView elem = (FndLUEntityView) arr.newRecord();
            FndAttributeInternals.internalAdd(arr, elem);
            prepareGetForRemove(elem);
         }
         else if (attr.isCompound()) { //Aggregate
            FndAbstractAggregate agg = (FndAbstractAggregate) attr;
            FndLUEntityView elem = (FndLUEntityView) FndAttributeInternals.internalGetRecord(agg);
            prepareGetForRemove(elem);
         }
      }
      // Include primary key and parent key attributes
      view.getPrimaryKey().include();
      if (view.getParentKey() != null) {
         view.getParentKey().include();
      }
   }

   /**
    * Removes an LU-view and all its details from database.
    * It is assumed that all LU details (to be removed) of given master view are already fetched from the database.
    * @param c database connection
    * @param view the root of master-detail structure to be removed from database
    */
   private void removeLUTree(FndConnection c, FndLUEntityView view) throws IfsException {
      //
      // First remove the complete tree of (dependent) detail records.
      // Visit attributes in reversed order.
      //
      for(int i = view.getAttributeCount() - 1; i >= 0; i--) {
         FndAttribute a = view.getAttribute(i);
         if(!a.isCompound()) {
            continue;
         }
         FndCompoundAttribute attr = (FndCompoundAttribute) a;

         if(attr.isNull()) {
            //
            // There were no matches in database or else
            // attr has been excluded because it is
            // a non cascade-delete detail or read-only detail
            //
            if(log.debug) {
               log.debug("Skipped non cascade-delete or read-only detail &1", attr.getName());
            }
         }
         else if (attr.isVector()) {
            // remove LU-array
            List records = FndAttributeInternals.getInternalRecords((FndAbstractArray) attr);
             for (Object record : records) {
                 removeLUTree(c, (FndLUEntityView) record);
             }
         }
         else {
            // remove LU-aggregate (which may be empty if fetched using SQL join)
            FndLUEntityView detail = (FndLUEntityView) FndAttributeInternals.internalGetRecord((FndAbstractAggregate) attr);
            if(!detail.objId.isNull() && !detail.objVersion.isNull())
               removeLUTree(c, detail);
         }
      }
      //
      // Now remove master record
      //
      if(view.isCustomLU()){
         plsqlRemoveCustom(c, view);
      }
      else{
         plsqlRemove(c, view);
      }
   }

   /**
    * Locks an LU instance in the database by calling PLSQL procedure Lock__.
    * @param c database connection
    * @param view instance to be locked in database
    */
   private void plsqlLock(FndConnection c, FndLUEntityView view) throws IfsException {
      /*
         PROCEDURE LOCK__
         Argument Name                  Type                    In/Out Default?
         ------------------------------ ----------------------- ------ --------
         INFO_                          VARCHAR2                OUT
         OBJID_                         VARCHAR2                IN
         OBJVERSION_                    VARCHAR2                IN
      */
       boolean projection = util.useProjectionStorage(view);
       FndAutoString sql = new FndAutoString(256);
       try (FndStatement stmt = createStatement(c, view)) {
           // retrieve OBJID from database for old native entities converted to LU entities
           if (view.objId.isNull()) {
               getObjId(c, view);
           }

           sql.append("{ call ");
           util.appendPlsqlPackageName(view, sql);

           if(projection) {
              sql.append(".CRUD_Lock(?,?,?," + util.viewTypeProjectionParameter(view) + ") }");
           }
           else {
              sql.append(".Lock__(?,?,?) }");
           }

           stmt.defineOutParameter("INFO", FndSqlType.STRING);
           stmt.defineInParameter(new FndSqlValue("OBJID", view.objId.getValue(), false, false));
           stmt.defineInParameter(new FndSqlValue("OBJVERSION", view.objVersion.getValue(), false, false));

           stmt.prepareCall(sql);
           stmt.execute();

           PlsqlUtil.processInfo(util.getTextOutParameter(stmt, 1));
       }
   }

   /**
    * Removes an LU instance from database by calling PLSQL procedure Remove__.
    * @param c database connection
    * @param view instance to remove from database
    */
   private void plsqlRemove(FndConnection c, FndLUEntityView view) throws IfsException {
      /*
         PROCEDURE REMOVE__
         Argument Name                  Type                    In/Out Default?
         ------------------------------ ----------------------- ------ --------
         INFO_                          VARCHAR2                OUT
         OBJID_                         VARCHAR2                IN
         OBJVERSION_                    VARCHAR2                IN
         ACTION_                        VARCHAR2                IN
      */
       boolean projection = util.useProjectionStorage(view);
       FndAutoString sql = new FndAutoString(256);
       try (FndStatement stmt = createStatement(c, view)) {
         // retrieve OBJID from database for old native entities converted to LU entities
         if (view.objId.isNull()) {
             getObjId(c, view);
         }

         if(projection) {
            /*
               FUNCTION CRUD_Delete(
                  etag_            IN VARCHAR2,
                  task_seq_        IN NUMBER,   --> typed PK attributes
                  action$_         IN VARCHAR2,
                  online_jt_task## IN VARCHAR2) RETURN Entity_Dec;
            */
            String etag = util.createCrudEtag(view);
            String pkg = view.getMeta().getPlsqlPackage();
            sql.append("DECLARE\n");
            sql.append("   ret_ ").append(pkg).append(".Entity_Dec := ").append(pkg).append(".CRUD_Delete(?");
            stmt.defineInParameter(new FndSqlValue("ETAG", etag));
            int pkSize = util.appendCrudPrimaryKeyParameters(c, view, stmt, sql);
            sql.append(", ?, ").append(util.viewTypeProjectionParameter(view)).append(");\n");
            stmt.defineInParameter(new FndSqlValue("ACTION", "DO", false, false));
            sql.append("BEGIN\n");
            sql.append("   ? := ret_.info;\n");
            sql.append("END;\n");
            stmt.defineOutParameter("INFO", FndSqlType.STRING);

            stmt.prepareCall(sql);
            stmt.execute();

            PlsqlUtil.processInfo(util.getTextOutParameter(stmt, pkSize + 3));
         }
         else {
           sql.append("{ call ");
           util.appendPlsqlPackageName(view, sql);
           sql.append(".Remove__(?,?,?,?) }");
           stmt.defineOutParameter("INFO", FndSqlType.STRING);
           stmt.defineInParameter(new FndSqlValue("OBJID", view.objId.getValue(), false, false));
           stmt.defineInParameter(new FndSqlValue("OBJVERSION", view.objVersion.getValue(), false, false));
           stmt.defineInParameter(new FndSqlValue("ACTION", "DO", false, false));

           stmt.prepareCall(sql);
           stmt.execute();

           PlsqlUtil.processInfo(util.getTextOutParameter(stmt, 1));
       }

      }
   }

   /**
    * Removes an LU instance from database by calling PLSQL procedure Remove__.
    * @param c database connection
    * @param view instance to remove from database
    */
   private void plsqlRemoveCustom(FndConnection c, FndLUEntityView view) throws IfsException {
       /*
       FUNCTION Projection_Config_Util_API.Execute_CRUD_Delete_(
         etag_            IN VARCHAR2,
         task_seq_        IN NUMBER,   --> typed PK attributes
         action$_         IN VARCHAR2,
         online_jt_task   IN VARCHAR2) RETURN Entity_Dec;
      */
      FndAutoString sql = new FndAutoString(256);
      try (FndStatement stmt = createStatement(c, view)) {
         // retrieve OBJID from database for old native entities converted to LU entities
         if (view.objId.isNull()) {
            getObjId(c, view);
         }
         String etag = util.createCrudEtag(view);
         String pkg = view.getMeta().getPlsqlPackage();
         sql.append("DECLARE\n");
         sql.append("   ret_ Projection_Config_Util_API.Entity_Dec := ").append("Projection_Config_Util_API.Execute_CRUD_Delete_(?");
         stmt.defineInParameter(new FndSqlValue("ETAG", etag));
         int pkSize = util.appendCrudPrimaryKeyParameters(c, view, stmt, sql);
         String entity = view.getMeta().getEntity();
         if (entity.contains(".")) {
            entity = entity.substring(entity.lastIndexOf(".") + 1);
         }
         sql.append(", ?, '").append(entity).append("');\n");
         stmt.defineInParameter(new FndSqlValue("ACTION", "DO", false, false));
         sql.append("BEGIN\n");
         sql.append("   ? := ret_.info;\n");
         sql.append("END;\n");
         stmt.defineOutParameter("INFO", FndSqlType.STRING);

         stmt.prepareCall(sql);
         stmt.execute();

         PlsqlUtil.processInfo(util.getTextOutParameter(stmt, pkSize + 3));
      }
   }


   /**
    * Stores an LU-view in database by calling PLSQL procedure New__.
    * @param c database connection
    * @param view instance to create in database
    * @param action action to perform (DO or PREPARE)
    */
   private void plsqlNew(FndConnection c, FndLUEntityView view, String action) throws IfsException {
      /*
         PROCEDURE NEW__
         Argument Name                  Type                    In/Out Default?
         ------------------------------ ----------------------- ------ --------
         INFO_                          VARCHAR2                OUT
         OBJID_                         VARCHAR2                OUT
         OBJVERSION_                    VARCHAR2                OUT
         ATTR_                          VARCHAR2                IN/OUT
         ACTION_                        VARCHAR2                IN
      */
       FndAutoString sql = new FndAutoString(256);
      AttributeString attrString = new AttributeString();
      AttributeString cfAttrString = null;
       boolean projection = util.useProjectionStorage(view);

       try (FndStatement stmt = createStatement(c, view)) {
           int count = view.getAttributeCount();
           for (int i = 0; i < count; i++) {
               FndAttribute attr = view.getAttribute(i);
               if (!attr.isNull() && !attr.isLong()) {
                  if (attr.getMeta().isCustomField()) {
                     if (cfAttrString == null) {
                         cfAttrString = new AttributeString();
                     }
                     util.appendToAttributeString(cfAttrString, attr);
                  }
                  else {
                       util.appendToAttributeString(attrString, attr);
                   }
               }
           }

           if(projection) {
            /*
               FUNCTION CRUD_Create(
                 attr_             IN VARCHAR2,
                 action_           IN VARCHAR2,
                 fnd_mot_company## IN VARCHAR2) RETURN Entity_Dec;
            */
            String pkg = view.getMeta().getPlsqlPackage();
            sql.append("DECLARE\n");
            sql.append("   ret_ ").append(pkg).append(".Entity_Dec := ").append(pkg).append(".CRUD_Create(?, ?, ")
               .append(util.viewTypeProjectionParameter(view)).append(");\n");
            sql.append("BEGIN\n");
            sql.append("   ? := ret_.etag;\n");
            sql.append("   ? := ret_.info;\n");
            sql.append("   ? := ret_.attr;\n");
            sql.append("END;\n");
            stmt.defineInParameter(new FndSqlValue("ATTR", attrString.toString(), false, false));
            stmt.defineInParameter(new FndSqlValue("ACTION", action, false, false));
            stmt.defineOutParameter("ETAG", FndSqlType.STRING);
            stmt.defineOutParameter("INFO", FndSqlType.STRING);
            stmt.defineOutParameter("ATTROUT", FndSqlType.STRING);
            stmt.prepareCall(sql);
            stmt.execute();

            if ("DO".equals(action)) {
               String etag = util.getTextOutParameter(stmt, 3);
               util.parseCrudEtag(etag, view, true);
            }
            PlsqlUtil.processInfo(util.getTextOutParameter(stmt, 4));
            util.convertAttributeStringToRecord(view, util.getTextOutParameter(stmt, 5), c);
           }
        else {
           sql.append("{ call ");
           util.appendPlsqlPackageName(view, sql);
           sql.append(".New__(?,?,?,?,?) }");
           stmt.defineOutParameter("INFO", FndSqlType.STRING);
           stmt.defineOutParameter("OBJID", FndSqlType.STRING);
           stmt.defineOutParameter("OBJVERSION", FndSqlType.STRING);
           stmt.defineInOutParameter(new FndSqlValue("ATTR", attrString.toString(), false, false));
           stmt.defineInParameter(new FndSqlValue("ACTION", action, false, false));

           stmt.prepareCall(sql);
           stmt.execute();
           PlsqlUtil.processInfo(util.getTextOutParameter(stmt, 1));
           if ("DO".equals(action)) {
               view.objId.setValue(util.getTextOutParameter(stmt, 2));
               view.objVersion.setValue(util.getTextOutParameter(stmt, 3));
           }
           util.convertAttributeStringToRecord(view, util.getTextOutParameter(stmt, 4), c);
         }

           if ("DO".equals(action)) {
               util.updateLuLobs(c, view);
               if (cfAttrString != null) {
                   plsqlSaveCustomFields(c, view, attrString, cfAttrString);
               }
           }
      }
      catch (ValidationException e) {
         util.addInfo(e, view); //Will rethrow exception
      }
      catch (DatabaseException e) {
         util.addInfo(e, view); //Will rethrow exception
       }
   }

   /**
    * Stores an LU-view in database by calling PLSQL procedure New__.
    * @param c database connection
    * @param view instance to create in database
    * @param action action to perform (DO or PREPARE)
    */
   private void plsqlNewCustom(FndConnection c, FndLUEntityView view, String action) throws IfsException {
      /*
      FUNCTION Projection_Config_Util_API.Execute_CRUD_Create_(
        attr_             IN VARCHAR2,
        action_           IN VARCHAR2,
        fnd_mot_company   IN VARCHAR2) RETURN Entity_Dec;
      */
      FndAutoString sql = new FndAutoString(256);
      AttributeString attrString = new AttributeString();
      AttributeString cfAttrString = null;
      try (FndStatement stmt = createStatement(c, view)) {
         int count = view.getAttributeCount();
         for (int i = 0; i < count; i++) {
            FndAttribute attr = view.getAttribute(i);
            if (!attr.isNull() && !attr.isLong()) {
               if (attr.getMeta().isCustomField()) {
                  if (cfAttrString == null) {
                     cfAttrString = new AttributeString();
                  }
                  util.appendToAttributeString(cfAttrString, attr);
               } else {
                  util.appendToAttributeString(attrString, attr);
               }
            }
         }
         sql.append("DECLARE\n");
         String entity = view.getMeta().getEntity();
         if (entity.contains(".")) {
            entity = entity.substring(entity.lastIndexOf(".") + 1);
         }
         sql.append("   ret_ Projection_Config_Util_API.Entity_Dec := ").append("Projection_Config_Util_API.Execute_CRUD_Create_(?, ?, '")
                 .append(entity).append("');\n");
         sql.append("BEGIN\n");
         sql.append("   ? := ret_.etag;\n");
         sql.append("   ? := ret_.info;\n");
         sql.append("   ? := ret_.attr;\n");
         sql.append("END;\n");
         stmt.defineInParameter(new FndSqlValue("ATTR", cfAttrString.toString(), false, false));
         stmt.defineInParameter(new FndSqlValue("ACTION", action, false, false));
         stmt.defineOutParameter("ETAG", FndSqlType.STRING);
         stmt.defineOutParameter("INFO", FndSqlType.STRING);
         stmt.defineOutParameter("ATTROUT", FndSqlType.STRING);
         stmt.prepareCall(sql);
         stmt.execute();


         if ("DO".equals(action)) {
            String etag = util.getTextOutParameter(stmt, 3);
            util.parseCrudEtag(etag, view, true);
         }
         PlsqlUtil.processInfo(util.getTextOutParameter(stmt, 4));
         util.convertAttributeStringToRecord(view, util.getTextOutParameter(stmt, 5), c);
         if ("DO".equals(action)) {
            util.updateLuLobs(c, view);
         }
      }
      catch (ValidationException e) {
         util.addInfo(e, view); //Will rethrow exception
      }
      catch (DatabaseException e) {
         util.addInfo(e, view); //Will rethrow exception
      }
   }


   /**
    * Stores an LU-view in database by calling PLSQL procedure Modify__.
    * @param c database connection
    * @param view record to modify
    */
   private void plsqlModify(FndConnection c, FndLUEntityView view) throws IfsException {
      /*
         PROCEDURE MODIFY__
         Argument Name                  Type                    In/Out Default?
         ------------------------------ ----------------------- ------ --------
         INFO_                          VARCHAR2                OUT
         OBJID_                         VARCHAR2                IN
         OBJVERSION_                    VARCHAR2                IN/OUT
         ATTR_                          VARCHAR2                IN/OUT
         ACTION_                        VARCHAR2                IN
      */
       FndAutoString sql = new FndAutoString(256);
      AttributeString attrString = new AttributeString();
      AttributeString cfAttrString = null;
       boolean projection = util.useProjectionStorage(view);

       try (FndStatement stmt = createStatement(c, view)) {
           // retrieve OBJID from database for old native entities converted to LU entities
           if (view.objId.isNull()) {
               getObjId(c, view);
           }

           view.objId.checkValuePresent(Texts.NOOBJID);
           view.objVersion.checkValuePresent(Texts.NOOBJVERSION);

           int count = view.getAttributeCount();
           for (int i = 0; i < count; i++) {
               FndAttribute attr = view.getAttribute(i);
               if (attr.isDirty() && !attr.isLong()) {
                  if (attr.getMeta().isCustomField()) {
                     if (cfAttrString == null) {
                         cfAttrString = new AttributeString();
                     }
                     util.appendToAttributeString(cfAttrString, attr);
                  }
                  else {
                     util.appendToAttributeString(attrString, attr);
                  }
               }
           }

           if(projection) {
            /*
               FUNCTION CRUD_Update (
                  etag_           IN VARCHAR2,
                  identity_       IN VARCHAR2, --> typed PK attributes
                  attr_           IN VARCHAR2,
                  action$_        IN VARCHAR2,
                  tst_user_info## IN VARCHAR2) RETURN Entity_Dec;
            */
            String etag = util.createCrudEtag(view);
            String pkg = view.getMeta().getPlsqlPackage();
            sql.append("DECLARE\n");
            sql.append("   ret_ ").append(pkg).append(".Entity_Dec := ").append(pkg).append(".CRUD_Update(?");
            stmt.defineInParameter(new FndSqlValue("ETAG", etag));
            int pkSize = util.appendCrudPrimaryKeyParameters(c, view, stmt, sql);
            sql.append(", ?, ?, ").append(util.viewTypeProjectionParameter(view)).append(");\n");
            stmt.defineInParameter(new FndSqlValue("ATTR", attrString.toString(), false, false));
            stmt.defineInParameter(new FndSqlValue("ACTION", "DO", false, false));
            sql.append("BEGIN\n");
            sql.append("   ? := ret_.etag;\n");
            sql.append("   ? := ret_.info;\n");
            sql.append("   ? := ret_.attr;\n");
            sql.append("END;\n");
            stmt.defineOutParameter("ETAGOUT", FndSqlType.STRING);
            stmt.defineOutParameter("INFO", FndSqlType.STRING);
            stmt.defineOutParameter("ATTROUT", FndSqlType.STRING);
            stmt.prepareCall(sql);
            stmt.execute();

            etag = util.getTextOutParameter(stmt, pkSize + 4);
            util.parseCrudEtag(etag, view, false);
            PlsqlUtil.processInfo(util.getTextOutParameter(stmt, pkSize + 5));

            util.convertAttributeStringToRecord(view, util.getTextOutParameter(stmt, pkSize + 6), c);
           }
        else {
           sql.append("{ call ");
           util.appendPlsqlPackageName(view, sql);
           sql.append(".Modify__(?,?,?,?,?) }");
           stmt.defineOutParameter("INFO", FndSqlType.STRING);
           stmt.defineInParameter(new FndSqlValue("OBJID", view.objId.getValue(), false, false));
           stmt.defineInOutParameter(new FndSqlValue("OBJVERSION", view.objVersion.getValue(), false, false));
           stmt.defineInOutParameter(new FndSqlValue("ATTR", attrString.toString(), false, false));
           stmt.defineInParameter(new FndSqlValue("ACTION", "DO", false, false));
           stmt.prepareCall(sql);
           stmt.execute();

           PlsqlUtil.processInfo(util.getTextOutParameter(stmt, 1));
           view.objVersion.setValue(util.getTextOutParameter(stmt, 3));
           util.convertAttributeStringToRecord(view, util.getTextOutParameter(stmt, 4), c);
         }
         util.updateLuLobs(c, view);
         if (cfAttrString != null) {
            plsqlSaveCustomFields(c, view, attrString, cfAttrString);
         }
      }
      catch (ValidationException e) {
         util.addInfo(e, view); //Will rethrow exception
      }
      catch (DatabaseException e) {
         util.addInfo(e, view); //Will rethrow exception
       }
   }

   /**
    * Stores an LU-view in database by calling PLSQL procedure Modify__.
    * @param c database connection
    * @param view record to modify
    */
   private void plsqlModifyCustom(FndConnection c, FndLUEntityView view) throws IfsException {
      FndAutoString sql = new FndAutoString(256);
      AttributeString attrString = new AttributeString();
      AttributeString cfAttrString = null;

      try (FndStatement stmt = createStatement(c, view)) {
         // retrieve OBJID from database for old native entities converted to LU entities
         if (view.objId.isNull()) {
            getObjId(c, view);
         }

         view.objId.checkValuePresent(Texts.NOOBJID);
         view.objVersion.checkValuePresent(Texts.NOOBJVERSION);

         int count = view.getAttributeCount();
         for (int i = 0; i < count; i++) {
            FndAttribute attr = view.getAttribute(i);
            if (attr.isDirty() && !attr.isLong()) {
               if (cfAttrString == null) {
                  cfAttrString = new AttributeString();
               }
               util.appendToAttributeString(cfAttrString, attr);
            }
         }

         /*
         FUNCTION Projection_Config_Util_API.Execute_CRUD_Update_(
           etag_           IN VARCHAR2,
           objkey_         IN VARCHAR2, --> typed PK attributes
           attr_           IN VARCHAR2,
           action$_        IN VARCHAR2,
           tst_user_info   IN VARCHAR2) RETURN Entity_Dec;
         */
         String etag = util.createCrudEtag(view);
         String pkg = view.getMeta().getPlsqlPackage();
         sql.append("DECLARE\n");
         sql.append("   ret_ Projection_Config_Util_API.Entity_Dec := ").append("Projection_Config_Util_API.Execute_CRUD_Update_(?");
         stmt.defineInParameter(new FndSqlValue("ETAG", etag));
         int pkSize = util.appendCrudPrimaryKeyParameters(c, view, stmt, sql);
         String entity = view.getMeta().getEntity();
         if (entity.contains(".")) {
            entity = entity.substring(entity.lastIndexOf(".") + 1);
         }
         sql.append(", ?, ?, '").append(entity).append("');\n");
         sql.append("BEGIN\n");
         sql.append("   ? := ret_.etag;\n");
         sql.append("   ? := ret_.info;\n");
         sql.append("   ? := ret_.attr;\n");
         sql.append("END;\n");
         stmt.defineInParameter(new FndSqlValue("ATTR", cfAttrString.toString(), false, false));
         stmt.defineInParameter(new FndSqlValue("ACTION", "DO", false, false));
         stmt.defineOutParameter("ETAGOUT", FndSqlType.STRING);
         stmt.defineOutParameter("INFO", FndSqlType.STRING);
         stmt.defineOutParameter("ATTROUT", FndSqlType.STRING);
         stmt.prepareCall(sql);
         stmt.execute();

         etag = util.getTextOutParameter(stmt, pkSize + 4);
         util.parseCrudEtag(etag, view, false);
         PlsqlUtil.processInfo(util.getTextOutParameter(stmt, pkSize + 5));

         util.convertAttributeStringToRecord(view, util.getTextOutParameter(stmt, pkSize + 6), c);
         util.updateLuLobs(c, view);
      }
      catch (ValidationException e) {
         util.addInfo(e, view); //Will rethrow exception
      }
      catch (DatabaseException e) {
         util.addInfo(e, view); //Will rethrow exception
      }

   }


   /**
    * Save custom field record. Use Cf_Modify__ for both new and modify since this is how it works in IFS EE.
    *
    * @param c Database connection
    * @param view   record to save (used to extract package name)
    * @param attrString attribute string for regular fields
    * @param cfAttrString attribute string for custom fields
    * @throws IfsException
    */
   private void plsqlSaveCustomFields(FndConnection c, FndLUEntityView view, AttributeString attrString, AttributeString cfAttrString) throws IfsException {
      /*
         PROCEDURE CF_MODIFY__
         Argument Name                  Type                    In/Out Default?
         ------------------------------ ----------------------- ------ --------
         INFO_                          VARCHAR2                OUT
         OBJID_                         VARCHAR2                IN
         CF_ATTR_                       VARCHAR2                IN/OUT
         ATTR_                          VARCHAR2                IN
         ACTION_                        VARCHAR2                IN
      */
       FndAutoString sql = new FndAutoString(256);

       try (FndStatement stmt = createStatement(c, view)) {
           // retrieve OBJID from database for old native entities converted to LU entities
           if (view.objId.isNull()) {
               getObjId(c, view);
           }

           view.objId.checkValuePresent(Texts.NOOBJID);
           view.objVersion.checkValuePresent(Texts.NOOBJVERSION);

           sql.append("{ call ");
           IFndCustomFieldProvider provider = FndContext.getCurrentContext().getCustomFieldProvider();
           //since there were custom fields in the record there should be a custom fields provider registered for the call, but we'll check anyway just to make sure
           //if there's no provider registered we'll simply ignore the custom fields
           if (provider == null) {
               return;
           } else {
               util.appendPlsqlPackageName(provider.getPlsqlPackage(view.getTable(), view.getMeta().getPlsqlPackage()), sql);
           }
           sql.append(".Cf_Modify__(?,?,?,?,?) }");

           stmt.defineOutParameter("INFO", FndSqlType.STRING);
           stmt.defineInParameter(new FndSqlValue("OBJID", view.objId.getValue(), false, false));
           stmt.defineInOutParameter(new FndSqlValue("CF_ATTR", cfAttrString.toString(), false, false));
           stmt.defineInParameter(new FndSqlValue("ATTR", attrString.toString(), false, false));
           stmt.defineInParameter(new FndSqlValue("ACTION", "DO", false, false));
           stmt.prepareCall(sql);
           stmt.execute();

           PlsqlUtil.processInfo(util.getTextOutParameter(stmt, 1));
           util.convertAttributeStringToRecord(view, util.getTextOutParameter(stmt, 3), c);
       } catch (ValidationException e) {
           util.addInfo(e, view); //Will rethrow exception
       } catch (DatabaseException e) {
           util.addInfo(e, view); //Will rethrow exception
       }
   }

   /** Update BLOB/CLOB.
    *  Lobs has to be updated separately. Get lob locators and update values using
    *  locators. Attributes with null value will not be updated.
    *  @param view Record with (possible) dirty lobs
    *  @param c Active database connection
    *  @param create Flag indicating if updateLobd was called from after insertion of a new row
    */
   void updateLobs(FndPersistentView view, FndConnection c, boolean create) throws IfsException {
      boolean first = true;
      FndStatement stmt = null;
      FndAutoString sql = new FndAutoString(256);
      ArrayList<FndAttribute> lobs = new ArrayList<>(); //List of updated attributes

      try {
         stmt = createStatement(c, view);
         sql.append("SELECT ");
         int count = view.getAttributeCount();
         for (int i = 0; i < count; i++) {
            FndAttribute attr = view.getAttribute(i);
            //Update dirty non-null long attributes
            if (util.isDirtyLob(attr, create)) {
               if (first) {
                  first = false;
               }
               else {
                  sql.append(", ");
               }

               util.appendColumnName(attr, sql);
               lobs.add(attr);
            }
         }
         sql.append("\n FROM ");
         if(view.getMeta().isLU()) {
            util.appendLobTableName((FndLUEntityView)view, sql);
         }
         else {
            util.appendTableName(view, sql);
         }
         util.appendPrimaryKeyCondition(view, stmt, sql, true, false, false, false, true);
         sql.append(" FOR UPDATE"); //Lock row

         if (!first) { //There were dirty long attributes
            stmt.prepare(sql, true); //Updateable result set
            stmt.executeQuery();
            FndResultSet result = stmt.getFndResult();
            if (result.next()) {
               FndDebug.increaseSqlFetchCounter(1);
               //Loop over all lobs to be updated
               int numLobs = lobs.size();
               for (int i = 0; i < numLobs; i++) {
                  FndAttribute attr = (FndAttribute) lobs.get(i);
                  if (attr instanceof FndBinary) {
                     FndBinary bin = (FndBinary)attr;
                     FndInputStreamManager inputMgr = bin.getInputStreamManager();
                     if(inputMgr != null) {
                        if(inputMgr.getInputStream() == null) {
                           throw new SystemException(Texts.INMGRNULL, attr.getMeta().getFullName());
                        }
                        FndServerContext.getCurrentServerContext().addStreamManager(inputMgr);
                        stmt.updateBlob(i + 1, inputMgr);
                     }
                     else
                        stmt.updateBlob(i + 1, bin.getValue());
                  }
                  else
                     stmt.updateClob(i + 1, ((FndText) attr).getValue());
               }
               result.updateRow(); //Write updates to database
               if (log.debug) {
                  log.debug("Lobs in &1 updated", view.getName());
               }
            }
            else {
               throw new SystemException(Texts.UPDLOB);
            }
         }
      }
      catch (SQLException e) {
         throw new SystemException(e, Texts.UPDATELOB, e.getMessage());
      }
      finally {
         if (stmt != null)
            stmt.close();
      }
   }

   /**
    * Throws an ApplicationException about chaged/removed database record.
    * @param view entity view representing record that has been changed or removed
    * @param c active database connection
    */
   void reportChangedRow(FndBaseEntityView view, FndConnection c) throws IfsException {
       FndAutoString sql = new FndAutoString(128);

       try (FndStatement stmt = createStatement(c, view)) {
           sql.append("SELECT 0 FROM ");
           util.appendTableName(view, sql);
           util.appendPrimaryKeyCondition(view, stmt, sql, true, false);

           stmt.prepare(sql);
           stmt.executeQuery();
           FndResultSet result = stmt.getFndResult();
           if (result.next()) {
               FndDebug.increaseSqlFetchCounter(1);
               throw new ApplicationException(Texts.OBJCHANGED, view.getName());
           } else {
               throw new ApplicationException(Texts.OBJREMOVE, view.getName());
           }
       } catch (SQLException e) {
           throw new ApplicationException(e, Texts.ROWCHNGFAIL);
       }
   }

   /**
    * Save an array of entities.
    * Removed records will be saved first, then new records and last modified records.
    * @param inRecord Array of entity records to be saved.
    * @return Saved records.
    * @throws IfsException
    */
   @Override
   public FndAbstractArray bulkSave(FndAbstractArray inRecord) throws IfsException {
      try {
         beforeCall();

         //Sort records on record state.
         inRecord.sortOnState();

         //Call standard save method for every record.
         int length = inRecord.size();
         for (int i = 0; i < length; i++) {
            FndBaseEntityView rec = (FndBaseEntityView) FndAttributeInternals.internalGet(inRecord, i);
            FndRecordState state = rec.getState();
            if (state != null && state != FndRecordState.QUERY_RECORD) {
               //save(rec);

               if (log.trace) {
                  log.trace("view=&1, index=&2", rec.getName(), String.valueOf(i));
               }

               //Validate view before saving
               markRecordsToRemove(rec, false);
               rec.validate(false);
               rec.abortIfInvalid();

               FndConnection c = context.getConnection(rec);

               save(c, rec, util.isIndependentEntity(rec), null);
            }
         }

         return inRecord;
      }
      finally {
         afterCall();
      }
   }

   /**
    * Save an array of entities updating each entity with the specified entity state.
    * Implementation of standard storage operation bulkUpdate.
    * The method is equivalent to calling save(rec, targetState) on every record in the array.
    * @param inRecord array of entity instances to save
    * @param targetState used to modify the entity state attribute for native entities
    *        (can not be modified directly)
    * @return array of saved records
    * @throws IfsException if the call fails for some reason
    */
   @Override
   public FndAbstractArray bulkSave(FndAbstractArray inRecord, FndEntityState.Enum targetState) throws IfsException {
      try {
         beforeCall();

         //Sort records on record state.
         inRecord.sortOnState();

         //Call standard save method for every record.
         int length = inRecord.size();
         for (int i = 0; i < length; i++) {
            FndBaseEntityView rec = (FndBaseEntityView) FndAttributeInternals.internalGet(inRecord, i);
            FndRecordState state = rec.getState();
            if (state != null && state != FndRecordState.QUERY_RECORD) {

               if (log.trace) {
                  log.trace("targetState=&1, view=&2, index=&3", targetState.toString(), rec.getName(), String.valueOf(i));
               }

               //Validate view before saving
               markRecordsToRemove(rec, false);
               rec.validate(false);
               rec.abortIfInvalid();

               FndConnection c = context.getConnection(rec);

               save(c, rec, util.isIndependentEntity(rec), targetState);
            }
         }

         return inRecord;
      }
      finally {
         afterCall();
      }
   }

   /**
    * Standard entity exist method checks for existence of entity instance in database
    * @param inRecord instance to check for
    * @return true if found, false otherwise
    * @throws IfsException
    */
   @Override
   public boolean exist(FndBaseEntityView inRecord) throws IfsException {
      FndStatement stmt = null;
      boolean found = false;
      boolean alias = true;

      try {
         beforeCall();
         FndConnection c = context.getConnection(inRecord);

         if(!FndStorageInstallationCache.isTableInstalled(inRecord, c)) {
            throw new SystemException(Texts.EXISTNOTINST, inRecord.getName());
         }

         stmt = createStatement(c, inRecord);
         FndAutoString sql = new FndAutoString(256);

         sql.append("SELECT 0 FROM ");
         util.appendTableName(inRecord, sql, alias);
         util.appendPrimaryKeyCondition(inRecord, stmt, sql, true, alias);
         stmt.prepare(sql);
         stmt.setMaxRows(1);
         stmt.setFetchSize(1);
         stmt.executeQuery();

         FndResultSet result = stmt.getFndResult();
         if (result.next()) {
            found = true;
         }

         return found;
      }
      catch (SQLException e) {
         throw new SystemException(e, Texts.EXISTFAIL, inRecord.getName(), e.getMessage().trim());
      }
      finally {
         if (stmt != null) {
            stmt.close();
         }
         afterCall();
      }
   }

   /**
    * Get from the database OBJID for specified entity instance.
    * The method is used for old native entities converted to LU entities.
    * @param view instance to set OBJID in
    */
   private void getObjId(FndConnection c, FndLUEntityView view) throws IfsException {
      FndStatement stmt = null;
      boolean alias = true;

      try {
         stmt = createStatement(c, view);
         FndAutoString sql = new FndAutoString(256);

         sql.append("SELECT OBJID, OBJVERSION FROM ");
         util.appendTableName(view, sql, alias);
         util.appendPrimaryKeyCondition(view, stmt, sql, true, alias);
         stmt.prepare(sql);
         stmt.setMaxRows(1);
         stmt.setFetchSize(1);
         stmt.executeQuery();

         FndResultSet result = stmt.getFndResult();
         if(result.next()) {
            FndAttributeInternals.setSqlValue(view.objId, stmt, 1);
            if(stmt.resultWasNull()) {
               view.objId.setNull();
            }

            if(view.objVersion.isNull()) {
               FndAttributeInternals.setSqlValue(view.objVersion, stmt, 2);
               if(stmt.resultWasNull()) {
                  view.objVersion.setNull();
               }
            }
         }
         else {
            throw new SystemException(Texts.GETOBJIDNOTFOUND, view.getName(), util.getPrimaryKeyImage(view));
         }
      }
      catch (SQLException e) {
         throw new SystemException(e, Texts.GETOBJIDFAIL, view.getName(), e.getMessage().trim());
      }
      finally {
         if(stmt != null) {
            stmt.close();
         }
      }
   }

   /**
    * Standard entity Get method.
    * <p>
    * Note! As a side-effect of this operation, the input record
    * may be modified.
    * <p>
    * @param inRecord View with value set on primary key attribute
    * @return Instance of entity
    * @throws IfsException
    */
   @Override
   public FndBaseEntityView get(FndBaseEntityView inRecord) throws IfsException {
      try {
         beforeCall();
         FndConnection c = context.getConnection(inRecord);
         return get(inRecord, c);
      }
      finally {
         afterCall();
      }
   }

   /**
    * Private implementation of get() that uses an already open connection.
    */
   private FndBaseEntityView get(FndBaseEntityView inRecord, FndConnection c) throws IfsException {

      if(!FndStorageInstallationCache.isTableInstalled(inRecord, c)) {
         throw new SystemException(Texts.GETNOTINST, inRecord.getName());
      }

      FndStatement stmt = null;
      boolean alias = true;

      try {
         context.isGetOperation = true;
         stmt = createStatement(c, inRecord);
         SelectStatementContext stmtCtx = new SelectStatementContext(stmt);
         FndAutoString sql = new FndAutoString(256); // no order by
         boolean conditionAdded = createBaseSelectStatement(inRecord, null, stmtCtx, sql, null, null, c);
         if (sql.length() > 0) {
            util.appendPrimaryKeyCondition(inRecord, stmt, sql, !conditionAdded, alias, false, true, false);

            stmt.prepare(sql);
            stmt.setMaxRows(1);
            stmt.setFetchSize(1);
            stmt.executeQuery();

            //Fetch result from database
            FndResultSet result = stmt.getFndResult();
            FndBaseEntityView outRecord = (FndBaseEntityView) fetch(result, stmtCtx, inRecord);
            stmt.close();
            stmt = null;
            if (outRecord == null) {
               StringBuilder buf = new StringBuilder();
               boolean first = true;
               buf.append(inRecord.getName()).append(" - ");
               FndAttribute.Iterator keys = inRecord.getPrimaryKey().iterator();
               while (keys.hasNext()) {
                  FndAttribute attr = keys.next();
                  if (!first) {
                     buf.append(", ");
                  }
                  else {
                     first = false;
                  }
                  buf.append(attr.getName()).append("=").append(attr.toString());
               }
               throw new DatabaseException(DatabaseException.INSTANCE_NOT_FOUND, buf.toString(), inRecord.getName());
            }

            //Query detail records
            queryCompoundAttributes(stmtCtx, c, inRecord, outRecord);

            outRecord.setState(FndRecordState.QUERY_RECORD);
            return outRecord;
         }
         else {
            //Select clause was empty
            return (FndBaseEntityView) inRecord.newInstance();
         }
      }
      finally {
         context.isGetOperation = false;
         if (stmt != null) {
            stmt.close();
         }
      }
   }

   /**
    * Standard entity query (cursor)
    * <p>
    * Note! As a side-effect of this operation, the condition record
    * aggregated in the query record may be modified.
    * <p>
    * @param inRecord Query record with conditions
    * @return An open FndQueryCursor
    * @throws IfsException
    */
   public FndQueryCursor query(FndQueryRecord inRecord) throws IfsException {
      FndStatement stmt;
      FndAutoString sql;
      FndAutoString sqlOrderBy;
      boolean alias = true;

      try {
         beforeCall();

         FndBaseEntityView condition = (FndBaseEntityView) inRecord.condition.getRecord();

         if (log.trace) {
            log.trace("view=&1, contextCreator=&2 (returns FndQueryCursor)", condition.getName(), String.valueOf(contextCreator));
         }

         FndConnection c = context.getConnection(condition);

         sql = new FndAutoString(256);
         sqlOrderBy = new FndAutoString(128);
         stmt = createStatement(c, condition);
         SelectStatementContext stmtCtx = new SelectStatementContext(stmt);
         boolean conditionAdded = createBaseSelectStatement(condition, null, stmtCtx, sql, inRecord.orderBy.getValue(), sqlOrderBy, c);
         if (sql.length() > 0) {
            if (util.appendAdvancedConditions(condition, sql, stmt, !conditionAdded, alias, c)) {
               conditionAdded = true;
            }
            util.appendSimpleConditions(condition, sql, stmt, !conditionAdded, alias);
            util.appendGroupBy(stmtCtx, condition, sql, alias, c);

            // check if sorting can be performed in database
            if (sqlOrderBy.length() > 0) {
               sql.append("\n ORDER BY ");
               sql.append(sqlOrderBy);
            }

            // abort if the query requires sorting in memory
            if (inRecord.orderBy.hasValue() && sqlOrderBy.length() == 0) {
               throw new SystemException("QUERYCURSORSORT:Cannot return a cursor for view &1 because order by [&2] cannot be performed in SQL",
                                         inRecord.getName(),
                                         inRecord.orderBy.getValue());
            }

            stmt.prepare(sql);
            stmt.setFetchSize(context.defaultFetchSize);

            stmt.executeQuery();
            FndQueryCursor cursor = new FndQueryCursor(stmt.getFndResult(), c, this, stmtCtx, condition);
            return cursor;
         }
         return null;
      }
      catch (IfsException e) {
         afterCall();
         throw new SystemException(e, "FETCHFAIL:Fetching data from database failed");
      }
      //finally : afterCall() is called when close the cursor
   }


   /**
    * Standard entity query
    * <p>
    * Note! As a side-effect of this operation, the condition record
    * aggregated in the query record may be modified.
    * <p>
    * @param inRecord Query record with conditions
    * @param outArray Array for found records
    * @throws IfsException
    */
   @Override
   public void query(FndQueryRecord inRecord, FndAbstractArray outArray) throws IfsException {
      FndStatement stmt = null;
      FndAutoString sql;
      FndAutoString sqlOrderBy;
      boolean alias = true;
      int max = 0;
      int rows = 0;

      try {
         beforeCall();
         FndBaseEntityView condition = (FndBaseEntityView) inRecord.condition.getRecord();

         if (log.trace) {
            log.trace("view=&1, contextCreator=&2", condition.getName(), String.valueOf(contextCreator));
         }

         outArray.setTemplateRecord(condition);
         FndConnection c = context.getConnection(condition);
         if(!FndStorageInstallationCache.isTableInstalled(condition, c)) {
            util.markNotInstalledCompoundAttribute(outArray, condition, "query");
            return;
         }

         if (!contextCreator && context.conditionContainer != null) {
            //
            // Recursive call to query a detail record, use parent-info from context
            //
            Container resultContainer = new Container(outArray);
            queryDetailView(null, condition, context.conditionContainer, context.metaParentKeyInElement, context.parentKeyInParent, resultContainer);
            if (resultContainer.size() == 0)
               outArray.setNonExistent();

            outArray.setState(FndRecordState.QUERY_RECORD);
            return;
         }

         sql = new FndAutoString(256);
         sqlOrderBy = new FndAutoString(128);
         stmt = createStatement(c, condition);
         SelectStatementContext stmtCtx = new SelectStatementContext(stmt);
         boolean conditionAdded = createBaseSelectStatement(condition, null, stmtCtx, sql, inRecord.orderBy.getValue(), sqlOrderBy, c);
         if (sql.length() > 0) {
            if (util.appendAdvancedConditions(condition, sql, stmt, !conditionAdded, alias, c)) {
               conditionAdded = true;
            }
            util.appendSimpleConditions(condition, sql, stmt, !conditionAdded, alias);
            util.appendGroupBy(stmtCtx, condition, sql, alias, c);

            // check if sorting can be performed in database
            if (sqlOrderBy.length() > 0) {
               sql.append("\n ORDER BY ");
               sql.append(sqlOrderBy);
            }

            stmt.prepare(sql);

            if (!inRecord.skipRows.isNull()) {
               rows = inRecord.skipRows.getValue().intValue(); //skipRows
            }
            if (!inRecord.maxRows.isNull()) {
               max = inRecord.maxRows.getValue().intValue(); //maxRows
            }

            if (max == 0) {
               max = Integer.MAX_VALUE;
            }

            if (max < Integer.MAX_VALUE) {
               stmt.setFetchSize(Math.min(max + rows, context.maxFetchSize));
               stmt.setMaxRows(max + rows); //May overflow!
            }
            else {
               stmt.setFetchSize(context.defaultFetchSize);
            }

            stmt.executeQuery();
            FndResultSet result = stmt.getFndResult();

            if (rows > 0) {
               if (log.debug) {
                  log.debug("Skipping " + rows + " row(s)");
               }

               for (int i = 0; i < rows; i++) {
                  if (!result.next()) {
                     return; //There is no result
                  }
               }
            }

            int j = 0;
            for (;;) {
               FndAbstractRecord resultRec = fetch(result, stmtCtx, condition);
               if (resultRec == null) {
                  break;
               }
               FndAttributeInternals.internalAdd(outArray, resultRec);
            }
            stmt.close();
            stmt = null;

            //Loop over result and fetch all compound attributes
            for (int i = 0; i < outArray.size(); i++) {
               FndPersistentView view = (FndPersistentView) FndAttributeInternals.internalGet(outArray, i);
               queryCompoundAttributes(stmtCtx, c, condition, view);
            }

            // check if sorting must be performed in memory
            if (inRecord.orderBy.hasValue() && sqlOrderBy.length() == 0) {
               outArray.sort(inRecord.orderBy.getValue());
            }

            outArray.setState(FndRecordState.QUERY_RECORD);
         }
      }
      catch (SQLException e) {
         throw new SystemException(e, Texts.FETCHFAIL);
      }
      finally {
         if (stmt != null) {
            stmt.close();
         }
         afterCall();
      }

   }

   /**
    * Get multiple entity instances in a single call.
    * Key attribute in every entity record in the argument array should be set.
    * Include/exclude flags will be read from the first element in the array.
    * <p>
    * Note! As a side-effect of this operation, the input array
    * may be modified.
    * <p>
    * @param inArray Array of entity records with key attribute set.
    * @param outArray Array for fetched entity instances.
    * @throws IfsException
    */
   @Override
   public void bulkGet(FndAbstractArray inArray, FndAbstractArray outArray) throws IfsException {
      FndStatement stmt = null;
      boolean alias = true;

      if (inArray.size() == 0) {
         return;
      }

      try {
         beforeCall();
         FndBaseEntityView condition = (FndBaseEntityView) FndAttributeInternals.internalGet(inArray, 0);
         outArray.setTemplateRecord(condition);
         FndConnection c = context.getConnection(condition);

         if(!FndStorageInstallationCache.isTableInstalled(condition, c)) {
            throw new SystemException(Texts.GETNOTINST, condition.getName());
         }

         stmt = createStatement(c, condition);
         SelectStatementContext stmtCtx = new SelectStatementContext(stmt);
         FndAutoString sql = new FndAutoString(256);
         // no order by
         boolean conditionAdded = createBaseSelectStatement(condition, null, stmtCtx, sql, null, null, c);
         if (sql.length() > 0) {
            appendKeyInCondition(inArray, stmt, sql, alias, conditionAdded);
            stmt.prepare(sql);
            stmt.setMaxRows(inArray.size());
            stmt.setFetchSize(inArray.size());

            stmt.executeQuery();
            FndResultSet result = stmt.getFndResult();

            int j = 0;
            for (;;) {
               FndAbstractRecord resultRec = fetch(result, stmtCtx, condition);
               if (resultRec == null) {
                  break;
               }
               resultRec.setState(FndRecordState.QUERY_RECORD);
               FndAttributeInternals.internalAdd(outArray, resultRec);
            }
            stmt.close();
            stmt = null;

            //Loop over result and fetch all compound attributes
            for (int i = 0; i < outArray.size(); i++) {
               FndPersistentView view = (FndPersistentView) FndAttributeInternals.internalGet(outArray, i);
               queryCompoundAttributes(stmtCtx, c, condition, view);
            }

            outArray.setState(FndRecordState.QUERY_RECORD);
         }
      }
      finally {
         if (stmt != null) {
            stmt.close();
         }
         afterCall();
      }
   }

   /**
    * Append in-condition from every key in an array of entity records.
    * @param inArray Array of entities with key attributes to use.
    * @param stmt Active statement.
    * @param sql SQL string.
    * @param alias controls if table alias should be appended
    * @param conditionAdded controls whether this is the first part of a WHERE-condition.
    */
   private void appendKeyInCondition(FndAbstractArray inArray, FndStatement stmt, FndAutoString sql, boolean alias, boolean conditionAdded)
      throws SystemException {

      boolean first = true;
      int size = inArray.size();
      for (int i = 0; i < size; i++) {
         FndBaseEntityView rec = (FndBaseEntityView) FndAttributeInternals.getInternalRecords(inArray).get(i);
         FndCompoundReference key = rec.getPrimaryKey();
         int numAttrs = key.getAttributeCount();

         if (first) {
            first = false;
            FndCompoundReferenceMeta keyMeta = key.getMeta();
            sql.append(conditionAdded ? "\n AND (" : "\n WHERE (");
            for (int j = 0; j < keyMeta.getAttributeCount(); j++) {
               FndAttributeMeta meta = keyMeta.getAttribute(j);
               if (j > 0) {
                  sql.append(", ");
               }

               if (alias) {
                  sql.append(util.getTableAlias(rec) + ".");
               }
               sql.append(meta.getColumn());
            }
            sql.append(") IN (");
         }
         else {
            sql.append(",");
         }

         FndAttribute.Iterator keyIterator = key.iterator();
         for (int j = 0; j < numAttrs; j++) {
            FndAttribute attr = keyIterator.next(); //key.getAttribute(j);
            if (j > 0) {
               sql.append(",");
            }
            else {
               sql.append("(");
            }

            if (attr.isNull()) {
               sql.append("NULL");
            }
            else {
               util.appendPlaceHolder(sql);
               stmt.defineInParameter(attr);
            }
         }
         sql.append(")");
      }
      if (size > 0) {
         sql.append(")");
      }
   }

   /**
    * Append select-clause.
    * @param container the container to join with, or null if no join conditions should be added
    * @param view record with include/exclude flags on attributes.
    * @param persistentContainer compound attribute containing the above view (if not null replaces
    *        the standard container-connection of the view)
    * @param stmtCtx active statement context
    * @param select string buffer for SELECT column list
    * @param from   string buffer for FROM table list
    * @param where  string buffer for WHERE clause
    * @param attributePositions out-argument: list with attribute positions in all
    *        joined records included in the select statement (deep-first order),
    *        the list is used during fetch operation and during generation of GROUP BY clause
    * @param c active database connection
    */
   private void appendSelectColumnList(
      FndAbstractAggregate container,
      FndPersistentView view,
      FndCompoundAttribute persistentContainer,
      SelectStatementContext stmtCtx,
      FndAutoString select,
      FndAutoString from,
      FndAutoString where,
      List<? super Object> attributePositions,
      FndConnection c)
   throws SystemException {

      if (log.debug) {
         log.debug("container=&1 view=&2", container == null ? "null" : container.getName(), view.getName());
      }

      // Exclude objId/objVersion attributes from a read-only view
      if(view.getMeta().isReadOnly()) {
         if(view instanceof FndLUEntityView) {
            FndLUEntityView entity = (FndLUEntityView) view;
            entity.objVersion.exclude();
            entity.objId.exclude();
         }
         else if(view instanceof FndEntityView) {
            FndEntityView entity = (FndEntityView) view;
            entity.objVersion.exclude();
         }
      }

      FndStatement stmt = stmtCtx.stmt;

      if (container != null) {
         util.appendJoinCondition(container, view, where, stmt);
      }

      boolean firstColInThisTable = true;

      if (from.length() > 0) {
         from.append(", ");
      }
      util.appendTableName(view, from, true);

      // Apply all filters in dirty-mode. In non-dirty-mode
      // skip filters on joined referent-records (reference-by-value)
      // but apply filters on root records and on dependent aggregates.
      // Apply stereotype filters for all but joined records.
      FndCompoundAttributeMeta metaContainer = null;
      if(persistentContainer != null)
         metaContainer = persistentContainer.getCompoundMeta();
      else if(container != null) {
         metaContainer = container.getCompoundMeta();
      }
      if(container == null) {
          boolean applyAllFilters = (context.dirtyPopulateMode || persistentContainer==null ||
            (persistentContainer != null && persistentContainer.containsDependentDetails()));
          util.resolveFilters(view, metaContainer, from, where, !applyAllFilters, c, stmt);
      }

      int numAttrs = view.getAttributeCount();
      for (int i = 0; i < numAttrs; i++) {
         FndAttribute attr = view.getAttribute(i);
         if (!attr.isExcluded() && !attr.isVector() && (attr.isCompound() || util.isQueryableColumn(attr.getMeta()))) {

            FndAbstractAggregate aggr = attr instanceof FndAbstractAggregate ? (FndAbstractAggregate) attr : null;

            FndPersistentView joinedRec = aggr != null ? stmtCtx.getJoinedAggregate(aggr) : null;

            if (aggr != null && joinedRec == null) {
               continue; // skip this aggregate attribute
            }

            //Attribute is included in select list as a column or as a nested record
            attributePositions.add(i);

            if (joinedRec != null) {
               List<Object> attrPos = new ArrayList<>();
               appendSelectColumnList(aggr, joinedRec, aggr, stmtCtx, select, from, where, attrPos, c);
               attributePositions.add(attrPos); // one element contains the whole nested list
            }
            else {
               //Attribute is included in select list as column

               boolean firstCol = select.length() == 7;

               if (!firstCol && firstColInThisTable) {
                  select.append("\n ");
               }

               firstColInThisTable = false;
               util.appendSelectColumn(attr, select, firstCol, true, c);
            }
         }
      }
   }

   /**
    * Creates the base select statement for a record.
    * The method performs the following steps:
    * <pre>
    *    1. create SELECT column list for specified record and all joined (nested) records
    *    2. create FROM table list for specified record and all joined (nested) records
    *    3. create WHERE conditions for joined records, if any
    *    4. resolve activated filters, which may result in expanded FROM table list and/or
    *       additional WHERE conditions
    *    5. create ORDER BY clasue by converting attribute names (paths) to column names
    * </pre>
    *
    * Note! Returned empty SQL text indicates that no columns has been included.
    *
    * @param view record with include/exclude flags on attributes.
    * @param viewContainer compound attribute containing the above view (if not null replaces
    *        the standard container-connection of the view)
    * @param stmtCtx active statement context
    * @param sql string buffer for SQL text
    * @param fndOrderBy order by clause defined in terms of attribute names (or paths)
    * @param sqlOrderBy string buffer for SQL order-by clause with attribute names converted
    *                   to column names (if not null fndOrderBy results in an empty sqlOrderBy
    *                   buffer it means that sorting cannot be performed in the database)
    * @param c active database connection
    * @return true if a condition has been added to WHERE clause, false otherwise
    */
   boolean createBaseSelectStatement(
      FndPersistentView view,
      FndCompoundAttribute viewContainer,
      SelectStatementContext stmtCtx,
      FndAutoString sql,
      String fndOrderBy,
      FndAutoString sqlOrderBy,
      FndConnection c)
   throws IfsException {

      stmtCtx.joinableAggregates = util.findJoinableAggregates(view, c);

      FndAutoString from = new FndAutoString();
      FndAutoString where = new FndAutoString();

      sql.append("SELECT ");
      appendSelectColumnList(null, view, viewContainer, stmtCtx, sql, from, where, stmtCtx.attrPos, c);
      if (sql.length() > 7) {
         sql.append("\n FROM ");
         sql.append(from);

         boolean conditionAdded = where.length() > 0;
         if (conditionAdded) {
            sql.append("\n WHERE ");
            sql.append(where);
         }

         if (fndOrderBy != null) {
            FndSort.OrderBy parsedOrderBy = FndSort.parseOrderByClause(fndOrderBy);
            FndAttribute[] orderByAttr = new FndAttribute[parsedOrderBy.getAttributeCount()];
            String[][] path = parsedOrderBy.getAttributePaths();
            FndSort.Direction[] direction = parsedOrderBy.getSortDirections();

            // try to fill all elements of orderByAttr array
            for (int i = 0; i < orderByAttr.length; i++) {
               FndAttribute attr = FndSort.findAttribute(view, path[i]);
               if (attr != null) {
                  FndAbstractRecord parent = attr.getParentRecord();
                  if (view.equals(parent)) {
                     // simple attribute in the main view --> check if it is persistent
                     if (!util.isQueryableColumn(attr.getMeta())) {
                        attr = null;
                     }
                  }
                  else {
                     FndCompoundAttribute container = parent.getContainer();
                     if (container instanceof FndAbstractAggregate) {
                        // nested aggregate --> check if it can be joined
                        if (!stmtCtx.joinableAggregates.contains(container)) {
                           attr = null;
                        }
                     }
                  }
               }
               if (attr == null) {
                  return conditionAdded; // select without order-by clause (sqlOrderBy is empty)
               }
               else {
                  orderByAttr[i] = attr;
               }
            }

            // convert orderByAttr to sqlOrderBy
            for (int i = 0; i < orderByAttr.length; i++) {
               FndAttribute attr = orderByAttr[i];
               if (i > 0) {
                  sqlOrderBy.append(", ");
               }
               if (attr.getMeta().isDerived()) {
                  sqlOrderBy.append(attr.getName()); // acts as alias for PLSQL function call in select list
               }
               else {
                  util.appendColumnName(attr, sqlOrderBy, true);
               }
               if (direction[i] == FndSort.DESCENDING) {
                  sqlOrderBy.append(" DESC");
               }
            }
         }

         return conditionAdded;
      }
      else {
         sql.clear(); // no columns included
         return false;
      }
   }

   /**
    * Fetch next row in result set.
    * @param result ResultSet to fetch data from
    * @param stmtCtx active select statement context
    * @param inRecord Used to get include/exclude flags on attributes
    * @return result record (same record type as inRecord) or null if no result
    */
   FndAbstractRecord fetch(FndResultSet result, SelectStatementContext stmtCtx, FndAbstractRecord inRecord) throws IfsException {
      try {
         if (result.next()) {
            FndDebug.increaseSqlFetchCounter(1);
            FndAbstractRecord outRecord = inRecord.newInstance();
            fetchAttributes(stmtCtx.stmt, inRecord, outRecord, result, new Counter(), stmtCtx.attrPos.iterator());
            if (log.debug) {
               log.debug("Row fetched");
            }
            return outRecord;
         }
         else {
            if (log.debug) {
               log.debug("No more rows");
            }

            //ResultSet will be automatically closed by the statement that created it
            return null;
         }
      }
      catch (SQLException e) {
         FndStatement stmt = stmtCtx.getStatement();
         if(stmt != null)
            stmt.reportSqlError(e);
         throw new SystemException(e, Texts.FETCHFAIL2, e.getMessage().trim());
      }
   }

   /**
    * Fetch current row and details in result set.
    * @param result ResultSet to fetch data from
    * @param c
    * @param stmtCtx active select statement context
    * @param condition Used to get include/exclude flags on attributes
    * @return result record (same record type as inRecord) or null if no result
    * @throws IfsException
    */
   protected FndAbstractRecord fetchCurrent(FndResultSet result, FndConnection c, SelectStatementContext stmtCtx, FndBaseEntityView condition) throws IfsException {
      try {
         context.serverContext.setStorageContext(context);

         FndDebug.increaseSqlFetchCounter(1);
         FndAbstractRecord outRecord = condition.newInstance();
         fetchAttributes(stmtCtx.stmt, condition, outRecord, result, new Counter(), stmtCtx.attrPos.iterator());
         if (log.debug) {
            log.debug("Row fetched");
         }

         //Loop over result and fetch all compound attributes
         FndPersistentView view = (FndPersistentView) outRecord;
         queryCompoundAttributes(stmtCtx, c, condition, view);

         outRecord.setState(FndRecordState.QUERY_RECORD);
         context.serverContext.setStorageContext(null);
         return outRecord;
      }
      catch (SQLException e) {
         context.serverContext.setStorageContext(null);
         throw new SystemException(e, Texts.FETCHFAIL2, e.getMessage().trim());
      }
   }

   /**
    * Fetch attributes for one joined record.
    * @param stmt active FndStatement
    * @param inRecord Used to get include/exclude flags on attributes
    * @param result ResultSet to fetch data from
    * @param colNr next column to be fetched from the result set
    * @param attrPosList list with attribute positions in the joined record
    * @return true if the corresponding joined database row exists in the database, false otherwise
    */
   private boolean fetchAttributes(
      FndStatement stmt,
      FndAbstractRecord inRecord,
      FndAbstractRecord outRecord,
      FndResultSet result,
      Counter colNr,
      Iterator attrPosList)
      throws IfsException, SQLException {

      boolean existentRow = true; // will be set to false if NULL is fetched into a not-null attribute
      FndAttribute sourceAttr, destAttr;

      checkAttributes(inRecord, outRecord);

      while (attrPosList.hasNext()) {
         int attrPos = ((Integer) attrPosList.next());
         sourceAttr = inRecord.getAttribute(attrPos);
         destAttr = outRecord.getAttribute(attrPos);
         if (sourceAttr.isCompound()) {
            // fetch nested joined record
            FndAbstractRecord nestedInRec = FndAttributeInternals.internalGetRecord((FndAbstractAggregate) sourceAttr);
            List list = (List) attrPosList.next();
            FndAbstractRecord nestedOutRec = nestedInRec.newInstance();
            FndAttributeInternals.internalSetRecord((FndAbstractAggregate) destAttr, nestedOutRec);
            if (!fetchAttributes(stmt, nestedInRec, nestedOutRec, result, colNr, list.iterator())) {
               destAttr.setNonExistent();
            }
         }
         else {
            FndQueryResultCategory category = sourceAttr.getResultCategory();
            colNr.value++;
            if (category == FndQueryResultCategory.COUNT)
               FndAttributeInternals.setCount(destAttr, stmt.getInt(colNr.value));
            else {

               // Copy reference to FndOutputStreamManager from source attribute to destination attribute
               if(sourceAttr instanceof FndBinary) {
                  FndBinary srcBin = (FndBinary) sourceAttr;
                  FndOutputStreamManager outputMgr = srcBin.getOutputStreamManager();
                  if(outputMgr != null) {
                     FndBinary destBin = (FndBinary) destAttr;
                     destBin.setOutputStreamManager(outputMgr);
                     FndServerContext.getCurrentServerContext().addStreamManager(outputMgr);
                  }
               }

               FndAttributeInternals.setSqlValue(destAttr, stmt, colNr.value);
               if (stmt.resultWasNull()) {
                  destAttr.setNull();
                  //
                  // outRecord was outer-joined through its parent key. NULL fetched into
                  // the parent key means that outRecord does not exist in the database
                  //
                  if (existentRow) {
                     FndCompoundReference parentKey = outRecord.getParentKey();
                     // parentKey == null for the main record, which is not not outer-joined
                     if (parentKey != null && parentKey.contains(destAttr))
                        existentRow = false;
                  }
               }
            }
         }
      }

      if (log.debug)
         log.debug("&1 -> &2", inRecord.getName(), String.valueOf(existentRow));

      return existentRow;
   }

   /**
    * Verify that the number of attributes in a record passed to query/get matches
    * the number of attributes in a new record of the same type.
    * The condition record (inRecord) may have more attributes than the new record (outRecord)
    * if all extra attributes are excluded from query.
    */
   private void checkAttributes(FndAbstractRecord inRecord, FndAbstractRecord outRecord) throws SystemException {

      int inCount = inRecord.getAttributeCount();
      int outCount = outRecord.getAttributeCount();

      if(inCount <= outCount)
         return;

      for(int i = outCount; i < inCount; i++) {
         if(!inRecord.getAttribute(i).isExcluded()) {

            // set for unexpected attributes used to format an error message
            Set<String> set = Util.newHashSet();

            // add included IN-attributes to the set
            for(int k = 0; k < inCount; k++) {
               FndAttribute attr = inRecord.getAttribute(k);
               if(!attr.isExcluded()) {
                  set.add(attr.getName());
               }
            }

            // remove all OUT-attributes from the set
            for(int k = 0; k < outCount; k++) {
               FndAttribute attr = outRecord.getAttribute(k);
               set.remove(attr.getName());
            }

            if(log.debug) {
               log.debug("Unexpected attributes found in condition-record.");
               log.debug("Condition-record:");
               log.debug(FndDebug.formatDebugRecord(inRecord));
               log.debug("New-record:");
               log.debug(FndDebug.formatDebugRecord(outRecord));
            }

            throw new SystemException(Texts.FETCHATTRCOUNT,inRecord.getClass().getName(), String.valueOf(inRecord.getAttributeCount()), String.valueOf(outRecord.getAttributeCount()), set.toString());
         }
      }
   }

   void queryCompoundAttributes(
      SelectStatementContext rootStmtCtx,
      FndConnection c,
      FndPersistentView conditionView,
      FndPersistentView resultView)
   throws IfsException {
      //Key is used as a hash key to cached statements.
      //Will contain complete path to the detail record being queried.
      context.stmtCacheKey.push(conditionView.getName());

      FndAttribute.Iterator condDetails = conditionView.details();
      FndAttribute.Iterator resultDetails = resultView.details();
      while (condDetails.hasNext() && resultDetails.hasNext()) {
         FndAttribute attr = condDetails.next();
         FndAttribute resAttr = resultDetails.next();

         FndQueryStorage detailStorage = attr instanceof FndQueryStorage ? (FndQueryStorage) attr : null;
         FndCompoundAttribute     condContainer = ((FndCompoundAttribute) attr);
         FndCompoundAttributeMeta metaContainer = condContainer.getCompoundMeta();
         FndCompoundReferenceMeta metaParentKeyInElement = metaContainer.getParentKeyInElement();
         if(metaParentKeyInElement == null)
            continue; // skip non-persitent array/aggregate

         if (attr.isVector() && !attr.isExcluded()) {
            //Array
            FndAbstractRecord rec;
            FndAbstractArray resultArr = (FndAbstractArray) resAttr;
            FndAbstractArray condArr = (FndAbstractArray) attr;
            if (condArr.isNull()) {
               //Create empty record (no search conditions)
               rec = condArr.newRecord();
               rec.setNonExistent();
               FndAttributeInternals.internalAdd(condArr, rec);
            }
            else {
               //Retreive search conditions from first element
               rec = FndAttributeInternals.internalGet(condArr, 0);
            }

            //Process only persistent views
            if (rec.isPersistent()) {
               FndPersistentView view = (FndPersistentView) rec;
               if(!FndStorageInstallationCache.isTableInstalled(view, c)) {
                  util.markNotInstalledCompoundAttribute(resultArr, view, "query");
               }
               else {
                  context.stmtCacheKey.push(attr.getName());
                  List<FndAbstractRecord> resultList;

                  if (detailStorage != null && view instanceof FndBaseEntityView) {
                     context.conditionContainer = condContainer;
                     context.metaParentKeyInElement = metaParentKeyInElement;
                     context.parentKeyInParent = resultArr.getParentKeyInParent();
                     FndAbstractArray outArr = detailStorage.query(new FndQueryRecord(view));
                     context.conditionContainer = null;
                     context.metaParentKeyInElement = null;
                     context.parentKeyInParent = null;
                     resultList = FndAttributeInternals.getInternalRecords(outArr);
                     if (resultList.size() > 0)
                        FndAttributeInternals.load(resultArr, resultList);
                     else
                        resultArr.setNonExistent();
                  }
                  else {
                     Container resultContainer = new Container(resultArr);
                     queryDetailView(c, view, condContainer, metaParentKeyInElement, resultArr.getParentKeyInParent(), resultContainer);
                     if (resultContainer.size() == 0)
                        resultArr.setNonExistent();
                  }

                  context.stmtCacheKey.pop();
               }
            }
         }
         else if (attr.isCompound() && !attr.isExcluded()) {

            //Aggregate
            FndAbstractAggregate resultAgg = (FndAbstractAggregate) resAttr;
            //Retreive search conditions from element
            FndAbstractRecord rec = FndAttributeInternals.internalGetRecord((FndAbstractAggregate) attr);

            //Only process persistent views
            if (rec != null && rec.isPersistent()) {
               FndPersistentView view = (FndPersistentView) rec;
               if(!FndStorageInstallationCache.isTableInstalled(view, c)) {
                  util.markNotInstalledCompoundAttribute(resultAgg, view, "query");
               }
               else {
                  context.stmtCacheKey.push(attr.getName());
                  List resultList;

                  if (rootStmtCtx.getJoinedAggregate((FndAbstractAggregate) attr) != null) {
                     //
                     // Result record has already been fetched by join operation
                     // Query compound attributes of the joined record
                     //
                     if (resultAgg.exist()) {
                        FndPersistentView detailView = (FndPersistentView) FndAttributeInternals.internalGetRecord(resultAgg);
                        queryCompoundAttributes(rootStmtCtx, c, view, detailView);
                     }
                  }
                  else {
                     if (detailStorage != null && view instanceof FndBaseEntityView) {
                        context.conditionContainer = condContainer;
                        context.metaParentKeyInElement = metaParentKeyInElement;
                        context.parentKeyInParent = resultAgg.getParentKeyInParent();
                        FndAbstractArray outArr = detailStorage.query(new FndQueryRecord(view));
                        context.conditionContainer = null;
                        context.metaParentKeyInElement = null;
                        context.parentKeyInParent = null;
                        resultList = FndAttributeInternals.getInternalRecords(outArr);
                        if (resultList.size() > 0)
                           FndAttributeInternals.internalSetRecord(resultAgg, (FndAbstractRecord) resultList.get(0));
                        else
                           resultAgg.setNonExistent();
                     }
                     else {
                        Container resultContainer = new Container(resultAgg);
                        queryDetailView(c, view, condContainer, metaParentKeyInElement, resultAgg.getParentKeyInParent(), resultContainer);
                        if (resultContainer.size() == 0)
                           resultAgg.setNonExistent();
                     }
                  }
                  context.stmtCacheKey.pop();
               }
            }
         }
      }
      context.stmtCacheKey.pop();
   }

   private void queryDetailView(
      FndConnection c,
      FndPersistentView conditionView,
      FndCompoundAttribute conditionContainer,
      FndCompoundReferenceMeta metaParentKeyInElement,
      FndCompoundReference parentKeyInParent,
      Container resultContainer)
   throws IfsException {

         if(parentKeyInParent == null)
            throw new SystemException(Texts.NOPARENTKEYINPARENT, conditionContainer.getMeta().getFullName(),
                      metaParentKeyInElement == null ? "null" : metaParentKeyInElement.getName());

         if (!context.populateMode && parentKeyInParent.isNull()) //NULL in the search key will return no rows
            return;

         if(conditionView instanceof FndBaseEntityView)
            c = context.getConnection((FndBaseEntityView)conditionView);

         boolean alias = true; //Alias may be needed if there is a filter condition
         FndStatement stmt;
         List attrPos = null;
         FndAutoString sql = new FndAutoString(256);
         String bindKey = null; //Key used to retrieve result from query result cache
         boolean resultFoundInCache = false;

         // Create cache key from stmtCacheKey stack.
         // The statement cache is not used in populate-mode.
         // Different detail records with the same path (from the top level master record) may
         // have different include-flags thus resulting in different select statements.
         String stmtKey = null;
         if(!context.populateMode) {
            FndAutoString keyBuf = new FndAutoString();
            for (int i = 0; i < context.stmtCacheKey.size(); i++) {
               if(i > 0)
                  keyBuf.append('^');
               keyBuf.append((String) context.stmtCacheKey.get(i));
            }
            stmtKey = keyBuf.toString();
         }

         //Do a cache lookup
         SelectStatementContext cachedStmt = stmtKey==null ? null : context.stmtCache.get(stmtKey);

         if (cachedStmt == null) {
            //Statement not found in cache
            stmt = createStatement(c, conditionView);
            cachedStmt = new SelectStatementContext(stmt); // no order by
            boolean conditionAdded = createBaseSelectStatement(conditionView, conditionContainer, cachedStmt, sql, null, null, c);
            if (sql.length() == 0)
               return; //No columns included in select list

            if(context.populateMode) {
               boolean parentKeyExist = !parentKeyInParent.isNull();
               if(parentKeyExist)
                  util.appendParentKeyCondition(conditionView, metaParentKeyInElement, parentKeyInParent, sql, stmt, alias, !conditionAdded);
               boolean primaryKeyExist = !conditionView.getPrimaryKey().isNull();
               if(primaryKeyExist)
                  util.appendPrimaryKeyCondition(conditionView, stmt, sql, !(conditionAdded || parentKeyExist), alias, parentKeyExist);

               if(!parentKeyExist && !primaryKeyExist)
                  throw new IfsRuntimeException("No parent/primary key in queryDetailView() in populate-mode");
            }
            else {
               //Store the number of filter bind variables created before calling appendParentKeyCondition()
               cachedStmt.filterBindVariableCount = cachedStmt.stmt.getParameterCount();
               util.appendParentKeyCondition(conditionView, metaParentKeyInElement, parentKeyInParent, sql, stmt, alias, !conditionAdded);
               FndRecordMeta viewMeta = conditionView.getMeta();
               if (!context.isGetOperation) {
                  util.appendAdvancedConditions(conditionView, sql, stmt, false, alias, c);
                  util.appendSimpleConditions(conditionView, sql, stmt, false, alias);
               }
            }

            stmt.prepare(sql);
            stmt.setFetchSize(context.defaultFetchSize);
            if(stmtKey != null)
               context.stmtCache.add(stmtKey, cachedStmt);
         }
         else {
            //Statement found in cache
            stmt = cachedStmt.stmt;
            attrPos = cachedStmt.attrPos;

            if (log.debug)
               log.debug("Reusing cached statement &1", stmt.toString());

            //Check query result cache
            bindKey = util.serializeParentKeyCondition(conditionView, metaParentKeyInElement, parentKeyInParent);
            resultFoundInCache = cachedStmt.getResult(bindKey, resultContainer);

            if (resultFoundInCache) {
               if (log.debug)
                  log.debug("   Query result (&1 elements) found in cache for bind key [&2]", String.valueOf(resultContainer.size()), bindKey);
            }
            else {
               //Keep filter bind variables created before calling appendParentKeyCondition()
               stmt.clearParameters(cachedStmt.filterBindVariableCount);
               //Redefine parameters. The sql string constructed (sql argument) will be ignored.
               util.appendParentKeyCondition(conditionView, metaParentKeyInElement, parentKeyInParent, sql, stmt, alias, false);
            }
         }

         if (!resultFoundInCache) {
            stmt.executeQuery();

            //Fetch result and add result records to the result container
            FndResultSet result = stmt.getFndResult();
            int j = 0;
            for (;;) {
               FndAbstractRecord resultRec = fetch(result, cachedStmt, conditionView);
               if (resultRec == null)
                  break;
               resultContainer.add(resultRec);
            }
            try {
               if (result != null) {
                  result.close();
               }
            }
            catch (SQLException e) {
               throw new SystemException(e, Texts.RESULTSETCLOSE); //TODO:Should we really throw an exception here?
            }

            //Store the result container in the query result cache
            if (bindKey == null)
               bindKey = util.serializeParentKeyCondition(conditionView, metaParentKeyInElement, parentKeyInParent);
            cachedStmt.putResult(bindKey, resultContainer);
         }

         //Loop over result and fetch all compound attributes
         int size = resultContainer.size();
         for (int i = 0; i < size; i++) {
            FndPersistentView view = resultContainer.get(i);
            queryCompoundAttributes(cachedStmt, c, conditionView, view);
         }
   }

   /** Class used to cache statements for detail queries.
    */
   private static class StatementCache {
      private Map<String,SelectStatementContext> cache = new HashMap<>(10);

      public StatementCache() {
      }

      /**
       * Close all cached statements.
       */
      public void close() {
         Iterator iterator = cache.values().iterator();
         while (iterator.hasNext()) {
            try {
               SelectStatementContext cs = (SelectStatementContext) iterator.next();
               cs.stmt.close();
            }
            catch (SystemException e) {
               Logger tmpLog = LogMgr.getDatabaseLogger();
               if(tmpLog.debug)
                  tmpLog.debug("Failed closing statement. Error ignored: &1", e.toString());
            }
         }
      }

      /** Add a statement to the cache.
       * @param key Statement identifier.
       * @param stmt Statement to cache.
       */
      public void add(String key, SelectStatementContext stmt) {
         cache.put(key, stmt);
      }

      public SelectStatementContext get(String key) {
         return (SelectStatementContext) cache.get(key);
      }
   }

   /**
    * Class containing cached FndStatement together with its context.
    * The context contains fetched attribute position list, query result cache per bind-key
    * and joinable-aggregate set.
    */
   public static class SelectStatementContext {
      FndStatement stmt;
      List<? super Object> attrPos;
      Map<String,Container> resultCache;
      int filterBindVariableCount;

      /**
       * Set with aggregate attributes that can be fetched using SQL join operation.
       */
      private Set joinableAggregates;

      SelectStatementContext(FndStatement stmt) {
         this.stmt = stmt;
         this.resultCache = new HashMap<>();
         this.attrPos = new ArrayList<>();
      }

      FndStatement getStatement() {
         return stmt;
      }

      /**
       * Store query result in the result cache.
       */
      void putResult(String bindKey, Container result) throws SystemException {
         try {
            resultCache.put(bindKey, (Container)result.clone()); // make snapshot of the result container
         }
         catch (CloneNotSupportedException e) {
            throw new SystemException(e, Texts.RESULTCACHECLONE);
         }
      }

      /**
       * Retrieve query result from the result cache into given Container.
       * @return true if the result was found in cache, false otherwise
       */
      boolean getResult(String bindKey, Container result) throws SystemException {
         Container cachedResult = (Container) resultCache.get(bindKey);
         if (cachedResult == null)
            return false;

         try {
            int size = cachedResult.size();
            for (int i = 0; i < size; i++) {
                 result.add((FndAbstractRecord) cachedResult.get(i).clone());
             }
         }
         catch (CloneNotSupportedException e) {
            throw new SystemException(e, Texts.RESULTCACHECLONE);
         }

         return true;
      }

      /**
       * Return a nested (aggregated) record that can be queried using SQL join operation.
       * Return null if join cannot be used.
       */
      FndPersistentView getJoinedAggregate(FndAbstractAggregate aggr) {
         if (joinableAggregates.contains(aggr))
            return (FndPersistentView) FndAttributeInternals.internalGetRecord(aggr);
         else
            return null;
      }
   }

   /**
    * Common interface for accessing arrays and aggregates.
    * Used by QueryDetailView.
    */
   private static class Container implements Cloneable {

      private int size = 0;
      private FndAbstractArray arr;
      private FndAbstractAggregate agg;

      Container(FndAbstractArray arr) {
         this.arr = arr;
      }

      Container(FndAbstractAggregate agg) {
         this.agg = agg;
      }

      void add(FndAbstractRecord rec) {
         size++;
         if (arr != null)
            FndAttributeInternals.internalAdd(arr, rec);
         else
            FndAttributeInternals.internalSetRecord(agg, rec);
      }

      int size() {
         return size;
      }

      FndPersistentView get(int i) {
         if (arr != null)
            return (FndPersistentView) FndAttributeInternals.internalGet(arr, i);
         else
            return (FndPersistentView) FndAttributeInternals.internalGetRecord(agg);
      }

      @Override
      public Object clone() throws CloneNotSupportedException {
         Container c = (Container) super.clone(); //Shallow copy

         if (c.arr != null)
            c.arr = (FndAbstractArray) c.arr.clone();
         else if (c.agg != null)
            c.agg = (FndAbstractAggregate) c.agg.clone();

         return c;
      }
   }

   /**
    * Class encapsulating an integer counter.
    */
   public static class Counter {
      private int value = 0;
   }

}
