/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.storage;

import ifs.fnd.base.IfsException;
import ifs.fnd.record.*;

/**
 * <B>Framework internal class:</B> Classes that implement entity storage handling should implement this interface.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public interface FndStorage {

   /**
    * Get several entity instances in one call (same as doing multiple gets).
    * @param arr array of entities to get (primary key value set on each record)
    * @param result array holding the result
    * @throws IfsException if the call fails for some reason
    */
   void bulkGet(FndAbstractArray arr, FndAbstractArray result) throws IfsException;

   /**
    * Save several entities in one call (same as doing multiple saves).
    * @param arr array of entity instances to save
    * @return array of saved entities
    * @throws IfsException if the call fails for some reason
    */
   FndAbstractArray bulkSave(FndAbstractArray arr) throws IfsException;

   /**
    * Update state of several entities in one call (same as doing multiple saves).
    * This method is only used for entities with an associated state machine when the entity
    * state should be modified.
    * @param arr array of entity instances to update
    * @param targetState new entity state
    * @return array of updated entities
    * @throws IfsException if the call fails for some reason
    */
   FndAbstractArray bulkSave(FndAbstractArray arr, FndEntityState.Enum targetState) throws IfsException;

   /**
    * Update a set of entities with the same attribute values.
    * The method performs a query based on the condition record and then
    * updates every entity in the result set with the attribute values defined in the value record.
    * @param value record defining attribute values to set
    * @param condition record with query condition defining the entity set to be updated
    * @param targetState used to modify the entity state attribute for native entities
   * @throws IfsException if the call fails for some reason
    */
   void batchSave(FndBaseEntityView value, FndBaseEntityView condition, FndEntityState.Enum targetState) throws IfsException;

   /**
    * Check for existence of a specific entity instance.
    * @param rec entity to check (primary key attribute should have a value)
    * @return <code>true</code> if the instance exists in the database
    * @throws IfsException if the call fails for some reason.
    */
   boolean exist(FndBaseEntityView rec) throws IfsException;

   /**
    * Get a single entity instance.
    * @param rec entity to get (primary key attribute should be set)
    * @return the entity (if found)
    * @throws IfsException if the call fails for some reason (or if the instance does not exist)
    */
   FndBaseEntityView get(FndBaseEntityView rec) throws IfsException;

   /**
    * Query an entity type.
    * @param record Query record with query conditions
    * @param result Array of found instances matching the conditions
    * @throws IfsException if the call fails for some reason
    */
   void query(FndQueryRecord record, FndAbstractArray result) throws IfsException;

   /**
    * Save an entity instance (create, update or remove depending on record state).
    * @param rec instance to save
    * @throws IfsException if the call fails for some reason
    */
   void save(FndBaseEntityView rec) throws IfsException;

   /**
    * Update an entity instance and update entity state.
    * This method is only used for entities with an associated state machine when the entity
    * state should be modified.
    * @param rec instance to save
    * @param targetState new entity state
    * @throws IfsException if the call fails for some reason
    */
   void save(FndBaseEntityView rec, FndEntityState.Enum targetState) throws IfsException;

   /**
    * Get default values for an entity (aka. prepare).
    * Only records marked as 'defaulting' will have default values set.
    * @param rec instance to prepare
    * @throws IfsException if the call fails for some reason
    */
   void getDefaults(FndBaseEntityView rec) throws IfsException;

   /**
    * Populate included parts of an entity with data.
    * @param view record to populate
    * @return the entity populated with data
    * @throws IfsException is there is a problem populating the entity
    */
   FndBaseEntityView populate(FndBaseEntityView view) throws IfsException;

   /**
    * Stores the current state of an entity in the database.
    * @param view record to import
    * @throws IfsException is there is a problem importing the entity
    */
   void importEntity(FndBaseEntityView view) throws IfsException;
}
