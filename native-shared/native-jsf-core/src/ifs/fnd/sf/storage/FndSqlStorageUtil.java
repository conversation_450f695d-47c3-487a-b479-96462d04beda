/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.storage;

import ifs.fnd.base.*;
import ifs.fnd.internal.FndAttributeInternals;
import ifs.fnd.log.Logger;
import ifs.fnd.record.*;
import ifs.fnd.record.serialization.FndAutoString;
import ifs.fnd.service.SQLRecognizer;
import ifs.fnd.service.Util;
import ifs.fnd.services.plsqlserver.AttributeString;
import ifs.fnd.sf.FndServerContext;
import ifs.fnd.util.Str;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringTokenizer;

/**
 * Utility class used by FndSqlStorage operations.
 */
class FndSqlStorageUtil {

   /**
    * Oracle date formats are used in generation of SQL text for context parametrs used in filters.
    * They correspond to format used by FndAttribute.toString() for date attributes.
    */
   static final String ORACLE_DATE_FORMAT      = "yyyy-mm-dd";
   static final String ORACLE_TIME_FORMAT      = "hh24.mi.ss";
   static final String ORACLE_TIMESTAMP_FORMAT = "yyyy-mm-dd-hh24.mi.ss";

   /**
    * Propagation result constants describe the result of key propagation from/to parent record.
    */
   static final int DEST_MODIFIED         = 1;
   static final int DEST_MODIFIED_TO_NULL = 2;
   static final int DEST_UNMODIFIED       = 3;

   /**
    * Oracle limitation. Maximum number of parameters inside IN clause is 1000.
    */
   private static final int MAX_PARAM_SIZE = 1000; 
         
   /**
    * Owner of this instance of FndSqlStorageUtil.
    */
   private final FndSqlStorage storage;

   private final FndAutoString tmpBuf = new FndAutoString();

   /**
    * Database logger used for debug output.
    */
   private final Logger log;

   /**
    * Constructs a FndSqlStorageUtil instance owned by (accessed from) specified FndSqlStorage.
    */
   FndSqlStorageUtil(FndSqlStorage storage, Logger log) {
      this.storage = storage;
      this.log = log;
   }

   /**
    * Return true if the specified view is an independent entity to be saved.
    * An independent entity is the root of the master-detail structure to be saved
    * or an entity which is not dependent on the master record. A dependent
    * entity is an entity linked to the master record by a reference pointing to
    * the primary key of the master record. All other entities are independent.
    * @return true if the specified view is an independent entity, false otherwise
    */
   boolean isIndependentEntity(FndBaseEntityView view) {
      FndCompoundAttribute container = view.getContainer();
      return container==null ? true : !container.containsDependentDetails();
   }

   /**
    * Check if this is a persistent long attribute that should be updated.
    */
   boolean isDirtyLob(FndAttribute attr, boolean create) {
      FndAttributeMeta meta = attr.getMeta();
      boolean isNull = attr.isNull();
      if(isNull && attr instanceof FndBinary && ((FndBinary)attr).getInputStreamManager() != null)
         isNull = false;
      return !attr.isCompound() && meta.isPersistent() && attr.isLong() && (attr.isDirty() || create) && !isNull;
   }

   private boolean isNullDirtyLob(FndAttribute attr) {
      FndAttributeMeta meta = attr.getMeta();
      return !attr.isCompound() && meta.isPersistent() && attr.isLong() && attr.isDirty() && attr.isNull();
   }

   /**
    * Append a argument placeholder to a SQL string.
    * @param sql SQL string
    */
   void appendPlaceHolder(FndAutoString sql) {
      sql.append("?");
   }

   /**
    * Return the table alias for a record.
    * @param obj record or object corresponding to a local alias in a filter
    * @return table alias
   */
   String getTableAlias(Object obj) {
      Map<Object, String> map = storage.context.tableAliases();
      String alias = map.get(obj);
      if(alias == null) {
         map.put(obj, alias = "a" + (map.size() + 1));
      }
      return alias;
   }

   /**
    * Append a LOB table name for an LU-view.
    */
   void appendLobTableName(FndLUEntityView rec, FndAutoString sql) {
      String appOwner = storage.context.appOwner();
      if(appOwner != null) {
         sql.append(appOwner);
         sql.append('.');
      }
      sql.append(rec.getLobTable());
   }

   /**
    * Append a table name for a persistent view.
    */
   void appendTableName(FndPersistentView rec, FndAutoString sql) {
      String appOwner = storage.context.appOwner();
      if(rec instanceof FndAdvancedQueryView) {
         sql.append(Str.replace(rec.getTable(), "&AO", appOwner));
      }
      else {
         sql.append(appOwner);
         sql.append('.');
         sql.append(rec.getTable());
      }
   }

   /**
    * Append a table name for a persistent view optionally adding table alias.
    */
   void appendTableName(FndPersistentView inRecord, FndAutoString sql, boolean alias) {
      appendTableName(inRecord, sql);
      if (alias) {
         sql.append(' ');
         sql.append(getTableAlias(inRecord));
      }
   }

   /**
    * Append a PL/SQL package name.
    */
   void appendPlsqlPackageName(FndLUEntityView rec, FndAutoString sql) {
      appendPlsqlPackageName(rec.getMeta().getPlsqlPackage(), sql);
   }

   void appendPlsqlPackageName(String packageName, FndAutoString sql) {
      String appOwner = storage.context.appOwner();
      if(appOwner != null) {
         sql.append(appOwner);
         sql.append('.');
      }
      sql.append(packageName);
   }

   /** Append column for an attribute.
    *  @param attr attribute to append column for.
    *  @param sql SQL string.
    *  @param first indicates if this is the first column added.
    *  @param alias controls if table alias should be added.
    *  @param c active database connection
    */
   void appendSelectColumn(FndAttribute attr, FndAutoString sql, boolean first, boolean alias, FndConnection c) throws SystemException {
      FndQueryResultCategory category = attr.getResultCategory();

      if (!first)
         sql.append(", ");

      if (category == FndQueryResultCategory.INCLUDE) {
         appendColumnNameOrPlsqlFunction(attr, sql, alias);
      }
      else if (category == FndQueryResultCategory.EXCLUDE)
         throw new SystemException(Texts.COLEXCLUDED, attr.getName());
      else if (category != null) {
         //SUM, MIN, MAX, COUNT or AVG
         sql.append(category.toString());
         sql.append("(");
         appendColumnNameOrPlsqlFunction(attr, sql, alias);
         sql.append(")");
      }

      // append column alias for a derived attribute
      if (attr.getMeta().isDerived()) {
         sql.append(" ");
         sql.append(attr.getName());
      }
   }

   /**
    * Append the function for a derived attribute or column name for other attributes.
    * @param attr attribute to append column or function for.
    * @param sql SQL string.
    * @param alias controls if table alias should be added.
    */
   void appendColumnNameOrPlsqlFunction(FndAttribute attr, FndAutoString sql, boolean alias) throws SystemException {
      if (attr.getMeta().isDerived()) // && !(attr.getParentRecord() instanceof FndAdvancedQueryView))
         appendPlsqlFunction(attr, sql);
      else
         appendColumnName(attr, sql, alias);
   }

   /**
    * Append column name for an attribute optionally adding the table alias.
    */
   void appendColumnName(FndAttribute attr, FndAutoString sql, boolean alias) {
      if (alias)
         sql.append(getTableAlias(attr.getParentRecord()) + ".");

      appendColumnName(attr, sql);
   }

   /**
    * Append a column or derived attribute function (optionally surrounded by a SQL function) and a SQL operator.
    */
   private void appendAttributeWithOperator(FndAttribute attr, boolean isNull, FndAlpha op, String sqlFunction, FndAutoString sql, boolean alias, FndStatement stmt) throws SystemException {
      tmpBuf.clear();
      appendColumnNameOrPlsqlFunction(attr, tmpBuf, alias);

      if(sqlFunction != null && sqlFunction.startsWith("__")) {
         String column = tmpBuf.toString();
         tmpBuf.clear();
         appendSqlFunction(tmpBuf, stmt, column, attr, sqlFunction);
      }

      String column = tmpBuf.toString();

      if (isNull)
         sql.append(FndQueryOperator.toNullSqlString(op, column));
      else
         sql.append(FndQueryOperator.toSqlString(op, column));
   }

   /**
    * Append a column or derived attribute function optionally surrounded by a SQL function.
    */
   private void appendAttribute(FndAttribute attr, boolean ignoreCase, String sqlFunction, FndAutoString sql, boolean alias, FndStatement stmt) throws SystemException {
      tmpBuf.clear();
      appendColumnNameOrPlsqlFunction(attr, tmpBuf, alias);

      if (ignoreCase)
         sql.append("{fn lcase(");

      if(sqlFunction != null && sqlFunction.startsWith("__"))
         appendSqlFunction(sql, stmt, tmpBuf.toString(), attr, sqlFunction);
      else
         sql.append(tmpBuf);

      if (ignoreCase)
         sql.append(")}");
   }

   /**
    * Append a bind variable optionally surrounded by a SQL function.
    * If the attribute value is of String type then it may refer to one context parameter.
    * In such a case the referred value will be bounded rather then the attribute value.
    */
   private void appendBindVariable(FndAttribute value, boolean ignoreCase, String sqlFunction, FndAutoString sql, FndStatement stmt) throws SystemException {
      tmpBuf.clear();
      appendPlaceHolder(tmpBuf);
      value = resolveBindParameter(value);
      stmt.defineInParameter(value);

      if (ignoreCase)
         sql.append("{fn lcase(");

      if(sqlFunction != null && sqlFunction.startsWith("__"))
         appendSqlFunction(sql, stmt, tmpBuf.toString(), value, sqlFunction);
      else
         sql.append(tmpBuf);

      if (ignoreCase)
         sql.append(")}");
   }

   /**
    * Append a SQL function to a SQL text buffer.
    * Note! The implementation of this function is Oracle specific.
    * @param sql SQL text
    * @param column the first argument to the function like column, constant or placeholder
    * @param base attribute representing the type of the argument, if any
    * @param functionPattern SQL function pattern normally passed as state of an attribute in a query condition
    */
   private void appendSqlFunction(FndAutoString sql, FndStatement stmt, String column, FndAttribute base, String functionPattern) throws SystemException {
      if(functionPattern.startsWith("__NVL=")) {
         FndAttribute arg;
         try {
            arg = (FndAttribute)base.clone();
         }
         catch(CloneNotSupportedException e) {
            throw new SystemException(e, Texts.APPENDSQLFUNC, e.getMessage());
         }
         String argValue = functionPattern.substring(6);
         try {
            arg.parseString(argValue);
         }
         catch(ParseException e) {
            throw new SystemException(e, Texts.APPENDSQLFUNC, e.getMessage());
         }
         stmt.defineInParameter(arg);
         sql.append("NVL(");
         sql.append(column);
         sql.append(", ");
         appendPlaceHolder(sql);
         sql.append(")");
      }
      else if(functionPattern.startsWith("__SOUNDEX")) {
         sql.append("SOUNDEX(");
         sql.append(column);
         sql.append(")");
      }
      else
         throw new SystemException(Texts.APPENDSQLFUNCPAT, functionPattern);
   }

   /**
    * Append PLSQL function call for a derived attribute.
    */
   private void appendPlsqlFunction(FndAttribute attr, FndAutoString sql) throws SystemException {

      String plsql = attr.getMeta().getColumn();

      if(plsql == null || plsql.length()==0) {
         // return null for derived attribute that has no SQL implementation defined in the model
         plsql = "NULL";
      }
      else {
         // fix application owner prefix(es)
         plsql = prepareSqlText(plsql, storage.context.appOwner());

         // replace &0-alias with the actual alias
         String mainAlias = getTableAlias(attr.getParentRecord());
         plsql = Str.replace(plsql, "&0", mainAlias);
      }

      // append PLSQL function text
      sql.append(plsql);
   }

   /**
    * Append column name for an attribute.
    */
   void appendColumnName(FndAttribute attr, FndAutoString sql) {
      sql.append(attr.getMeta().getColumn());
   }

   /**
    * Generate a new value for a text/string attribute.
    * @param attr attribute to set new value on
    */
   void generateSystemKey(FndAttribute attr) {
      FndAttributeInternals.internalSetValue(attr, java.util.UUID.randomUUID().toString());
   }

   /**
    * Appends an attribute name and value to the attribute string.
    * This method skips the following attributes:
    *    non-existent attribute,
    *    compound attribute,
    *    non-persistent attribute,
    *    derived attribute,
    *    OBJ_ID attribute,
    *    OBJ_VERSION attribute.
    */
   void appendToAttributeString(AttributeString str, FndAttribute attr) {
      if (!attr.exist() || attr.isCompound() || !attr.getMeta().isPersistent()) return;
      if(attr.getMeta().isDerived()) {
         attr.setNonExistent();
         return;
      }
      String name = attr.getName();
      if ("OBJ_ID".equals(name) || "OBJ_VERSION".equals(name)) return;
      String column = attr.getMeta().getColumn();
      String value = FndAttributeInternals.toDatabaseString(attr);
      str.add(column, value);
   }

   /**
    * Parse an attribute string, returned by the server.
    * @param view view record (in/out)
    * @param str attribute string (in)
    * @param c active database connection
    */
   void convertAttributeStringToRecord(FndPersistentView view, String str, FndConnection c) throws IfsException {

      // create set with IID attributes found in the view
      Set<FndAttribute> iids = new HashSet<>();
      int attrCount = view.getAttributeCount();
      for(int i=0; i<attrCount; i++) {
         FndAttribute attr = view.getAttribute(i);
         if(attr instanceof FndEnumeration && !(attr instanceof FndEntityState) && attr.getMeta().getColumn() != null)
            iids.add(attr);
      }

      AttributeString attrStr = new AttributeString(str);

      while (true) {
         FndText i = attrStr.getNextItem();
         if (i == null) break;
         String name = i.getName();
         String val = i.getValue();
          FndAttribute attr;
          switch (name) {
              case "__OBJSTATE":
                  // __OBJSTATE has two destinations: OBJSTATE and ENTITYSTATE
                  attr = view.getAttribute("OBJSTATE");
                  if (attr != null) {
                      parseDatabaseString(attr, val);
                  }
                  attr = view.getAttribute("ENTITYSTATE");
                  if (attr != null) {
                      parseDatabaseString(attr, val);
                  }
                  break;
              case "__OBJEVENTS":
                  // __OBJEVENTS has one destination: OBJEVENTS
                  attr = view.getAttribute("OBJEVENTS");
                  if (attr != null) {
                      parseDatabaseString(attr, val);
                  }
                  break;
              default:
                  attr = getAttributeForColumn(view, name);
                  if (attr != null) {
                      // skip IID attributes
                      if (!iids.contains(attr)) {
                          //log.debug("--- attribute &1.&2 found for column &3", view.getName(), attr.getName(), i.getName());
                          parseDatabaseString(attr, val);
                      }
                  } else if (log.debug) {
                      log.debug("*** PLSQL attribute &1 not found in destination record &2",
                              i.getName(),
                              view.getName());
                  }
                  break;
          }
      }

      // try to retrieve a value for each IID attribute from the attribute string
      Iterator iter = iids.iterator();
      while(iter.hasNext()) {
         FndEnumeration iid = (FndEnumeration)iter.next();
         String col = iid.getMeta().getColumn().toUpperCase();
         String column, columnDb;
         if(col.endsWith("_DB")) {
            column = col.substring(0, col.length() - 3);
            columnDb = col;
         }
         else {
            column = col;
            columnDb = col + "_DB";
         }

         String valueDb = attrStr.getItemValue(columnDb);
         if(valueDb == null) {
            String value = attrStr.getItemValue(column);
            if(value == null)
               assert true;
            else if("".equals(value))
               valueDb = "";
            else {
               if(log.debug)
                  log.debug("*** IID attribute &1 found in attribute string. Retrieving corresponding &2 value from IID cache.",
                                 column,
                                 columnDb);

               // get valueDb from IID cache for the current language
               String iidPlsqlPackage = iid.getMeta().getIidPlsqlPackage();
               if(iidPlsqlPackage != null)
                  valueDb = FndIidCache.translateIidValue(iidPlsqlPackage, value, c);
            }
         }

         if(valueDb != null) {
            if("".equals(valueDb))
               iid.setNull();
            else
               FndAttributeInternals.parseDatabaseString(iid, valueDb);
         }
      }
   }

   private static void parseDatabaseString(FndAttribute attr, String val) throws ParseException {
      if(val==null || val.length()==0)
         attr.setNull();
      else
         FndAttributeInternals.parseDatabaseString(attr, val);
      attr.setExistent();
   }

   /**
    * Return attribute corresponding to given column.
    * @param view the record to search an attribute in
    * @param column the column name to match
    * @return found attribute that matches given column, or null if there is no such attribute
    */
   FndAttribute getAttributeForColumn(FndPersistentView view, String column) {
      int count = view.getAttributeCount();
      for (int i=0; i<count; i++) {
         FndAttribute attr = view.getAttribute(i);
         FndAttributeMeta meta = attr.getMeta();
         if(meta.isPersistent() && column.equals(meta.getColumn()))
            return attr;
      }
      return null;
   }

   /**
    * Add extra information to a thrown ValidationException (attribute causing the error).
    * @param e Original exception
    * @param view The view containing the offending attribute.
    * @throws ValidationException Exception will be re-thrown.
    */
   void addInfo(ValidationException e, FndPersistentView view) throws ValidationException {
      //Figure out which attribute is the cause
      String message = e.getMessage();
      int start = message.indexOf('[') + 1;
      int stop = message.indexOf(']');
      if(start>0 && stop>start) {
         String columnName = message.substring(start, stop);

         //Locate an attribute in view with storage in a column named columnName.
         FndAttribute attr = getAttributeForColumn(view, columnName);
         if (attr != null) {
            attr.addInvalidValueInfo(message);
            e.setExtraInfo(view);
         }
      }
      throw e; //Rethrow exception
   }

   /**
    * Add extra information to a thrown DatabaseException (view causing the error).
    * @param e Original exception
    * @param view The view causing the error.
    * @throws IfsException Exception will be rethrown.
    */
   void addInfo(DatabaseException e, FndPersistentView view) throws IfsException {
      e.setRecordName(view.getName());

      if(e.getErrorType() == DatabaseException.UNIQUE_CONSTRAINT) {
         String table = view.getMeta().getTable().toUpperCase();
         if(table != null && table.endsWith("_TAB"))
            table = table.substring(0, table.length() - 4);
         if(e.getMessage().indexOf(table + "_PK)") > 0)
            throw new ValidationException(view, Texts.DUPLICATEPK, view.getName(), getPrimaryKeyNameImage(view), getPrimaryKeyImage(view));
      }

      throw e; //Rethrow exception
   }

   /**
    * Check if update of an attribute is allowed.
    * Attributes that are part of the primary key should not be updateable.
    * Entity state attribute may not be modified.
    * The attribute will be marked with invalid value info if update is not allowed.
    * @param attr Attribute to check.
    */
   void checkUpdateAllowed(FndAttribute attr) {
      FndAttribute.Iterator pk = attr.getParentRecord().getPrimaryKey().iterator();
      while(pk.hasNext()) {
         FndAttribute keyattr = pk.next();
         if(attr.equals(keyattr))
            attr.invalidate(Texts.NOKEYMOD, attr.getMeta().getFullName());
      }
      if (attr instanceof FndEntityState)
         attr.invalidate(Texts.NOSTATEMOD, attr.getMeta().getFullName());
   }

   /**
    * Assigns the parent key from the parent record to the parent key in given detail record.
    * The method does nothing if given view is not an element of an array in a parent record.
    * @param view detail record to copy parent key values to
    * @return key propagation result constant (DEST_MODIFIED, DEST_MODIFIED_TO_NULL, DEST_UNMODIFIED)
    */
   int copyParentKeyFromParent(FndPersistentView view) throws IfsException {
      FndCompoundAttribute attr = view.getContainer();
      if(attr != null) {
         FndAbstractRecord parent = attr.getParentRecord();
         if(parent != null && parent.isPersistent()) {
            FndCompoundReference parentKeyInParent = attr.getParentKeyInParent();
            FndCompoundReference parentKeyInElement = view.getParentKey();
            if(parentKeyInParent==null || parentKeyInElement==null)
               throw new SystemException(Texts.COPYPKFROMPARENT, view.getName());
            return propagateReference(parentKeyInParent, parentKeyInElement, true);
         }
      }
      return DEST_UNMODIFIED;
   }

   /**
    * Assigns the parent key from given (detail) record to the parent key in the parent record.
    * The method does nothing if given view is not an element of an array in a parent record.
    * @param view detail record to copy parent key values from
    * @return key propagation result constant (DEST_MODIFIED, DEST_MODIFIED_TO_NULL, DEST_UNMODIFIED)
    */
   int copyParentKeyToParent(FndPersistentView view) throws IfsException {
      FndCompoundAttribute attr = view.getContainer();
      if(attr != null) {
         FndAbstractRecord parent = attr.getParentRecord();
         if(parent != null && parent.isPersistent()) {
            FndCompoundReference parentKeyInParent = attr.getParentKeyInParent();
            FndCompoundReference parentKeyInElement = view.getParentKey();
            if(parentKeyInParent==null || parentKeyInElement==null)
               throw new SystemException(Texts.COPYPKTOPARENT, view.getName());
            return propagateReference(parentKeyInElement, parentKeyInParent, false);
         }
      }
      return DEST_UNMODIFIED;
   }


   /**
    * Propagates values from one reference to another.
    * The argument fromParent controls the process in the following way:
    * <pre>
    *   fromParent==true (from master to detail)
    *      1. Propagation of keys does not make changed attributes dirty
    *      2. Non-existent source attributes are ignored
    *
    *   fromParent==false (from detail to master)
    *      1. Propagation of keys makes changed attributes dirty
    *      2. Non-existent source attributes nullify destination attributes
    * </pre>
    *
    * @param source compound reference to get attribute values from
    * @param dest   compound reference to set attribute values in
    * @param fromParent true for master-to-detail direction, false for detail-to-master
    * @return key propagation result constant (DEST_MODIFIED, DEST_MODIFIED_TO_NULL, DEST_UNMODIFIED)
    */
   private int propagateReference(FndCompoundReference source,
                                  FndCompoundReference dest,
                                  boolean fromParent) throws IfsException {

      boolean destModified = false;
      boolean destNull = true;
      int count = source.getAttributeCount();
      if(count != dest.getAttributeCount())
         throw new SystemException(Texts.PROPAGATEREFCOUNT, source.getFullName(), dest.getFullName());

      FndAttribute.Iterator sourceIterator = source.iterator();
      FndAttribute.Iterator destIterator   = dest.iterator();

      while(sourceIterator.hasNext()) {
         FndAttribute sourceAttr = sourceIterator.next();
         FndAttribute destAttr = destIterator.next();
         if(sourceAttr.getMeta().getType() != destAttr.getMeta().getType())
            throw new SystemException(Texts.PROPAGATEREFTYPE, source.getFullName(), dest.getFullName());

         Object sourceValue;

         if(sourceAttr instanceof FndGenericReference) {
            sourceValue = sourceAttr.toString();
         }
         else {
            if(fromParent && !sourceAttr.exist())
               continue;
            sourceValue = sourceAttr.exist() ? FndAttributeInternals.internalGetValue(sourceAttr) : null;
         }

         Object destValue = FndAttributeInternals.internalGetValue(destAttr);

         if(!destAttr.exist() || (destValue==null && sourceValue!=null) || (destValue!=null && !destValue.equals(sourceValue))) {
            FndAttributeInternals.internalSetValue(destAttr, sourceValue); // destAttr becomes dirty
            if(fromParent)
               destAttr.setDirty(false);
            destModified = true;
            if(sourceValue!=null)
               destNull = false;
         }
      }
      if(destModified)
         return destNull ? DEST_MODIFIED_TO_NULL : DEST_MODIFIED;
      else
         return DEST_UNMODIFIED;
   }


   /**
    * Sets and checks the primary key attributes.
    * This method first gets parent primary key values from the parent record (if the
    * second argument is true) and then verifies that all primary key attributes for
    * the specified record are set.
    * @param view persistent view
    * @param copyParentKey true if the parent key should be copied to the specified view
    */
   void preparePrimaryKey(FndPersistentView view, boolean copyParentKey) throws IfsException {
      if(copyParentKey)
         copyParentKeyFromParent(view);

      FndAttribute.Iterator pk = view.getPrimaryKey().iterator();
      while(pk.hasNext()) {
         pk.next().checkValuePresent(Texts.PREPAREPK);
   }
   }


   /**
    * Returns true if the specified array/aggregate represents a reference-to-reference relationship.
    */
   boolean isReferenceToReferenceRelationship(FndCompoundAttribute container) {
      if(container == null) {
         return false;
      }

      FndAttributeMeta meta = container.getMeta();
      if(!(meta instanceof FndCompoundAttributeMeta)) {
         return false; // Old array/aggregates cannot represent a reference-to-reference relationship
      }

      return ((FndCompoundAttributeMeta)meta).isReferenceToReferenceRelationship();
   }

   /**
    * Retrieve a Text out-parameter from a stored procedure call.
    */
   String getTextOutParameter(FndStatement stmt, int pos) throws IfsException {
      String value = stmt.getText(pos);
      if(log.debug)
         log.debug("   &1: &2", String.valueOf(pos), value);
      return value;
   }


   void appendParentKeyCondition(FndPersistentView inRecord,
                                 FndCompoundReferenceMeta ref,
                                 FndCompoundReference key,
                                 FndAutoString sql,
                                 FndStatement stmt,
                                 boolean alias,
                                 boolean first) throws SystemException {

      if (first)
         sql.append("\n WHERE ");
      else
         sql.append("\n AND ");

      int numRefs = ref.getAttributeCount();
      FndAttribute.Iterator keyIterator = key.iterator();

      for (int i = 0; i < numRefs; i++) {
         FndAttributeMeta meta = ref.getAttribute(i);
         if (i > 0)
            sql.append("\n AND ");
         if (alias)
            sql.append(getTableAlias(inRecord) + ".");

         sql.append(meta.getColumn()); //Append column name
         sql.append(" = ");
         appendPlaceHolder(sql);

         stmt.defineInParameter(keyIterator.next()); //getAttribute(i));
      }
   }

   /**
    * Serialize parent key condition into a bind-key used for lookup in query result cache.
    */
   String serializeParentKeyCondition(FndPersistentView inRecord,
                                      FndCompoundReferenceMeta ref,
                                      FndCompoundReference key) throws SystemException {

      FndAutoString buf = new FndAutoString();

      int numRefs = ref.getAttributeCount();
      FndAttribute.Iterator keyIterator = key.iterator();

      for (int i = 0; i < numRefs; i++) {
         FndAttributeMeta meta = ref.getAttribute(i);
         if (i > 0)
            buf.append((char)0);

         FndAttribute attr = keyIterator.next();
         buf.append(attr.toString());
      }

      return buf.toString();
   }

   /**
    * Appends parent key condition based on attributes that have a not-null value.
    */
   void appendNotNullParentKeyCondition(FndPersistentView view, FndAutoString sql, FndStatement stmt) throws SystemException {

      boolean first = true;
      FndAttribute.Iterator keyIterator = view.getParentKey().iterator();

      sql.append("\n WHERE ");

      while(keyIterator.hasNext()) {
         FndAttribute attr = keyIterator.next();
         if(attr.isNull()) continue;

         if(first)
            first = false;
         else
            sql.append("\n AND ");

         sql.append(attr.getMeta().getColumn()); //Append column name
         sql.append(" = ");
         appendPlaceHolder(sql);
         stmt.defineInParameter(attr);
      }
   }

   boolean appendSimpleConditions(FndPersistentView inRecord, FndAutoString sql, FndStatement stmt, boolean first, boolean alias) throws SystemException {
      boolean conditionAdded = false;
      int numAttrs = inRecord.getAttributeCount();

      for (int i = 0; i < numAttrs; i++) {
         FndAttribute attr = inRecord.getAttribute(i);
         //Do not generate condition for long data types (unless null)
         if (!attr.isLong() || !attr.isNull()) {
            if (!attr.isCompound() && attr.exist() && isQueryableColumn(attr.getMeta())) {
               conditionAdded = appendAttrCondition(attr, stmt, sql, first, alias, false);
               if (conditionAdded)
                  first = false;
            }
         }
      }
      return conditionAdded;
   }

   boolean appendAdvancedConditions(FndPersistentView inRecord, FndAutoString sql, FndStatement stmt, boolean first, boolean alias, FndConnection c) throws SystemException {
      boolean conditionAdded = false;
      FndCondition cond = inRecord.getCondition();
      if (cond != null) { //There are advanced conditions on this record
         if (first)
            sql.append("\n WHERE ");
         else
            sql.append("\n AND ");

         if (cond instanceof FndSimpleCondition)
            appendSimpleCondition((FndSimpleCondition)cond, inRecord, stmt, sql, alias);
         else if (cond instanceof FndDetailCondition)
            appendDetailCondition((FndDetailCondition)cond, inRecord, stmt, sql, alias, c);
         else
            appendCompoundCondition(cond, inRecord, stmt, sql, alias, c);

         conditionAdded = true;
      }

      return conditionAdded;
   }

   private void appendCompoundCondition(FndCondition cond, FndPersistentView inRecord, FndStatement stmt, FndAutoString sql, boolean alias, FndConnection c) throws SystemException {
      FndCondition left = (FndCondition)cond.left.getValue();
      FndCondition right = (FndCondition)cond.right.getValue();

      sql.append("(");
      //Left condition
      if (left instanceof FndSimpleCondition)
         appendSimpleCondition((FndSimpleCondition)left, inRecord, stmt, sql, alias);
      else if (left instanceof FndDetailCondition)
         appendDetailCondition((FndDetailCondition)left, inRecord, stmt, sql, alias, c);
      else
         appendCompoundCondition(left, inRecord, stmt, sql, alias, c);

      sql.append("\n " + cond.category.getValue() + " "); //Append operator (or/and)

      //Right condition
      if (right instanceof FndSimpleCondition)
         appendSimpleCondition((FndSimpleCondition)right, inRecord, stmt, sql, alias);
      else if (right instanceof FndDetailCondition)
         appendDetailCondition((FndDetailCondition)right, inRecord, stmt, sql, alias, c);
      else
         appendCompoundCondition(right, inRecord, stmt, sql, alias, c);
      sql.append(")");
   }

   private void appendSimpleCondition(FndSimpleCondition cond, FndPersistentView inRecord, FndStatement stmt, FndAutoString sql, boolean alias) throws SystemException {
      // check if the condition is of type attribute-value or attribute-attribute
      boolean isAttrAttrCondition = cond.isAttributeAttributeCondition();

      //First item is always the attribute name
      FndText nameAttr = (FndText)cond.getAttribute(0);
      String name = nameAttr.getValue();
      FndAttribute attr = inRecord.getAttribute(name);

      //Second item is a query operator
      FndAlpha op = (FndAlpha)cond.getAttribute(1);
      if (FndQueryOperator.isDomainOperator(op))
         throw new SystemException(Texts.DOMAINOP);

      // IN
      if (FndQueryOperator.isInOperator(op)) {
         //Third item is the number of arguments in in-list
         int count = ((FndInteger)cond.getAttribute(2)).getValue().intValue();
         
         if (count < MAX_PARAM_SIZE) {
            appendAttributeWithOperator(attr, false, op, nameAttr.getState(), sql, alias, stmt);
            sql.append("(");
           
            for (int i = 3; i < count + 3; i++) { //The rest are in-list values
               if (i > 3)
                  sql.append(", ");
               FndAttribute value = cond.getAttribute(i);
               if (value.isNull())
                  sql.append("NULL");
               else
                  appendBindVariable(value, false, value.getState(), sql, stmt);
            }

            if(count == 0)
               sql.append("NULL");

            sql.append(")");
         } else {    // Splitting to several IN clauses.
               sql.append("(");
               int noOfInClauses = (int)Math.floor(count/MAX_PARAM_SIZE);
               int i;
               
               for (int n = 1; n <= noOfInClauses; n++) {
                  if (n>1)
                     sql.append(") OR ");
                  appendAttributeWithOperator(attr, false, op, nameAttr.getState(), sql, alias, stmt);
                  sql.append("(");
                  i = (n==1)? 3 : (MAX_PARAM_SIZE*(n-1))+3;
                  
                  for (int j = i; j < (MAX_PARAM_SIZE*n)+3; j++) { 
                     if (j > i)
                        sql.append(", ");
                     FndAttribute value = cond.getAttribute(j);
                     if (value.isNull())
                        sql.append("NULL");
                     else
                        appendBindVariable(value, false, value.getState(), sql, stmt);
                  }
               }
               i = noOfInClauses*MAX_PARAM_SIZE;
               if (i < count) {
                  sql.append(") OR ");
                  appendAttributeWithOperator(attr, false, op, nameAttr.getState(), sql, alias, stmt);
                  sql.append("(");
               
                  for (int m = i+3; m < count+3; m++) { 
                  if (m > i+3)
                     sql.append(", ");
                  FndAttribute value = cond.getAttribute(m);
                  if (value.isNull())
                     sql.append("NULL");
                  else
                     appendBindVariable(value, false, value.getState(), sql, stmt);
                  }
               }
              sql.append("))");
         }
      }

      // BETWEEN
      else if (FndQueryOperator.isBetweenOperator(op)) {
         appendAttributeWithOperator(attr, false, op, nameAttr.getState(), sql, alias, stmt);

         if(isAttrAttrCondition) {
            FndAttribute name1 = cond.getAttribute(2);
            FndAttribute name2 = cond.getAttribute(3);
            FndAttribute operand1 = inRecord.getAttribute(name1.toString());
            FndAttribute operand2 = inRecord.getAttribute(name2.toString());
            validateAttributeAttributeCondition(attr, operand1);
            validateAttributeAttributeCondition(attr, operand2);
            appendAttribute(operand1, false, name1.getState(), sql, alias, stmt);
            sql.append(" AND ");
            appendAttribute(operand2, false, name2.getState(), sql, alias, stmt);
         }
         else {
            //Number of items in argument list (must be 2 for between condition)
            int count = ((FndInteger)cond.getAttribute(2)).getValue().intValue();
            if (count != 2)
               throw new SystemException(Texts.APPBETWEENCOND, String.valueOf(count));

            FndAttribute value = cond.getAttribute(3);
            appendBindVariable(value, false, value.getState(), sql, stmt);
            sql.append(" AND ");
            value = cond.getAttribute(4);
            appendBindVariable(value, false, value.getState(), sql, stmt);
         }
      }

      // Boolean expression
      else if(isAttrAttrCondition && attr instanceof FndBoolean) {
         FndAttribute operandName = cond.getAttribute(2);
         FndAttribute operand = inRecord.getAttribute(operandName.toString());
         validateAttributeAttributeCondition(attr, operand);
         appendBooleanAttributeAttributeCondition((FndBoolean)attr, nameAttr.getState(),
                                                   op,
                                                  (FndBoolean)operand, operandName.getState(),
                                                  sql, alias, stmt);
      }

      // LIKE with client-defined format mask on DATE/NUMBER attribute
      else if(!isAttrAttrCondition && FndQueryOperator.isLikeOperator(op) && (attr instanceof FndAbstractDate || attr instanceof FndAbstractNumber)) {

         //Number of items in argument list (must be 2: likePattern and formatMask)
         int count = ((FndInteger)cond.getAttribute(2)).getValue().intValue();
         if (count != 2)
            throw new SystemException(Texts.DATNUMLIKECOND, String.valueOf(count));

         FndAttribute likePattern = cond.getAttribute(3);
         FndAttribute formatMask = cond.getAttribute(4);

         sql.append(" TO_CHAR(");
         appendColumnNameOrPlsqlFunction(attr, sql, alias);
         if(!formatMask.isNull()) {
            sql.append(", ");
            appendBindVariable(formatMask, false, formatMask.getState(), sql, stmt);
         }
         sql.append(")");
         sql.append(FndQueryOperator.toSqlString(op, ""));
         appendBindVariable(likePattern, false, likePattern.getState(), sql, stmt);
      }

      // Other (simple) operators
      else {
         //Third item is the value (or operand name)
         FndAttribute value = cond.getAttribute(2);
         boolean valueIsNull = value.isNull();
         boolean ignoreCase = FndQueryOperator.isIgnoreCaseOperator(op);
         appendAttributeWithOperator(attr, valueIsNull, op, nameAttr.getState(), sql, alias, stmt);
         if (!valueIsNull) {
            if(isAttrAttrCondition) {
               FndAttribute operand = inRecord.getAttribute(value.toString());
               validateAttributeAttributeCondition(attr, operand);
               appendAttribute(operand, ignoreCase, value.getState(), sql, alias, stmt);
            }
            else {
               appendBindVariable(value, ignoreCase, value.getState(), sql, stmt);
            }
         }
      }
   }

   /**
    * Append SQL expression comparing two FndBoolean attributes.
    * The generated expression will return FALSE if any of columns has NULL value.
    * @param attr1 the first attribute to compare
    * @param oper FndAlpha attribute containing the value of the query operator (EQUAL or NOT_EQUAL)
    * @param attr2 the second attribute to compare
    */
   private void appendBooleanAttributeAttributeCondition(FndBoolean attr1, String function1,
                                                               FndAlpha oper,
                                                               FndBoolean attr2, String function2,
                                                               FndAutoString sql,
                                                               boolean alias,
                                                               FndStatement stmt)
   throws SystemException {
      //
      //  EQUAL:     ((A = 'YES' AND B = 1) OR (A = 'NO' AND B = 0))
      //  NOT_EQUAL: ((A = 'YES' AND B = 0) OR (A = 'NO' AND B = 1))
      //
      boolean equal = FndQueryOperator.isEqualOperator(oper);
      sql.append("((");
      appendAttribute(attr1, false, function1, sql, alias, stmt);
      sql.append(" = ");
      appendSqlImageOfBooleanValue(attr1, sql, true);
      sql.append(" AND ");
      appendAttribute(attr2, false, function2, sql, alias, stmt);
      sql.append(" = ");
      appendSqlImageOfBooleanValue(attr2, sql, equal);
      sql.append(") OR (");
      appendAttribute(attr1, false, function1, sql, alias, stmt);
      sql.append(" = ");
      appendSqlImageOfBooleanValue(attr1, sql, false);
      sql.append(" AND ");
      appendAttribute(attr2, false, function2, sql, alias, stmt);
      sql.append(" = ");
      appendSqlImageOfBooleanValue(attr2, sql, !equal);
      sql.append("))");
   }

   private void appendSqlImageOfBooleanValue(FndBoolean attr, FndAutoString sql, boolean value) {
      if(attr instanceof FndBooleanString) {
         sql.append("'");
         sql.append(((FndBooleanString)attr).toDatabaseString(value));
         sql.append("'");
      }
      else {
         sql.append(value ? "1" : "0");
      }
   }

   private void validateAttributeAttributeCondition(FndAttribute attr1, FndAttribute attr2) throws SystemException {

      // FndEnumeration, FndDate, FndTime, FndTimestamp
      if(attr1.getClass() == attr2.getClass())
         return;

      // FndNumber, FndInteger, FndDecimal
      if(attr1 instanceof FndAbstractNumber && attr2 instanceof FndAbstractNumber)
         return;

      // FndText, FndAlpha, FndGuid
      if(attr1 instanceof FndAbstractString && attr2 instanceof FndAbstractString)
         return;

      // FndBoolean, FndBooleanString
      if(attr1 instanceof FndBoolean && attr2 instanceof FndBoolean)
         return;

      throw new SystemException(Texts.QRYCONDATTRTYPE, attr1.getClass().getName(), attr2.getClass().getName());
   }

   private void appendDetailCondition(FndDetailCondition cond, FndPersistentView master, FndStatement stmt, FndAutoString sql, boolean alias, FndConnection c) throws SystemException {
      String masterAlias = null, detailAlias = null;
      FndPersistentView detail = cond.getDetail();
      if (alias) {
         masterAlias = getTableAlias(master);
         detailAlias = getTableAlias(detail);
      }
      sql.append(cond.getCategory().toSqlString()); //EXISTS or NOT EXISTS
      sql.append(" (SELECT 1 FROM ");
      appendTableName(detail, sql, alias);

      FndAutoString where = new FndAutoString();
      resolveFilters(detail, cond.getMetaContainer(), sql, where, c, stmt);
      sql.append("\n WHERE ");
      if(where.length() > 0) {
         sql.append("(");
         sql.append(where);
         sql.append(")\n AND ");
      }

      FndCompoundReferenceMeta ref = cond.getParentKeyInElement();
      FndCompoundReferenceMeta key = cond.getParentKeyInParent();
      //FndCompoundReferenceMeta key = master.getPrimaryKey().getMeta();

      /** Check if this condition is allowed.
       */
      if(ref.isPrimaryKey() && key.isPrimaryKey())
         assert true; // PK-PK connection cannot be checked in this way (because referents are different in new model)
      else if(ref.getReferent().getRootMeta()!=key.getReferent().getRootMeta())
         throw new SystemException(Texts.DETAILCONDERR, master.getName(), detail.getName());

      int count = key.getAttributeCount(); //ref and key have the same number of attributes

      for (int i=0; i<count; i++) {
         FndAttributeMeta keyMeta = key.getAttribute(i);
         FndAttributeMeta refMeta = ref.getAttribute(i);

         if (i>0)
            sql.append("\n AND ");

         if (alias)
            sql.append(masterAlias + ".");
         sql.append(keyMeta.getColumn());

         sql.append(" = ");

         if (alias)
            sql.append(detailAlias + ".");
         sql.append(refMeta.getColumn());

      }

      appendAdvancedConditions(detail, sql, stmt, false, alias, c);
      appendSimpleConditions(detail, sql, stmt, false, alias);
      sql.append(")");
   }

   /**
    * Append group by clause if necessary.
    * @param stmtCtx active statement context
    * @param inRecord Record used in query
    * @param sql SQL string
    * @param alias Indicates whether column alias should be used
    * @param c active database connection
    */
   void appendGroupBy(FndSqlStorage.SelectStatementContext stmtCtx, FndPersistentView inRecord, FndAutoString sql, boolean alias, FndConnection c) throws SystemException {

      // skip the costly generation of a GROUP BY clause if no set-function has been used
      if(!isSetFunctionUsed(stmtCtx.attrPos.iterator(), inRecord))
         return;

      // build a list of attributes included in the GROUP BY clause
      List<FndAttribute> grpByAttrs = new ArrayList<>();
      createGroupByAttributeList(stmtCtx.attrPos.iterator(), inRecord, grpByAttrs);

      // append the GROUP BY clause, if the created attribute list is not empty
      int size = grpByAttrs.size();
      if(size > 0) {
         sql.append("\n GROUP BY ");
         for(int i=0; i<size; i++) {
            FndAttribute attr = (FndAttribute) grpByAttrs.get(i);
            if (attr.isLong())
               throw new SystemException(Texts.LOBGRPBY);

            if (i > 0)
               sql.append(", ");

            appendColumnNameOrPlsqlFunction(attr, sql, alias);
         }
      }
   }

   /**
    * Checks if a set-function (like MIN or COUNT) has been used in the select list.
    * @param attrPosList list with attribute positions in the joined records
    * @param inRecord a record in the condition view structure (main record or a nested record)
    * @return true if the select list contains at least one attribute marked with
    *         a query result category corresponding to a set function: SUM, MIN, MAX, AVG, or COUNT.
    */
   private boolean isSetFunctionUsed(Iterator attrPosList, FndAbstractRecord inRecord) {
      while (attrPosList.hasNext()) {
         int attrPos = (Integer) attrPosList.next();
         FndAttribute attr = inRecord.getAttribute(attrPos);
         if (attr.isCompound()) {
            // check attributes in a nested joined record
            FndAbstractRecord joinedRec = FndAttributeInternals.internalGetRecord((FndAbstractAggregate) attr);
            List list = (List) attrPosList.next();
            if(isSetFunctionUsed(list.iterator(), joinedRec))
               return true;
         }
         else {
            if (attr.getResultCategory() != FndQueryResultCategory.INCLUDE)
               return true;
         }
      }
      return false;
   }

   /**
    * Create a list with attributes included in the group-by clause.
    * @param attrPosList list with attribute positions in the joined records
    * @param inRecord a record in the condition view structure (main record or a nested record)
    * @param dest list with instances of FndAttribute included in the group-by clause
    */
   private void createGroupByAttributeList(Iterator attrPosList, FndAbstractRecord inRecord, List<FndAttribute> dest) {
      boolean grouping = false;
      while (attrPosList.hasNext()) {
         int attrPos = (Integer) attrPosList.next();
         FndAttribute attr = inRecord.getAttribute(attrPos);
         if (attr.isCompound()) {
            // append attributes for a nested joined record
            FndAbstractRecord joinedRec = FndAttributeInternals.internalGetRecord((FndAbstractAggregate) attr);
            List list = (List) attrPosList.next();
            createGroupByAttributeList(list.iterator(), joinedRec, dest);
         }
         else {
            if (attr.getResultCategory() == FndQueryResultCategory.INCLUDE)
               dest.add(attr);
         }
      }
   }

   /**
    * Create CRUD ETag from OBJID and OBJVERSION.
    */
   String createCrudEtag(FndLUEntityView view) {
      return "W/" + '"' + view.objId.getValue() + ":" + view.objVersion.getValue() + '"';
   }

   void parseCrudEtag(String etag, FndLUEntityView view, boolean setObjId) throws IfsException {
      if(etag == null) {
         throw new SystemException(Texts.CRUDEMPTYETAG, etag);
      }

      int colon = etag.indexOf(':');
      if(colon > 0) {
         if(setObjId) {
            String objid = etag.substring(3, colon);
            if(log.debug) {
               log.debug("Retrieved objid [&1] from ETag [&2]", objid, etag);
            }
            view.objId.setValue(objid);
         }
         String objversion = etag.substring(colon + 1, etag.length() - 1);
         if(log.debug) {
            log.debug("Retrieved objversion [&1] from ETag [&2]", objversion, etag);
         }
         view.objVersion.setValue(objversion);
      }
      else {
         if(setObjId) {
            if(log.debug) {
               log.debug("Retrieved objkey from ETag [&1] and set as objid on virtual entity", etag);
            }
            view.objId.setValue(etag);
         }
         view.objVersion.setValue("1");
      }
   }

   /**
    * Fetch values for primary key from database.
    * @return primary key from temporary record, input record is not modified
    */
   private FndCompoundReference fetchCrudPrimaryKeyFromDatabase(FndConnection c, FndLUEntityView view) throws IfsException {
      if(log.trace) {
         log.trace("Fetching primary keys from database for view=&1 and objid=&2 ", view.getName(), view.objId);
      }
      FndLUEntityView tmp = (FndLUEntityView) view.newInstance();
      tmp.excludeQueryResults();
      tmp.objId.assign(view.objId);

      FndCompoundReference key = tmp.getPrimaryKey();
      key.include();

      FndAutoString sql = new FndAutoString(256);
      FndStatement stmt = c.createStatement();

      FndAttribute.Iterator keyIterator = key.iterator();
      while(keyIterator.hasNext()) {
         FndAttribute attr = keyIterator.next();
         sql.append(sql.length() == 0 ? "SELECT ": ", ").append(attr.getMeta().getColumn().toLowerCase());
         if(attr instanceof FndEnumeration && sql.endsWith("_db")) {
            /*
             * For enumeration column in primary key fetch client value,
             * as Offline version of CRUD methods did in PLSQL.
             */
            sql.setLength(sql.length() - 3);
         }
      }
      sql.append(" FROM ").append(view.getMeta().getTable().toLowerCase()).append(" WHERE objid = ?");
      stmt.defineInParameter(tmp.objId);

      stmt.prepare(sql);
      stmt.setMaxRows(1);
      stmt.setFetchSize(1);
      stmt.executeQuery();

      FndResultSet result = stmt.getFndResult();
      try {
         if(!result.next()) {
            throw new SystemException(Texts.CRUDGETPKNOTFOUND, view.getName(), view.objId.toString());
         }
      }
      catch (SQLException e) {
         throw new SystemException(e, Texts.CRUDGETPKFAIL, view.getName(), e.getMessage().trim());
      }

      int colNr = 0;
      keyIterator = key.iterator();
      while(keyIterator.hasNext()) {
         FndAttribute attr = keyIterator.next();
         colNr++;
         FndAttributeInternals.setSqlValue(attr, stmt, colNr);
         if(log.debug) {
            log.debug("Fetched value for PK attribute &1: &2 ", attr.getName(), attr.toString());
         }
      }
      return key;
   }

   /**
    * Append primary key attributes to CRUD function call.
    * @return number of attributes in the primary key
    */
   int appendCrudPrimaryKeyParameters(FndConnection c,
                                          FndLUEntityView view,
                                          FndStatement stmt,
                                          FndAutoString sql) throws IfsException {
      /*
       * Ignore primary key in view. Fetch primary key from database,
       * as Offline version of CRUD methods did in PLSQL.
       */
      FndCompoundReference key = fetchCrudPrimaryKeyFromDatabase(c, view);

      int pkSize = 0;
      FndAttribute.Iterator keyIterator = key.iterator();
      while(keyIterator.hasNext()) {
         FndAttribute attr = keyIterator.next();
         sql.append(", ");
         appendPlaceHolder(sql);
         stmt.defineInParameter(attr);
         pkSize++;
      }
      return pkSize;
   }

   /**
    * Append condition on primary key attributes
    */
   void appendPrimaryKeyCondition(FndPersistentView inRecord,
                                      FndStatement stmt,
                                      FndAutoString sql,
                                      boolean first,
                                      boolean alias) throws SystemException {

      appendPrimaryKeyCondition(inRecord, stmt, sql, first, alias, false);
   }

   /**
    * Append condition on primary key attributes.
    */
   void appendPrimaryKeyCondition(FndPersistentView inRecord,
                                      FndStatement stmt,
                                      FndAutoString sql,
                                      boolean first,
                                      boolean alias,
                                      boolean skipParentKeyAttributes) throws SystemException {
      appendPrimaryKeyCondition(inRecord, stmt, sql, first, alias, skipParentKeyAttributes, false, false);
   }

   /**
    * Append condition on primary key attributes.
    * @param removeDbSuffixForEnums true to remove "_DB" suffix from the column name for enumeration attributes
    */
   void appendPrimaryKeyCondition(FndPersistentView inRecord,
                                      FndStatement stmt,
                                      FndAutoString sql,
                                      boolean first,
                                      boolean alias,
                                      boolean skipParentKeyAttributes,
                                      boolean skipStereotypeKeyAttributes,
                                      boolean removeDbSuffixForEnums) throws SystemException {

      FndCompoundReference parentKeyInDetail = inRecord.getParentKey();
      FndCompoundReference key = inRecord.getPrimaryKey();
      if(key == null) {
         throw new SystemException(Texts.NOPKCONDITION, inRecord.getName());
      }

      FndAttribute.Iterator keyIterator = key.iterator();
      while(keyIterator.hasNext()) {
         FndAttribute attr = keyIterator.next();
         if (!attr.exist() || !attr.isSet()) {
            throw new SystemException(Texts.PKEYNOTSET, inRecord.getName(), attr.getName());
         }

         if(skipParentKeyAttributes && parentKeyInDetail.contains(attr))
            continue;

         if (appendAttrCondition(attr, stmt, sql, first, alias, removeDbSuffixForEnums && attr instanceof FndEnumeration))
            first = false;
      }
      if(!skipStereotypeKeyAttributes)
         appendStereotypeKeyCondition(inRecord, stmt, sql, first);
   }

   /**
    * Append where condition based on stereotype filter.
    */
   void appendStereotypeKeyCondition(FndPersistentView view,
                                         FndStatement stmt,
                                         FndAutoString sql,
                                         boolean first) throws SystemException {
      FndStereotypeFilter stereotypeFilter = view.getMeta().getStereotypeFilter();
      FndServerContext ctx = storage.context.getFndServerContext();
      if(stereotypeFilter != null) {
         String[][] insertColumns = stereotypeFilter.getInsertColumns();
         if(insertColumns != null) {
            for(int i=0; i <insertColumns.length; i++) {
               String name = insertColumns[i][0];
               String value = insertColumns[i][1];
               FndAttribute attr;

               if (first) {
                  sql.append("\n WHERE ");
                  first = false;
               }
               else
                  sql.append("\n AND ");

               sql.append(name);

               if(value.startsWith("@")) {
                  //
                  // (1) value refers to exactly one context parameter
                  //
                  if(!value.endsWith("@"))
                     throw new SystemException(Texts.FNDFILTERINSERTEND1, value);
                  String paramName = value.substring(1, value.length()-1);
                  attr = getContextParameterAttribute(paramName, ctx);
                  if(attr == null) {
                     sql.append(" IS NULL");
                  }
                  else {
                     sql.append(" = ");
                     appendPlaceHolder(sql);
                     stmt.defineInParameter(attr);
                  }
               }
               else {
                  //
                  // (2) value contains SQL text that may refer to many context parameters
                  //
                  sql.append(" = ");
                  replaceBindParameters(value, sql, stmt, null, null);
               }
            }
         }
      }
   }

   /** Append condition set on an attribute (attribute set to a value)
    *  @param stmt active FndStatement
    *  @param sql SQL statement
    *  @param attr attribute to use as source for condition
    *  @param first controls if this is the first condition to add
    *  @param removeDbSuffix true to remove "_DB" suffix from the column name
    *  @return true if a condition was added
    */
   private boolean appendAttrCondition(FndAttribute attr,
                                          FndStatement stmt,
                                          FndAutoString sql,
                                          boolean first,
                                          boolean alias,
                                          boolean removeDbSuffix) throws SystemException {

      if (isQueryableColumn(attr.getMeta()) && attr.isSet()) {
         if (first)
            sql.append("\n WHERE ");
         else
            sql.append("\n AND ");

         appendColumnNameOrPlsqlFunction(attr, sql, alias);
         if(removeDbSuffix && sql.endsWith("_DB")) {
            sql.setLength(sql.length() - 3);
         }

         if (attr.isNull())
            sql.append(" IS NULL");
         else {
            sql.append(" = ");
            appendPlaceHolder(sql);
            stmt.defineInParameter(attr);
         }
         return true;
      }
      return false; //No condition added
   }


   /**
    * Get an iterator over filters currently active for given view or attribute.
    * The method removes duplicate filters.
    */
   private Iterator getFilters(FndRecordMeta metaView, FndCompoundAttributeMeta metaAttr) {

      // root meta-view is used to find active filters for a view
      metaView = metaView.getRootMeta();

      //TODO: check if root meta-attribute should be used to find active filters for an attribute
      //metaAttr = metaAttr.getRootMeta();

      Set<FndFilter> set = new LinkedHashSet<>(); // preserve original ordering of filters

      FndFilterMap map = storage.context.getFilterMap();
      Iterator<FndFilter> filters;

      filters = map.getFilters(metaView);
      while(filters.hasNext()) {
         set.add(filters.next());
      }

      filters = metaView.getFilters();
      while(filters.hasNext()) {
         set.add(filters.next());
      }

      if(metaAttr != null) {
         filters = map.getFilters(metaAttr);
         while(filters.hasNext()) {
            set.add(filters.next());
         }

         filters = metaAttr.getFilters();
         while(filters.hasNext()) {
            set.add(filters.next());
         }
      }

      return set.iterator();
   }


   /**
    * Generate SQL text for filters attached to a record.
    * @param mainRecord the record which is supposed to be filtered
    * @param metaContainer meta-attribute containing the above record (if not null replaces
    *        the standard container-connection of the record)
    * @param from   string buffer for FROM table list (the main table/alias already appended)
    * @param where  string buffer for WHERE clause
    * @param c active database connection.
    * @param stmt active statement.
    */
   void resolveFilters(FndPersistentView mainRecord,
                       FndCompoundAttributeMeta metaContainer,
                       FndAutoString from,
                       FndAutoString where,
                       FndConnection c,
                       FndStatement stmt) throws SystemException {
      resolveFilters(mainRecord, metaContainer, from, where, false, c, stmt);
   }

   /**
    * Generate SQL text for filters attached to a record.
    * @param mainRecord the record which is supposed to be filtered
    * @param metaContainer meta-attribute containing the above record (if not null replaces
    *        the standard container-connection of the record)
    * @param from   string buffer for FROM table list (the main table/alias already appended)
    * @param where  string buffer for WHERE clause
    * @param onlyStereotypeFilters true if only stereotype filters should be applied.
    * @param stmt active statement.
    */
   void resolveFilters(FndPersistentView mainRecord,
                         FndCompoundAttributeMeta metaContainer,
                         FndAutoString from,
                         FndAutoString where,
                         boolean onlyStereotypeFilters,
                         FndConnection connection,
                         FndStatement stmt) throws SystemException {

      if(log.debug)
         log.debug("FndSqlStorageUtil.resolveFilters(&1)", mainRecord.getMeta().getType());

      FndRecordMeta metaView = mainRecord.getMeta();
      FndCompoundAttributeMeta metaAttr = metaContainer;

      if(metaAttr == null) {
         FndCompoundAttribute c = mainRecord.getContainer();
         if(c != null) {
            FndAttributeMeta m = mainRecord.getContainer().getMeta();
            if(m instanceof FndCompoundAttributeMeta)
               metaAttr = (FndCompoundAttributeMeta)m;
         }
      }

      String mainAlias = getTableAlias(mainRecord);
      Iterator filters = getFilters(metaView, metaAttr);
      while(filters.hasNext()) {
         FndFilter filter = (FndFilter)filters.next();
         if(!onlyStereotypeFilters || filter instanceof FndStereotypeFilter)
            resolveFilter(filter, mainAlias, from, where, stmt);
      }
   }

   /**
    * Generate SQL text of the specified filter.
    * Assign a global alias to every local alias found in the FROM part of the filter.
    * Replace local aliases with the global aliases in FROM and WHERE parts of the filter.
    * Append AppOwner prefix to tables in the FROM part as well as to tables and PLSQL packages
    * in the WHERE part. Example of a filter:
    * <pre>
    *    FROM:   "tab1 &A, tab2 &B, ..."
    *    WHERE:  "&A.col1 = &B.col2 and Demo_Product_API.Is_Valid(company, product_id) = 'TRUE'"
    * </pre>
    * Note that '&'-character cannot be used in string literals in the WHERE part of a filter.
    *
    * @param mainAlias alias assigned to the above record
    * @param from   string buffer for FROM table list
    * @param where  string buffer for WHERE clause
    * @param stmt active statement
    */
   private void resolveFilter(FndFilter filter,
                                String mainAlias,
                                FndAutoString from,
                                FndAutoString where,
                                FndStatement stmt) throws SystemException {

      if(log.debug)
         log.debug("FndSqlStorageUtil.resolveFilter(&1.&2)", filter.getPackage(), filter.getName());

      Map<String, String> map = new HashMap<>(); // local alias -> global alias

      String appOwner = storage.context.appOwner();
      String tmpWhere = filter.getWhere();

      // replace @-parameters
      ArrayList<BindVariable> bindVars = null; // List<BindVariable> for context parameters & Filter Parameters
      if(tmpWhere.indexOf('@') >= 0) {
         FndAutoString tmp = new FndAutoString();
         bindVars = new ArrayList<>();
         replaceBindParameters(tmpWhere, tmp, null, bindVars, filter.getParameters());
         tmpWhere = tmp.toString();
      }

      // replace &0 (local alias for main table) in WHERE part
      tmpWhere = Str.replace(tmpWhere, "&0", mainAlias);

      StringTokenizer st = new StringTokenizer(filter.getFrom(), ",");
      while(st.hasMoreTokens()) {
         String table = st.nextToken();

         // Now split table token into table name and alias
         StringTokenizer st2 = new StringTokenizer(table, " \n\r\t");
         if(!st2.hasMoreTokens())
            throw new SystemException(Texts.FNDFILTERTABLE, filter.getFrom());
         table = st2.nextToken();

         if(!st2.hasMoreTokens())
            throw new SystemException(Texts.FNDFILTERALIAS, filter.getFrom());
         String alias = st2.nextToken();

         if(st2.hasMoreTokens())
            throw new SystemException(Texts.FNDFILTERMANY, st2.nextToken());

         // find or create a global alias (in the main select statement) corresponding to the local alias (in this filter)
         String globalAlias = map.get(alias);
         if(globalAlias == null) {
            map.put(alias, globalAlias = getTableAlias(new Object()));
         }

         from.append(", ");

         if(appOwner != null && table.indexOf('.') < 0) { // do nothing if there is already a prefix
            from.append(appOwner);
            from.append(".");
         }
         from.append(table);
         from.append(" ");
         from.append(globalAlias);

         // replace local alias for current table in WHERE part
         tmpWhere = Str.replace(tmpWhere, alias, globalAlias);
      }

      if(tmpWhere.length() > 0) {
         if(where.length() > 0)
            where.append("\n AND (");
         else
            where.append("(");

         // fix WHERE part: pre-append application owner prefix(es)
         where.append(prepareSqlText(tmpWhere, appOwner));
         int size = bindVars == null ? 0 : bindVars.size();
         for(int i=0; i<size; i++) {
            stmt.defineInParameter(((BindVariable)bindVars.get(i)).variable);
         }
         where.append(")");
      }
   }

   /**
    * Resolve a reference to a context parameter in the value of a text attribute.
    * If the specified attribute is an instance of FndAbstractString with the value that
    * refers to a context parameter then the referred attribute is returned, otherwise
    * the original attribute is returned.
    * @param attr an attribute that may refer to a context parameter
    * @return an attribute that should be used to define the bind variable
    */
   private FndAttribute resolveBindParameter(FndAttribute attr) throws SystemException {
      if(!(attr instanceof FndAbstractString))
         return attr;

      String value = attr.toString();
      if(value == null || !value.startsWith("@"))
         return attr;

      if(!isContextParameterAt(value, 0))
         return attr;

      if(!value.endsWith("@"))
         throw new SystemException(Texts.FNDVALUEPARAMEND, value);

      if(countChar(value, '@') != 2)
         throw new SystemException(Texts.FNDVALUEPARAMBAD, value);

      String paramName = value.substring(1, value.length() - 1);
      FndAttribute refAttr = getContextParameterAttribute(paramName, storage.context.getFndServerContext());

      if(refAttr == null) {
         // referred context parameter does not exist, create new attribute with null value
         try {
            refAttr = attr.getType() == FndAttributeType.UNKNOWN ? new FndText(attr.getName(), null) : (FndAttribute)attr.clone();
            refAttr.setNull();
         }
         catch(CloneNotSupportedException e) {
            throw new SystemException(e, Texts.RESOLVECTXPARAM);
         }
      }
      else {
         if(refAttr.isCompound())
            throw new SystemException(Texts.RESOLVECTXPARAMCOMP, value);
      }
      return refAttr;
   }

   /**
    * Replace @-parameters in a SQL text with bind variables.
    * Depending on the type, one filter/context parameter may be replaced with one typed bind variable
    * (simple attribute), a list of bind variables (aggregate attribute) or a list of previous
    * two types (array attribute).
    * @param sqlText SQL text that may contain references to filter/context parameters
    * @param sql text buffer for the generated SQL statement
    * @param stmt a FndStatement to define bind variables for (may be null)
    * @param bindVariables a list (of BindVariable) to add bind variables to (may be null)
    * @param bindParameters a FndRecord (of FndAttribute) holds filter parameters (may be null)
    */
   private void replaceBindParameters(String sqlText, FndAutoString sql, FndStatement stmt, List<BindVariable> bindVariables, FndRecord bindParameters) throws SystemException {
      FndServerContext ctx = storage.context.getFndServerContext();
      //FndDebug.debug("FndContext parameters:");
      //FndDebug.debugRecord(ctx.getAppContext());

      int i = 0, j;
      while (true) {
         j = sqlText.indexOf('@', i);
         if (j < 0) {
            sql.append(sqlText.substring(i));
            return;
         }
         else if(isContextParameterAt(sqlText, j)) {
            sql.append(sqlText.substring(i, j));
            i = sqlText.indexOf('@', j + 1);
            if(i < 0)
               throw new SystemException(Texts.FNDFILTERPARAMEND, sqlText);
            String paramName = sqlText.substring(j + 1, i);
            appendContextParameter(paramName, sql, stmt, bindVariables, ctx);
            i = i + 1;
         }
         else {
            sql.append(sqlText.substring(i, j));
            i = sqlText.indexOf('@', j + 1);
            if(i < 0)
               throw new SystemException(Texts.FNDFILTERPARAMEND, sqlText);
            String paramName = sqlText.substring(j + 1, i);

            if(isFilterParameterIn(paramName, bindParameters)){
               appendFilterParameter(paramName, sql, stmt, bindVariables, bindParameters);
               i = i + 1;
            }
            else{
               sql.append(sqlText.substring(j, i + 1));
               i = i + 1;
            }
         }
      }
   }

   /**
    * Generates SQL for one context parameter.
    * The method appends both SQL text and the bind variable(s) corresponing to the parameter.
    * Depending on the parameter type (simple, array, aggregate) one or more bind variables may be added.
    * @param paramName name of a context parameter
    * @param sql text buffer for the generated SQL statement
    * @param stmt a FndStatement to define bind variables for (may be null)
    * @param bindVariables a list (of BindVariable) to add bind variables to (may be null)
    * @param ctx current context
    */
   private void appendContextParameter(String paramName, FndAutoString sql, FndStatement stmt, List<BindVariable> bindVariables, FndServerContext ctx) throws SystemException {
      FndAttribute attrCtx = getContextParameterAttribute(paramName, ctx);
      if(attrCtx != null)
         appendParameterAttribute(attrCtx, sql, stmt, bindVariables);
      else
         sql.append("NULL");
   }

   /**
    * Generates SQL for one filter parameter.
    * The method appends both SQL text and the bind variable(s) corresponing to the parameter.
    * Depending on the parameter type (simple, array, aggregate) one or more bind variables may be added.
    * @param paramName name of a filter parameter
    * @param sql text buffer for the generated SQL statement
    * @param stmt a FndStatement to define bind variables for (may be null)
    * @param bindVariables a list (of BindVariable) to add bind variables to (may be null)
    * @param bindParameters a FndRecord (of FndAttribute) holds filter parameters (may be null)
    */
   private void appendFilterParameter(String paramName, FndAutoString sql, FndStatement stmt, List<BindVariable> bindVariables, FndRecord bindParameters) throws SystemException {
      FndAttribute attr = bindParameters.getAttribute(paramName);
      if(attr!=null)
         appendParameterAttribute(attr, sql, stmt, bindVariables);
      else
         sql.append("NULL");
   }


   /**
    * Generates SQL for a simple or compound parameter attribute.
    */
   private void appendParameterAttribute(FndAttribute attr, FndAutoString sql, FndStatement stmt, List<BindVariable> bindVariables) throws SystemException {
      FndAttributeType type = attr.getType();
      if (type == FndAttributeType.AGGREGATE)
         appendParameterAggregate((FndAggregate) attr, sql, stmt, bindVariables);

      else if (type == FndAttributeType.ARRAY)
         appendParameterArray((FndArray) attr, sql, stmt, bindVariables);

      else {
         if(type == FndAttributeType.UNKNOWN)
            attr = new FndText(attr.getName(), attr.toString());
         appendSimpleParameterAttribute(attr, sql, stmt, bindVariables);
      }
   }

   /**
    * Generates SQL for a record being a part of a compound parameter attribute.
    */
   private void appendParameterRecord(FndAbstractRecord rec, FndAutoString sql, FndStatement stmt, List<BindVariable> bindVariables) throws SystemException {
      sql.append("(");
      int count = rec.getAttributeCount();
      for(int i=0; i<count; i++) {
         if(i > 0)
            sql.append(", ");
         appendParameterAttribute(rec.getAttribute(i), sql, stmt, bindVariables);
      }
      sql.append(")");
   }

   /**
    * Generates SQL for a simple parameter attribute.
    */
   private void appendSimpleParameterAttribute(FndAttribute attr, FndAutoString sql, FndStatement stmt, List<BindVariable> bindVariables) throws SystemException {
      if(stmt != null) {
         stmt.defineInParameter(attr);
      }
      else {
         BindVariable v = new BindVariable();
         v.variable = FndAttributeInternals.toFndSqlValue(attr);
         if(v.variable == null) // create STRING variable by default
            v.variable = new FndSqlValue(attr.getName(), attr.toString(), false, false);
         v.variable.setDirection(FndSqlValue.DIRECTION_IN);
         //v.pos = sql.length();
         bindVariables.add(v);
      }
      appendPlaceHolder(sql);
   }

   /**
    * Generates SQL for a parameter attribute of type FndAggregate.
    */
   private void appendParameterAggregate(FndAggregate agg, FndAutoString sql, FndStatement stmt, List<BindVariable> bindVariables) throws SystemException {
      appendParameterRecord(agg.getRecord(), sql, stmt, bindVariables);
   }

   /**
    * Generates SQL for a parameter attribute of type FndArray.
    */
   private void appendParameterArray(FndArray arr, FndAutoString sql, FndStatement stmt, List<BindVariable> bindVariables) throws SystemException {
      for(int i=0; i<arr.size(); i++) {
         if(i > 0)
            sql.append(", ");
         appendParameterRecord(arr.get(i), sql, stmt, bindVariables);
      }
   }

   /**
    * Checks if a text refers to a @-parameter at specified position.
    */
   private static boolean isContextParameterAt(String text, int pos) {
      return startsWithIgnoreCase(text, pos, "@CONTEXT/USER") ||
             startsWithIgnoreCase(text, pos, "@CONTEXT/LANGUAGE") ||
             startsWithIgnoreCase(text, pos, "@CONTEXT/APP/");
   }

   /**
    * Checks if a bindText refers to a filter parameter.
    */
   private boolean isFilterParameterIn(String bindText, FndRecord bindParameters) {
      if(bindParameters!=null && bindParameters.getAttribute(bindText)!=null)
         return true;
      else
         return false;
   }

   /**
    * Find a context parameter.
    */
   private FndAttribute getContextParameterAttribute(String name, FndServerContext ctx) throws SystemException {
      name = name.toUpperCase();
      if("CONTEXT/USER".equals(name))
         return new FndText("USER", ctx.getFndUserIdentity());
      else if("CONTEXT/LANGUAGE".equals(name))
         return new FndText("LANGUAGE", ctx.getLanguage());
      else if(name.startsWith("CONTEXT/APP/")) {
         name = name.substring(12);
         FndRecord app = ctx.getAppContext();
         return app.getAttribute(name);
      }

      return null;
   }

   /**
    * Append join condition between two records.
    * @param container the container to join with
    * @param view joined record
    * @param where string buffer for join condition
    * @param stmt active FndStatement
    */
   void appendJoinCondition(FndAbstractAggregate container,
                            FndPersistentView view,
                            FndAutoString where,
                            FndStatement stmt) throws SystemException {

      if(where.length() == 0)
         where.append("(");
      else
         where.append("\n AND (");

      FndAttribute.Iterator i1 = container.getParentKeyInParent().iterator();
      FndAttribute.Iterator i2 = view.getParentKey().iterator();
      FndStereotypeFilter stereotypeFilter = view.getMeta().getStereotypeFilter();
      boolean first = true;

      String tableAlias = getTableAlias(view);
      while(i1.hasNext()) {
         if(!first)
            where.append(" AND ");

         FndAttribute a1 = i1.next();
         FndAttribute a2 = i2.next();

         appendColumnNameOrPlsqlFunction(a1, where, true);
         where.append(" = ");
         where.append(tableAlias); // necessary because of super references
         where.append(".");
         appendColumnName(a2, where, false);
         where.append("(+)");
         first = false;
      }
      if(stereotypeFilter != null) {
         String stereotypeWhere = stereotypeFilter.getJoinWhere();
         if(stereotypeWhere!=null) {
            if(!first)
               where.append(" AND ");
            stereotypeWhere = Str.replace(stereotypeWhere, "&0", tableAlias);
            replaceBindParameters(stereotypeWhere, where, stmt, null, null);
            //where.append(replaceBindParameters(stereotypeWhere));
         }
      }
      where.append(")");
   }

   /**
    * Find all aggregated (nested) records that mey be fetched using SQL join.
    * This method defines the restrictions that must be met by an aggregate attribute
    * and its aggregated record to be able to fetch this record using SQL join operation.
    * Attributes that match all conditions are inserted into a set.
    * @param view a record representing query conditions
    * @param c active database connection
    * @return set with instances of FndAbstractAttribute that can be joined
    */
   Set findJoinableAggregates(FndPersistentView view, FndConnection c) throws SystemException {
      Set<FndAbstractAggregate> set = new HashSet<>();

      // comment out the following line to deactivate joins
      findJoinableAggregates(null, view, 0, set, c, new HashSet<>());

      if(log.debug) {
         log.debug("FndSqlStorageUtil.findJoinableAggregates(&1):", view.getName());
         Iterator i = set.iterator();
         while(i.hasNext()) {
            FndAbstractAggregate a = (FndAbstractAggregate)i.next();
            log.debug("   &1.&2", a.getParentRecord().getName(), a.getName());
         }
      }
      return set;
   }

   /**
    * Find all aggregated (nested) records that mey be fetched using SQL join.
    * @param aggr aggregate attribute to be checked for joinability, null for the  root view
    * @param view record aggregated in the above aggragate
    * @param lobCount the number of LOB columns included in the select statement
    * @param set the set for instances of FndAbstractAggregate that can be joined
    * @param c active database connection
    */
   private void findJoinableAggregates(FndAbstractAggregate aggr, FndPersistentView view, int lobCount, Set<FndAbstractAggregate> set, FndConnection c, Set<FndCompoundAttributeMeta> recursionCheck) throws SystemException {

      // count LOB columns
      //int attrCount = view.getAttributeCount();
      //for(int i=0; i<attrCount; i++) {
      //   FndAttribute attr = view.getAttribute(i);
      //   if(attr.isLong() && !attr.isExcluded())
      //      lobCount++;
      //      //if(++lobCount > 1)
      //      //   return;
      //}

      // store joinable aggregate
      if(aggr != null)
         set.add(aggr);
      // recursively check all details
      FndAttribute.Iterator details = view.details();
      while(details.hasNext()) {
         FndAttribute attr = details.next();
         if(attr instanceof FndAbstractAggregate && !attr.isExcluded())
            findJoinableAggregates((FndAbstractAggregate)attr, lobCount, set, c, recursionCheck);
      }
   }

   /**
    * Find all aggregated (nested) records that may be fetched using SQL join.
    * @param aggr aggregate attribute to be checked for joinability
    * @param lobCount the number of LOB columns included in the select statement
    * @param set the set for instances of FndAbstractAggregate that can be joined
    * @param c active database connection.
    */
   private void findJoinableAggregates(FndAbstractAggregate aggr, int lobCount, Set<FndAbstractAggregate> set, FndConnection c, Set<FndCompoundAttributeMeta> recursionCheck) throws SystemException {

      if(aggr.customQuery() && (aggr instanceof FndQueryStorage)) // custom query operation exists
         return;

      if(aggr instanceof FndGenericAggregate) // don't join with an aspect aggregate
         return;

      FndAbstractRecord rec = FndAttributeInternals.internalGetRecord(aggr);

      if(rec == null || !rec.isPersistent()) // only process persistent views
         return;

      // do not join with a not-installed table
      if(!FndStorageInstallationCache.isTableInstalled((FndPersistentView)rec, c)) {
         if(log.debug)
            log.debug("   Skipping join with not-installed table &1", rec.getMeta().getTable());
         return;
      }
      FndRecordMeta metaView = rec.getMeta();
      FndCompoundAttributeMeta metaAttr = aggr.getCompoundMeta();
      if(recursionCheck.contains(metaAttr))
         return;
      recursionCheck.add(metaAttr);

      //TODO: Join should be accepted if getConnection(detail) == getConnection(master)
      //      The following code rejects only FNDEXT-FNDBAS joins.
      //
      // Skip joins between LU and non-LU records
      //FndAbstractRecord parent = aggr.getParentRecord();
      //if(parent != null && parent.getMeta().isLU() != metaView.isLU())
      //   return;

      if(aggr.containsDependentDetails()) {
         //
         //  Filters are NOT used for independent details (ref-by-value) so it's ok to join.
         //  But for dependent details apply the following rule:
         //  do not join if there is a filter on joined record, because
         //  outer join could conflict with filter conditions.
         //
         Iterator filters = getFilters(metaView, metaAttr);
         if(filters.hasNext())
            return;
      }

      findJoinableAggregates(aggr, (FndPersistentView)rec, lobCount, set, c, recursionCheck);
   }


   /**
    * Include attributes contained in parent keys as needed by checkParentKeys().
    * The method will include parent keys corresponding to:
    *    (1) existent (and set) non-dirty compound attributes
    *    (X) existent excluded non-dirty compound attributes
    *    (X) existent included non-dirty compound attributes.
    * These attributes will be used to check master-detail connections in non-dirty populate mode.
    * Also, include the primary key.
    * @param view record to include parent keys in
    * @throws SystemException is something goes wrong
    */
   void includeParentKeys(FndPersistentView view) throws IfsException {
      if(log.debug)
         log.debug("FndSqlStorageUtil.includeParentKeys(&1):", view.getName());
      view.getPrimaryKey().include();
      FndAttribute.Iterator details = view.details();
      while (details.hasNext()) {
         FndCompoundAttribute attr = (FndCompoundAttribute) details.next();
         if(!attr.exist() || !attr.isSet())
            continue;
         FndCompoundReference ref = attr.getParentKeyInParent();
         if(ref == null) // true for FndArray/FndAggregate (if the client sends an invalid array/aggregate)
            continue;
         if(/*attr.isExcluded() ||*/ !attr.isDirty())
            ref.include();
      }
   }

   /**
    * Verify that parent keys in condition view match the parent keys in result view.
    * The method will check parent keys corresponding to:
    *    (1) existent (and set) non-dirty compound attributes
    *    (X) existent excluded non-dirty compound attributes
    *    (X) existent included non-dirty compound attributes.
    * @param condition record with attributes sent from the client
    * @param result record with attributes fetched from database
    * @throws SystemException if parent keys in condition view do not match the values in result record
    */
   void checkParentKeys(FndPersistentView condition, FndPersistentView result) throws IfsException {
      if(log.debug)
         log.debug("FndSqlStorageUtil.checkParentKeys(&1):", condition.getName());

      // match attributes in memory-record (condition) against database-record (result)
      FndAttribute.Iterator details = condition.details();
      while (details.hasNext()) {
         FndCompoundAttribute attr = (FndCompoundAttribute) details.next();
         if(!attr.exist() || !attr.isSet())
            continue;
         FndCompoundReference ref = attr.getParentKeyInParent();
         if(ref == null) // true for FndArray/FndAggregate (if the client sends an invalid array/aggregate)
            continue;
         if(/*attr.isExcluded() ||*/ !attr.isDirty()) {
            FndCompoundReference dbRef = result.findCompoundReference(ref.getMeta());
            FndAttribute.Iterator mem = ref.iterator();
            FndAttribute.Iterator db  = dbRef.iterator();
            while(mem.hasNext()) {
               FndAttribute memAttr = mem.next();
               if(memAttr.isNull())
                  continue;
               FndAttribute dbAttr = db.next();
               if(memAttr.compareTo(dbAttr) != 0)
                  throw new SystemException(Texts.FNDBADPARENTKEY, memAttr.toString(), memAttr.getMeta().getFullName(), dbAttr.toString());
            }
         }
      }

      if(log.debug)
         log.debug("FndSqlStorageUtil.checkParentKeys(&1): OK", condition.getName());
   }

   /**
    * Verify that parent key in detail record matches the parent key in master record.
    * The method does nothing if given view is an element of an array in a non-persitent record.
    * @param view detail record to check parent key in
    * @param container compound attribute that contains the detail view
    * @throws SystemException if parent key in detail record does not match the parent key in master record
    */
   void checkParentKeyInDetail(FndCompoundAttribute container, FndPersistentView view) throws SystemException {
      if(log.debug)
         log.debug("FndSqlStorageUtil.checkParentKeyInDetail(&1):", view.getName());

      FndAbstractRecord parent = container.getParentRecord();
      if(parent != null && parent.isPersistent()) {
         FndCompoundReference parentKeyInParent = container.getParentKeyInParent();
         FndCompoundReference parentKeyInElement = view.getParentKey();
         if(parentKeyInParent==null || parentKeyInElement==null)
            throw new SystemException(Texts.FNDMISSINGPARENTKEY, view.getName());

         FndAttribute.Iterator i1 = parentKeyInParent.iterator();
         FndAttribute.Iterator i2 = parentKeyInElement.iterator();
         while(i1.hasNext()) {
            FndAttribute a1 = i1.next();
            if(a1.isNull())
               continue;
            FndAttribute a2 = i2.next();
            if(a1.compareTo(a2) != 0)
               throw new SystemException(Texts.FNDBADDETAILKEY, a1.getMeta().getFullName(), a1.toString(), a2.getMeta().getFullName(), a2.toString());
         }
      }

      if(log.debug)
         log.debug("FndSqlStorageUtil.checkParentKeyInDetail(&1): OK", view.getName());
   }

   /**
    * Creates a string representation of current attribute values in a primary key.
    * @param view record to retrieve primary key from
    * @return Comma-separated list of attribute values contained in the primary key
    */
   String getPrimaryKeyImage(FndPersistentView view) {
      FndAutoString key = new FndAutoString();
      FndAttribute.Iterator iter = view.getPrimaryKey().iterator();
      while(iter.hasNext()) {
         FndAttribute attr = iter.next();
         if(key.length() > 0)
            key.append(", ");
         key.append(attr.toString());
      }
      return key.toString();
   }

   /**
    * Creates a string representation of attribute names in a primary key.
    * @param view record to retrieve primary key names from
    * @return Comma-separated list of attribute names contained in the primary key
    */
   String getPrimaryKeyNameImage(FndPersistentView view) {
      FndAutoString key = new FndAutoString();
      FndAttribute.Iterator iter = view.getPrimaryKey().iterator();
      while(iter.hasNext()) {
         FndAttribute attr = iter.next();
         if(key.length() > 0)
            key.append(", ");
         key.append(attr.getName());
      }
      return key.toString();
   }

   /**
    * Append default values for Stereotype Filters to the insert statement
    */
   void appendStereotypeInsertColumns(FndAutoString colSql, FndAutoString valSql, FndPersistentView view, FndStatement stmt, boolean first) throws IfsException {
      FndStereotypeFilter stereotypeFilter = view.getMeta().getStereotypeFilter();
      if(stereotypeFilter != null) {
         String[][] insertColumns = stereotypeFilter.getInsertColumns();
         if(insertColumns != null) {
            for(int i=0; i <insertColumns.length; i++) {
               String name = insertColumns[i][0];
               String value = insertColumns[i][1];

               if(!first) {
                  colSql.append(", ");
                  valSql.append(", ");
               }
               else {
                  first = false;
               }

               colSql.append(name);
               replaceBindParameters(value, valSql, stmt, null, null);
               //value = replaceBindParameters(value);
               //valSql.append(value);
            }
         }
      }
   }

   /**
    * Prepare an array/aggregate of non-installed views to be returned to the client.
    * The method sets insert-not-allowed flag on the specified compound attribute
    * and prints a warning to the standard output.
    */
   void markNotInstalledCompoundAttribute(FndCompoundAttribute attr, FndPersistentView element, String operation) {
      markNotInstalledCompoundAttribute(attr, element.getMeta(), operation);
   }

   /**
    * Prepare an array/aggregate of non-installed views to be returned to the client.
    * The method sets insert-not-allowed flag on the specified compound attribute
    * and prints a warning to the standard output.
    */
   void markNotInstalledCompoundAttribute(FndCompoundAttribute attr, FndRecordMeta element, String operation) {
      //
      // Unset persistent flag UPDATE_ALLOWED and set transient flag NON_INSTALLED.
      // The last one, in case of entity state rules, makes FndAttributeRule.applyRules()
      // to ignore UPDATE_ALLOWED value defined in meta data.
      //
      FndAttributeInternals.setNotInstalled(attr);
      attr.setUpdateAllowed(false);
      String table = element.getTable().toUpperCase();
      FndServerContext fndctx = storage.context.getFndServerContext();
      if(!fndctx.containsNotInstalledObject(element)) {
         if(log.info)
            log.info("Table for view &1 is not installed. Call to &2() ignored.", element.getViewClassName(), operation);
         fndctx.addNotInstalledObject(element);
      }
      if(log.debug)
         log.debug("   WARNING! Table &1 is not installed. Call to &2() ignored.", table, operation);
   }

   /**
    * Prepares SQL text for execution.
    * The method pre-appends application owner prefix.
    * @param sqltext a SQL text to prepare
    * @param appOwner application owner
    */
   private String prepareSqlText(String sqltext, String appOwner) throws SystemException {

      // parse SQL text and pre-append application owner
      String dbPrefix = appOwner==null ? "" : appOwner + ".";
      SQLRecognizer r = null;
      try {
         r = new SQLRecognizer(sqltext, dbPrefix);
      }
      catch(Exception e) {
         throw new SystemException(e, e.toString());
      }
      sqltext = r.getSQLText();
      return sqltext;
   }

   private static class BindVariable {
      FndSqlValue variable; // value passed to FndStatement
      //int pos;              // character offest in the SQL text
   }

   private static boolean startsWithIgnoreCase(String a, int start, String b) {
      if(a == null || b == null)
         return false;
      int len1 = a.length();
      int len2 = b.length();
      if(len1 - start < len2)
         return false;
      for(int i=0; i<len2; i++) {
         if(Character.toLowerCase(a.charAt(start + i)) != Character.toLowerCase(b.charAt(i)))
            return false;
      }
      return true;
   }

   private static int countChar(String str, char ch) {
      int count = 0;
      int len = str.length();
      for(int i=0; i<len; i++) {
         if(str.charAt(i) == ch)
            count++;
      }
      return count;
   }

   /**
    * Stores LOB attributes of an LU-view by calling SQL update statement.
    * ObjVersion is checked but not stored.
    * @param c database connection
    * @param view record to store in database
    */
   void updateLuLobs(FndConnection c, FndLUEntityView view) throws IfsException {
      boolean create = view.getState() == FndRecordState.NEW_RECORD;

      // Create a list with LOB attributes to be updated
      ArrayList<FndAttribute> lobAttrs = null;
      Set<FndAttribute> nullLobAttrs = null;
      int count = view.getAttributeCount();
      for (int i = 0; i < count; i++) {
         FndAttribute attr = view.getAttribute(i);

         boolean isDirtyLob = isDirtyLob(attr, create);
         if(!isDirtyLob && !create && isNullDirtyLob(attr)) {
            if(nullLobAttrs == null)
               nullLobAttrs = Util.newHashSet();
            nullLobAttrs.add(attr);
            isDirtyLob = true;
         }

         if (isDirtyLob) {
            if(lobAttrs == null)
               lobAttrs = Util.newArrayList(1);
            lobAttrs.add(attr);
         }
      }
      if(lobAttrs == null)
         return;

      FndStatement stmt = null;
      FndAutoString sql = new FndAutoString(256);

      try {
         view.objVersion.checkValuePresent(Texts.NOVALUELOB);
         stmt = storage.createStatement(c, view);

         boolean projection = useProjectionStorage(view);
         if(projection) {
            updateProjectionLobs(c, view, lobAttrs);
         }
         else {
            preparePrimaryKey(view, false);
            sql.append("UPDATE ");
            appendLobTableName(view, sql);
            sql.append(" SET ");
            appendUpdateLobColumns(sql, lobAttrs, nullLobAttrs, stmt);
            //
            // If an enumeration is included in the primary key then remove "_DB" suffix from the column name,
            // because {column}_DB in the database view corresponds to {column} in the LOB-table.
            //
            //                                        first, alias, skipParentKeyAttributes, skipStereotypeKeyAttributes, removeDbSuffixForEnums
            appendPrimaryKeyCondition(view, stmt, sql, true, false, false, false, true);
            //sql.append("\n AND ROWVERSION = ");
            //appendPlaceHolder(sql);
            //stmt.defineInParameter(view.objVersion);
            stmt.prepare(sql);
            int rowProcessed = stmt.executeUpdate();
            stmt.close();

            if (rowProcessed == 0)
               storage.reportChangedRow(view, c);

            if(nullLobAttrs != null) {
               lobAttrs.removeAll(nullLobAttrs);
               if(lobAttrs.isEmpty()) {
                  return;
               }
            }

            storage.updateLobs(view, c, create);
         }
      }
      catch (ValidationException e) {
         addInfo(e, view); //Will rethrow exception
      }
      catch (DatabaseException e) {
         addInfo(e, view); //Will rethrow exception
      }
      finally {
         if (stmt != null)
            stmt.close();
      }
   }

   boolean useProjectionStorage(FndLUEntityView view) {
      return view.getMeta().getPlsqlPackage().endsWith("_SVC");
   }

   String viewTypeProjectionParameter(FndLUEntityView view) {
      return view.getMeta().getType().toLowerCase() + "## => ''";
   }

   private void updateProjectionLobs(FndConnection c, FndLUEntityView view, ArrayList<FndAttribute> lobAttrs) throws IfsException {
      for(FndAttribute attr : lobAttrs) {
         try(FndStatement stmt = c.createStatement()) {
            /*
               FUNCTION CRUD_Upload (
                  etag_                    IN VARCHAR2,
                  answer_id_               IN NUMBER,
                  description##            IN CLOB,
                  jt_task_survey_answers## IN VARCHAR2) RETURN VARCHAR2;

               FUNCTION CRUD_Upload (
                  etag_                    IN VARCHAR2,
                  answer_id_               IN NUMBER,
                  stream_info##            IN Stream_Info_Rec,
                  picture_data##           IN BLOB,
                  jt_task_survey_answers## IN VARCHAR2) RETURN VARCHAR2;
            */
            FndAutoString sql = new FndAutoString(256);
            String etag = createCrudEtag(view);
            String pkg = view.getMeta().getPlsqlPackage();
            boolean isBlob = attr instanceof FndBinary;
            boolean virtualEntity = view.getMeta().getTable().startsWith("(");
            sql.append("BEGIN\n");
            sql.append("   ? := ").append(pkg).append(".CRUD_Upload(?");
            stmt.defineOutParameter("ETAGOUT", FndSqlType.STRING);
            stmt.defineInParameter(new FndSqlValue("ETAG", etag));
            appendCrudPrimaryKeyParameters(c, view, stmt, sql);
            if(isBlob && !virtualEntity) {
               sql.append(", NULL"); // empty Stream_Info_Rec
            }
            String lobParamName = attr.getName() + "##";
            sql.append(", " + lobParamName.toLowerCase() + " => ?, " + viewTypeProjectionParameter(view) + ");\n");
            sql.append("END;\n");
            if(isBlob) {
               stmt.defineInParameter(new FndSqlValue(lobParamName, ((FndBinary)attr).getValue()));
            }
            else  {
               stmt.defineInParameter(new FndSqlValue(lobParamName, ((FndText)attr).getValue(), true, true));
            }
            stmt.prepareCall(sql.toString());
            stmt.execute();

            etag = getTextOutParameter(stmt, 1);
            parseCrudEtag(etag, view, false);
         }
      }
   }

   /**
    * Append LOB columns to be updated in an LU entity instance.
    * @param sql string buffer with SQL statement
    * @param lobAttrs the list with LOB attributes to be updated
    * @param stmt active statement
    */
   private void appendUpdateLobColumns(FndAutoString sql, List<FndAttribute> lobAttrs, Set<FndAttribute> nullLobAttrs, FndStatement stmt) throws IfsException {
      int count = lobAttrs.size();
      for (int i = 0; i < count; i++) {
         FndAttribute attr = lobAttrs.get(i);
         checkUpdateAllowed(attr);
         if (i > 0)
            sql.append(", ");
         appendColumnName(attr, sql);
         sql.append(" = ");
         if(nullLobAttrs != null && nullLobAttrs.contains(attr)) {
            sql.append("NULL");
         }
         else {
            stmt.initializeLocator(attr, sql);
         }
      }
   }

   /**
    * Check if the specified attribute may be used in a select statement.
    */
   boolean isQueryableColumn(FndAttributeMeta meta) {
      return meta.isPersistent() || (meta.isDerived() && meta.getColumn() != null);
   }
}
