/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.storage;

import ifs.fnd.base.IfsException;
import ifs.fnd.base.SystemException;
import ifs.fnd.record.FndAttribute;
import ifs.fnd.record.FndCompoundAttributeMeta;
import ifs.fnd.record.FndCompoundReferenceMeta;
import ifs.fnd.record.FndFilter;
import ifs.fnd.record.FndRecordMeta;
import ifs.fnd.record.FndSqlValue;
import ifs.fnd.sf.FndServerContext;
import ifs.fnd.util.Str;

import java.sql.SQLException;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <B>Framework internal class:</B> Cache with instances of FndFilter per entity.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public final class FndFilterCache {

   //==============================================================================
   // Static variables and methods
   //==============================================================================

   /**
    * Constant representing a filter type. Standard filters are modeled in IFS Developer Studio.
    */
   public static final int STANDARD_FILTER = 1;

   /**
    * Constant representing a filter type. Permission set filters are defined in IFS Solution Manager.
    */
   public static final int PERMISSION_SET_FILTER = 2;

   /**
    * The singleton instance of the cache.
    */
   private static final FndFilterCache INSTANCE = new FndFilterCache();

   /**
    * Select statement fetching a permission set filter definition from the database.
    */
   private static final String SELECT_PERMISSION_SET_FILTER = createSelectPermissionSetFilter();

   /**
    * Gets a filter entry from the cache.
    * Filters not found in the cache are instantiated according to definition retrieved from the database.
    * @param connection an open database connection
    * @param filterId the identity of the filter
    * @param filterType the type of the filter
    * @return an immutable filter entry retrieved from the cache
    * @throws IfsException
    */
   public static Entry getFilterEntry(FndConnection connection, String filterId, int filterType) throws IfsException {
      return INSTANCE.getEntry(connection, filterId, filterType);
   }

   /**
    * Removes all filter entries from the cache.
    */
   public static void clearCache() {
      INSTANCE.map.clear();
   }

   /**
    * Class representing a filter attached to an entity.
    */
   public static class Entry {
      private int type;         // type is here only for debugging purposes
      private FndFilter filter;
//      private FndRecordMeta meta;
      private String key;

      /**
       * Creates an uninitialized entry.
       */
      Entry() {
      }

      /**
       * Initializes this entry by setting filter definition fetched from database.
     * @throws SecurityException
     * @throws NoSuchFieldException
     * @throws IllegalAccessException
     * @throws IllegalArgumentException
     * @throws SystemException
     * @throws ClassNotFoundException
       */
      void init(FndServerContext ctx, int type, FndFilter filter, String entityName) throws
            NoSuchFieldException, IllegalArgumentException, IllegalAccessException, SystemException, ClassNotFoundException {
         this.type = type;
         this.filter = filter;
         FndRecordMeta meta = ctx.getFilterEnabledEntities().getEntry(entityName);
    	 this.key = createKey(meta);
      }

      /**
       * Create the key used to identify given view in the filter map: PKG.VIEW
       */
      static String createKey(FndRecordMeta metaView) {
         return "VIEW:" + metaView.getPackage() + "." + metaView.getType();
      }

      /**
       * Create the key used to identify given attribute in the filter map: PKG.VIEW.ATTR
       */
      static String createKey(FndCompoundAttributeMeta metaAttr) {
         FndRecordMeta owner = metaAttr.getRecordMeta();
         return "ATTR:" + owner.getPackage() + "." + owner.getType() + "." + metaAttr.getName();
      }

      /**
       * Create the key used to identify given reference in the filter map: PKG.VIEW.REF
       */
      static String createKey(FndCompoundReferenceMeta metaRef) {
         FndRecordMeta owner = metaRef.getOwner();
         return "REF:" + owner.getPackage() + "." + owner.getType() + "." + metaRef.getName();
      }

      /**
       * Gets the filter form this cache entry.
       * @return
       */
      public FndFilter getFndFilter() {
         return filter;
      }

//      /**
//       * Gets the entity (record meta-data) form this cache entry.
//       */
//      public FndRecordMeta getRecordMeta() {
//         return meta;
//      }

      /**
       * Gets the cache key.
       * @return
       */
      public String getKey() {
         return key;
      }

      /**
       * Sets the cache key.
       * @param key
       */
      public void setKey(String key) {
         this.key = key;
      }


      /**
       * Gets a string representation of this cache entry.
       * @return
       */
      @Override
      public String toString() {
         //String entityStr = meta.getPackage() + "." + meta.getType();
         String filterStr = filter.getPackage() + "." + filter.getName();
         return key + ":" + filterStr + (type == STANDARD_FILTER ? "(static)" : "(dynamic)"); // entityStr + ":" + filterStr + (type == STANDARD_FILTER ? "(static)" : "(dynamic)");
      }
   }

   //==============================================================================
   // Instance variables and methods
   //==============================================================================

   // cache with filter entries
   private final ConcurrentHashMap<String, Entry> map;

   private FndFilterCache() {
      map = new ConcurrentHashMap<>(1024);
   }

   /**
    * @param c an open database connection
    * @param filterId ID that uniquely identifies a filter in the cache
    * @param filterType the type that controls the way a filter is defined and created
    */
   private Entry getEntry(FndConnection c, String filterId, int filterType) throws IfsException {
      Entry entry = map.get(filterId);
      if(entry == null) {
         //
         // Create and store a new uninitialized entry, putIfAbsent is used to guarantee that no duplicates are created.
         //
         Entry newEntry = new Entry();
         entry = map.putIfAbsent(filterId, newEntry);
         if(entry == null) {
            entry = newEntry; // put succeeded, use new value
         }
         //
         // Initialize the entry by fetching filter definition from the database.
         // Synchronize on the filter entry, so that other filters may be initialized in parallel.
         //
         synchronized(entry) {
            if(entry.getFndFilter() == null) {
               if(filterType == STANDARD_FILTER) {
                  throw new RuntimeException("There are no FNDDR definitions in the database");
               }
               else {
                  setPermissionSetFilterFromDatabase(entry, c, filterId);
               }
            }
         }
      }
      return entry;
   }

   //=============================================================================
   // Permission Set Filter
   //=============================================================================

   private static String createSelectPermissionSetFilter() {
      String appOwner = FndPlsqlConfig.getApplicationOwner();
      String sql =
         " select F.name filter_name, where_stmt, F.entity_id entity_name \n" +
         " from &AO.permission_set_filter_tab F \n" +
         " where F.filter_id = ?";
      sql = Str.replace(sql, "&AO", appOwner);
      return sql;
   }

   private void setPermissionSetFilterFromDatabase(Entry entry, FndConnection c, String filterId) throws IfsException {
      FndStatement stmt = null;

      try {
         FndServerContext ctx = FndServerContext.getCurrentServerContext();
         FndAttribute workspaceAttr = ctx.getAppContext().getAttribute("WORKSPACE");
         String workspace = workspaceAttr == null ? "*" : workspaceAttr.toString();
         stmt = c.createStatement();
         stmt.defineParameter(new FndSqlValue("FILTER",    filterId));
         stmt.prepare(SELECT_PERMISSION_SET_FILTER);
         stmt.executeQuery();

         FndResultSet rs = stmt.getFndResult();
         if(!rs.next())
            throw new SystemException(Texts.GETPERMSETFILTER, filterId, workspace);

         String filterName = stmt.getText(1);
         String whereStmt  = stmt.getLongText(2);
         String entityName = stmt.getText(3);
         FndFilter fndFilter = new FndFilter(entityName.toUpperCase(), filterName, "", whereStmt);
         entry.init(ctx, PERMISSION_SET_FILTER, fndFilter, entityName);

//         String entityClassName = "ifs.entity." + entityName.toLowerCase() + "." + entityName + "Entity";
//         Class<?> entityClass = Class.forName(entityClassName);
//         Field viewMetaField = entityClass.getField("viewMeta");
//         FndRecordMeta meta = (FndRecordMeta) viewMetaField.get(null);
//
//         FndFilter fndFilter = new FndFilter(entityName.toUpperCase(), filterName, "", whereStmt);
//         entry.init(PERMISSION_SET_FILTER, fndFilter, meta);
      }
      catch (NoSuchFieldException | IllegalAccessException | SQLException | ClassNotFoundException e) {
         throw new SystemException(e, Texts.GETFILTERENTRY, e.getMessage());// toSystemException(e);
      }
      finally {
         if (stmt != null)
            stmt.close();
      }
   }

//   private static SystemException toSystemException(Exception e) {
//      return new SystemException(e, Texts.GETFILTERENTRY, e.getMessage());
//   }
}
