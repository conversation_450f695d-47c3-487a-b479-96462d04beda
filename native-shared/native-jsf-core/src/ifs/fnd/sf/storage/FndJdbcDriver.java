/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.storage;

/**
 * <B>Framework internal class:</B> Enumeration class representing a JDBC driver used at runtime.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public enum FndJdbcDriver { //TODO:This class could be removed as there is only one driver.

   /**
    * Constant representing Oracle JDBC Driver, synonym: "Oracle" (the default driver).
    */
   ORACLE ("Oracle");

   private final String synonym;

   private FndJdbcDriver(String synonym) {
      this.synonym = synonym;
   }

   /**
    * Returns an alternate name for this driver.
    * @return a mixed-case synonym defined in this class
    */
   public final String synonym() {
      return synonym;
   }

   /**
    * Returns an instance of FndJdbcDriver with the specified name or synonym.
    * If the specified argument is null then the default driver is returned.
    * @param name the declared upper-case name or mixed-case synonym defined in this class (extraneous whitespace characters are removed)
    * @return an instance of FndJdbcDriver
    * @throws IllegalArgumentException if there is no FndJdbcDriver with the specified name or synonym
    */
   public static FndJdbcDriver getDriver(String name) {
      name = name.trim();
      for (FndJdbcDriver driver : FndJdbcDriver.values()) {
         if(driver.synonym().equals(name))
            return driver;
      }

      return valueOf(name); // name must match an upper-case identifier used to declare an enum constant in this class
   }

   /**
    * Constant indicating what JDBC driver is used at runtime for getting database connections.
    */
   private static final FndJdbcDriver CURRENT_DRIVER = ORACLE;

   /**
    * Gets the type of JDBC driver that is used at runtime for getting database connections.
    * @return a not-null instance of this enum class
    */
   public static FndJdbcDriver getCurrentDriver() {
      return CURRENT_DRIVER;
   }
}