/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */


package ifs.fnd.sf.storage;

import ifs.fnd.record.FndValidation;
import ifs.fnd.record.FndView;
import ifs.fnd.util.ResetableListMap;
import java.util.Iterator;

/**
 * <B>Framework internal class:</B> Collection of active validation rules.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class FndValidationMap {

   private final ResetableListMap<Class<?>, FndValidation> map;   // (view class -> list of FndValidation)

   public FndValidationMap() {
      map = ResetableListMap.newMap("FndValidationMap");
   }

   /**
    * Add the specified validation rules to the map.
    * @param <V> a subclass of FndView
    * @param rules validation rules to add
    */
   public <V extends FndView> void addRules(FndValidation<V> rules) {
      map.add(rules.getViewClass(), rules);
   }

   /**
    * Apply all rules active for the specified view and for all its parents (superclasses).
    * @param <V> a subclass of FndView
    * @param view a view to apply the validation rules on
    */
   public <V extends FndView> void applyRules(V view) {
      Class c = view.getClass();
      do {
         Iterator<FndValidation> iter = map.iterator(c);
         while(iter.hasNext()) {
            // This cast is correct. Why?
            @SuppressWarnings("unchecked") FndValidation<? super V> rules = iter.next();
            rules.applyRules(view);
         }
         c = c.getSuperclass();
      }
      while(c != FndView.class);
   }

   /**
    * Class that represents a state of the validation map.
    */
   public static class State {
      private final ResetableListMap.State<FndValidation> state;

      State(ResetableListMap.State<FndValidation> state) {
         this.state = state;
      }
   }

   /**
    * Marks the current state of the validation map.
    * @return an instance of nested class State that identifies the current state of the map
    */
   public State mark() {
      return new State(map.mark());
   }

   /**
    * Resets the state of the validation map.
    * @param state an instance of nested class State returned by mark()
    * @throws IllegalStateException if the specified state does not match the state returned by the last call to mark()
    */
   public void reset(State state) throws IllegalStateException {
      map.reset(state.state);
   }

   @Override
   public String toString() {
      return map.toString();
   }
}
