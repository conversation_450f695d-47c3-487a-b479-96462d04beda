/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.storage;

import ifs.fnd.base.IfsException;
import ifs.fnd.record.*;

/***
 * Interface used to save instances of modeled FndLUWrapperViews
 */
public interface IFndLUWrapperSave {

   /**
    * Store changes to an FndLUWrapperView in the corresponding Logical Unit in IFS Applications.
    * @param   view the input FndLUWrapperView.
    * @throws  IfsException   if there is a problem saving the data
    */   
   void save(FndLUWrapperView view) throws IfsException;
}


