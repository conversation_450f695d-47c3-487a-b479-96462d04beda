/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.storage;

import java.sql.Connection;
import java.sql.SQLException;

/**
 * <B>Framework internal class:</B> Common utilities for JDBC connections.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public final class FndJdbcUtil {

   private FndJdbcUtil() {
   }

   /**
    * Check if a connection is valid.
    * @param c the database connection to check
    * @return true if a call to a driver specific validation method succeeds, false otherwise
    */
   public static boolean isValid(Connection c) {
      if (c instanceof oracle.jdbc.OracleConnection) {
         try {
            return ((oracle.jdbc.OracleConnection) c).pingDatabase() == 0;
         }
         catch (SQLException e) {
            return false;
         }
      }
      else {
         return true;
      }
   }
}

