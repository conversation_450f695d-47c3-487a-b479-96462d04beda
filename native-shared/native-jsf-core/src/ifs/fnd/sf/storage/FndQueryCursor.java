/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.storage;

import ifs.fnd.base.IfsException;
import ifs.fnd.base.SystemException;
import ifs.fnd.log.Logger;
import ifs.fnd.record.FndBaseEntityView;
import java.sql.SQLException;

/**
 * <B>Framework internal class:</B> Class representing a cursor over JDBC ResultSet.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class FndQueryCursor {

   private final FndResultSet resultSet; // used by nextRecord() method only
   private final FndConnection connection;   // used by nextRecord() method only
   private final FndSqlStorage.SelectStatementContext stmtCtx; // used by nextRecord() and close() method only
   private final FndSqlStorage storage; // used by nextRecord() and close() method only
   private final FndBaseEntityView condition; // used by nextRecord() method only

   /**
    * Create a new instance of FndQueryCursor.
    * @param resultSet
    * @param c
    * @param storage
    * @param stmtCtx
    * @param condition
    * @throws SystemException
    */
   protected FndQueryCursor(FndResultSet resultSet,
                            FndConnection c,
                            FndSqlStorage storage,
                            FndSqlStorage.SelectStatementContext stmtCtx,
                            FndBaseEntityView condition) throws SystemException {
      this.resultSet = resultSet;
      this.stmtCtx = stmtCtx;
      this.storage = storage;
      this.connection = c;
      this.condition = condition;
   }

   /**
    * Moves the cursor to the next record in the result set.
    * @return the current record, return null if there are no more records
    * @throws IfsException
    */
   protected FndBaseEntityView nextRecord() throws IfsException {
      Logger log = storage.getLogger();
      try{
         if(resultSet.next()) {
            FndBaseEntityView view = (FndBaseEntityView) storage.fetchCurrent(resultSet, connection, stmtCtx, condition);
            if(log.debug)
               log.debug("Row fetched");
            return view;
         }
         else{
            if(log.debug)
               log.debug("No more rows");
            close();
            return null;
         }
      }
      catch (SQLException e) {
         throw new SystemException(e, "REFCURSORNEXT:Unable to move cursor to next record." + " Error is: &1", e.getMessage().trim());
      }
   }

   /**
    * Close this cursor and its database connection.
    * @throws SystemException
    */
   public void close() throws SystemException {
      Logger log = storage.getLogger();
      if(log.debug)
         log.debug("Closing cursor &1", toString());
      try {
         stmtCtx.getStatement().close(); // unregister FndAbortableProcess
      }
      finally {
         storage.afterCall();
      }
   }


   /**
    * Check if result of last fetched value from the result set was null.
    * @return true if result was null, false otherwise.
    * @throws IfsException
    */
   public boolean resultWasNull() throws IfsException {
      return stmtCtx.stmt.resultWasNull();
   }
}
