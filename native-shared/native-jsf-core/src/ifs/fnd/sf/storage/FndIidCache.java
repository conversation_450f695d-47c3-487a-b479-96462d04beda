/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.storage;

import ifs.fnd.util.Str;
import ifs.fnd.base.*;
import ifs.fnd.log.LogMgr;
import ifs.fnd.log.Logger;
import ifs.fnd.record.FndSqlType;
import java.sql.SQLException;
import java.util.*;

/**
 * <B>Framework internal class:</B> Cache with IID enumerations per lanuage.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public final class FndIidCache {

   private static class Iid {
      private String[] values;
      private String[] dbValues;
   }
                            //      lang       plsqlPkg
   private Map<String, Map<String, Iid>> languageMap; // Map<String, <Map<String, Iid>>

   /**
    * The singleton instance of the cache.
    */
   private static FndIidCache instance = new FndIidCache();

   private FndIidCache() {
      languageMap = Collections.synchronizedMap(new HashMap<String, Map<String, Iid>>());
   }

   /**
    * Translates an IID value to the corresponding DB-value.
    * @param iidPlsqlPackage PLSQL package used to retrieve IID values and DB-values from the database
    * @param value a client value for the current language
    * @param c active database connection
    * @return the database value corresponding to the specified client value
    */
   public static String translateIidValue(String iidPlsqlPackage, String value, FndConnection c) throws IfsException {
      return instance.translateIidValueImpl(iidPlsqlPackage, value, c);
   }

   private String translateIidValueImpl(String iidPlsqlPackage, String value, FndConnection c) throws IfsException {
      String language = FndContext.getCurrentLanguage();
      Map<String, Iid> iidMap = (Map<String, Iid>)languageMap.get(language);
      if(iidMap == null) {
         iidMap = Collections.synchronizedMap(new HashMap<String, Iid>());
         languageMap.put(language, iidMap);
      }

      Iid iid = (Iid) iidMap.get(iidPlsqlPackage);
      if(iid == null) {
         iid = loadIid(iidPlsqlPackage, c);
         iidMap.put(iidPlsqlPackage, iid);
      }

      int index = indexOf(iid.values, value);
      if(index < 0)
         throw new SystemException("BADIIDVALUE:Invalid client value &1 for IID package: &2", value, iidPlsqlPackage);
      return iid.dbValues[index];
   }

   private static final String ENUMERATE_IID_VALUES = createEnumerateIidValues();

   private static String createEnumerateIidValues() {
      // 1. Enumerate -> client values for current language
      // 2. Encode every client value to db value
      // 3. Create two out-variables :VALUES and :DB_VALUES using '^-separator
      String appOwner = FndPlsqlConfig.getApplicationOwner();
      String sql =
         "declare \n" +
         "   sep_ constant varchar2(1) := '^'; \n" +
         "   values_    varchar2(32000); \n" +
         "   db_values_ varchar2(32000); \n" +
         "   value_     varchar2(32000); \n" +
         "   db_value_  varchar2(32000); \n" +
         "   i integer; \n" +
         "   j integer; \n" +
         "begin \n" +
         "   &AO.&PKG.Enumerate(values_); \n" +
         "   values_ := replace(values_, &AO.Client_SYS.field_separator_, sep_); \n" +
         "   i := 1; \n" +
         "   loop \n" +
         "      j := instr(values_, sep_, i); \n" +
         "      exit when j = 0; \n" +
         "      value_ := substr(values_, i, j - i); \n" +
         "      i := j + 1; \n" +
         "      db_value_ := &AO.&PKG.Encode(value_); \n" +
         "      db_values_ := db_values_ || db_value_ || sep_; \n" +
         "   end loop; \n" +
         "   ? := values_; \n" +
         "   ? := db_values_; \n" +
         "end;";
      sql = Str.replace(sql, "&AO", appOwner);
      return sql;
   }

   /**
    * Fetch from the database values and DB-values for the specified IID in the current language.
    */
   private Iid loadIid(String iidPlsqlPackage, FndConnection c) throws IfsException {
      FndStatement stmt = null;

      try {
         stmt = c.createStatement();
         String sql = Str.replace(ENUMERATE_IID_VALUES, "&PKG", iidPlsqlPackage);
         stmt.prepareCall(sql);
         stmt.defineOutParameter("VALUES", FndSqlType.TEXT);
         stmt.defineOutParameter("DB_VALUES", FndSqlType.TEXT);
         stmt.execute();
         String values = stmt.getOutString(1);
         String dbValues = stmt.getOutString(2);
         Logger log = LogMgr.getDatabaseLogger();
         if(log.debug) {
            log.debug("Loaded IID values for &1", iidPlsqlPackage);
            log.debug("   values: &1", values);
            log.debug("   DB-values &1", dbValues);
         }

         Iid iid = new Iid();
         iid.values = parseValueList(values);
         iid.dbValues = parseValueList(dbValues);
         return iid;
      }
      catch (SQLException e) {
         throw new SystemException(e, "EXECENUMIIDVALUES:Failed enumerating IID values: &1", e.toString());
      }
      finally {
         if(stmt != null)
            stmt.close();
      }
   }

   /**
    * Parses a "^"-separated list of values.
    */
   private static String[] parseValueList(String valueList) {
      StringTokenizer st = new StringTokenizer(valueList, "^");
      String[] arr = new String[st.countTokens()];
      for(int i=0; i<arr.length; i++)
         arr[i] = st.nextToken();
      return arr;
   }

   /**
    * Finds the index of a specified value.
    */
   private static int indexOf(String[] arr, String value) {
      int len = arr.length;
      for(int i=0; i<len; i++)
         if(arr[i].equals(value))
            return i;
      return -1;
   }
}

