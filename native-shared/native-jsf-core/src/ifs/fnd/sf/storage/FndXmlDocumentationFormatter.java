/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.storage;

import ifs.fnd.base.ParseException;
import ifs.fnd.util.Str;
import java.io.*;
import javax.swing.text.rtf.RTFEditorKit;
import javax.swing.text.*;

/**
 * Utility class for formatting documentation tags in XML documents.
 * The class transforms raw documentation tags containing term and term-usage identifiers
 * into English text by fetching term descriptions from database.
 */
public final class FndXmlDocumentationFormatter {

   private FndXmlDocumentationFormatter() {
   }

   public static String formatDocumentationText(String termDef, String def) throws ParseException {
      String termPart       = Str.isEmpty(termDef) ? "" : rtfToText(termDef).trim();
      String definitionPart = Str.isEmpty(def)     ? "" : "Technical Description: " + def;

      String midPart = "";
      if(!Str.isEmpty(termPart) && !Str.isEmpty(definitionPart)) {
         midPart = termPart.endsWith(".") ? " " : ". ";
      }

      return termPart + midPart + definitionPart;
   }

   public static String rtfToText(String rtf) throws ParseException {
      try {
         RTFEditorKit kit = new RTFEditorKit();
         Document doc = kit.createDefaultDocument();
         Reader reader = new StringReader(rtf);
         kit.read(reader, doc, 0);
         String plainText = doc.getText(0, doc.getLength());
         return Str.isEmpty(plainText) ? rtf : plainText;
      }
      catch(IOException | BadLocationException x) {
         throw new ParseException(x, "XML_DOC_RTF2TXT: Error while converting RTF documentation to text: &1", x.getMessage());
      }
   }
}
