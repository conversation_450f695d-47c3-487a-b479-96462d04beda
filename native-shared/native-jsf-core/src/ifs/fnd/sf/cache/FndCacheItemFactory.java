/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.cache;

import ifs.fnd.base.IfsException;

/**
 * <B>Framework internal class:</B> Interface that cache item factories must implement.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public interface FndCacheItemFactory {

   /** Create a new cacheable item.
    *  @param identifier Cache item key.
    *  @return A new cacheable object or null if nothing to cache.
    */
   FndCacheable getCacheItem(String identifier) throws IfsException;

}
