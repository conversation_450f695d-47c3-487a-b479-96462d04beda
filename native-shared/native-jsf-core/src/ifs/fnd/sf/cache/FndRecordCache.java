/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.cache;

import ifs.fnd.base.*;
import ifs.fnd.record.*;

/**
 * <B>Framework internal class:</B> FndRecordCache caches records (FndAbstractRecord).
 *  @see FndCacheManager
 *  @see FndDynamicCacheManager
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public final class FndRecordCache {

   private FndCacheManager cache = null;
   private FndCacheItemFactory factory = null;

   /** Creates a new instance of FndRecordCache.
    *  @param lifespan Cache item lifespan (0 means item lives forever).
    *  @param factory Factory for cached items.
    *  @param capacity Initial cache capacity.
    */
   public FndRecordCache(int lifespan, FndRecordCacheItemFactory factory, int capacity) {
      this.factory = new CacheFactory(factory, lifespan);
      this.cache = new FndCacheManager(this.factory, capacity);
   }

   /** Creates a new instance of FndRecordCache.
    *  @param lifespan Cache item lifespan (0 means item lives forever).
    *  @param factory Factory for cached items.
    *  @param capacity Initial cache capacity.
    *  @param dynamic Specifies whether or not to use dynamic cache manager (true = dynamic).
    */
   public FndRecordCache(int lifespan, FndRecordCacheItemFactory factory, int capacity, boolean dynamic) {
      this.factory = new CacheFactory(factory, lifespan);
      if (dynamic)
         this.cache = new FndDynamicCacheManager(this.factory, capacity);
      else
         this.cache = new FndCacheManager(this.factory, capacity);
   }

   /** Get a record from cache from cache. (Record will not be cloned!)
    *  @param identifier Cached record identifier.
    *  @return Cached record or null or not found.
    */
   public FndAbstractRecord get(String identifier) throws IfsException {
      FndCacheable item = cache.get(identifier);

      if (item == null)
         return null;
      else
         return (FndAbstractRecord)item.getObject();
   }

   /** Get a record from cache. Optionally clone the record in cache.
    *  @param identifier Cached record identifier.
    *  @param cloning True = return a clone of the record found in cache.
    *  @return Record found in cache (or a clone) or null if not found.
    */
   public FndAbstractRecord get(String identifier, boolean cloning) throws IfsException {
      FndAbstractRecord rec = get(identifier);
      if (cloning && rec != null) {
         try {
            rec = (FndAbstractRecord)rec.clone();
         }
         catch (CloneNotSupportedException e) {
            throw new SystemException(e, "RECCACHE:Unable to clone record in cache.");
         }
      }
      return rec;
   }

   /** Find a record in cache without updating the cache if the record is not found.
    *  @param identifier Cached record identifier.
    *  @return Record found in cache or null if not found.
    */
   public FndAbstractRecord find(String identifier) {
      FndCacheable item = cache.find(identifier);
      if (item == null)
         return null;
      else
         return (FndAbstractRecord) item.getObject();
   }

   /** Remove an item from cache.
    *  @param identifier Identifier (cache key) of record to remove from cache.
    */
   public void remove(String identifier) throws IfsException {
      cache.remove(identifier);
   }

   /** Remove all cached records from cache.
    */
   public void clear() {
      cache.clear();
   }

   /** Cache item factory.
    */
   private static class CacheFactory implements FndCacheItemFactory {
      private FndRecordCacheItemFactory recordFactory;
      private int lifespan = 0;

      public CacheFactory(FndRecordCacheItemFactory factory, int lifespan) {
         this.recordFactory = factory;
         this.lifespan = lifespan;
      }

      @Override
      public FndCacheable getCacheItem(String identifier) throws IfsException {
         FndAbstractRecord rec = recordFactory.getRecord(identifier);
         if (rec != null)
            return new FndCacheItem(rec, identifier, lifespan);
         else
            return null;
      }
   }

}
