/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.cache;

import java.util.WeakHashMap;
import java.util.Collections;

/**
 * <B>Framework internal class:</B> A generic cache manager.
 * Caches any object implementing the FndCacheable interface.
 * The cache will never grow to the point were an OutOfMemoryError is thrown.
 * Cached items that have not yet expired may in some cases be removed from cache.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public final class FndDynamicCacheManager extends FndCacheManager {

   /**
    * Creates a new instance of FndDynamicCacheManager.
    * @param factory Factory to use to create new cacheable items.
    */
   public FndDynamicCacheManager(FndCacheItemFactory factory) {
      super(factory);
      this.cache = Collections.synchronizedMap(new WeakHashMap<String, FndCacheable>());
   }

   /**
    * Creates a new instance of FndDynamicCacheManager.
    * @param factory Factory to use to create new cacheable items.
    * @param capacity Initial cache capacity.
    */
   public FndDynamicCacheManager(FndCacheItemFactory factory, int capacity) {
      super(factory);
      cache = Collections.synchronizedMap(new WeakHashMap<String, FndCacheable>(capacity, 0.75f));
   }
}
