/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.cache;

import ifs.fnd.base.IfsException;
import ifs.fnd.base.SystemException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * <B>Framework internal class:</B> Generic cache manager.
 * Any object implementing the FndCacheable interface can be cached.
 * Cache size may grow very large unless you really know what you are doing.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class FndCacheManager {

   private final FndCacheItemFactory factory;
   protected Map<String, FndCacheable> cache;

   //Disable instantiations
   private FndCacheManager() {
      factory = null;
   }

   /**
    * Create a new cache instance.
    * Initial cache capacity defaults to 256.
    * @param factory Factory to use to create new cacheable items.
    */
   public FndCacheManager(FndCacheItemFactory factory) {
      this.cache = new ConcurrentHashMap<>(256);
      this.factory = factory;
   }

   /**
    * Create a new cache instance.
    * @param factory Factory to use to create new cacheable items.
    * @param capacity Initial cache capacity.
    */
   public FndCacheManager(FndCacheItemFactory factory, int capacity) {
      this.cache = new ConcurrentHashMap<>(capacity);
      this.factory = factory;
   }

   /**
    * Get an object from cache.
    * @param identifier Cache item identifier (key).
    * @return Cached object or null if not found.
    */
   private FndCacheable getCache(String identifier) throws SystemException {
      if (identifier == null || identifier.equals(""))
         throw new SystemException("FNDCACHENOID:Can not get cache item with no identifier");

      return (FndCacheable)cache.get(identifier);
   }

   /**
    * Remove an item from cache.
    * @param identifier Cache item identifier (key).
    */
   private void removeCache(String identifier) {
      cache.remove(identifier);
   }

   /**
    * Put an item in cache.
    * @param object Item to cache.
    */
   private void put(FndCacheable object) {
      cache.put(object.getIdentifier(), object);
   }

   /**
    * Try to get item using cache item factory.
    * @param identifier Cache item identifier (key).
    * @return Cached object or null if not found.
    */
   private FndCacheable refresh(String identifier) throws IfsException {
      FndCacheable item = factory.getCacheItem(identifier);
      if (item != null) {
         put(item);
         return item;
      }
      return null;
   }

   /**
    * Get an item from cache. If an item is not found, cache item factory will be
    * used to get and cache the object.
    * @return FndCacheable object from cache or null if not found.
    * @param identifier Cached item identifier.
    */
   public FndCacheable get(String identifier) throws IfsException {
      FndCacheable object = getCache(identifier);

      if (object == null) //Not found in cache.
         return refresh(identifier);

      if (object.isExpired()) {
         //Object has expired. Refresh it.
         removeCache(identifier);
         return refresh(identifier);
      }
      else //Object found in cache.
         return object;
   }

   /**
    * Find an item in the cache without updating the cache if the item is not found.
    * @return FndCacheable object from cache or null if not found.
    * @param identifier Cached item identifier.
    */
   public FndCacheable find(String identifier) {
      return (FndCacheable) cache.get(identifier);
   }

   /**
    * Remove an item from cache.
    * @param identifier Cache item identifier (key).
    */
   public void remove(String identifier) {
      removeCache(identifier);
   }

   /**
    * Clear cache (remove all cached items).
    */
   public void clear() {
      cache.clear();
   }

}