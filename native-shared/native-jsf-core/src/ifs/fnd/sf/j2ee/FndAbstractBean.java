/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.j2ee;

import ifs.fnd.sf.storage.FndTransaction;
import ifs.fnd.base.FndConstants;
import ifs.fnd.base.SystemException;
import ifs.fnd.base.IfsException;
import ifs.fnd.base.FndContext;
import ifs.fnd.base.FndFramework;
import ifs.fnd.base.capability.ApplicationCapability;
import ifs.fnd.capability.CapabilitySet;
import ifs.fnd.entities.fnduser.FndUserCache;
import ifs.fnd.log.*;
import ifs.fnd.sf.storage.FndAbstractHandler;
import java.util.Map;

import jakarta.annotation.Resource;
import jakarta.ejb.SessionContext;
import jakarta.ejb.TransactionAttribute;
import jakarta.interceptor.AroundInvoke;
import jakarta.interceptor.InvocationContext;
import javax.sql.DataSource;

/**
 * Base class for all EJB classes.
 */
public abstract class FndAbstractBean {

   private static final Tags FND_FLOW = FndAbstractHandler.FND_FLOW;

   // this logger may be used during initialization of a bean; it is then recreated by @AroundInvoke method
   protected Logger log;

   // logger used for debugging of EJB specific events
   protected Logger clsLog;

   protected FndAbstractBean() {
      log = LogMgr.getFrameworkLogger();
      clsLog = LogMgr.getClassLogger(FndAbstractBean.class);
      if(clsLog.debug) {
         clsLog.debug("Created bean [&1]", getClass().getName());
      }
   }

   @Resource
   protected SessionContext sessionContext;

   protected final DataSource ds = FndDataSource.FNDBAS.getDataSource();

   public DataSource getDataSource() throws SystemException {
      return ds;
   }

   protected SessionContext getSessionContext() {
      return sessionContext;
   }

   @AroundInvoke
   @SuppressWarnings("PMD.UnusedPrivateMethod")
   private Object aroundInvoke(InvocationContext ctx) throws Exception {

      // recreate the bean-protected instances of logger
      log = LogMgr.getFrameworkLogger();
      clsLog = LogMgr.getClassLogger(FndAbstractBean.class);

      Object[] params = ctx.getParameters();

      // prepare current FndContext for invocation of a business method
      FndJ2eeContext fndctx;
      if(this instanceof FndActivityBean) {

         Object lastParam = params[params.length - 1];
         if(lastParam instanceof FndContext) {
            // typed invoke(): set passed FndContext as current context
            fndctx = (FndJ2eeContext) lastParam;
            setCallerAsApplicationUser(fndctx);
            FndContext.setCurrentContext(fndctx);
         }
         else {
            // untyped invoke(): create new FndContext and set it as current context
            fndctx = (FndJ2eeContext) FndFramework.getFramework().newContext();
            setCallerAsApplicationUser(fndctx);
            FndContext.setCurrentContext(fndctx);

            if (lastParam instanceof Map) {
               @SuppressWarnings("unchecked")
               Map<String, Object> paramMap = (Map<String, Object>)lastParam;
               byte[] capabilities = (byte[]) paramMap.get("CAPABILITIES");
               fndctx.setSupportedCapabilities(CapabilitySet.fromByteArray(capabilities, ApplicationCapability.class));
            }
         }
      }
      else {
         // for nested beans the current FndContext has been already set
         fndctx = FndJ2eeContext.getCurrentJ2eeContext();
      }

      int ejbLevel = fndctx.getMethodStackSize();
      String ejbIndent = log.trace ? "               ".substring(0, ejbLevel) : null;
      FndEjbMethod method = new FndEjbMethod(this, ctx.getMethod());

      // assign a (new, existing or null) transaction to the method
      FndTransaction t = getTransction(fndctx.getCurrentEjbMethod(), method);
      method.setFndTransaction(t);

      // Clear InitFndSession counters in current context if top-level operation has been called.
      // Many such operations are called from FNDWEB servlet request passing the same FndRemoteContext.
      if(ejbLevel == 0) {
         fndctx.clearInitFndSessionCounters();
      }

      // push this method to the stack with current EJB methods
      fndctx.pushCurrentMethod(method);
      if(log.trace) {
         log.trace(FND_FLOW, "[&1] &2Enter EJB method &3", ejbLevel, ejbIndent, method);
      }

      if(!fndctx.isDebuggerRoleGrantedSet() && this instanceof FndActivityBean) {
         try {
            fndctx.setDebuggerRoleGranted(FndUserCache.isSystemPrivilegeGranted(FndConstants.DEBUGGER));
         }
         catch (IfsException e) {
            /* The call to isSystemPrivilegeGranted may fail, for example if the current transaction has been marked for rollback.
             * Should that happen it's mostly harmless as the only effect is that the enduser wouldn't see the stack trace in the
             * EE debug console.
             */
            fndctx.setDebuggerRoleGranted(false);
            log.warning("Failed checking if the user is granted the DEBUGGER system privilege");
         }
      }

      try {
         // invoke business method
         return ctx.proceed();
      }
      finally {
         // pop this method from the stack with current EJB methods
         fndctx.popCurrentMethod(method);
         if(log.trace) {
            log.trace(FND_FLOW, "[&1] &2Exit  EJB method &3", ejbLevel, ejbIndent, method);
            if(ejbLevel == 0) {
               log.trace(FND_FLOW, "Exit top-level operation [&1.&2]. Init_Fnd_Session calls = &3(&4)", getClass().getSimpleName(), ctx.getMethod().getName(), fndctx.getInitFndSessionDbCount(), fndctx.getInitFndSessionCount());
            }
         }

         // clear the current context after top-level bean method invocation
         if( this instanceof FndActivityBean && fndctx.isMethodStackEmpty() ) {
            FndContext.setCurrentContext(null);
            if(clsLog.debug) {
               clsLog.debug("Current context cleared on exit from bean [&1]", getClass().getName());
            }
         }
      }
   }

   /**
    * Sets the authenticated user as application user in the specified context.
    */
   protected void setCallerAsApplicationUser(FndJ2eeContext ctx) throws IfsException {
      try {
         String user = sessionContext.getCallerPrincipal().getName();
         if("<anonymous>".equals(user)) {
            user = "anonymous";
         }
         ctx.setApplicationUser(user);
         if(log.debug) {
            log.debug("Current application user set to authenticated user: &1", user);
         }
      } catch (IllegalStateException e) {
         throw new ifs.fnd.base.SecurityException(e, Texts.FNDSESSIONBEANSEC);
      }
   }

   /**
    * Mark transaction for rollback.
    */
   protected void rollback() {
      try {
         sessionContext.setRollbackOnly();
         Logger fmrLog = LogMgr.getFrameworkLogger();
         if (fmrLog.info) {
            fmrLog.info("Transaction rolled back");
         }
      } catch (IllegalStateException e) {
         /**
          * Ignore error (thrown by setRollbackOnly). There could be cases where
          * there is no transaction to abort.
          */
         assert true;
      }
   }

   /**
    * Gets an FndTransaction for the child method according to EJB specification.
    * @return parent transaction, new transaction or null (no transaction)
    */
   private FndTransaction getTransction(FndEjbMethod parent, FndEjbMethod child) {

      /*
         Transaction Attribute   Client's Transaction    Business Method's Transaction
         =====================   ====================    =============================
         Required                None                    T2
                                 T1                      T1

         RequiresNew             None                    T2
                                 T1                      T2

         Mandatory               None                    Error
                                 T1                      T1

         NotSupported            None                    None
                                 T1                      None

         Supports                None                    None
                                 T1                      T1

         Never                   None                    None
                                 T1                      Error
      */

      TransactionAttribute childAttr = child.getTransactionAttribute();
      if(childAttr == null) {
         return null;
      }

      FndTransaction parentTran = parent == null ? null : parent.getFndTransaction();

      switch(childAttr.value()) {

         case REQUIRED:
            if(parentTran != null) {
               return parentTran;
            }
            else {
               return new FndTransaction();
            }

         case REQUIRES_NEW:
            return new FndTransaction();

         case MANDATORY:
            return parentTran;

         case NOT_SUPPORTED:
            return null;

         case SUPPORTS:
            return parentTran;

         case NEVER:
            return null;
      }

      throw new AssertionError();
   }
}