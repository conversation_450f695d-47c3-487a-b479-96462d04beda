/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.j2ee;

import ifs.fnd.base.IfsException;
import ifs.fnd.base.SystemException;
import ifs.fnd.sf.storage.FndAbstractConnectionManager;
import ifs.fnd.sf.storage.FndConnection;

import java.sql.Connection;
import java.sql.SQLException;
import javax.sql.DataSource;

/**
 * <B>Framework internal class:</B> J2EE-specific connection manager.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class FndJ2eeConnectionManager extends FndAbstractConnectionManager {

   /**
    * Get a database connection.
    * @param username the database user on whose behalf the connection is being made.
    * @param password the user's password.
    * @return database connection
    */
   @Override
   protected FndConnection getConnection(String username, String password) throws IfsException {
      FndJ2eeContext ctx = FndJ2eeContext.getCurrentJ2eeContext();
      FndAbstractBean bean = ctx.findCurrentBean();
      try {
         DataSource ds = ctx.getDataSource();
         if(ds == null) {
            ds = bean==null ? FndDataSource.FNDBAS.getDataSource() : bean.getDataSource();
         }
         Connection c = username == null ? ds.getConnection() : ds.getConnection(username, password);
         return new FndConnection(c);
      }
      catch (SQLException e) {
         throw new SystemException(e, Texts.GETDBCONN, e.getMessage());
      }
   }
}
