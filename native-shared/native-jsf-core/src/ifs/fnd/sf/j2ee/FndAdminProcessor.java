/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.sf.j2ee;

import jakarta.jms.Message;
import jakarta.jms.JMSException;

import ifs.fnd.base.IfsException;
import ifs.fnd.base.IfsRuntimeException;
import ifs.fnd.sf.admin.*;
import ifs.fnd.log.*;

/**
 * <B>Framework internal class:</B> Processes administrative tasks for the application,
 * such as aborting requests and flushing configuration data.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public final class FndAdminProcessor {

   //Name of 'method' property
   public static final String METHOD_PROPERTY_NAME = "METHOD";
   //Value of 'method' property used to clear system cache
   public static final String CLEAR_CACHE_METHOD = "CLEAR_CACHE";

   private FndAdminProcessor() {
   }

   public static void proccessRequest(Message m) {
      Logger log = LogMgr.getFrameworkLogger();
      try {
         String method = m.getStringProperty("admin_method");
         String value  =  m.getStringProperty("admin_value");
         if (log.trace) {
            log.trace("FndAdminProcessor: Started processing request for [&1] with value [&2]", method, value);
         }
         switch (method) {
            case CLEAR_CACHE_METHOD:
                  FndAdminProcessor.clearCache(value);
               break;
            default:
               if (log.trace) {
                  log.trace("FndAdminProcessor: Doesn't support requests for [&1]", method);
               }
         }
         if (log.trace) {
            log.trace("FndAdminProcessor: Ended processing request for [&1] with value [&2]", method, value);
         }
      }
      catch (JMSException | IfsException | RuntimeException e) {
         log.error(e, "FndAdminProcessor: Unable to process request: &1", e.getMessage());
         if(e instanceof RuntimeException) {
            throw new IfsRuntimeException(e, "FndAdminProcessor failed to process message &1: &2", m.toString(), e.toString());
         }
      }
   }

   private static void clearCache(String cacheType) throws JMSException, IfsException {
      Logger log = LogMgr.getFrameworkLogger();
      if (log.trace) {
         log.trace("FndAdminProcessor received clear cache request for: &1", cacheType);
      }
      (new ClearCacheOperation()).perform(cacheType, log);
   }
}
