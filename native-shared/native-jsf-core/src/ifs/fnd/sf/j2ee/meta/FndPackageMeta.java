/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.j2ee.meta;

import ifs.fnd.base.IfsException;
import ifs.fnd.service.Util;
import java.util.ArrayList;

/**
 * <B>Framework internal class:</B> Immutable meta-object describing a package in a J2EE application.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public final class FndPackageMeta {

   private String name;
   private String component;
   private ArrayList<FndHandlerMeta> handlers;

   /**
    * Constructs a new FndPackageMeta with the specified contents.
    */
   FndPackageMeta(String name, String component) {
      this.name = name;
      this.component = component;
      handlers = Util.newArrayList();
   }

   /**
    * Ends the creation phase.
    */
   void freeze() throws IfsException {
      handlers.trimToSize();
      for(FndHandlerMeta handler : handlers) {
         handler.freeze();
      }
   }

   /**
    * Adds a handler to this package.
    */
   void add(FndHandlerMeta handler) {
      handlers.add(handler);
   }

   /**
    * Returns the component name this package belongs to.
    */
   public String getComponent() {
      return component;
   }

   /**
    * Returns the name of this package.
    */
   public String getName() {
      return name;
   }

   /**
    * Gets the number of handlers in this package.
    */
   public int getHandlerCount() {
      return handlers.size();
   }

   /**
    * Gets the handler at specified index on the list with all handlers in this package.
    */
   public FndHandlerMeta getHandler(int index) {
      return handlers.get(index);
   }
}
