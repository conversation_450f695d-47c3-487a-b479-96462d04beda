/*
 * ==================================================================================
 * File:         OperationArray
 * Package:      Client_Gateway_Support
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 * ==================================================================================
 */

package ifs.fnd.sf.j2ee.meta;

import ifs.fnd.record.*;
import ifs.fnd.record.serialization.*;
import ifs.fnd.base.*;

/**
 * Array of <code>Operation</code>.
 */
public class OperationArray extends FndAbstractArray {

   public OperationArray() {
      super();
   }

   public OperationArray(FndAttributeMeta meta) {
      super(meta);
   }

   public boolean add(Operation operation) {
      return internalAdd(operation);
   }

   public void add(int index, Operation operation) {
      internalAdd(index, operation);
   }

   public void assign(OperationArray from) throws SystemException {
      assign(from, true);
   }

   public boolean contains(Operation operation) {
      return internalContains(operation);
   }

   public Operation firstElement() {
      return (Operation)internalFirstElement();
   }

   public Operation get(int index) {
      return (Operation)internalGet(index);
   }

   public int indexOf(Operation operation) {
      return internalIndexOf(operation);
   }

   public Operation lastElement() {
      return (Operation)internalLastElement();
   }

   public int lastIndexOf(Operation operation) {
      return internalLastIndexOf(operation);
   }

   public Operation remove(int index) {
      return (Operation)internalRemove(index);
   }

   public Operation set(int index, Operation operation) {
      return (Operation)internalSet(index, operation);
   }

   @Override
   public FndAbstractRecord newRecord() {
      return new Operation();
   }

   @Override
   protected FndAbstractRecord newRecord(FndTokenReader stream) throws ParseException {
      Operation operation = new Operation();
      operation.parse(stream);
      return operation;
   }
   @Override
   protected FndAttribute newAttribute(FndAttributeMeta meta) {
       return new OperationArray(meta);
   }

}
