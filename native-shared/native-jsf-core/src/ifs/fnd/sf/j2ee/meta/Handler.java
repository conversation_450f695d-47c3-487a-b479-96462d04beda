/*
 * ==================================================================================
 * File:         Handler.java
 * Package:      Client_Gateway_Support
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 * ==================================================================================
 */

package ifs.fnd.sf.j2ee.meta;

import ifs.fnd.record.*;
import ifs.fnd.base.SystemException;

/**
 * <code>Handler</code> view.
 */
public class Handler extends FndView {
   private static final FndRecordMeta viewMeta  = new FndRecordMeta("META_CACHE", "HANDLER");

   private static final FndAttributeMeta nameMeta       = new FndAttributeMeta(viewMeta, "NAME", null, 0, 100);
   private static final FndAttributeMeta typeMeta       = new FndAttributeMeta(viewMeta, "TYPE", null, 0, 100);
   private static final FndAttributeMeta operationsMeta = new FndAttributeMeta(viewMeta, "OPERATIONS");

   public final FndAlpha name = new FndAlpha(nameMeta);
   public final FndAlpha type = new FndAlpha(typeMeta);
   public final OperationArray operations = new OperationArray(operationsMeta);

   public Handler() {
      super(viewMeta);
      add(name);
      add(type);
      add(operations);
   }

   @Override
   public FndAbstractRecord newInstance() {
      return new Handler();
   }

   public void assign(Handler from)  throws SystemException {
      super.assign(from);
   }

}
