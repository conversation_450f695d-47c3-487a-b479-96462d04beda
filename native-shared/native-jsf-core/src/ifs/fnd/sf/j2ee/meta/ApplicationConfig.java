/*
 * ==================================================================================
 * File:         Application.java
 * Package:      Client_Gateway_Support
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 * ==================================================================================
 */

package ifs.fnd.sf.j2ee.meta;

import ifs.fnd.record.*;
import ifs.fnd.base.SystemException;


/**
 * View containing configuration data.
 */
public class ApplicationConfig extends FndView {
   private static final FndRecordMeta viewMeta  = new FndRecordMeta("META_CACHE", "APPLICATION_CONFIG");

   private static final FndAttributeMeta packagesMeta = new FndAttributeMeta(viewMeta, "PACKAGES");

   /**
    * Packages in this application
    */
   public final PkgArray                 packages     = new PkgArray(packagesMeta);

        /**
         * Create a new view instance.
         */
   public ApplicationConfig() {
      super(viewMeta);
      add(packages);
   }

        /**
         * Create a new instance.
         */
   @Override
   public FndAbstractRecord newInstance() {
      return new ApplicationConfig();
   }

        /**
         * Assignment between two views of the same type.
         * @param from view to take values from
         * @throws SystemException if assignment fails
         */
   public void assign(ApplicationConfig from)  throws SystemException {
      super.assign(from);
   }

}
