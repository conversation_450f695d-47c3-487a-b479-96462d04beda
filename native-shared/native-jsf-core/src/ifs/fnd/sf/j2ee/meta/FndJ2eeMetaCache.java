/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.j2ee.meta;

import ifs.fnd.base.*;
import ifs.fnd.log.*;
import ifs.fnd.record.*;
import ifs.fnd.record.serialization.FndXmlUtil;
import ifs.fnd.service.IfsProperties;
import ifs.fnd.sf.j2ee.FndImplementationBean;
import ifs.fnd.util.IoUtil;
import java.io.*;
import java.lang.reflect.*;
import java.util.*;

/**
 * <B>Framework internal class:</B> Cache with meta-objects describing the current J2EE application.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public final class FndJ2eeMetaCache {

   private static final String LOG_DIR;
   private static final String LOG_SOAP_LOCATION;

   static {
      IfsProperties p = IfsProperties.getSnapshot(); // Static IFS property
      LOG_DIR = p.getProperty("fnd.j2eeMetaCacheLogDir");
      LOG_SOAP_LOCATION = p.getProperty("fnd.j2eeMetaCacheLogSoapLocation");
   }

   /**
    * The singleton instance of the current meta-application.
    */
   private static FndApplicationMeta application;

   private FndJ2eeMetaCache() {}

   /**
    * Returns a read-only meta-object describing the current J2EE application.
    * @return the singleton instance of FndApplicationMeta
    */
   public static FndApplicationMeta getApplication() {
      return application;
   }

   /**
    * Structure definition used for meta cache initialization
    * from servlet
    */
   public static class HandlerDef {

      public static class OperationDef {

         private final String name;

         private OperationDef(String name) {
            this.name = name;
         }
      }

      private final String        packageName;
      private final String        handlerName;
      private List<OperationDef>  operations;

      public HandlerDef(String pkgName, String handlerName) {
         this.packageName = pkgName;
         this.handlerName = handlerName;
      }

      public OperationDef addOperation(String name) {
         OperationDef op = new OperationDef(name);
         if(this.operations==null)
            this.operations = new ArrayList<>();
         this.operations.add(op);
         return op;
      }

      @Override
      public String toString() {
         StringBuilder buf = new StringBuilder();
         buf.append(this.packageName).append('.').append(this.handlerName).append('{');
         if(this.operations!=null) {
            boolean first = true;
            for(OperationDef op:this.operations) {
               if(!first)
                  buf.append(';');
               buf.append(op.name);
               first = false;
            }
         }
         buf.append('}');
         return buf.toString();
      }
   }

   /**
    * Register mappings handler -> bean, class
    * @param appCfg application configuration
    * @param log an instance of ifs.fnd.log.Logger class
    * @return the list with names of all beans in the current application
    */
   private static List<HandlerDef> createMappings( ApplicationConfig appCfg, Logger log ) throws SystemException {
      if(appCfg == null)
         throw new SystemException("FNDEXTCFGERR: Configuration file has not been set.");

      List<HandlerDef> beanList = new ArrayList<>();
      if(log.debug) log.debug("Reading application configuration file.");

      int pkgSize = appCfg.packages.size();
      for( int i=0; i<pkgSize; i++ ) {
         Pkg pkg = appCfg.packages.get(i);
         String pkgName = pkg.name.getValue();

         int handlerSize = pkg.handlers.size();
         for( int j=0; j<handlerSize; j++ ) {
            Handler handler = pkg.handlers.get(j);
            String  hName   = handler.name.getValue();
            if(log.debug) log.debug("Adding EJB: &1.&2 to beanList", pkgName, hName);

            HandlerDef hDef = new HandlerDef(pkgName, hName);
            beanList.add(hDef);
         }
      }
      return beanList;
   }

   /**
    * Initializes the internal data structures with meta data.
    * @param appName the name of the current J2EE application
    * @param is InputStream containing application configuration data
    */
   public static void init(String appName, InputStream is) {
      Logger log = LogMgr.getFrameworkLogger();
      ApplicationConfig applicationConfig = new ApplicationConfig();

      try {
         if(is != null) {
            FndXmlUtil.parseRecord(is, applicationConfig, false);
            if(log.debug) log.debug("Application &1: reading application-config.xml", appName);
         }
         List<HandlerDef> beanList = createMappings(applicationConfig, log);
         init(appName, beanList);
      }
      catch(IfsException e) {
         throw new IfsRuntimeException(e, "Could not initialize Meta Cache");
      }
   }

   /**
    * Initializes the internal data structures with meta data.
    * The method calls by reflection a static initialization method on
    * every bean class from the specified list. This initialization method
    * loads the beans's meta-data into the cache.
    * @param appName the name of the current J2EE application
    * @param beanList the list with names (in form "<packageName>.<beanName>") of all activity/service beans
    *        contained in the current application
    * @throws SystemException if the initialization fails for some reasons
    */
   private static void init(String appName, List<HandlerDef> beanList) throws IfsException {
      Logger log = LogMgr.getFrameworkLogger();
      if(log.info)
         log.info("Initializing meta-cache for J2EE application &1", appName);
      if(log.trace)
         log.trace("   beanList: "+beanList);

      if(application != null) {
         if(log.info)
            log.info("   WARNING! Meta-cache already initialized");
      }
      synchronized(FndJ2eeMetaCache.class)
      {
         if(application==null) {
            application = new FndApplicationMeta(appName);
            Class implBeanCls = FndImplementationBean.class;
            for(HandlerDef handler : beanList) {
               String clsName = "ifs.application." + handler.packageName.toLowerCase() + "." + handler.handlerName + "Bean";

               try {
                  Class<?> cls = Class.forName(clsName);
                  if(implBeanCls.isAssignableFrom(cls))
                     continue;
                  if(log.trace)
                     log.trace("Loading EJB class "+clsName);
                  Method method = cls.getMethod("initMetaCache", (Class[])null);
                  method.invoke(null, (Object[])null);
               }
               catch(ClassNotFoundException | IllegalAccessException | InvocationTargetException e) {
                  initError(e, clsName);
               }
               catch (NoSuchMethodException e) {
                  if(log.info)
                     log.info("WARNING! Method initMetaCache() not found in "+clsName);
               }
            }
            application.freeze();

            if(log.info)
               log.info("FndJ2eeMetaCache: Loaded: " +
                        application.getPackageCount() + " packages (EJB modules), " +
                        application.getHandlerCount() + " handlers (beans)");

            if(LOG_DIR != null) {
               spoolCache(LOG_DIR + File.separator + "cache", appName + ".lis");
               spoolCacheXml(LOG_DIR + File.separator + "cache", appName + ".xml");
            }

            if(LOG_DIR != null && LOG_SOAP_LOCATION != null)
               spoolWsdl(LOG_DIR + File.separator + "wsdl");
         }
      }
   }

   /**
    * Clears the internal data structures with meta data.
    */
   public static void clear() {
      application = null;
   }

   private static void initError(Exception cause, String clsName) {
      Logger log = LogMgr.getFrameworkLogger();
      if(log.warning)
         log.warning(cause, "WARNING! Could not initialize meta cache for bean class &1", clsName);
   }

   private static void spoolCache(String dir, String fileName) throws SystemException
   {
      try
      {
         IoUtil.mkdirs(new File(dir));
         BufferedWriter out = IoUtil.newBufferedWriter(dir, fileName);
         out.write("FndApplicationMeta: " + application.getName());
         out.newLine();
         out.newLine();

         for(int m=0; m<application.getPackageCount(); m++)
         {
            FndPackageMeta pkg = application.getPackage(m);
            out.write("   "+m+" FndPackageMeta: " + pkg.getName());
            out.newLine();
            out.newLine();

            for(int h=0; h<pkg.getHandlerCount(); h++)
            {
               FndHandlerMeta handler = pkg.getHandler(h);
               out.write("      "+h+" FndHandlerMeta: " + handler.getName());
               if(handler.getTermPath() != null)
                  out.write(" termPath=" + handler.getTermPath());
               out.newLine();
               out.newLine();

               for(int o=0; o<handler.getOperationCount(); o++)
               {
                  FndOperationMeta operation = handler.getOperation(o);
                  out.write("         "+o+" FndOperationMeta: " + operation.getName() +
                            " returnsArray=" + operation.returnsArray());
                  out.newLine();

                  FndParameterMeta requestParam  = operation.getRequestParameter();
                  FndParameterMeta responseParam = operation.getResponseParameter();
                  FndParameterMeta returnParam   = operation.getReturnParameter();

                  out.write("             REQ: FndParameterMeta:");
                  spoolParamMeta(requestParam, out);
                  out.newLine();

                  out.write("             RSP: FndParameterMeta:");
                  spoolParamMeta(responseParam, out);
                  out.newLine();

                  out.write("             RET: FndParameterMeta:");
                  spoolParamMeta(returnParam, out);
                  out.newLine();

                  for(int p=0; p<operation.getParameterCount(); p++)
                  {
                     FndParameterMeta param = operation.getParameter(p);
                     out.write("             "+p+":   FndParameterMeta:");
                     if( param==requestParam )
                        out.write(" = REQ");
                     else if( param==responseParam )
                        out.write(" = RSP");
                     else if( param==returnParam )
                        out.write(" = RET");
                     else
                        spoolParamMeta(param, out);
                     out.newLine();
                  }
                  out.newLine();
               }
               out.newLine();
            }
         }
         out.close();
      }
      catch(IOException e)
      {
         throw new SystemException(e, "FndJ2eeMetaCache spool failed: &1", e.getMessage());
      }
   }

   private static void spoolParamMeta( FndParameterMeta param, BufferedWriter out ) throws IOException
   {
      if(param==null) return;

      out.write("{"+System.identityHashCode(param)+"} name='"+param.getName()+"' type='"+param.getType()+"' direction='"+param.getDirection()+
                "' isArray="+param.isArray()+" isQueryCondition="+param.isQueryCondition() );
      FndRecordMeta intView = param.getInternalView();
      FndRecordMeta extView = param.getExternalView();
      out.newLine();
      spoolRecordMeta("internal", intView, out);
      out.newLine();
      spoolRecordMeta("external", extView, out);
      out.newLine();
   }

   private static void spoolRecordMeta( String prefix, FndRecordMeta view, BufferedWriter out ) throws IOException
   {
      out.write("                "+prefix+"View:");
      if(view==null) return;
      out.write(" type="+view.getType()+" className="+view.getViewClassName()+" parameterListView="+view.isParameterListView());
   }

   private static void spoolWsdl(String dir) throws SystemException {
      try {
         IoUtil.mkdirs(new File(dir));
      }
      catch (IOException e) {
         throw new SystemException(e, e.getMessage());
      }
      for (int p = 0; p < application.getPackageCount(); p++) {
         FndPackageMeta pkg = application.getPackage(p);
         for (int h = 0; h < pkg.getHandlerCount(); h++) {
            FndHandlerMeta handler = pkg.getHandler(h);
            String handlerName = handler.getName();
            try {
               String wsdl = FndJ2eeMetaUtil.formatWsdl(handlerName, LOG_SOAP_LOCATION);
               ifs.fnd.util.IoUtil.writeFile(dir + File.separator + handlerName + ".wsdl", wsdl);
            }
            catch (Exception e) {
               if (e.getMessage().indexOf("WSDL not supported for signature of operation") >= 0
                     || e.getMessage().indexOf("View class name missing in meta data for view") >= 0
                     || e.getMessage().indexOf("Missing meta view for record") >= 0) {
                  LogMgr.getFrameworkLogger().info("   WARNING! WSDL generation failed for handler &1: &2", handlerName, e.getMessage());
               }
               else if (e instanceof SystemException) {
                  throw (SystemException) e;
               }
               else if (e instanceof RuntimeException) {
                  throw (RuntimeException) e;
               }
               else {
                  throw new SystemException(e, e.getMessage());
               }
            }
         }
      }
   }

   //==========================================================================
   //
   //==========================================================================

   private static void spoolCacheXml(String dir, String fileName) throws SystemException
   {
      try
      {
         IoUtil.mkdirs(new File(dir));
         BufferedWriter out = IoUtil.newBufferedWriter(dir, fileName);
         out.write("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
         out.newLine();

         out.write("<application name=\"" + application.getName() + "\">");
         out.newLine();

         for(int m=0; m<application.getPackageCount(); m++)
         {
            FndPackageMeta pkg = application.getPackage(m);
            out.write(" <bean index=\""+m+"\" name=\""+pkg.getName()+"\">");
            out.newLine();

            for(int h=0; h<pkg.getHandlerCount(); h++)
            {
               FndHandlerMeta handler = pkg.getHandler(h);
               out.write("  <handler index=\""+h+"\" name=\""+handler.getName()+"\"");
               if(handler.getTermPath() != null)
                  out.write(" termPath=\""+handler.getTermPath()+"\"");
               out.write(">");
               out.newLine();

               for(int o=0; o<handler.getOperationCount(); o++)
               {
                  FndOperationMeta operation = handler.getOperation(o);
                  out.write("   <operation index=\""+o+"\" name=\""+operation.getName()+"\""+
                            " returnsArray=\""+operation.returnsArray()+"\">");
                  out.newLine();

                  FndParameterMeta requestParam  = operation.getRequestParameter();
                  FndParameterMeta responseParam = operation.getResponseParameter();
                  FndParameterMeta returnParam   = operation.getReturnParameter();

                  spoolParamMetaXml(requestParam,  out, "request" , -1);
                  spoolParamMetaXml(responseParam, out, "response", -1);
                  spoolParamMetaXml(returnParam,   out, "return"  , -1);

                  for(int p=0; p<operation.getParameterCount(); p++)
                  {
                     FndParameterMeta param = operation.getParameter(p);
                     if( param==requestParam )
                     {
                        out.write("    <parameter index=\""+p+"\" ref=\"request\"/>");
                        out.newLine();
                     }
                     else if( param==responseParam )
                     {
                        out.write("    <parameter index=\""+p+"\" ref=\"response\"/>");
                        out.newLine();
                     }
                     else if( param==returnParam )
                     {
                        out.write("    <parameter index=\""+p+"\" ref=\"return\"/>");
                        out.newLine();
                     }
                     else
                        spoolParamMetaXml(param, out, null, p);
                  }

                  out.write("   </operation>");
                  out.newLine();
               }
               out.write("  </handler>");
               out.newLine();
            }
            out.write(" </bean>");
            out.newLine();
         }

         out.write("</application>");
         out.newLine();

         out.close();
      }
      catch(IOException e)
      {
         throw new SystemException(e, "FndJ2eeMetaCache spool failed: &1", e.getMessage());
      }
   }

   private static void spoolParamMetaXml( FndParameterMeta param, BufferedWriter out, String type, int index ) throws IOException
   {
      if(param==null) return;

      out.write("    <parameter");
      if( index>=0 )
         out.write(" index=\""+index+"\"");
      if( type!=null )
         out.write(" parType=\""+type+"\"");

      out.write(" hash=\""+System.identityHashCode(param)+"\" name=\""+param.getName()+"\" type=\""+param.getType()+
                "\" direction=\""+param.getDirection()+
                "\" array=\""+param.isArray()+"\" query=\""+param.isQueryCondition()+"\">" );
      out.newLine();

      FndRecordMeta intView = param.getInternalView();
      FndRecordMeta extView = param.getExternalView();

      spoolRecordMetaXml("internal", intView, out);
      spoolRecordMetaXml("external", extView, out);

      out.write("    </parameter>");
      out.newLine();
   }

   private static void spoolRecordMetaXml( String prefix, FndRecordMeta view, BufferedWriter out ) throws IOException
   {
      out.write("     <"+prefix+"View");
      if(view!=null)
         out.write(" type=\""+view.getType()+"\" className=\""+view.getViewClassName()+"\" parameterListView=\""+view.isParameterListView()+"\"");
      out.write("/>");
      out.newLine();
   }
}
