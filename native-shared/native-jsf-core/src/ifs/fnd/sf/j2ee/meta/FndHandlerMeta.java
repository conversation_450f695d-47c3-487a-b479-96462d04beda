/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.j2ee.meta;

import ifs.fnd.base.IfsException;
import ifs.fnd.service.Util;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <B>Framework internal class:</B> Immutable meta-object describing an EJB handler in a J2EE application.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public final class FndHandlerMeta {

   private final FndPackageMeta pkg;
   private final String name;
   private String activityName;
   private final String definition;
   private final String activityType;
   private final String serviceLevel;
   private String termPath;

   private final ArrayList<FndOperationMeta> operations;
   private ArrayList<FndEntityMeta>    entityList;
   private Map<String, FndEntityMeta>  entityMap;

   /**
    * Constructs a new FndHandlerMeta with the specified contents.
    */
   public FndHandlerMeta(String name, String packageName, String component, Type dummy, String definition, String activityType, String serviceLevel) {

      this.name = name;
      operations = Util.newArrayList();
      this.pkg = FndJ2eeMetaCache.getApplication().getOrCreatePackage(packageName, component);
      pkg.add(this);

      this.definition = definition;
      this.activityType = activityType;
      this.serviceLevel = serviceLevel;
   }

   /**
    * Adds an operation to this handler.
    */
   void add(FndOperationMeta operation) {
      operations.add(operation);
   }

   /**
    * Ends the creation phase.
    */
   void freeze() throws IfsException {
      operations.trimToSize();
      FndEntityMeta.Builder entityBuilder = new FndEntityMeta.Builder();
      for(int i=0; i<operations.size(); i++) {
         FndOperationMeta operation = getOperation(i);
         operation.freeze();
         operation.addEntities(entityBuilder);
      }
      entityMap = entityBuilder.getEntityMap();
      entityList = entityBuilder.getEntityList();
   }

   /**
    * Adds handler entity map to application entity map.
    */
   void addEntities(Map<String, FndEntityMeta> appEntityMap) {
      appEntityMap.putAll(entityMap);
   }

   /**
    * Gets the number of entities this handler is using in its operation arguments.
    */
   public int getEntityCount() {
      return entityList.size();
   }

   /**
    * Gets the entity at specified index on the list of entities used by this handler.
    */
   public FndEntityMeta getEntity(int index) {
      return entityList.get(index);
   }

   /**
    * Finds an entity used by this handler.
    */
   public FndEntityMeta getEntity(String name) {
      return entityMap.get(name);
   }

   /**
    * Returns list of entities this handler is using in its operation arguments.
    */
   public List<FndEntityMeta> getEntities() {
      return Collections.unmodifiableList(entityList);
   }

   /**
    * Returns the package that owns this handler.
    * @return the package meta
    */
   public FndPackageMeta getPackage() {
      return pkg;
   }

   /**
    * Returns the name of this handler.
    */
   public String getName() {
      return name;
   }

   /**
    * Returns the definition of this handler.
    */
   public String getDefinition() {
      return definition;
   }

   /**
    * Returns the activity type for this handler.
    */
   public String getActivityType() {
      return activityType;
   }

   /**
    * Returns the service level for this handler.
    */
   public String getServiceLevel() {
      return serviceLevel;
   }

   /**
    * Gets the number of operations in this handler.
    */
   public int getOperationCount() {
      return operations.size();
   }

   /**
    * Gets the operation at specified index on the list with all operations in this handler.
    */
   public FndOperationMeta getOperation(int index) {
      return operations.get(index);
   }

   /**
    * Finds an operation in this handler.
    * @param operationName name of an operation
    * @return the found meta-object or null if the named operation does not exist
    */
   public FndOperationMeta getOperation(String operationName) {
      for(FndOperationMeta operation : operations) {
         if(operationName.equals(operation.getName()))
            return operation;
      }
      return null;
   }

   /**
    * Sets activity name of this handler
    * @param name activity name
    */
   public FndHandlerMeta setActivityName(String name) {
      this.activityName = name;
      return this;
   }

   /**
    * Gets activity name of this handler
    * @return activity name
    */
   public String getActivityName() {
      return this.activityName;
   }

    /**
     * Sets the term path that can be used for term bounding
     * @param termPath path used for term bounding on form &lt;ServerPackageName&gt;.&lt;HandlerName&gt;
     */
    public FndHandlerMeta setTermPath(String termPath) {
       this.termPath = termPath;
       return this;
    }

    /**
     * Gets path used for term bounding of this handler
     * @return the term path
     */
    public String getTermPath() {
       return this.termPath;
    }

   //======================================================================
   // Type
   //======================================================================

   /**
    * <B>Framework internal class:</B> Enumeration class that represents the type of a hanler.
    *
    * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
    */
   public enum Type {SERVICE} //we need to preserve this enumeration because of references from the generated code
}
