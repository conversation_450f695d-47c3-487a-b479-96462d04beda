/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.j2ee;

import ifs.fnd.sf.storage.FndStorageUtil;
import ifs.fnd.sf.*;
import ifs.fnd.log.*;
import ifs.fnd.base.FndContext;
import ifs.fnd.base.IfsException;
import ifs.fnd.service.IfsProperties;
import ifs.fnd.sf.storage.FndConnectionManager;
import ifs.fnd.sf.storage.FndStatement;
import ifs.fnd.sf.storage.FndConnection;
import ifs.fnd.sf.storage.FndOracleStatementForOracle;

/**
 * <B>Framework internal class:</B> Class representing J2EE server framework.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class FndJ2eeFramework extends FndServerFramework {

   private static volatile String appOwnerDirectoryId = null;

   public static String getAppOwnerDirectoryId() throws IfsException {
      if(appOwnerDirectoryId==null) {
         appOwnerDirectoryId = FndStorageUtil.getApplicationUser(IfsProperties.getSnapshot().getApplicationOwner()); // Static IFS property
      }
      return appOwnerDirectoryId;
   }


   /**
    * Return string representing the type of this server framework.
    * @return Constant FndFramework.J2EE representing J2EE server framework.
    */
   @Override
   public String getType() {
      return J2EE;
   }

   /**
    * Create new instance of FndContext valid for J2EE framework.
    * @return a newly created extension of FndServerContext
    */
   @Override
   public FndContext newContext() {
      return new FndJ2eeContext();
   }

   /**
    * Create new instance of FndConnectionManager valid for J2EE framework.
    * @return a newly created instance that implements FndConnectionManager interface
    */
   @Override
   public FndConnectionManager newConnectionManager() {
      return new FndJ2eeConnectionManager();
   }

   /**
    * Create a new statement valid for J2EE framework.
    * @param connection the database connection to create a statement for
    * @return a subclass of FndStatement valid in J2EE framework
    */
   @Override
   public FndStatement newStatement(FndConnection connection) {
      return new FndOracleStatementForOracle(connection);
   }

   /**
    * Initializes java server framework.
    */
   public static void init() {
      Logger log = LogMgr.getFrameworkLogger();
      if(log.info) {
         log.info("Initializing J2EE framework");
      }
   }

   /**
    * Shuts down java server framework.
    */
   public static void shutdown() {
      Logger log = LogMgr.getFrameworkLogger();
      if(log.info) {
         log.info("Shutting down J2EE framework");
      }
   }
}
