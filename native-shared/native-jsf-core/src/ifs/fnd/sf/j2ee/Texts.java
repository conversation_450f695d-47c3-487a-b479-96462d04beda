/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.j2ee;

import ifs.fnd.base.FndTranslatableText;

/**
 * This class contains translatable texts used by the framework.
 */
final class Texts {

   private static final String PKG = Texts.class.getPackage().getName();

   private Texts() {
      //Prevent instantiations
   }

   //FndAdminRequestUtil
   static final FndTranslatableText CREMESS = new FndTranslatableText("CREMESS", "Could not create message: '&1'", PKG);
   static final FndTranslatableText SENDMESS = new FndTranslatableText("SENDMESS", "Could not send message: '&1'", PKG);
   static final FndTranslatableText SETMESSVAL = new FndTranslatableText("SETMESSVAL", "Could not set message value: '&1'", PKG);
   static final FndTranslatableText SETSTRINGPROP = new FndTranslatableText("SETSTRINGPROP", "Could not set string property: '&1'", PKG);
   static final FndTranslatableText JMSCLOSE = new FndTranslatableText("JMSCLOSE", "Failed closing JMS connection/session: '&1'", PKG);
   static final FndTranslatableText JMSACCESS = new FndTranslatableText("JMSACCESS", "Cannot access FndAdminTopic and FndTopicFactory in context of current bean [&1]. Only StandardEntityHandlerBean has access to these JMS resources.", PKG);

   //FndAbstractBean
   static final FndTranslatableText FNDSESSIONBEANSEC = new FndTranslatableText("FNDSESSIONBEANSEC","No security context set. Check that security has been configured for the application.", PKG);

   //FndActivityBean
   static final FndTranslatableText FNDACTIVITYBEANABORT = new FndTranslatableText("FNDACTIVITYBEANABORT","Request has been aborted", PKG);

   //FndJ2eeConnectionManager
   static final FndTranslatableText GETDBCONN = new FndTranslatableText("GETDBCONN", "Unable to get database connection. (&1)", PKG);

   //FndJ2eeFramework
   static final FndTranslatableText PLGATEWAYPOOLINIT = new FndTranslatableText("PLGATEWAYPOOLINIT", "Failed to initialize Plsql Gateway connection pool: &1", PKG);

   //FndAdminMessageSender
   static final FndTranslatableText SENDMESSTOAQ = new FndTranslatableText("SENDMESSTOAQ", "Could not send message to AQ JMS: '&1'", PKG);
}
