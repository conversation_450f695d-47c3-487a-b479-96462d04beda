/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.j2ee;

import ifs.fnd.base.SystemException;
import ifs.fnd.sf.j2ee.meta.FndJ2eeMetaCache;
import javax.naming.InitialContext;
import javax.naming.NamingException;

/**
 * Factory class for standard entity bean.
 */
final class StandardEntityHandlerFactory {

   private StandardEntityHandlerFactory() {};

   static StandardEntityHandler getHandler() throws SystemException {
      FndJ2eeContext ctx = FndJ2eeContext.getCurrentJ2eeContext();
      return ctx.getCurrentDependentBean().getStandardEntityHandler();
   }

   static StandardEntityHandler lookupHandler() throws SystemException {
      FndJ2eeContext ctx = FndJ2eeContext.getCurrentJ2eeContext();
      FndDependentBean bean = ctx.findCurrentDependentBean();
      if(bean!=null)
         return bean.getStandardEntityHandler();

      try {
         String appName = FndJ2eeMetaCache.getApplication().getName();
         String jndiName = "java:global/" + appName + "/ifs-fnd-j2ee-ejb/Standard_Entity_Handler";
         return InitialContext.doLookup(jndiName);
      }
      catch(NamingException e) {
         throw new SystemException(e, "Could not lookup StandardEntityHandler");
      }
   }
}


