/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.sf.j2ee.security.reauth.spi;

import java.util.Map;

public abstract class Authenticator {

   public enum Config {

      /**
       * Constant representing the URL of the re-authentication provider
       */
      AUTH_URL,
      /**
       * Constant representing the use of SSL for the connection to the provider
       */
      USE_SSL,
      /**
       * Constant representing the JDBC driver class in use
       */
      JDBC_DRIVER_CLASS
   }

   /**
    * Authenticates a user using the method of re-authentication of this
    * provider.
    *
    * @param user Username
    * @param password Password
    * @param opts Configuration options
    * @throws ReAuthenticationException If re-authentication fails
    */
   public abstract void authenticate(String user, String password, Map opts) throws ReAuthenticationException;

   /**
    * Returns the type/method of re-authentication of this provider.
    *
    * @return Method of re-authentication of the provider.
    */
   public abstract AuthMethod getAuthMethod();
}
