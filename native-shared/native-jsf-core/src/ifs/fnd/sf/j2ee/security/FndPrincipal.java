/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.sf.j2ee.security;

import java.security.Principal;

/**
 * <B>Framework internal class:</B> Implementation of <code>java.security.Principal</code> interface.
 *
 * <P><B>This is a framework internal class! Backward compatibility is not guaranteed.</B>
 */
public class FndPrincipal implements Principal, java.io.Serializable {

   /**
    * @serial
    */
   private String directoryId;

   /**
    * Create a FndPrincipal with a containing directoryId.
    * @param directoryId the directoryId for this user.
    * @exception IllegalArgumentException if the <code>directoryId</code> is <code>null</code>.
    */
   public FndPrincipal(String directoryId) {
      if (directoryId == null)
         throw new IllegalArgumentException("FndPrincipal: 'directoryId' may not be null");

      this.directoryId = directoryId;
   }

   /**
    * Compares the specified Object with this <code>FndPrincipal</code>
    * for equality.  Returns true if the given object is also a
    * <code>FndPrincipal</code> and the two FndPrincipals
    * have the same directoryId.
    * @param o Object to be compared for equality with this <code>FndPrincipal</code>.
    * @return true if the specified Object is equal equal to this <code>FndPrincipal</code>.
    */
   public boolean equals(Object o) {
      if (o == null) {
         return false;
      }
      if (this == o) {
         return true;
      }
      if (!(o instanceof FndPrincipal)) {
         return false;
      }

      FndPrincipal that = (FndPrincipal) o;

      if (this.getName().equals(that.getName())) {
         return true;
      }
      return false;
   }

   /**
    * Return the directoryId for this <code>FndPrincipal</code>.
    * @return the directoryId for this <code>FndPrincipal</code>
    */
   public String getName() {
      return directoryId;
   }

   /**
    * Return a hash code for this <code>FndPrincipal</code>.
    * @return a hash code for this <code>FndPrincipal</code>.
    */
   public int hashCode() {
      return directoryId.hashCode();
   }

   /**
    * Return a string representation of this <code>FndPrincipal</code>.
    * @return a string representation of this <code>FndPrincipal</code>.
    */
   public String toString() {
      return ("FndPrincipal: " + directoryId);
   }
}
