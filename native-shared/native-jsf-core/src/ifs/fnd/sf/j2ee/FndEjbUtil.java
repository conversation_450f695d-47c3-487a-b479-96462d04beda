/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package ifs.fnd.sf.j2ee;

import ifs.fnd.log.*;
import javax.naming.InitialContext;
import javax.naming.NamingException;

/**
 * IFS Gateway Utilities.
 */
public final class FndEjbUtil {

   /**
    * Performs JNDI lookup of a local EJB in a container specific way.
    * @param beanName name of a local bean
    * @param log logger for spooling debug information
    * @return an instance of local business interface extending FndActivityLocal, or null if the specified EJB has not been found
    * @throws NamingException - if creation of JNDI context failed
    */
   public static FndActivityLocal lookupLocalEjb(String beanName, Logger log) throws NamingException {
      String moduleName = "server-ejb";
      String jndiName = "java:app/" + moduleName + "/" + beanName;

      if (log.debug) {
         log.debug("Looking up local EJB \"&1\"", jndiName);
      }

      try {
         return InitialContext.doLookup(jndiName);
      }
      catch (NamingException x) {
         log.error(x, "Local EJB &1 not found.", jndiName);
         return null;
      }
   }
}
