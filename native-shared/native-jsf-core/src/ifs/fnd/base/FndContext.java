/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.base;

import ifs.fnd.base.capability.ApplicationCapability;
import ifs.fnd.capability.CapabilitySet;
import ifs.fnd.internal.FndAttributeInternals;
import ifs.fnd.internal.FndRecordInternals;
import ifs.fnd.log.LogMgr;
import ifs.fnd.log.Logger;
import ifs.fnd.log.LoggerOutput;
import ifs.fnd.log.ThreadLoggers;
import ifs.fnd.record.*;
import ifs.fnd.record.serialization.*;
import ifs.fnd.sf.FndUserFeedback;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Represents the context that is sent with every request to the server. The
 * context is thread local. "Current context" refers to the context in the
 * current thread. All methods that start with getCurrent- or setCurrent- are
 * static (can be called using FndContext.getCurrentSomething) and operate on
 * current context.
 */
public abstract class FndContext implements FndRecordSerialization, Serializable {

   static {
      // initialize internal arrays in class java.util.Locale in a thread-safe way
      Locale.getISOCountries();
      Locale.getISOLanguages();
   }

   protected List<FndAbstractRecord> records = new ArrayList<>();

   protected List<FndAttribute> attributes = new ArrayList<>();

   protected FndRecord appContext, sysContext, errormsg, userInfo, warningmsg, debugInfo, counters, callRouting;

   protected FndAttribute serializationMode, runAs, domain, language, locale, interactiveMode, bodyType, requestId, plsqlDebugger, sqlTrace, operation, encodeBinaryValues, showStack;

   protected FndAttribute clientId, clientStatistics, clientTracing;

   //Current serialization target (CLIENT or SERVER)
   private transient FndRecordFormat.SerializationTarget serializationTarget = FndRecordFormat.SERVER;

   //A cached java.util.Locale.
   protected transient Locale javaLocale;

   //Directory ID of the current user
   protected transient String directoryId;

   private FndAttribute status = null;

   private transient String taskId = null;

   private int externalBodyType = FndSerializeConstants.BODY_TYPE_BUF;

   protected byte[] fndSecurityData;

   private final FndStorageType storageType = FndStorageType.STANDARD_STORAGE;

   private SimpleDateFormat dateFormat, timeFormat, timestampFormat;

   private SimpleDateFormat xmlTimeFormat, xmlTimestampFormat;

   private static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";

   private static final String DEFAULT_TIME_FORMAT = "HH.mm.ss";

   private static final String DEFAULT_XML_TIME_FORMAT = "HH:mm:ss";

   private static final String DEFAULT_TIMESTAMP_FORMAT = "yyyy-MM-dd-HH.mm.ss";

   private static final String DEFAULT_XML_TIMESTAMP_FORMAT = "yyyy-MM-dd'T'HH:mm:ss";

   private SimpleDateFormat debugDateFormat; // Lazy initialization used for this.

   private static final String DEFAULT_DEBUG_TIME_FORMAT = "HH:mm:ss.SSS";

   private final FndAutoString debugOutBuffer = new FndAutoString(); // Temporally
                                                               // hold debug
                                                               // lines untill
                                                               // set to debug
                                                               // attribute in
                                                               // this context
   /**
    * Hook called for record creations in the current request
    */
   private IFndCustomFieldProvider customFieldProvider = null;

   /**
    * Temporally enable/disable encryption routines until all access provides
    * are updated
    */
   private FndAttribute encryptionEnabled;

   /**
    * Status error
    */
   public static final String ERROR = "ERROR";

   /**
    * Status done
    */
   public static final String DONE = "DONE";

   /**
    * Status unknown
    */
   public static final String UNKNOWN = "UNKNOWN";

   /**
    * The default serialization mode (all attribute values are serialized).
    */
   public static final String ALL = "ALL";

   /**
    * The DIFF serialization mode (unchanged attribute values are not serialized).
    */
   public static final String DIFF = "DIFF";

   /*
    * Mode settings - used to determine where the application is run
    */
   public static final int ONLINE_CLIENT = 1;

   public static final int OFFLINE_CLIENT = 2;

   public static final int SERVER = 3;

   private int implementationMode = SERVER;

   public static class InvalidPropertiesException extends RuntimeException {
      private InvalidPropertiesException(Throwable cause) {
         super(cause);
      }
   }

   /**
    * Temporary storage for debug lines.
    */
   private final LoggerOutput loggerOutput = (String msg) -> {
      if(isdebugOn()) {
         debugOutBuffer.append(msg);
      }
   };

   private static final ThreadLocal<Object> currentContext = new ThreadLocal<Object>() {
      @Override
      protected synchronized Object initialValue() {
         return null;
      }
   };

   /**
    * Serialize the state of this object to a stream.
    */
   private void writeObject(ObjectOutputStream out) throws IOException {
      Logger log = LogMgr.getClassLogger(java.io.Serializable.class);
      try {
         BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(out, FndSerializeConstants.BUFFER_CHARSET));
         FndTokenWriter writer = new FndTokenWriter(bw);
         format(writer);
         bw.flush();
         out.writeObject(fndSecurityData);
      }
      catch (ParseException e) {
         throw new IOException("Serialization of FndContext failed: " + e.getMessage(), e);
      }
      if(log.debug) {
          log.debug("FndContext serialized.");
      }
   }

   /**
    * De-serialize the state of this object from a stream.
    */
   private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
      Logger log = LogMgr.getClassLogger(java.io.Serializable.class);
      try {
         //Constructor is not called, we need to initialize these
         records = new ArrayList<>();
         attributes = new ArrayList<>();
         BufferedReader br = new BufferedReader(new InputStreamReader(in, FndSerializeConstants.BUFFER_CHARSET));
         FndTokenReader reader = new FndTokenReader(br);
         parse(reader);
         fndSecurityData = (byte[]) in.readObject();
      }
      catch (ParseException e) {
         throw new IOException("Deserialization of FndContext failed: " + e.getMessage(), e);
      }
      if(log.debug) {
          log.debug("FndContext deserialized.");
      }
   }

   /**
    * Returns the current context. The context is thread local.
    *
    * @return the current context
    */
   public static FndContext getCurrentContext() {
      FndContext ctx = (FndContext) currentContext.get();
      if(ctx == null) {
         ctx = FndFramework.getFramework().newContext();
         setCurrentContext(ctx);
      }
      Logger log = getClassLogger();
      if(log.debug) {
         log.debug("Current FndContext is: (&1)", ctx.hashCode());
      }
      return ctx;
   }

   /**
    * Sets the context for the current thread.
    *
    * @param ctx
    *           Context
    */
   public static void setCurrentContext(FndContext ctx) {
      if(ctx == null) {
         FndContext c = (FndContext) currentContext.get();
         LogMgr.getThreadLoggers().unregisterLoggerOutput(c == null ? null : c.loggerOutput);
         LogMgr.reset();
      }
      Logger log = getClassLogger();
      if(log.debug) {
         log.debug("Setting new current context (&1)",// in call from:\n&2\n.",
                   ctx==null ? "null" : ""+ctx.hashCode() );//,
                   //Str.getStackTrace(new Throwable()) );
      }
      currentContext.set(ctx);
   }

   /**
    * Clears FndContext and IFS loggers attached to the current thread.
    * The method should be called when the current thread will not need FndContext any more,
    * for example at the end of a servlet's init(), service() and destroy() methods.
    */
   public static void clearCurrentContext() {
      setCurrentContext(null);
      LogMgr.clearThreadLoggers();
   }

   /**
    * Sets the status part of the context. This is done by the server to
    * indicate success or failure of a request.
    *
    * @param value
    *           Status
    */
   private void setStatus(String value) {
      if (status == null) {
         status = new FndText("STATUS", value);
         attributes.add(0, status);
      }
      else {
         FndAttributeInternals.internalSetValue(status, value);
      }
   }

   /**
    * Sets the error to be returned in the context from this server call.
    *
    * @param name
    *           Error name
    * @param category
    *           Error category - shoould be one defined in
    *           ifs.fnd.base.IfsException
    * @param message
    *           Error message
    */
   public void setErrorMessage(String name, String category, String message) {
      FndRecord error = new FndRecord(FndContext.ERROR);
      error.add("CATEGORY", category);
      error.add("MESSAGE", message);
      error.add("NAME", name);
      error.add("SEVERITY", "1"); //Severity is probably not used. Remove?
      this.setContextRecord(records, error);
      errormsg = error;
   }

   /**
    * Set the error to be returned in context from a server call.
    *
    * @param e
    *           Exception to send to caller.
    */
   public void setErrorMessage(IfsException e) {
      FndRecord error = new FndRecord(FndContext.ERROR);
      error.add("CATEGORY", e.getType());
      error.add("MESSAGE", e.getMessage());
      error.add("NAME", "");
      error.add("SEVERITY", "1"); //Severity is not used. Remove?
      error.add("EXTRAINFO", e.getExtraInfo());
      error.add("TRANSLATIONID", e.getTranslationID()); // Used by mobile client

      //Add stack trace if this is a SystemException (if the SystemPrevilage DEBUGGER is granted)
      if(getShowStack()) {
         if (e instanceof SystemException) {
            StringBuilder detailValue = new StringBuilder();
            for (int i = 0; i < e.getStackTrace().length; i++) {
               StackTraceElement element = e.getStackTrace()[i];
               if (element != null) {
                   detailValue.append("   at ").append(element.toString());
               }
            }
            error.add("STACKTRACE", detailValue.toString());
         }
      }

      //Add error message from cause (inner exception)
      if (e.getCause() != null) {
         error.add("CAUSE", e.getCause().getMessage());
      }

      //Add PLSQL error details if available
      FndRecord details = getPlsqlErrorDetails();
      if(details != null) {
         FndRecordInternals.addRecord(error, details);
      }

      setContextRecord(records, error);
      errormsg = error;
   }

   /**
    * Returns the text of the error message in this context.
    *
    * @return Error message
    */
   public String getErrorMessage() {
      if (errormsg == null)
         return null;
      else {
         FndAttribute msg = errormsg.getAttribute("MESSAGE");
         if (msg != null) {
            return msg.toString();
         }
         else {
            return null;
         }
      }
   }

   /**
    * Gets the type (category) for the error message (if one exists).
    *
    * @return the error type or <code>null</code> if no error exists.
    */
   public String getErrorType() {
      if (errormsg == null)
         return null;
      else {
         FndAttribute msg = errormsg.getAttribute("CATEGORY");
         if (msg != null) {
            return msg.toString();
         }
         else {
            return null;
         }
      }
   }

   /**
    * Gets the extra info for the error message (if one exists).
    *
    * @return the error type or <code>null</code> if no extra info exists.
    */
   public String getErrorExtraInfo() {
      if (errormsg == null) {
         return null;
      }
      else {
         FndAttribute msg = errormsg.getAttribute("EXTRAINFO");
         if (msg != null) {
            return msg.toString();
         }
         else {
            return null;
         }
      }
   }

   /**
    * Adds a text that is to be returned to the client as "user info" in the
    * context.
    *
    * @param text
    *           Text to be added
    */
   public static void addUserInfo(String text) {
      getCurrentContext().addUserInfoActual(text);
   }

   /**
    * Adds a text that is to be returned to the client as "user info" in the
    * context.
    *
    * @param text
    *           Translatable Text to be added
    */
   public static void addUserInfo(FndTranslatableText text) {
      getCurrentContext().addUserInfoActual(text!=null ? text.translate(): null);
   }

   private void addUserInfoActual(String text) {
      FndRecord sysCtx = getSysContext();
      if (userInfo == null) {
         userInfo = new FndRecord("USER_INFO");
         setContextRecord(FndRecordInternals.getRecords(sysCtx), userInfo);
      }

      userInfo.add("msg", text);
   }

   /**
    * Context is divided into system and application parts. This function gets
    * the system part.
    *
    * @return Record containing system context
    */
   public FndRecord getSysContext() {
      if (sysContext == null) {
         sysContext = new FndRecord("FND_CONTEXT");
         records.add(sysContext);
      }

      return sysContext;
   }

   //Start Methods related to the Debuging
   private void setCounters() {
      if ((debugOn()) && (counters == null)) {
         counters = new FndRecord("COUNTERS");
         counters.add("SQL_STATEMENT", 0);
         counters.add("SQL_FETCH", 0);
         counters.add("CORBA_CALL", -1);
         FndRecordInternals.addRecord(getSysContext(), counters);
      }
   }

   /**
    * Increase a named counter in the context. Used for debugging.
    *
    * @param type
    *           Name of the counter to increase
    * @param value
    *           Value to add to the counter
    */
   static void increaseCounter(String type, int value) {
      getCurrentContext().incCounter(type, value);
   }

   private void incCounter(String type, int value) {
      if (debugOn()) {
         setCounters();
         FndAttribute counter = counters.getAttribute(type);
         if (counter != null) {
            FndAttributeInternals.internalSetValue(counter, String.valueOf(Long.parseLong(counter.toString()) + value));
         }
      }
   }

   /**
    * Checks if debug is enabled for the current context. Does not imply that
    * any debug categories are enabled. <B>Note! Do not use this method directly
    * to determine debug status. Use {@link ifs.fnd.base.FndDebug FndDebug}
    * class instead! </B>
    *
    * @return <code>true</code> if debug is enabled.
    */
   static boolean debugOn() {
      return getCurrentContext().isdebugOn();
   }

   /**
    * Checks if debug is enabled in for this context. Does not imply that any
    * debug categories are enabled. <B>Note! Do not use this method directly to
    * determine debug status. Use {@link ifs.fnd.base.FndDebug FndDebug} class
    * instead! </B>
    *
    * @return <code>true</code> if debug is enabled.
    */
   public boolean isdebugOn() {
      if (debugInfo == null)
         return false;
      else
         return debugInfo.getAttribute("OUTPUT") != null;
   }

   /**
    * Returns a unique id for the current context. Used by debug.
    *
    * @return A number for current context
    */
   public static String getTaskId() {
      return getCurrentContext().currentTaskId();
   }

   /**
    * Creates a unique task id for this context.
    *
    * @return the unique id.
    */
   private String currentTaskId() {
      if (taskId == null) {
         taskId = Integer.toHexString(new Random().nextInt(2147483647)).toUpperCase();
         if (taskId.length() < 8)
            taskId = (new StringBuilder("00000000")).replace(8 - taskId.length(), 8, taskId).toString();
      }
      return taskId;
   }

   /**
    * Adds a line of debug information to the current context. <B>Note! This
    * method should no be used directly. For debug traces, use
    * {@link ifs.fnd.base.FndDebug#debug(java.lang.String) FndDebug} class
    * instead! </B>
    *
    * @param text
    *           the text to add.
    */
   static void addDebugLine(StringBuilder text) {
      getCurrentContext().appendDebugLine(text);
   }

   /**
    * Adds a line of debug information to this context (Temporally to a buffer).
    *
    * @param text
    *           the text to add.
    */
   private void appendDebugLine(StringBuilder text) {
      if (debugOn() && text != null) {
         debugOutBuffer.append(text.toString());
      }
   }

   /**
    * Checks if the current user is allowed to debug IFS Applications.
    * @return true
    */
   public boolean isDebuggingAllowed() {
      return true;
   }

   /**
    * Set debug information on buffer to this context.
    */
   private void setDebugOutToContext() {
      if (debugOn() && isDebuggingAllowed()) {
         FndAttribute output = debugInfo.getAttribute("OUTPUT");
         FndAttributeInternals.internalSetValue(output, debugOutBuffer.toString());
      }
   }

   /**
    * Internal method used by debugX..() methods. Checks if a debugging category
    * is enabled for this context.
    *
    * @param cat
    *           the category to check for.
    * @return <code>true</code> if debugging is enabled for the given
    *         category.
    */
   private boolean isDebugOnIn(String cat) {
      return debugInfo != null && debugInfo.getAttribute(cat) != null;
   }

   /**
    * Gets PLSQL debug flag sent from the client.
    * @param name name of the flag
    * @return value of the flag, or null if the flag has not been set
    */
   public String getPlsqlDebugFlag(String name) {
      if(debugInfo != null) {
         FndAttribute attr = debugInfo.getAttribute(name);
         if(attr != null) {
            return attr.toString();
         }
      }
      return null;
   }

   /**
    * Checks if debugging is enabled for debug category DB_ACCESS for the
    * current context. <B>Note! Do not use this method to determine debug status
    * for this category. Use the corresponding method in
    * {@link ifs.fnd.base.FndDebug#isDebugDbAccessOn() FndDebug} class instead!
    * </B>
    *
    * @return <code>true</code> if debugging is enabled.
    */
   public static boolean debugDbAccess() {
      return getCurrentContext().isDebugOnIn("DB_ACCESS");
   }

   /**
    * Checks if debugging is enabled for debug category STUB_ARGUMENTS for the
    * current context. <B>Note! Do not use this method to determine debug status
    * for this category. Use the corresponding method in
    * {@link ifs.fnd.base.FndDebug#isDebugStubArgumentsOn() FndDebug}class
    * instead! </B>
    *
    * @return <code>true</code> if debugging is enabled.
    */
   public static boolean debugStubArguments() {
      return getCurrentContext().isDebugOnIn("STUB_ARGUMENTS");
   }

   /**
    * Checks if debugging is enabled for debug category SKELETON_ARGUMENTS for
    * the current context. <B>Note! Do not use this method to determine debug
    * status for this category. Use the corresponding method in
    * {@link ifs.fnd.base.FndDebug#isDebugSkeletonArgumentsOn() FndDebug} class
    * instead! </B>
    *
    * @return <code>true</code> if debugging is enabled.
    */
   public static boolean debugSkeletonArguments() {
      return getCurrentContext().isDebugOnIn("SKELETON_ARGUMENTS");
   }

   /**
    * Checks if debugging is enabled for debug category CALL_SEQUENCE for the
    * current context. <B>Note! Do not use this method to determine debug status
    * for this category. Use the corresponding method in
    * {@link ifs.fnd.base.FndDebug#isDebugCallSequenceOn() FndDebug} class
    * instead! </B>
    *
    * @return <code>true</code> if debugging is enabled.
    */
   public static boolean debugCallSequence() {
      return FndContext.getCurrentContext().isDebugOnIn("CALL_SEQUENCE");
   }

   /**
    * Checks if debugging is enabled for debug category APPLICATION_CODE for the
    * current context. <B>Note! Do not use this method to determine debug status
    * for this category. Use the corresponding method in
    * {@link ifs.fnd.base.FndDebug#isDebugApplicationCodeOn() FndDebug} class
    * instead! </B>
    *
    * @return <code>true</code> if debugging is enabled.
    */
   public static boolean debugApplicationCode() {
      return getCurrentContext().isDebugOnIn("APPLICATION_CODE");
   }

   /**
    * Does the work for setting/unsetting debug flags for the current context.
    *
    * @param cat
    *           the debug category to set/unset debug flag. If set to
    *           <code>null</code>, all categories are set/unset.
    * @param on
    *           the flag wheter to set or unset the debug category.
    */
   private void setDebugFlag(String cat, boolean on) {
      if (on) {
         // Set debug flag.
         if (debugInfo == null) {
            debugInfo = new FndRecord("DEBUG");
            FndRecordInternals.addRecord(sysContext, debugInfo);
         }

         if (debugInfo.getAttribute("OUTPUT") == null)
            FndRecordInternals.add(debugInfo, new FndText("OUTPUT", ""));

         if (cat == null) {
            if (debugInfo.getAttribute("APPLICATION_CODE") == null)
               FndRecordInternals.add(debugInfo, new FndBoolean("APPLICATION_CODE", Boolean.TRUE));
            if (debugInfo.getAttribute("CALL_SEQUENCE") == null)
               FndRecordInternals.add(debugInfo, new FndBoolean("CALL_SEQUENCE", Boolean.TRUE));
            if (debugInfo.getAttribute("DB_ACCESS") == null)
               FndRecordInternals.add(debugInfo, new FndBoolean("DB_ACCESS", Boolean.TRUE));
            if (debugInfo.getAttribute("SKELETON_ARGUMENTS") == null)
               FndRecordInternals.add(debugInfo, new FndBoolean("SKELETON_ARGUMENTS", Boolean.TRUE));
            if (debugInfo.getAttribute("STUB_ARGUMENTS") == null)
               FndRecordInternals.add(debugInfo, new FndBoolean("STUB_ARGUMENTS", Boolean.TRUE));
         }
         else if (debugInfo.getAttribute(cat) == null)
            FndRecordInternals.add(debugInfo, new FndBoolean(cat, Boolean.TRUE));
      }
      else {
         // Unset debug flag
         if (cat == null) {
            FndRecordInternals.getRecords(sysContext).remove(debugInfo);
            debugInfo = null;
         }
         else {
            FndAttribute attr = debugInfo.getAttribute(cat);
            if (attr != null)
               FndRecordInternals.remove(debugInfo, attr);
         }
      }
      setThreadLoggers();
   }

   /**
    * Sets/Unsets all debug flags for this context.
    *
    * @param on
    *           controls if debug should be on or off
    */
   public void setDebugAll(boolean on) {
      setDebugFlag(null, on);
   }

   /**
    * Sets/Unsets debug flag APPLICATION_CODE for this context.
    *
    * @param on
    *           controls if debug should be on or off
    */
   public void setDebugApplicationCode(boolean on) {
      setDebugFlag("APPLICATION_CODE", on);
   }

   /**
    * Sets/Unsets debug flag CALL_SEQUENCE for this context.
    *
    * @param on
    *           controls if debug should be on or off
    */
   public void setDebugCallSequence(boolean on) {
      setDebugFlag("CALL_SEQUENCE", on);
   }

   /**
    * Sets/Unsets debug flag DB_ACCESS for this context.
    *
    * @param on
    *           controls if debug should be on or off
    */
   public void setDebugDbAccess(boolean on) {
      setDebugFlag("DB_ACCESS", on);
   }

   /**
    * Sets/Unsets debug flag SKELETON_ARGUMENTS for this context.
    *
    * @param on
    *           controls if debug should be on or off
    */
   public void setDebugSkeletonArguments(boolean on) {
      setDebugFlag("SKELETON_ARGUMENTS", on);
   }

   /**
    * Sets/Unsets local debug flag STUB_ARGUMENTS.
    *
    * @param on
    *           controls if debug should be on or off
    */
   public void setDebugStubArguments(boolean on) {
      setDebugFlag("STUB_ARGUMENTS", on);
   }

   /**
    * Gets the current debug output if there is any for this context.
    *
    * @return current debug output
    */
   public String getDebugOutput() {
      setDebugOutToContext(); // set debug buffer to context
      if (isdebugOn())
         return debugInfo.getAttribute("OUTPUT").toString();
      return null;
   }

   /**
    * Creates debug trace for the current context.
    */
   public static void debugContext(Logger log) {
      getCurrentContext().debug(log);
   }

   protected void debugToRecord(FndRecord rec) {
      FndRecord ctx = new FndRecord("FND_CONTEXT");
      Iterator<FndAttribute> a = attributes.iterator();
      while (a.hasNext()) {
         addToDebugRecord(a.next(), ctx);
      }
      Iterator<FndAbstractRecord> r = records.iterator();
      while (r.hasNext()) {
         addToDebugRecord(r.next(), ctx);
      }
      addToDebugRecord(serializationTarget, "SERIALIZATION_TARGET" , ctx);
      addToDebugRecord(javaLocale         , "JAVA_LOCALE"          , ctx);
      addToDebugRecord(directoryId        , "DIRECTORY_ID"         , ctx);
      addToDebugRecord(taskId             , "TASK_ID"              , ctx);
      addToDebugRecord(externalBodyType   , "EXTERNAL_BODY_TYPE"   , ctx);
      addToDebugRecord(storageType        , "STORAGE_TYPE"         , ctx);
      addToDebugRecord(customFieldProvider, "CUSTOM_FIELD_PROVIDER", ctx);
      addToDebugRecord(encryptionEnabled  , "ENCYPTION_ENABLED"    , ctx);
      addToDebugRecord(implementationMode , "IMPLEMENTATION_MODE"  , ctx);
      addToDebugRecord(ctx, rec);
   }

   protected void addToDebugRecord(Object obj, String name, FndRecord rec) {
      if(obj != null) {
         FndRecordInternals.add(rec, new FndText(name, obj.toString()));
      }
   }

   protected void addToDebugRecord(Boolean obj, String name, FndRecord rec) {
      if(obj != null) {
         FndRecordInternals.add(rec, new FndBoolean(name, obj));
      }
   }

   protected void addToDebugRecord(Integer obj, String name, FndRecord rec) {
      if(obj != null) {
         FndRecordInternals.add(rec, new FndInteger(name, obj.longValue()));
      }
   }

   protected void addToDebugRecord(FndAbstractRecord obj, FndRecord rec) {
      if(obj != null) {
         FndRecordInternals.addRecord(rec, obj);
      }
   }

   protected void addToDebugRecord(FndAttribute obj, FndRecord rec) {
      if(obj != null && !obj.isNull()) {
         FndRecordInternals.add(rec, obj);
      }
   }

   /**
    * Creates debug trace for this context.
    */
   public void debug(Logger log) {
      setDebugOutToContext(); // set debug buffer to context
      FndRecord rec = new FndRecord("MAIN");
      debugToRecord(rec);
      FndDebug.debugContext(rec, log);
   }

   /**
    * Gets system context from current context.
    *
    * @return Record containing system context.
    */
   public static FndRecord getCurrentSysContext() {
      return getCurrentContext().getSysContext();
   }

   /**
    * Returns the status of this context.
    *
    * @return Status
    */
   public String getStatus() {
      if (status != null)
         return status.toString();
      else
         return UNKNOWN;
   }

   /**
    * Returns the implementation mode used to determine where the application is run.
    *
    * @return integer constant identifying the implementation mode.
    */
   public static int getImplementationMode() {
      return getCurrentContext().getCurrentImplementationMode();
   }

   /**
    * Returns the implementation mode of the application for a given handler.
    * This method implicitly calls <code>getImplementationMode()</code>. The
    * input parameter <code>handler</code> is ignored. <br /><br />
    *
    * NOTE: This is a dummy method to solve the build failure introduced by the
    *       code generation changes in Bug #86299 (as a requirement
    *       of the Mobile Client).
    *
    * @param  handler Name of the handler
    * @return An integer constant to identify the implementation mode.
    *         The returned value is equivalent to the output of <code>getImplementationMode()</code>.
    */
   public static int getImplementationMode(String handler) {
      return getCurrentContext().getCurrentImplementationMode();
   }

   private int getCurrentImplementationMode() {
      return implementationMode;
   }

   /**
    * Sets the implementation mode.
    *
    * @param mode integer constant identifying the implementation mode.
    */
   protected void setCurrentImplementationMode(int mode) {
      implementationMode = mode;
   }

   /**
    * Returns the storage type.
    *
    * @return Storage type
    */
   public static FndStorageType getStorageType() {
      return getCurrentContext().getCurrentStorageType();
   }

   private FndStorageType getCurrentStorageType() {
      return storageType;
   }

   /**
    * Returns the current Custom Fields Provider (if any)
    * @return Custom Fields Provider
    */
    public IFndCustomFieldProvider getCustomFieldProvider() {
        return customFieldProvider;
    }

   /**
    * Set the Custom Fields Hook for this request
    * @param provider  The IFndCustomFieldProvider to use for this request
    */
    public void setCustomFieldsProvider(IFndCustomFieldProvider provider) {
        customFieldProvider = provider;
    }

   /**
    * Get the current request identifier
    *
    * @return Request identifier (set by client).
    */
   public static String getRequestId() {
      return FndContext.getCurrentContext().getCurrentRequestId();
   }

   private String getCurrentRequestId() {
      if (this.requestId != null)
         return (String) FndAttributeInternals.internalGetValue(this.requestId);
      else
         return null;
   }

   /**
    * Returns <code>true</code> if the context is set to be in interactive
    * mode (which enables user feedback messages).
    *
    * @return true if interactive
    */
   public boolean isInteractiveMode() {
      if (interactiveMode != null && !interactiveMode.isNull())
         return FndAttributeInternals.internalGetValue(interactiveMode).equals("TRUE");
      else
         return true;
   }

   /**
    * Sets whether this context is in interactive mode or not. Interactive mode
    * enables user feedback messages.
    *
    * @param on
    *           true for interactive mode
    */
   public void setInteractiveMode(boolean on) {
      if (interactiveMode == null) {
         interactiveMode = FndAttributeInternals.newAttribute("INTERACTIVE_MODE");
         FndRecordInternals.add(getSysContext(), interactiveMode);
      }

      if (on)
         FndAttributeInternals.internalSetValue(interactiveMode, "TRUE");
      else
         FndAttributeInternals.internalSetValue(interactiveMode, "FALSE");
   }

   /**
    * Sets whether the error stack should be shown to the user in the event of an
    * error
    *
    * @param show
    *           true if the error stack should be shown
    */
   public void setShowStack(boolean show) {
      if (showStack == null) {
         showStack = FndAttributeInternals.newAttribute("SHOW_STACK");
         FndRecordInternals.add(getSysContext(), showStack);
      }

      if (show)
         FndAttributeInternals.internalSetValue(showStack, "TRUE");
      else
         FndAttributeInternals.internalSetValue(showStack, "FALSE");
   }

   /**
    * Returns the flag controlling the showing of the error stack.
    * @return true if the error stack should be shown to the user, false otherwise
    */
   public boolean getShowStack() {
      if (showStack != null && !showStack.isNull())
         return FndAttributeInternals.internalGetValue(showStack).equals("TRUE");
      else
         return false;
   }

   /**
    * Sets the context parameter controlling SQL trace in database.
    * If this parameter is set then SQL trace will be activated during each access to database.
    */
   public void setSqlTrace() {
      if (sqlTrace == null) {
         sqlTrace = FndAttributeInternals.newAttribute("SQL_TRACE");
         FndRecordInternals.add(getSysContext(), sqlTrace);
      }
      FndAttributeInternals.internalSetValue(sqlTrace, "TRUE");
   }

   /**
    * Returns the flag controlling SQL trace in database.
    * @return true if SQL trace should be activated for each open database connection, false otherwise
    */
   public boolean isSqlTraceOn() {
      if (sqlTrace != null && !sqlTrace.isNull())
         return FndAttributeInternals.internalGetValue(sqlTrace).equals("TRUE");
      else
         return false;
   }

   /**
    * Gets the client ID for the current request.
    * @return String identifier send from the client
    */
   public String getClientId() {
      if (clientId != null && !clientId.isNull())
         return (String) FndAttributeInternals.internalGetValue(clientId);
      else
         return null;
   }

   /**
    * Returns the flag controlling SQL tracing in database.
    * @return true if SQL trace should be activated, false otherwise
    */
   public boolean isClientTracingOn() {
      if (clientTracing != null && !clientTracing.isNull())
         return FndAttributeInternals.internalGetValue(clientTracing).equals("TRUE");
      else
         return false;
   }

   /**
    * Returns the flag controlling SQL trace statistics in database.
    * @return true if SQL trace statistics should be generated, false otherwise
    */
   public boolean isClientStatisticsOn() {
      if (clientStatistics != null && !clientStatistics.isNull())
         return FndAttributeInternals.internalGetValue(clientStatistics).equals("TRUE");
      else
         return false;
   }

   /**
    * Sets the context parameter controlling encoding of binary values.
    * The default value of this parameter is true.
    * @param flag true if binary values should be encoded, false otherwise
    */
   public void setEncodeBinaryValues(boolean flag) {
      if (encodeBinaryValues == null) {
         encodeBinaryValues = FndAttributeInternals.newAttribute("ENCODE_BINARY_VALUE");
         FndRecordInternals.add(getSysContext(), encodeBinaryValues);
      }
      FndAttributeInternals.internalSetValue(encodeBinaryValues, flag ? "TRUE" : "FALSE");
   }

   /**
    * Returns the context parameter controlling encoding of binary values.
    * @return true if binary values should be encoded, false otherwise
    */
   public boolean encodeBinaryValues() {
      if (encodeBinaryValues != null && !encodeBinaryValues.isNull())
         return FndAttributeInternals.internalGetValue(encodeBinaryValues).equals("TRUE");
      else
         return true;
   }

   /**
    * Gets the host and port of the remote debugger for PLSQL code.
    *
    * @return the host and port in form " <host>: <port>", or null if the remote
    *         debugger has not been set.
    */
   public String getPlsqlDebugger() {
      if (plsqlDebugger != null && !plsqlDebugger.isNull())
         return (String) FndAttributeInternals.internalGetValue(plsqlDebugger);
      else
         return null;
   }

   /**
    * Sets host and port for remote debugging of PLSQL code. A remote debugging
    * session will be opened for each PLSQL connection used by the current
    * request.
    *
    * @param host
    *           the host of the remote debugger
    * @param port
    *           the port of the remote debugger
    */
   public void setPlsqlDebugger(String host, int port) {
      if (plsqlDebugger == null) {
         plsqlDebugger = FndAttributeInternals.newAttribute("PLSQL_DEBUGGER");
         FndRecordInternals.add(getSysContext(), plsqlDebugger);
      }
      FndAttributeInternals.internalSetValue(plsqlDebugger, host + ":" + port);
   }

   /**
    * Sets domain of a context. Use FndContext.setCurrentDomain to set domain of
    * current context.
    *
    * @param newDomain
    *           Domain to set
    * @return Previous value of the domain
    */
   public String setDomain(String newDomain) {
      if (domain == null) {
         domain = new FndText("DOMAIN", newDomain);
         FndRecordInternals.add(getSysContext(), domain);
         return null;
      }
      else {
         String oldDomain = domain.toString();
         FndAttributeInternals.internalSetValue(domain, newDomain);
         return oldDomain;
      }
   }

   /**
    * Returns the default date format for the current context.
    *
    * @return date format
    */
   public static SimpleDateFormat getCurrentDateFormat() {
      return getCurrentContext().getDateFormat();
   }

   /**
    * Returns the default date format for this context.
    *
    * @return date format
    */
   private SimpleDateFormat getDateFormat() {
      if (dateFormat == null)
         dateFormat = new SimpleDateFormat(DEFAULT_DATE_FORMAT, Locale.US);
      return dateFormat;
   }

   /**
    * Returns the default time format for the current context.
    *
    * @return time format
    */
   public static SimpleDateFormat getCurrentTimeFormat() {
      return getCurrentContext().getTimeFormat();
   }

   /**
    * Returns the default time format for this context.
    *
    * @return time format
    */
   private SimpleDateFormat getTimeFormat() {
      if (timeFormat == null)
         timeFormat = new SimpleDateFormat(DEFAULT_TIME_FORMAT, Locale.US);
      return timeFormat;
   }

   /**
    * Returns the XML time format for the current context.
    *
    * @return time format
    */
   public static SimpleDateFormat getCurrentXMLTimeFormat() {
      return getCurrentContext().getXMLTimeFormat();
   }

   /**
    * Returns the XML time format for this context.
    *
    * @return time format
    */
   private SimpleDateFormat getXMLTimeFormat() {
      if (xmlTimeFormat == null)
         xmlTimeFormat = new SimpleDateFormat(DEFAULT_XML_TIME_FORMAT, Locale.US);
      return xmlTimeFormat;
   }

   /**
    * Returns the XML timestamp format for the current context.
    *
    * @return timestamp format
    */
   public static SimpleDateFormat getCurrentXMLTimestampFormat() {
      return getCurrentContext().getXMLTimestampFormat();
   }

   /**
    * Returns the XML timestamp format for this context.
    *
    * @return timestamp format
    */
   private SimpleDateFormat getXMLTimestampFormat() {
      if (xmlTimestampFormat == null)
         xmlTimestampFormat = new SimpleDateFormat(DEFAULT_XML_TIMESTAMP_FORMAT, Locale.US);
      return xmlTimestampFormat;
   }

   /**
    * Returns the default timestamp format for the current context.
    *
    * @return timestamp format
    */
   public static SimpleDateFormat getCurrentTimestampFormat() {
      return getCurrentContext().getTimestampFormat();
   }

   /**
    * Returns the default timestamp format for this context.
    *
    * @return timestamp format
    */
   private SimpleDateFormat getTimestampFormat() {
      if (timestampFormat == null)
         timestampFormat = new SimpleDateFormat(DEFAULT_TIMESTAMP_FORMAT, Locale.US);
      return timestampFormat;
   }

   /**
    * Returns the default time format for debug messages for the current
    * context.
    *
    * @return time format
    */
   public static SimpleDateFormat getCurrentDebugTimeFormat() {
      return getCurrentContext().getDebugTimeFormat();
   }

   /**
    * Return the default time format for debug messages for this context.
    *
    * @return time format
    */
   private SimpleDateFormat getDebugTimeFormat() {
      if (debugDateFormat == null)
         debugDateFormat = new SimpleDateFormat(DEFAULT_DEBUG_TIME_FORMAT, Locale.US);
      return debugDateFormat;
   }

   /**
    * Get the "run as" identity for this context. This value is used to do user
    * impersonation.
    *
    * @return directory ID of the run as identity.
    */
   public final String getRunAs() {
      if (runAs != null) {
         return (String) FndAttributeInternals.internalGetValue(runAs);
      }
      else {
         return null;
      }
   }

   /**
    * Set the "run as" user identity for this context. This is used for user
    * impersonation.
    *
    * @param identity
    *           directory ID for the "run as" user.
    */
   public final void setRunAs(final String identity) {
      if (identity != null) {
         if (this.runAs == null) {
            this.runAs = new FndText("RUN_AS");
            FndRecordInternals.add(getSysContext(), this.runAs);
         }
         FndAttributeInternals.internalSetValue(this.runAs, identity);
      }
   }

   /**
    * Gets the current serialization mode ("ALL" or "DIFF"). The value controls
    * formatting of records and attributes when current serialization target = CLIENT.
    *
    * @return the current serialization mode
    * @see #getSerializationTarget
    */
   public final String getSerializationMode() {
      if (serializationMode != null) {
         return (String) FndAttributeInternals.internalGetValue(serializationMode);
      }
      else {
         return FndContext.ALL;
      }
   }

   /**
    * Sets the current serialization mode ("ALL" or "DIFF"). The value controls
    * formatting of records and attributes when current serialization target = CLIENT.
    *
    * @param mode the current serialization mode
    * @throws ParseException if the specified mode has invalid value
    * @see #setSerializationTarget
    */
   public final void setSerializationMode(final String mode) throws ParseException {
      if (mode != null) {
         if (this.serializationMode == null) {
            this.serializationMode = new FndText("SERIALIZATION_MODE");
            FndRecordInternals.add(getSysContext(), this.serializationMode);
         }
         FndAttributeInternals.internalSetValue(this.serializationMode, mode);
         validateSerializationMode();
      }
   }

   /**
    * Checks if the current serialization mode is valid ("ALL" or "DIFF").
    * @throws ParseException if the current serialization mode has invalid value
    */
   private void validateSerializationMode() throws ParseException {
      String mode = getSerializationMode();
      if(mode != null && !mode.equals(ALL) && !mode.equals(DIFF)) {
         throw new ParseException(Texts.BADSERIALMODE, mode);
      }
   }

   /**
    * Gets the current serialization target (CLIENT or SERVER).
    * The value controls formatting of records and attributes.
    * The default value for serialization target is {@link ifs.fnd.record.serialization.FndRecordFormat#SERVER SERVER}.
    *
    * @return the current value of serialization target
    * @see #setSerializationTarget
    */
   public final FndRecordFormat.SerializationTarget getSerializationTarget() {
      return serializationTarget;
   }

   /**
    * Sets the current serialization target (CLIENT or SERVER).
    * The value controls formatting of records and attributes.
    *
    * @param target the serialization target to set
    * @see #getSerializationTarget
    */
   public final void setSerializationTarget(FndRecordFormat.SerializationTarget target) {
      serializationTarget = target;
   }

   /**
    * Returns the language for the current context.
    *
    * @return IFS language code for the current context
    */
   public static String getCurrentLanguage() {
      return getCurrentContext().getLanguage();
   }

   /**
    * Returns the language for this context. If no language has been set, the
    * default language will be returned.
    *
    * @return IFS language code for this context
    */
   public String getLanguage() {
      if (language != null) {
         String langCode = (String) FndAttributeInternals.internalGetValue(language);
         if (langCode != null && !"".equals(langCode))
            return langCode;
      }
      return FndConstants.defaultLanguage; //return default language
   }

   /**
    * Sets the language for the current context.
    *
    * @param language
    *           language to set
    */
   public static void setCurrentLanguage(String language) {
      getCurrentContext().setIfsLanguage(language);
   }

   /**
    * Sets the IFS specific language for this context.
    *
    * @param language
    *           language to set
    */
   protected void setIfsLanguage(final String language) {
      String lang = language;
      if (this.language == null) {
         this.language = new FndText("LANGUAGE");
         FndRecordInternals.add(getSysContext(), this.language);
      }
      FndAttributeInternals.internalSetValue(this.language, lang);
   }

   /* Locale-related methods */

   /**
    * Returns the <code>Locale</code> for this context. If no locale is set,
    * the {@link ifs.fnd.base.FndConstants#defaultLocale default locale}is
    * returned.
    *
    * @return the <code>Locale</code> for this context, or the default if none
    *         is set.
    */
   public Locale getLocale() {
      if (javaLocale != null)
         return javaLocale;

      return FndConstants.defaultLocale; //return default locale
   }

   /**
    * Returns the <code>Locale</code> for the current locale.
    *
    * @return The current Locale
    * @see #getLocale()
    */
   public static Locale getCurrentLocale() {
      return getCurrentContext().getLocale();
   }

   /**
    * Sets the <code>Locale</code> for this context.
    *
    * @param loc
    *           the <code>Locale</code> to set.
    */
   public void setLocale(Locale loc) {
      javaLocale = loc;
      if (this.locale == null) {
         this.locale = new FndText("LOCALE");
         FndRecordInternals.add(getSysContext(), this.locale);
      }
      FndAttributeInternals.internalSetValue(this.locale, getLanguageTag());
   }

   /**
    * Sets the locale for the current context.
    *
    * @param loc
    *           the <code>Locale</code> to set.
    * @see #setLocale(java.util.Locale)
    */
   public static void setCurrentLocale(Locale loc) {
      getCurrentContext().setLocale(loc);
   }

   /**
    * <P>
    * Checks that the locale & language set are valid.
    * </P>
    * <P>
    * This implementation only checks that the locale are valid RFC 3066
    * language/country codes.
    * </P>.
    * <P>
    * Sub-types may map between RFC 3066 codes and IFS specific language codes.
    * </P>
    * @throws ifs.fnd.base.ParseException
    */
   protected void validateLocaleAndLanguage() throws ParseException {
      if (locale != null) {
         String s = (String) FndAttributeInternals.internalGetValue(locale);
         if (s != null && !"".equals(s)) {
            // This checks that the langauge & country codes are valid
            // (as far as Java is concerned).
            try{
               javaLocale = parseRfc3066String(s);
            }
            catch(ParseException e) {
               javaLocale=changeLocaleToDefault(s);
            }
         }
      }
   }

/* Method add Client information message and return defalut local en-US if the incoming locale is invalid.
 * @param locale invalid Locale.
 * @return default Locale en-US.
 */
  protected Locale changeLocaleToDefault(String locale) {
      String localeChanged =(String)getAppContext().getAttributeValue("LOCALE_CHANGED");
      if (localeChanged==null || localeChanged.equals("")) {
         getCurrentContext().getAppContext().add("LOCALE_CHANGED","True");
         FndUserFeedback.addUserInfo(Texts.DSCHANGED2DEF.translate(locale));
      }
      return Locale.US ;
  }

   /**
    * Utility method that parses an RFC 3066 2-letter language/country code into
    * a {@link java.util.Locale Locale}object.
    *
    * An optional middle part of the input, like in "sr-Latn-RS", is passed as variant to the Locale constructor.
    *
    * @param rfc3066Code
    *           the {@link java.lang.String String}to parse.
    * @throws ParseException
    *            if the String is not a valid RFC 3066 code.
    * @return the Locale object for the input string.
    */
   protected static Locale parseRfc3066String(String rfc3066Code) throws ParseException {
      int ix = rfc3066Code.indexOf('-');
      if (ix == -1)
         throw new ParseException(Texts.INVALIDRFC3066, rfc3066Code);

      String variant = "";
      int ix2 = rfc3066Code.indexOf('-', ix + 1);
      if(ix2 > 0) {
         variant = rfc3066Code.substring(ix + 1, ix2);
         rfc3066Code = rfc3066Code.substring(0, ix) + rfc3066Code.substring(ix2);
      }

      String rfc3066LangCode = rfc3066Code.substring(0, ix);
      if (rfc3066LangCode.length() != 2 || !isIso2LetterLanguageCode(rfc3066LangCode))
         throw new ParseException(Texts.INVALIDISO639, rfc3066LangCode);

      String rfc3066CntryCode = rfc3066Code.substring(ix + 1);
      if (rfc3066CntryCode.length() != 2 || !isIso2LetterCountryCode(rfc3066CntryCode))
         throw new ParseException(Texts.INVALIDISO3166, rfc3066CntryCode);

      return new Locale(rfc3066LangCode, rfc3066CntryCode, variant);
   }

   /**
    * Formats language tag representing current Locale.
    *
    * If the variant retrieved from the current Locale is defined, then the method returns
    * a three-part language tag (language-variant-country, according to RFC 4646 specification), like "sr-Latn-RS".
    * Otherwise, a two-part language tag (language-country, according to RFC 3066 specification), like "en-US",
    * is returned.
    *
    * @return two- or three-part language tag
    * @see #getLocale()
    */
   public String getLanguageTag() {
      Locale loc = getLocale();
      String variant = loc.getVariant();
      return loc.getLanguage() + (variant.length() > 0 ? "-" + variant + "-" : "-") + loc.getCountry();
   }

   /**
    * Utility method that checks is a <code>String</code> is a correct
    * (according to Java) 2-letter ISO 639 language code.
    *
    * @param s
    *           the <code>String</code> to check.
    * @return <code>true</code> if it's a valid code, <code>false</code>
    *         otherwise or if the <code>String</code> is <code>null</code>
    *         or empty.
    */
   protected static boolean isIso2LetterLanguageCode(String s) {
      if (s == null || s.length() == 0)
         return false;

      String[] langCodes = Locale.getISOLanguages();
      for (String langCode : langCodes) {
         if (langCode.equalsIgnoreCase(s)) {
            return true;
         }
      }
      return false;
   }

   /**
    * Utility method that checks is a <code>String</code> is a correct
    * (according to Java) 2-letter ISO 3166 country code.
    *
    * @param s
    *           the <code>String</code> to check.
    * @return <code>true</code> if it's a valid code, <code>false</code>
    *         otherwise or if the <code>String</code> is <code>null</code>
    *         or empty.
    */
   protected static boolean isIso2LetterCountryCode(String s) {
      if (s == null || s.length() == 0)
         return false;

      String[] cntryCodes = Locale.getISOCountries();
      for (String cntryCode : cntryCodes) {
         if (cntryCode.equalsIgnoreCase(s)) {
            return true;
         }
      }
      return false;
   }

   /**
    * <B>Framework internal method:</B> Utility method to find the translated text for
    * given text.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not guaranteed.</B>
    *
    * @param   path  Unique path for translation.
    * @param   text  the text message to translate.
    * @return  the translated text message (or original text message to translate if not found).
    */
   protected String getTranslatedText(String path, String text) {
      return text;
   }


   /* Application User Stuff */

   /**
    * Returns the identity of the current user.
    *
    * @return directory ID of the current user (null if the value has not yet
    *         been set).
    */
   public String getApplicationUser() {
      return directoryId;
   }

   /**
    * Gets the identity of the user for the current context.
    *
    * @return directory ID of the current user (null if the value has not yet
    *         been set).
    */
   public static String getCurrentApplicationUser() {
      FndContext ctx = getCurrentContext();
      return ctx.getApplicationUser();
   }

   /**
    * Returns application context from this context
    *
    * @return Record containing application context
    */
   public FndRecord getAppContext() {
      if (appContext == null) {
         appContext = new FndRecord("APP_CONTEXT");
         records.add(appContext);
      }
      return appContext;
   }

   /**
    * Sets body type for the request. BUFFER or XML.
    *
    * @param s
    *           body type
    */
   public void setBodyType(String s) {
      if (bodyType != null)
         FndAttributeInternals.internalSetValue(bodyType, s);
      else {
         bodyType = FndAttributeInternals.newAttribute("BODY_TYPE");
         attributes.add(bodyType);
         FndAttributeInternals.internalSetValue(bodyType, s);
      }
   }

   /**
    * Get the external body type for this context
    *
    * @return body type
    */
   public int getExternalBodyType() {
      return externalBodyType;
   }

   /**
    * Get the external body type for the current context
    *
    * @return body type
    */
   public static int getCurrentExternalBodyType() {
      FndContext ctx = getCurrentContext();
      return ctx.getExternalBodyType();
   }

   /**
    * Returns application context from the current context
    *
    * @return Record containing application context
    */
   public static FndRecord getCurrentAppContext() {
      FndContext ctx = getCurrentContext();
      return ctx.getAppContext();
   }

   /**
    * Called from skeleton for transaction handling.
    *
    * @param value
    *           status value
    */
   public void afterCall(String value) {
      setStatus(value);
   }

   private void setAttribute(FndAttribute newAttr) {
      boolean found = false;
      //FndAttribute attr = new FndAttribute();
      for (int i = 0; i < attributes.size(); i++) {
         FndAttribute attr = (FndAttribute) attributes.get(i);
         if (attr != null) {
            if (attr.getName().equals(newAttr.getName())) {
               attributes.set(i, newAttr); // Set Existing attribute
               found = true;
            }
         }
      }
      if (!found)
         attributes.add(newAttr); // Add new One
   }

   private void setContextRecord(List<? super FndAbstractRecord> sysCtx, FndRecord newRecord) {

      boolean found = false;
      for (int i = 0; i < sysCtx.size(); i++) {
         FndRecord rec = (FndRecord) sysCtx.get(i);
         if (rec != null) {
            if (rec.getName().equals(newRecord.getName())) {
               sysCtx.set(i, newRecord); // Set Existing Record
               found = true;
            }
         }
      }
      if (!found)
         sysCtx.add(newRecord); // Add new One
   }

   /**
    * Parse a stream in to this context
    *
    * @param stream
    *           stream to parse
    * @throws ParseException
    *            if parse fails
    */
   @Override
   public void parseBuffer(FndTokenReader stream) throws ParseException {
      parse(stream);
   }

   /**
    * Parse an item in to this context
    *
    * @param stream
    *           stream to parse
    * @throws ParseException
    *            if parse fails
    */
   @Override
   public void parseItem(FndTokenReader stream) throws ParseException {
      parse(stream);
   }

   /**
    * Format this context to buffer
    *
    * @param stream
    *           stream to format to
    * @throws ParseException
    *            if formatting fails
    */
   @Override
   public void formatBuffer(FndTokenWriter stream) throws ParseException {
      format(stream);
   }

   /**
    * Format buffer item to a stream
    *
    * @param stream
    *           stream to format to
    * @throws ParseException
    *            if formatting fails
    */
   @Override
   public void formatItem(FndTokenWriter stream) throws ParseException {
      format(stream);
   }

   /**
    * Format (serialize) this context
    *
    * @param stream
    *           stream to format to
    * @throws ParseException
    *            if formatting fails
    */
   @Override
   public void format(FndTokenWriter stream) throws ParseException {
      // set debug buffer to context
      setDebugOutToContext();

      stream.write(FndSerializeConstants.BEGIN_BUFFER_MARKER);

      for (FndAttribute attribute : attributes) {
         if (attribute.exist())
            FndAttributeInternals.format(attribute, stream);
      }
      for (FndAbstractRecord record : records) {
         if (record.getName() != null) {
            stream.write(FndSerializeConstants.NAME_MARKER);
            stream.write(record.getName());
         }
         record.formatBuffer(stream);
      }
      stream.write(FndSerializeConstants.END_BUFFER_MARKER);
   }

   /**
    * Approximates the size of the serialized form of this context.
    * @return the number of bytes needed for the serialized form of the context
    */
   public final int serializedSize() {
      int size = 256;
      if(debugInfo != null)
         size += debugInfo.serializedSize();
      return size;
   }

   private int findRecord(String name) {
      for (int i = 0; i < records.size(); i++) {
           if (((FndRecord) records.get(i)).getName().equals(name))
              return i;
       }
      return -1;
   }

   /**
    * Parse (de-serialize) into this context
    *
    * @param stream
    *           stream to parse from
    * @throws ParseException
    *            if parse fails
    */
    @Override
    public void parse(FndTokenReader stream) throws ParseException {
        char ch = stream.getDelimiter();

        if (ch == FndSerializeConstants.HEAD_MARKER) {
            ch = stream.getDelimiter();
        }

        while (ch == FndSerializeConstants.BEGIN_BUFFER_MARKER) {
            ch = stream.getDelimiter();
        }

        while (ch != FndSerializeConstants.END_BUFFER_MARKER) {
            String type, name, state;
            FndAttribute attribute = null;
            FndRecord record, debugRecord;

            if (ch == FndSerializeConstants.NAME_MARKER) {
                name = stream.getToken();
                ch = stream.getDelimiter();

                if (ch == FndSerializeConstants.TYPE_MARKER) {
                    type = stream.getToken();
                    attribute = FndAttributeInternals.newAttribute(type, name);
                    FndAttributeInternals.parse(attribute, stream);
                    setAttribute(attribute);
                } else {
                    if (ch == FndSerializeConstants.STATUS_MARKER) {
                        state = stream.getToken();
                        ch = stream.getDelimiter();
                    }
                    if (ch == FndSerializeConstants.BEGIN_BUFFER_MARKER) {
                        record = new FndRecord(name);
                        record.parseBuffer(stream);
                        int recordPos = findRecord(name);
                        if (recordPos > -1) {
                            records.set(recordPos, record);
                        } else {
                            records.add(record);
                        }
                        switch (name) {
                            case "FND_CONTEXT":
                                sysContext = record;
                                runAs = sysContext.getAttribute("RUN_AS");
                                serializationMode = sysContext.getAttribute("SERIALIZATION_MODE");
                                validateSerializationMode();
                                domain = sysContext.getAttribute("DOMAIN");
                                language = sysContext.getAttribute("LANGUAGE");
                                locale = sysContext.getAttribute("LOCALE");
                                validateLocaleAndLanguage();
                                interactiveMode = sysContext.getAttribute("INTERACTIVE_MODE");
                                sqlTrace = sysContext.getAttribute("SQL_TRACE");
                                clientId = sysContext.getAttribute("CLIENT_ID");
                                clientStatistics = sysContext.getAttribute("CLIENT_STATISTICS");
                                clientTracing = sysContext.getAttribute("CLIENT_TRACING");
                                encodeBinaryValues = sysContext.getAttribute("ENCODE_BINARY_VALUE");
                                plsqlDebugger = sysContext.getAttribute("PLSQL_DEBUGGER");
                                operation = sysContext.getAttribute("OPERATION");
                                encryptionEnabled = sysContext.getAttribute("ENCRYPTION_ENABLED");
                                requestId = sysContext.getAttribute("REQUEST_ID");
                                // Get debug information
                                Iterator iter = FndRecordInternals.getRecords(record).iterator();
                                while(iter.hasNext()) {
                                    debugRecord = (FndRecord)iter.next();
                                    if (debugRecord.getName().equalsIgnoreCase("DEBUG")) {
                                        debugInfo = debugRecord;
                                        setThreadLoggers();
                                    } else if (debugRecord.getName().equalsIgnoreCase("COUNTERS")) {
                                        counters = debugRecord;
                                    } else if (debugRecord.getName().equalsIgnoreCase("CALL_ROUTING")) {
                                        callRouting = debugRecord;
                                    } else if (debugRecord.getName().equalsIgnoreCase("USER_INFO")) {
                                        userInfo = debugRecord;
                                    }
                                }
                                setCounters();
                                if (status == null) {
                                    status = sysContext.getAttribute("STATUS");
                                }
                                break;
                            case "APP_CONTEXT":
                                appContext = record;
                                break;
                            case "ERROR":
                                errormsg = record;
                                break;
                        }
                    } else if (ch == FndSerializeConstants.VALUE_MARKER) {
                        attribute = FndAttributeInternals.newAttribute(FndAttributeType.UNKNOWN, name, stream.getToken());
                        setAttribute(attribute);
                    }
                }
                if (attribute != null) {
                    switch (name) {
                        case "BODY_TYPE":
                            bodyType = attribute;
                            if (!bodyType.isNull() && FndAttributeInternals.internalGetValue(bodyType).equals("XML")) {
                                externalBodyType = FndSerializeConstants.BODY_TYPE_XML;
                                FndAttributeInternals.internalSetValue(bodyType, "BUF");
                            }
                            break;
                        case "STATUS":
                            status = attribute;
                            break;
                    }
                }
            }
            ch = stream.getDelimiter();
        }

        if (appContext == null) {
            appContext = new FndRecord("APP_CONTEXT");
            records.add(appContext);
        }
    }

   /**
    * This is a deprecated method which doesn't do anything in this release.
    * @param flag ignored parameter
    * @deprecated
    */
   public void setUseXA(boolean flag) {
   }

   /**
    * Validate language
    *
    * @param language
    *           language code to validate
    * @throws ParseException
    *            if language is not a valid ISO language code
    */
   /*
    * private void checkIsoLanguage(String language) throws ParseException { if
    * (!isoLanguageExists(language)) throw new ParseException("FNDCONTEXTLANG:&1
    * is not a valid ISO language", language); }
    */

   /**
    * Checks if dynamic creation of aspect aggregates is disabled.
    *
    * @return true if no generic aspects are enabled, false if at least one
    *         generic aspect is enabled.
    */
   public boolean areGenericAspectsDisabled() {
      if (genericAspects == null)
         createGenericAspectMap();
      return genericAspects.isEmpty();
   }

   /**
    * Maps enabled generic aspect name to action flag like include, count,
    * ...etc. Note that this map is transient.
    */
   private transient Map<String, String> genericAspects; // Map<String, String>

   /**
    * Checks if dynamic creation of an aspect aggregates is enabled.
    *
    * @param aspectName
    *           the name of an aspect-entity
    * @return true if the specified generic aspect is enabled, false otherwise.
    */
   public boolean isGenericAspectEnabled(String aspectName) {
      if (genericAspects == null)
         createGenericAspectMap();
      return genericAspects.get(aspectName) != null;
   }

   private void createGenericAspectMap() {
      genericAspects = new HashMap<>();
      FndAttribute attr = getSysContext().getAttribute("GENERIC_ASPECTS");
      if (attr != null && !attr.isNull()) {
         StringTokenizer st = new StringTokenizer(attr.toString(), " ,");
         while (st.hasMoreTokens()) {
            String token = st.nextToken();
            int pos = token.indexOf(':');
            String aspectName = pos >= 0 ? token.substring(0, pos) : token;
            String flag = pos >= 0 ? token.substring(pos + 1).toLowerCase() : null;
            if (flag == null)
               flag = "include";
            if (!"exclude".equals(flag))
               genericAspects.put(aspectName, flag);
         }
      }
   }

   /**
    * Get encryption enabled flag for this context.
    *
    * @return the true/false
    */
   public boolean isEncryptionEnabled() {
      if (encryptionEnabled != null && !encryptionEnabled.isNull())
         return FndAttributeInternals.internalGetValue(encryptionEnabled).equals("TRUE");
      else
         return false;
   }

   /**
    * Set encryption enabled flag for this context.
    *
    * @param flag
    *           the flag to be set
    */
   public void setEncryptionEnabled(boolean flag) {
      if (encryptionEnabled == null) {
         encryptionEnabled = FndAttributeInternals.newAttribute("ENCRYPTION_ENABLED");
         FndRecordInternals.add(getSysContext(), encryptionEnabled);
      }
      if (flag)
         FndAttributeInternals.internalSetValue(encryptionEnabled, "TRUE");
      else
         FndAttributeInternals.internalSetValue(encryptionEnabled, "FALSE");
   }

   /**
    * Set thread local loggers according to debug flags in debugInfo record.
    */
   private void setThreadLoggers() {
      ThreadLoggers loggers = LogMgr.getThreadLoggers();
      if(debugInfo == null) {
         loggers.setLevel(LogMgr.UNDEFINED);
         loggers.unregisterLoggerOutput(loggerOutput);
      }
      else {
         int app  = getLogLevelForCategory("APPLICATION_CODE"  , "DEBUG");
         int call = getLogLevelForCategory("CALL_SEQUENCE"     , "DEBUG");
         int db   = getLogLevelForCategory("DB_ACCESS"         , "TRACE");
         int skel = getLogLevelForCategory("SKELETON_ARGUMENTS", "DEBUG");
         int stub = getLogLevelForCategory("STUB_ARGUMENTS"    , "DEBUG");
         loggers.setApplicationLevel (app);
         loggers.setCallSequenceLevel(call);
         loggers.setDatabaseLevel    (db);
         loggers.setRequestLevel     (skel);
         loggers.setResponseLevel    (stub);
         if(app > LogMgr.UNDEFINED || call > LogMgr.UNDEFINED || db > LogMgr.UNDEFINED || skel > LogMgr.UNDEFINED || stub > LogMgr.UNDEFINED)
            loggers.registerLoggerOutput(loggerOutput);
         else
            loggers.unregisterLoggerOutput(loggerOutput);
      }
   }

   private int getLogLevelForCategory(String cat, String defaultLevel) {
      FndAttribute attr = debugInfo.getAttribute(cat);
      if(attr == null)
         return LogMgr.UNDEFINED;

      String level = attr.toString();
      if("TRUE".equals(level))
         level = defaultLevel;

      return LogMgr.translateLogLevel(level);
   }

   /**
    * <B>Framework internal method:</B> Gets the supported capability set for
    * the current request.
    * <P>
    *
    * This method simply returns an empty capability set so that the java-ap
    * will not be affected by the capability implementation.
    *
    * <B>This is a framework internal method! Backward compatibility is not
    * guaranteed.</B>
    *
    * @return supported capability set
    */
   public Set<ApplicationCapability> getSupportedCapabilities() {
      return Collections.unmodifiableSet(
         CapabilitySet.noneOf(ApplicationCapability.class));
   }

   /**
    * <B>Framework internal method:</B> Sets the supported capability set for
    * the current request. This method is not implemented in <code>FndContext
    * </code>. Please refer to <code>FndServerContext.setSupportedCapabilities
    * </code>.
    * <P>
    * <B>This is a framework internal method! Backward compatibility is not
    * guaranteed.</B>
    *
    * @param set
    *           supported capability set
    */
   public void setSupportedCapabilities(CapabilitySet<ApplicationCapability> set) {
      throw new UnsupportedOperationException(
            "setSupportedCapabilities is not implements in FndContext");
   }

   /**
    * Gets the class logger for debugging of FndContext class hierarchy.
    * @return class logger for FndContext.class
    */
   protected static Logger getClassLogger() {
      return LogMgr.getClassLogger(FndContext.class);
   }

   /**
    * Gets a record with details of the last PLSQL error.
    * The default implementation, which returns null, may be overridden by subclasses.
    * @return null
    */
   protected FndRecord getPlsqlErrorDetails() {
      return null;
   }
}
