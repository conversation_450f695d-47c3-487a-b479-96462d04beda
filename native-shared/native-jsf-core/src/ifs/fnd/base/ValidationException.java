/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.base;

import ifs.fnd.record.FndAbstractRecord;
import ifs.fnd.record.serialization.FndBufferUtil;
import ifs.fnd.record.serialization.FndUtil;
import java.io.IOException;

/**
 * Exception thrown when a validation error occurs on an attribute or record.
 */
public class ValidationException extends ApplicationException {

   /**
    * extraInfo is used to store the serialized form of a record containing one or more
    * attributes marked with invalid value info. (Used by client)
    */
   private String extraInfo;

   /**
    * Create a new instance.
    * @param extraInfo extra info text
    * @param msg translatable error message
    * @since 4.1.0
    */
   public ValidationException(final String extraInfo, final FndTranslatableText msg) {
      super(msg);
      this.error = VALIDATION_ERROR;
      this.extraInfo = extraInfo;
   }

   /**
    * Create a new instance.
    * The first argument is an optional record which will be formatted as extra info attached to this exception.
    * @param rec record containing one or more attributes marked with invalid value info
    * @param msg error message
    * @param p1  placeholder texts
    * @since 4.1.0
    */
   public ValidationException(final FndAbstractRecord rec, final FndTranslatableText msg, final String... p1) {
      super(msg, p1);
      this.error = VALIDATION_ERROR;
      setExtraInfo(rec);
   }

   /**
    * Create a new instance. Framework internal constructor, do not use it!
    * @param msg error message
    */
   public ValidationException(final String msg) {
      // as this constructor is used from FndAbstractRecord with dynamic text,
      // we can't use FndTranslatableText here
      super(msg);
      this.error = VALIDATION_ERROR;
   }
   
   //
   // To be obsolete
   //

   /**
    * Create a new instance.
    * @param extraInfo extra info text
    * @param msg error message
    * @deprecated
    */
   public ValidationException(final String extraInfo, final String msg) {
      super(msg);
      this.error = VALIDATION_ERROR;
      this.extraInfo = extraInfo;
   }

   /**
    * Create a new instance.
    * The first argument is an optional record which will be formatted as extra info attached to this exception.
    * @param rec record containing one or more attributes marked with invalid value info
    * @param msg error message
    * @param p1  placeholder texts
    * @deprecated
    */
   public ValidationException(final FndAbstractRecord rec, final String msg, final String... p1) {
      super(msg, p1);
      this.error = VALIDATION_ERROR;
      setExtraInfo(rec);
   }

   /**
    * Return the extra info attached to this exception.
    * @return Base-64 encoded serialized form of a record containing one or more invalid attributes
    * @see #setExtraInfo(FndAbstractRecord)
    */
    @Override
   public String getExtraInfo() {
      return extraInfo;
   }

   /**
    * Store the serialized form of a record as extra info
    * @param rec record containing one or more attributes marked with invalid value info
    */
   public final void setExtraInfo(final FndAbstractRecord rec) {
      if (rec != null) {
         setInvalidRecord(rec);
      }
   }

   /**
    * Stores the serialized form of a record in the extra info field.
    * @param rec record containing attributes marked with invalid value info
    */
   public void setInvalidRecord(final FndAbstractRecord rec) {
      try {
         extraInfo = FndUtil.toBase64Text(FndBufferUtil.formatRecord(rec));
      }
      catch (IOException | ParseException e) {
         throw new IfsRuntimeException(e, "FNDSETINVREC:Formatting of invalid record failed: &1", e.getMessage());
      }
   }
}
