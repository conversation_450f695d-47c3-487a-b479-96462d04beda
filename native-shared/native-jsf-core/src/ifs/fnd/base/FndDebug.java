/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.base;

import ifs.fnd.buffer.Buffer;
import ifs.fnd.buffer.BufferFormatException;
import ifs.fnd.buffer.Buffers;
import ifs.fnd.buffer.ExtendedBufferFormatter;
import ifs.fnd.buffer.StandardBuffer;
import ifs.fnd.internal.*;
import ifs.fnd.log.LogMgr;
import ifs.fnd.log.Logger;
import ifs.fnd.log.ThreadLoggers;
import ifs.fnd.record.*;
import ifs.fnd.record.serialization.FndBufferUtil;
import ifs.fnd.record.serialization.FndSerializeConstants;
import ifs.fnd.record.serialization.FndUtil;
import ifs.fnd.util.Str;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * This service class can be used as help for debugging server code.
 * <P>
 * Typically, application code will check if the debug output has been enabled
 * by calling {@link #isDebugApplicationCodeOn}. Then {@link #debug debug} methods
 * are used to format debug information.
 * The methods {@link #debugRecord debugRecord} and {@link #debugArrayRecord debugArrayRecord}
 * are used to display the contents of a record and array of records respectively.
 * <P>
 * The following example builds a simple query. The input record as well as the output array are
 * spooled to debug output.
 * <pre>
 *    boolean debug = FndDebug.<B>isDebugApplicationCodeOn</B>();
 *
 *    PersonInfo person = new PersonInfo();
 *    person.excludeQueryResults();
 *    person.objId.include();
 *    person.objVersion.include();
 *    person.name.include();
 *    person.personId.include();
 *    person.creationDate.include();
 *    person.country.include();
 *    person.iSOCountry.include();
 *    person.addCondition(person.country.createIsNotNullCondition());
 *
 *    FndQueryRecord qry = new FndQueryRecord(person);
 *    qry.maxRows.setValue(10);
 *    qry.setOrderBy(person.name);
 *
 *    if(debug) {
 *       FndDebug.<B>debug</B>("Query condition:");
 *       FndDebug.<B>debugRecord</B>(qry);
 *    }
 *
 *    PersonInfoHandler handler = PersonInfoHandlerFactory.getHandler();
 *    PersonInfoArray arr = (PersonInfoArray) handler.query(qry);
 *
 *    if(debug) {
 *       FndDebug.<B>debug</B>("Query result (&1 records):", String.valueOf(arr.size()));
 *       FndDebug.<B>debugArrayRecord</B>(arr);
 *    } </pre>
 * The input record will result in the following debug output. It presents all (simple and compound)
 * attributes contained in the query record. The query attributes MAX_ROWS and ORDER_BY have
 * not null values. The aggregated condition record PERSON_INFO has attributes that are excluded
 * from query result marked with <B>X</B> in attribute state.
 * Note that query conditions are also presented in debug output.
 * <pre>
 *    Query condition:
 *    Fnd_View PERSON_INFO_QUERY (DIRTY) Modify
 *    Integer SKIP_ROWS/Q Non_Existent
 *    Integer (DIRTY) <B>MAX_ROWS</B>/* = <B>10</B>
 *    Text (DIRTY) <B>ORDER_BY</B>/* = <B>NAME</B>
 *    Array RESULT/Q Non_Existent
 *    Aggregate (DIRTY) CONDITION/*:
 *       Fnd_View PERSON_INFO Create
 *       <B>CONDITION:(COUNTRY /= Null)</B>
 *          Alpha OBJ_ID/Q Null
 *          Alpha OBJ_VERSION/Q Null
 *          Text PERSON_ID/QM Null
 *          Text NAME/QM Null
 *          Timestamp CREATION_DATE/Q Null
 *          Text PARTY/QX Null
 *          Number PICTURE_ID/Q<B>X</B> Null
 *          Boolean IS_PROTECTED/Q<B>X</B>M Null
 *          Text DEFAULT_DOMAIN/Q<B>X</B>M Null
 *          Text USER_ID/Q<B>X</B> Null
 *          Text COUNTRY/Q Null
 *          ...
 *          Array ADDRESSES/Q<B>X</B> (0 elements):
 *          Array COMMUNICATION_METHODS/Q<B>X</B> (0 elements):
 *          Aggregate ISO_COUNTRY/Q Null
 *          Aggregate ISO_LANGUAGE/Q<B>X</B> Null
 *          Aggregate PICTURE_DATA/Q<B>X</B> Null
 *          ... </pre>
 * The array returned by the query produces the following debug output
 * (only the first of 10 returned records are presented here).
 * Simple attributes that where included in query result have not-null or null values.
 * Attributes (both simple and compound) that where excluded from query result
 * are marked as Non_Existent.
 * The debug output of a record presents also References, which are used to connect
 * master and detail records. In this case COUNTRY_CODE, a member of ABSTRACT_ISO_COUNTRY_KEY in
 * ISO_COUNTRY (detail record) corresponds to COUNTRY, a member of ISO_COUNTRY_REFERENCE in
 * PERSON_INFO (master record).
 * <pre>
 *    Query result (10 records):
 *    Fnd_View PERSON_INFO_ARRAY
 *    Array ARR (10 elements):
 *       1:Fnd_View PERSON_INFO (DIRTY) Q element of PERSON_INFO_ARRAY.ARR
 *          Alpha OBJ_ID = AAAUPvAADAAAI21AA6
 *          Alpha OBJ_VERSION = 3
 *          Text PERSON_ID/MN = AGBJ
 *          Text NAME/M = Agneta Bjorklund
 *          Timestamp CREATION_DATE/N = Sat Dec 01 00:00:00 CET 2001
 *          Text PARTY/Q Non_Existent
 *          Number PICTURE_ID/Q Non_Existent
 *          Boolean IS_PROTECTED/QM Null
 *          Text DEFAULT_DOMAIN/QM Null
 *          Text USER_ID/Q Non_Existent
 *          Text <B>COUNTRY</B> = SE
 *          ...
 *          Array ADDRESSES/Q Non_Existent
 *          Array COMMUNICATION_METHODS/Q Non_Existent
 *          Aggregate ISO_COUNTRY:
 *             Fnd_View ISO_COUNTRY (DIRTY) Q
 *                Alpha OBJ_ID = AAAGiPAADAAAD6XAAi
 *                Alpha OBJ_VERSION = SWEDENKingdom of SwedenTRUEYN
 *                Text <B>COUNTRY_CODE</B>/N = SE
 *                Text DESCRIPTION/M = SWEDEN
 *                Text FULL_NAME = Kingdom of Sweden
 *                Enum EU_MEMBER/M = Y
 *                Boolean SALES_TAX = false
 *                Text ALL_DESCRIPTIONS = SWEDEN
 *                Boolean USED_IN_APPLICATION/M = true
 *                References (1):
 *                   <B>ABSTRACT_ISO_COUNTRY_KEY</B> (COUNTRY_CODE) Primary_Key, Parent_Key
 *          Aggregate ISO_LANGUAGE/Q Non_Existent
 *          Aggregate PICTURE_DATA/Q Non_Existent
 *          References (5):
 *             ABSTRACT_PERSON_INFO_KEY (PERSON_ID) Primary_Key
 *             FND_USER_REFERENCE (USER_ID)
 *             <B>ISO_COUNTRY_REFERENCE</B> (COUNTRY)
 *             ISO_LANGUAGE_REFERENCE (DEFAULT_LANGUAGE)
 *             PICTURE_DATA_REFERENCE (PICTURE_ID)
 * </pre>
 * @see #debugQueryRecord debugQueryRecord
 * @see #debugAttribute debugAttribute
 * @see #isLogOn()
 * @see #log(String)
 */
public final class FndDebug {

   private static final int MAX_BINARY_IMAGE_SIZE = 16 * 1024;

   private static final int indentWidth = 3;

   private static final int MAX_LINE_SIZE = 2000;

   private static final int MAX_ELEM_SIZE = 500;

   private static final StringBuilder indentStr = createIndentBuffer();

   private static StringBuilder createIndentBuffer() {
      StringBuilder buf = new StringBuilder();
      for (int i = 0; i < 200 * indentWidth; i++) {
         buf.append(" ");
      }
      return buf;
   }

   //Avoid instantiations
   private FndDebug() {
   }

   /**
    * Checks if any debug flags are enabled, either local or remote.
    *
    * @return <code>true</code> if a debug flag is enabled.
    */
   public static boolean isDebugOn() {
      return findDebugLogger() != null;
   }

   /**
    * Finds an active logger for debugging.
    * @return the found instance of Logger, or null if all debug loggers are disabled.
    */
   private static Logger findDebugLogger() {
      ThreadLoggers loggers = LogMgr.getThreadLoggers();

      Logger logger = loggers.getLastCheckedLogger();
      if(logger != null && logger.debug)
         return logger;

      if(loggers.getApplicationLevel() == LogMgr.DEBUG)
         return loggers.getApplicationLogger();

      else if(loggers.getDatabaseLevel() == LogMgr.DEBUG)
         return loggers.getDatabaseLogger();

      else if(loggers.getCallSequenceLevel() == LogMgr.DEBUG)
         return loggers.getCallSequenceLogger();

      else if(loggers.getRequestLevel() == LogMgr.DEBUG)
         return loggers.getRequestLogger();

      else if(loggers.getResponseLevel() == LogMgr.DEBUG)
         return loggers.getResponseLogger();

      else
         return null;
   }

   /**
    * Checks debug flag for application code. <B>This is the one to use by
    * application developers! </B>
    * <P>
    * See this class {@link FndDebug description} for example of usage.
    *
    * @return <code>true</code> if debug is enabled.
    */
   public static boolean isDebugApplicationCodeOn() {
      ThreadLoggers loggers = LogMgr.getThreadLoggers();
      Logger logger = loggers.getApplicationLogger();
      if(logger.debug)
         loggers.setLastCheckedLogger(logger);
      return logger.debug;
   }

   /**
    * Checks debug flag for call sequence.
    *
    * @return <code>true</code> if debug is enabled.
    */
   public static boolean isDebugCallSequenceOn() {
      ThreadLoggers loggers = LogMgr.getThreadLoggers();
      Logger logger = loggers.getCallSequenceLogger();
      if(logger.debug)
         loggers.setLastCheckedLogger(logger);
      return logger.debug;
   }

   /**
    * Checks debug flag for db access.
    *
    * @return <code>true</code> if trace or debug is enabled.
    */
   public static boolean isDebugDbAccessOn() {
      ThreadLoggers loggers = LogMgr.getThreadLoggers();
      Logger logger = loggers.getDatabaseLogger();
      if(logger.trace)
         loggers.setLastCheckedLogger(logger);
      return logger.trace;
   }

   /**
    * Checks debug flag for skeleton arguments.
    *
    * @return <code>true</code> if debug is enabled.
    */
   public static boolean isDebugSkeletonArgumentsOn() {
      ThreadLoggers loggers = LogMgr.getThreadLoggers();
      Logger logger = loggers.getRequestLogger();
      if(logger.debug)
         loggers.setLastCheckedLogger(logger);
      return logger.debug;
   }

   /**
    * Checks debug flag for stub arguments.
    *
    * @return <code>true</code> if debug is enabled.
    */
   public static boolean isDebugStubArgumentsOn() {
      ThreadLoggers loggers = LogMgr.getThreadLoggers();
      Logger logger = loggers.getResponseLogger();
      if(logger.debug)
         loggers.setLastCheckedLogger(logger);
      return logger.debug;
   }

   static int debugFlagToLevel(boolean on) {
      return on ? LogMgr.DEBUG : LogMgr.UNDEFINED;
   }

   /**
    * Sets/Unsets all local debug flags.
    *
    * @param on
    *           flag inidicating if debug should be on or off
    */
   public static void setDebugAll(boolean on) {
      LogMgr.setDefaultLevel(debugFlagToLevel(on));
   }

   /**
    * Sets/Un-sets local debug flag APPLICATION_CODE.
    *
    * @param on
    *           flag indicating if debug should be on or off
    */
   public static void setDebugApplicationCode(boolean on) {
      LogMgr.setDefaultApplicationLevel(debugFlagToLevel(on));
   }

   /**
    * Sets/Un-sets local debug flag CALL_SEQUENCE.
    *
    * @param on
    *           flag indicating if debug should be on or off
    */
   public static void setDebugCallSequence(boolean on) {
      LogMgr.setDefaultCallSequenceLevel(debugFlagToLevel(on));
   }

   /**
    * Sets/Un-sets local debug flag DB_ACCESS.
    *
    * @param on
    *           flag indicating if debug should be on or off
    */
   public static void setDebugDbAccess(boolean on) {
      LogMgr.setDefaultDatabaseLevel(debugFlagToLevel(on));
   }

   /**
    * Sets/Un-sets local debug flag SKELETON_ARGUMENTS.
    *
    * @param on
    *           flag indicating if debug should be on or off
    */
   public static void setDebugSkeletonArguments(boolean on) {
      LogMgr.setDefaultRequestLevel(debugFlagToLevel(on));
   }

   /**
    * Sets/Un-sets local debug flag STUB_ARGUMENTS.
    *
    * @param on
    *           flag indicating if debug should be on or off
    */
   public static void setDebugStubArguments(boolean on) {
      LogMgr.setDefaultResponseLevel(debugFlagToLevel(on));
   }

   /**
    * Prints a debug message.
    *
    * @param logger non-null instance of active Logger
    * @param msg message to print
    */
   private static void printDebug(Logger logger, String msg) {
      logger.debug(msg);
   }

   /**
    * The "debug" functions print a debug message, at the correct indentation.
    * The parameters in the message work in the same way as parameters for error
    * messages: write &amp; and a number to create a paramter place holder.
    * <P>
    * See this class {@link FndDebug description} for example of usage.
    *
    * @param line
    *           Message
    */
   public static void debug(String line) {
      debug(line, "NoParameter", "", "", "", "", false);
   }

   /**
    * The "debug" functions print a debug message, at the correct indentation.
    * The parameters in the message work in the same way as parameters for error
    * messages: write &amp; and a number to create a paramter place holder.
    * <P>
    * See this class {@link FndDebug description} for example of usage.
    *
    * @param line
    *           Message
    * @param p1
    *           first place holder text
    */
   public static void debug(String line, String p1) {
      debug(line, p1, "", "", "", "", false);
   }

   /**
    * The "debug" functions print a debug message, at the correct indentation.
    * The parameters in the message work in the same way as parameters for error
    * messages: write &amp; and a number to create a paramter place holder.
    * <P>
    * See this class {@link FndDebug description} for example of usage.
    *
    * @param line
    *           Message
    * @param p1
    *           first place holder text
    * @param p2
    *           second place holder text
    */
   public static void debug(String line, String p1, String p2) {
      debug(line, p1, p2, "", "", "", false);
   }

   /**
    * The "debug" functions print a debug message, at the correct indentation.
    * The parameters in the message work in the same way as parameters for error
    * messages: write &amp; and a number to create a paramter place holder.
    * <P>
    * See this class {@link FndDebug description} for example of usage.
    *
    * @param line
    *           Message
    * @param p1
    *           first place holder text
    * @param p2
    *           second place holder text
    * @param p3
    *           third place holder text
    */
   public static void debug(String line, String p1, String p2, String p3) {
      debug(line, p1, p2, p3, "", "", false);
   }

   /**
    * The "debug" functions print a debug message, at the correct indentation.
    * The parameters in the message work in the same way as parameters for error
    * messages: write &amp; and a number to create a paramter place holder.
    * <P>
    * See this class {@link FndDebug description} for example of usage.
    *
    * @param line
    *           Message
    * @param p1
    *           first place holder text
    * @param p2
    *           second place holder text
    * @param p3
    *           third place holder text
    * @param p4
    *           fourth place holder text
    */
   public static void debug(String line, String p1, String p2, String p3, String p4) {
      debug(line, p1, p2, p3, p4, "", false);
   }

   /**
    * The "debug" functions print a debug message, at the correct indentation.
    * The parameters in the message work in the same way as parameters for error
    * messages: write &amp; and a number to create a paramter place holder.
    * <P>
    * See this class {@link FndDebug description} for example of usage.
    *
    * @param line
    *           Message
    * @param p1
    *           first place holder text
    * @param p2
    *           second place holder text
    * @param p3
    *           third place holder text
    * @param p4
    *           fourth place holder text
    * @param p5
    *           fifth place holder text
    */
   public static void debug(String line, String p1, String p2, String p3, String p4, String p5) {
      debug(line, p1, p2, p3, p4, p5, false);
   }

   private static StringBuilder formatMessage(StringBuilder message, String p1, String p2, String p3, String p4, String p5) {
      StringBuilder val;
      int index;

      for (int i = 1; i < 6; i++) {
         val = new StringBuilder("&");
         val.append(i);
         index = message.toString().indexOf(val.toString());
         if (index >= 0) {
            message.delete(index, index + 2);
            switch (i) {
            case 1:
               if (p1 != null)
                  message.insert(index, p1);
               break;
            case 2:
               if (p2 != null)
                  message.insert(index, p2);
               break;
            case 3:
               if (p3 != null)
                  message.insert(index, p3);
               break;
            case 4:
               if (p4 != null)
                  message.insert(index, p4);
               break;
            case 5:
               if (p5 != null)
                  message.insert(index, p5);
               break;
            default:
               break;
            }
         }
      }
      return message;
   }

   /**
    * Print a debug message
    *
    * @param line
    *           message to print
    * @param p1
    *           first placeholder text
    * @param p2
    *           second placeholder text
    * @param p3
    *           third placeholder text
    * @param p4
    *           fourth placeholder text
    * @param p5
    *           fifth placeholder text
    * @param toStdOut
    *           not used
    */
   public static void debug(String line, String p1, String p2, String p3, String p4, String p5, boolean toStdOut) {
      Logger logger = findDebugLogger();
      if (logger != null)
         printDebug(logger, new Instance().formatLine(line, p1, p2, p3, p4, p5, false));
   }

   /**
    * Prints out information about an attribute: name, value, type and state.
    *
    * @param att
    *           Attribute to debug
    */
   public static void debugAttribute(FndAttribute att) {
      debug("ATTRIBUTE: &1 Value: &2 TYPE: &3 STATUS: &4", att.getName(), (att.isNull()) ? "Null" : att.toString(), att.getType().toString(), att.getState());
   }

   /**
    * This function should be called from the framework for each CORBA call
    * made.
    *
    * @param value
    *           Value to increase with
    */
   public static void increaseCorbaCallCounter(int value) {
      FndContext.increaseCounter("CORBA_CALL", value);
   }

   /**
    * For manipulating SqlFetch counter in the context.
    *
    * @param value
    *           value to increase with
    */
   public static void increaseSqlFetchCounter(int value) {
      FndContext.increaseCounter("SQL_FETCH", value);
   }

   /**
    * For manipulating SqlStmt counter in the context.
    *
    * @param value
    *           value to increase with
    */
   public static void increaseSqlStmtCounter(int value) {
      FndContext.increaseCounter("SQL_STATEMENT", value);
   }

   /**
    * Prints an array to debug output.
    * <P>
    * See this class {@link FndDebug description} for example of usage.
    *
    * @param arr
    *           Array to print
    */
   public static void debugArrayRecord(FndAbstractArray arr) {
      Logger logger = findDebugLogger();
      if (logger != null)
         printDebug(logger, formatDebugArrayRecord(arr));
   }

   /**
    * Prints a query record to debug output.
    *
    * @param record
    *           Query to debug
    * @param result
    *           Array with results from the query (to print a query together
    *           with results)
    */
   public static void debugQueryRecord(FndQueryRecord record, FndAbstractArray result) {
      Logger logger = findDebugLogger();
      if (logger != null)
         printDebug(logger, formatDebugQueryRecord(record, result));
   }

   /**
    * Prints a record to debug output.
    * <P>
    * See this class {@link FndDebug description} for example of usage.
    *
    * @param record
    *           Record to print
    */
   public static void debugRecord(FndAbstractRecord record) {
      if (record != null) {
         Logger logger = findDebugLogger();
         if (logger != null)
            printDebug(logger, formatDebugRecord(record));
      }
   }

   /**
    * Creates a string with debug information about an array.
    *
    * @param arr the array to debug
    * @return string containing the debug information about the contents of the array
    */
   public static String formatDebugArrayRecord(FndAbstractArray arr) {
      try {
         arr = (FndAbstractArray)arr.clone();
      }
      catch(CloneNotSupportedException e) {
         return "Clone is not supported for this array";
      }
      FndAbstractRecord arrayView = FndBufferUtil.createArrayView(arr.newRecord());
      FndAttributeInternals.load(((FndArray) arrayView.getAttribute("ARR")), FndAttributeInternals.getInternalRecords(arr));
      return formatDebugRecord(arrayView);
   }

   /**
    * Creates a string with debug information about a query result.
    *
    * @param record the query record to debug
    * @param result the array with results from the query
    * @return string containing the debug information about the contents of the query record and the result array
    */
   public static String formatDebugQueryRecord(FndQueryRecord record, FndAbstractArray result) {
      FndAttributeInternals.load(((FndArray) record.getAttribute("RESULT")), FndAttributeInternals.getInternalRecords(result));
      String str = formatDebugRecord(record);
      ((FndArray) record.getAttribute("RESULT")).reset();
      return str;
   }

   /**
    * Creates a string with debug information about a record.
    *
    * @param record the record to debug
    * @return string containing the debug information about the contents of the record
    */
   public static String formatDebugRecord(FndAbstractRecord record) {
      return new Instance().formatDebugRecord(record);
   }

   /**
    * Used automatically from the framework. To debug context,
    *
    * @param record context record
    * @param logger logger
    */
   public static void debugContext(FndAbstractRecord record, Logger logger) {
      new Instance().debugContext(record, logger);
   }

   private static String attributeType(FndAttribute attr) {
      String name = attr.getType().getName();
      if (attr.isDirty())
         name = name + " (DIRTY)";
      String info = attr.getInvalidValueInfo();
      if (info != null)
         name = name + " (INVALID: " + info + ")";
      return name;
   }

   private static String attributeName(FndAttribute attr) {
      String name = attr.getName();
      String state = attr.getState();
      return name + ("".equals(state) ? "" : "/"+state);
   }

   /**
    * Debug all references from a record
    *
    * @param rec
    *           record to debug
    */
   public static void debugReferences(FndAbstractRecord rec) {
      new Instance().debugReferences(rec);
   }

   private static String formatStringArray(String[] arr) {
      if (arr == null)
         return "null";

      StringBuilder buf = new StringBuilder();
      buf.append('{');
      for (int i = 0; i < arr.length; i++) {
         if (!appendNextElement(buf, i))
            break;
         String value = arr[i];

         if (value == null) {
            buf.append("null");
            continue;
         }

         if (value.length() > MAX_ELEM_SIZE)
            value = value.substring(0, MAX_ELEM_SIZE) + "...";
         buf.append(value);
      }
      buf.append("} (").append(arr.length).append(" elements)");
      return buf.toString();
   }

   private static String formatDateArray(Date[] arr) {
      if (arr == null)
         return "null";

      SimpleDateFormat dateFormat = FndContext.getCurrentTimestampFormat();

      StringBuilder buf = new StringBuilder();
      buf.append('{');
      for (int i = 0; i < arr.length; i++) {
         if (!appendNextElement(buf, i))
            break;
         buf.append(arr[i] == null ? "null" : dateFormat.format(arr[i]));
      }
      buf.append("} (").append(arr.length).append(" elements)");
      return buf.toString();
   }

   private static String formatDoubleArray(double[] arr) {
      if (arr == null)
         return "null";

      StringBuilder buf = new StringBuilder();
      buf.append('{');
      for (int i = 0; i < arr.length; i++) {
         if (!appendNextElement(buf, i))
            break;
         buf.append(arr[i]);
      }
      buf.append("} (").append(arr.length).append(" elements)");
      return buf.toString();
   }

   private static String formatBooleanArray(boolean[] arr) {
      if (arr == null)
         return "null";

      StringBuilder buf = new StringBuilder();
      buf.append('{');
      for (int i = 0; i < arr.length; i++) {
         if (!appendNextElement(buf, i))
            break;
         buf.append(arr[i]);
      }
      buf.append("} (").append(arr.length).append(" elements)");
      return buf.toString();
   }

   private static String formatLongArray(long[] arr) {
      if (arr == null)
         return "null";

      StringBuilder buf = new StringBuilder();
      buf.append('{');
      for (int i = 0; i < arr.length; i++) {
         if (!appendNextElement(buf, i))
            break;
         buf.append(arr[i]);
      }
      buf.append("} (").append(arr.length).append(" elements)");
      return buf.toString();
   }

   private static String formatBinaryArray(byte[][] arr) {
      if (arr == null)
         return "null";

      StringBuilder buf = new StringBuilder();
      buf.append('{');
      for (int i = 0; i < arr.length; i++) {
         if (!appendNextElement(buf, i))
            break;

         byte[] data = arr[i];

         if (data == null) {
            buf.append("null");
            continue;
         }

         byte[] d2 = new byte[((data.length <= 16) ? data.length : 16)];
         System.arraycopy(data, 0, d2, 0, d2.length);
         String value = FndUtil.toHexText(d2);
         if (data.length > d2.length)
            value += "...";
         buf.append("16#").append(value).append("# (").append(data.length).append(" bytes)");
      }
      buf.append("} (").append(arr.length).append(" elements)");
      return buf.toString();
   }

   private static boolean appendNextElement(StringBuilder buf, int i) {
      if (i > 0)
         buf.append(", ");
      if (buf.length() > MAX_LINE_SIZE) {
         buf.append("...");
         return false;
      }
      return true;
   }

   private static String binaryValueImage(byte[] data) {
      int maxLen = MAX_BINARY_IMAGE_SIZE;
      byte[] buf = data.length <= maxLen ? data : Arrays.copyOf(data, maxLen);

      String txt = bufferImage(buf);
      if(txt == null) {
         txt = textImage(buf);
         if(txt == null) {
            return hexImage(data);
         }
      }

      if(data.length > buf.length && !txt.endsWith("...")) {
         txt += "...";
      }

      txt = replaceInvalidCharacters(txt);
      return txt;
   }

   private static String replaceInvalidCharacters(String in) {
      if(in == null)
         return in;

      StringBuilder out = new StringBuilder(in.length());
      for(int i=0; i<in.length(); i++) {
         Character ch = in.charAt(i);
         out.append( ch==9 || ch==10 || ch==13 || ch>31 && ch<127 || ch>159 && ch<256 ? ch : '.');
      }
      return out.toString();
   }

   private static String bufferImage(byte[] data) {
      if(data.length==0 || data[0] != FndSerializeConstants.BEGIN_BUFFER_MARKER) {
         return null;
      }
      Buffer rec = new StandardBuffer();
      ExtendedBufferFormatter fmt = new ExtendedBufferFormatter();
      String suffix = "";
      try {
         fmt.parse(data, rec);
      }
      catch(BufferFormatException.EndOfStreamException e) {
         suffix = "...";
      }
      catch(BufferFormatException | IOException e) {
         return null;
      }
      return Buffers.listToString(rec) + suffix;
   }

   private static String textImage(byte[] data) {
      String str = Str.bytesUtf8ToString(data);
      if(str.startsWith("<?xml")) {
         return str;
      }
      int letters = 0;
      int spaces = 0;
      for(int i = 0; i < str.length(); i++) {
         if(Character.isLetterOrDigit(str.charAt(i))) {
            letters++;
         }
         else if(Character.isSpaceChar(str.charAt(i))) {
            spaces++;
         }
      }
      return (double) letters / (str.length() - spaces + 1) > 0.6 ? str : null;
   }

   private static String hexImage(byte[] data) {
      int maxLen = 16;
      if(data.length <= maxLen) {
         return FndUtil.toHexText(data);
      }
      else {
         return FndUtil.toHexText(Arrays.copyOf(data, maxLen)) + "...";
      }
   }

   private static class Instance {

      private int indentValue = 0;

      /**
       * Increase the indentation of debug output by one level.
       */
      private void pushDebugIndent() {
         indentValue += indentWidth;
      }

      /**
       * Decrease the debugging output indentation by one level.
       */
      private void popDebugIndent() {
         int newIndentValue = indentValue - indentWidth;
         if (newIndentValue >= 0)
            indentValue = newIndentValue;
      }

      /**
       * Resets the debugging output indentation to none.
       */
      private void resetDebugIndent() {
         indentValue = 0;
      }

      /**
       * Used by other debugging functions to create a string with correct
       * indentation.
       *
       * @return A string containing blank spaces for indentation
       */
      private String indent() {
         return indentStr.substring(0, indentValue);
      }

      private String formatLine(String line, String p1, String p2, String p3, String p4, String p5) {
         return formatLine(line, p1, p2, p3, p4, p5, true);
      }

      private String formatLine(String line, String p1, String p2, String p3, String p4, String p5, boolean appendNewLine) {
         StringBuilder message = new StringBuilder(line);
         message = formatMessage(message, p1, p2, p3, p4, p5);
         message.insert(0, indent());
         return message.toString();
      }

      private String formatDebugRecord(FndAbstractRecord record) {
         if(record==null) return "<NULL>";

         resetDebugIndent();
         StringBuilder message = debugRecord(record, -1, "", "");
         resetDebugIndent();
         return message.toString();
      }

      private void debugContext(FndAbstractRecord record, Logger logger) {
         if (logger != null) {
            resetDebugIndent();
            StringBuilder message = parseContext(record);
            printDebug(logger, " " + message.toString());
            resetDebugIndent();
         }
      }

      private StringBuilder debugRecord(FndAbstractRecord record, int itemNo, String parentName, String arrayType) {
         FndAttribute element;
         FndAbstractArray elementVector;
         String value;
         StringBuilder debugMessage = new StringBuilder();
         FndAbstractRecord elementRecord;
         FndAbstractRecord conditionRecord;

         String state = record.getState() == null ? "" : record.getState().toString();
         if (record.isDirty())
            state = "(DIRTY) " + state;

         String identity = FndRecordInternals.getIdentity(record);
         if(identity != null)
            identity = " identity="+identity;
         else
            identity = "";

         if (itemNo < 0) {
            debugMessage.append(formatLine("&1 (Fnd_View) &2 "+identity, record.getName(), state, "", "", ""));
         }
         else
            debugMessage.append(formatLine("&1:&2 (Fnd_View) &3 element of &4.&5 "+identity, String.valueOf(itemNo), record.getName(), state, parentName, arrayType));
         FndAbstractRecord.Iterator records = record.records();
         while (records.hasNext()) {
            conditionRecord = records.next();
            if ((conditionRecord instanceof FndCondition) || (conditionRecord instanceof FndSimpleCondition))
               debugMessage.append(formatLine("CONDITION:&1", debugAdditionalCondition(conditionRecord).toString(), "", "", "", ""));
         }

         pushDebugIndent();
         int attrCount = record.getAttributeCount();

         SortedMap<String, FndAttribute> map = new TreeMap<>();
         for(int i=0; i<attrCount; i++) {
            element = record.getAttribute(i);
            String key = element.getName()+"+"+i;
            if(element.isVector())
               key = "V+"+key;
            else if(element.getType().equals(FndAttributeType.AGGREGATE))
               key = "G+"+key;
            else
               key = "A+"+key;
            map.put(key, element);
         }
         Iterator<FndAttribute> attrIter = map.values().iterator();

         for(int i=0; i<attrCount; i++) {
            if(attrIter.hasNext())
               element = attrIter.next();
            else {
               debugMessage.append(">>> NO MORE ATTRIBUTES!!! <<<");
               continue;
            }

            if (element.isVector()) {
               if (element.exist()) {
                  elementVector = (FndAbstractArray) element;
                  debugMessage.append(formatLine("&1 (&2) (&3 elements):", attributeName(element), attributeType(element), String.valueOf(elementVector.size()), "", ""));
                  for (int j = 0; j < elementVector.size(); j++) {
                     pushDebugIndent();
                     debugMessage.append(debugRecord(FndAttributeInternals.internalGet(elementVector, j), j + 1, record.getName(), element.getName()).toString());
                     popDebugIndent();
                  }
               }
               else
                  debugMessage.append(formatLine("&1 (Array) Non_Existent", attributeName(element), "", "", "", ""));
            }
            else {
               if (element instanceof FndSimpleArray) {
                  debugSimpleArray(debugMessage, (FndSimpleArray) element);
               }
               else if (!element.exist()) {
                  debugMessage.append(formatLine("&1 (&2) Non_Existent", attributeName(element), attributeType(element), "", "", ""));
               }
               else if (FndAttributeInternals.internalGetValue(element) == null) {
                  debugMessage.append(formatLine("&1 (&2) Null", attributeName(element), attributeType(element), "", "", ""));
               }
               else if (element.getType().equals(FndAttributeType.AGGREGATE)) {
                  elementRecord = FndAttributeInternals.internalGetRecord((FndAbstractAggregate) element);
                  debugMessage.append(formatLine("&1 (&2):", attributeName(element), attributeType(element), "", "", ""));
                  pushDebugIndent();
                  debugMessage.append(debugRecord(elementRecord, -1, elementRecord.getName(), elementRecord.getName()).toString());
                  popDebugIndent();
               }
               else if (element.getType().equals(FndAttributeType.ENUMERATION)) {
                  value = element.toString();
                  debugMessage.append(formatLine("&1 (&2) = &3", attributeName(element), attributeType(element), value, "", ""));
               }
               else if (element.getType().equals(FndAttributeType.BINARY)) {
                  byte[] data = ((FndBinary) element).getValue();
                  value = binaryValueImage(data);
                  debugMessage.append(formatLine("&1 (&2) = &3 bytes: &4", attributeName(element), attributeType(element), Integer.toString(data.length), value, ""));
               }
               else {
                  if (FndAttributeInternals.internalGetValue(element) == null)
                     value = null;
                  else{
                     if(element.getMeta().isEncrypted()) {
                        try{
                           value = FndAttributeInternals.encryptString(FndAttributeInternals.internalGetValue(element).toString());
                        }
                        catch(EncryptionException e) {
                           throw new IfsRuntimeException(e, Texts.ENCRYPTERROR, element.getName(), record.getName(), e.getMessage());
                        }
                     }
                     else
                        value = FndAttributeInternals.internalGetValue(element).toString();
                  }
                  debugMessage.append(formatLine("&1 (&2) = &3", attributeName(element), attributeType(element), value, "", ""));
               }
            }
         }
         debugReferences(record, debugMessage);
         popDebugIndent();
         return debugMessage;
      }

      private StringBuilder debugAdditionalCondition(FndAbstractRecord record) {
         if (record instanceof FndSimpleCondition) {
            String condName = "";
            List<String> condValue = new ArrayList<>();
            FndAttribute element;
            FndAlpha condOperator = null;
            FndAttribute operand = null;
            FndAttribute operand2 = null;
            int attrCount = record.getAttributeCount();
            for (int i = 0; i < attrCount; i++) {
               element = record.getAttribute(i);
               switch (element.getName()) {
                  case "NAME":
                     condName = element.toString();
                     break;
                  case "VALUE":
                     Object attrValue = FndAttributeInternals.internalGetValue(element);
                     if (attrValue == null) {
                         condValue.add(null);
                     } else {
                         condValue.add(attrValue.toString());
                     }
                     break;
                  case "OPERATOR":
                     condOperator = (FndAlpha) element;
                     break;
                  case "OPERAND":
                     operand = element;
                     break;
                  case "OPERAND2":
                     operand2 = element;
                     break;
               }
            }
            String condOperValue = condOperator==null ? null : condOperator.getValue();
            if (FndQueryOperator.isBetweenOperator(condOperator)) {
               if (operand != null) {
                  return formatMessage(new StringBuilder("(:&1 :&2 &3 :&4)"), condName, operand.toString(), condOperValue, String.valueOf(operand2), "");
               }
               else {
                  return formatMessage(new StringBuilder("(&1 &2 &3 &4)"), condName, (String) condValue.get(0), condOperValue, (String) condValue.get(1), "");
               }
            }
            else if (FndQueryOperator.isInOperator(condOperator)) {
               StringBuilder value = new StringBuilder(condValue.isEmpty() || condValue.get(0) == null ? "" : condValue.get(0));
               for (int i = 1; i < condValue.size(); i++) {
                  value.append(",").append(condValue.get(i));
               }
               return formatMessage(new StringBuilder("(&1 &2 [&3])"), condName, condOperValue, value.toString(), "", "");
            }
            else if (FndQueryOperator.isLikeOperator(condOperator) && condValue.size() == 2) {
               return formatMessage(new StringBuilder("(&1 &2 '&3' [&4])"), condName, condOperValue, (String) condValue.get(0), (String) condValue.get(1), "");
            }
            else if (operand != null) {
               return formatMessage(new StringBuilder("(:&1 &2 :&3)"), condName, condOperValue, operand.toString(), "", "");
            }
            else if (condValue.isEmpty()) {
               return formatMessage(new StringBuilder("(&1 &2 &3)"), condName, condOperValue, "Null", "", "");
            }
            else {
               String condValueStr;
               if (condValue.get(0) != null) {
                  condValueStr = condValue.get(0);
               }
               else {
                  condValueStr = "Null";
               }
               return formatMessage(new StringBuilder("(&1 &2 &3)"), condName, condOperValue, condValueStr, "", "");
            }
         }
         else if (record instanceof FndDetailCondition) {
            StringBuilder debugMessage = new StringBuilder();
            String category = record.getAttribute("CATEGORY").toString();
            FndAbstractRecord detail = (FndAbstractRecord) FndAttributeInternals.internalGetValue(record.getAttribute("DETAIL"));
            debugMessage.append(category).append(" ");
            pushDebugIndent();
            debugMessage.append(debugRecord(detail, -1, record.getName(), detail.getName()).toString());
            popDebugIndent();
            debugMessage.append(indent());
            return debugMessage;
         }
         else if (record instanceof FndCondition) {
            FndCondition cond = (FndCondition) record;
            FndAbstractRecord left = (FndAbstractRecord) FndAttributeInternals.internalGetValue(cond.left);
            FndAbstractRecord right = (FndAbstractRecord) FndAttributeInternals.internalGetValue(cond.right);
            return formatMessage(new StringBuilder("(&1 &2 &3)"), debugAdditionalCondition(left).toString(), cond.category.getValue(), debugAdditionalCondition(right).toString(), "", "");
         }
         else {
            return null;
         }
      }

      private StringBuilder parseContext(FndAbstractRecord record) {
         FndAttribute element;
         FndAbstractArray elementVector;
         FndAbstractRecord elementRecord;
         String value;
         int i = 0;
         StringBuilder debugMessage = new StringBuilder();
         pushDebugIndent();
         int attrCount = record.getAttributeCount();
         for(int a=0; a<attrCount; a++) {
            element = record.getAttribute(a);
            if (element.isVector()) {
               elementVector = (FndAbstractArray) element;
               debugMessage.append(formatLine("&1 (Array) (&2 elements):", element.getName(), String.valueOf(elementVector.size()), "", "", ""));
               for (int j = 0; j < elementVector.size(); j++) {
                  pushDebugIndent();
                  debugMessage.append(parseContext(FndAttributeInternals.internalGet(elementVector, j)).toString());
                  popDebugIndent();
               }
            }
            else {
               if (FndAttributeInternals.internalGetValue(element) == null)
                  value = "Null";
               else {
                  value = FndAttributeInternals.internalGetValue(element).toString();
                  if (element.getName().trim().equalsIgnoreCase("OUTPUT") && value.length() >= 20)
                     value = formatLine("&1 ......[Size=&2]", value.substring(0, 20), String.valueOf(value.length()), "", "", "");
                  else if (element.getName().equals("PASSWORD"))
                     value = "*********";
               }
               debugMessage.append(formatLine("&1:$&2&3&4 = &5", String.valueOf(i + 1), element.getName(), (element.getType() == FndAttributeType.UNKNOWN) ? "" : ":",
                     (element.getType() == FndAttributeType.UNKNOWN) ? "" : element.getType().toString(), value));
            }
            i++;
         }
         int k = 0;
         FndAbstractRecord.Iterator records = record.records();
         while (records.hasNext()) {
            elementRecord = records.next();
            debugMessage.append(formatLine("&1:&2&3=", String.valueOf(i + k + 1), "$", elementRecord.getName(), "", ""));
            debugMessage.append(parseContext(elementRecord).toString());
            k++;
         }
         popDebugIndent();
         return debugMessage;
      }

      private void debugReferences(FndAbstractRecord rec, StringBuilder buf) {
         int count = rec.getCompoundReferenceCount();
         if (count == 0)
            return;
         buf.append(formatLine("References (&1):", String.valueOf(count), "", "", "", ""));
         pushDebugIndent();
         boolean hasOwnParentKey = false;
         for (int i = 0; i <= count; i++) {
            //
            // the loop for i==count handles not-own parentKey reference in a
            // detail
            // record that iherits part of its primary key from parent
            //
            if (i == count && hasOwnParentKey)
               break;
            FndCompoundReference ref = i < count ? rec.getCompoundReference(i) : rec.getParentKey();
            if (ref == null)
               break;
            FndAttribute.Iterator refIterator = ref.iterator();
            StringBuilder attrList = new StringBuilder();
            while (refIterator.hasNext()) {
               FndAttribute att = refIterator.next();
               if (attrList.length() > 0)
                  attrList.append(", ");
               FndAbstractRecord parent = att.getParentRecord();
               if (parent != null && !parent.equals(rec))
                  attrList.append(parent.getType()).append(".");
               attrList.append(att.getName());
            }
            String isA = "";
            if (ref == rec.getPrimaryKey() && ref == rec.getParentKey()) {
               isA = " Primary_Key, Parent_Key";
               hasOwnParentKey = true;
            }
            else if (ref == rec.getPrimaryKey()) {
               isA = " Primary_Key";
            }
            else if (ref == rec.getParentKey()) {
               isA = " Parent_Key";
               hasOwnParentKey = true;
            }

            buf.append(formatLine("&1 (&2)&3", ref.getName(), attrList.toString(), isA, "", ""));
         }
         popDebugIndent();
      }

      private void debugReferences(FndAbstractRecord rec) {
         if (rec != null) {
            Logger logger = findDebugLogger();
            if (logger != null) {
               FndCompoundReference contRef = rec.getParentKey();
               StringBuilder buf = new StringBuilder(256);
               buf.append(formatLine("&1 references:", rec.getName(), "", "", "", ""));
               pushDebugIndent();
               for (int i = 0; i < rec.getCompoundReferenceCount(); i++) {
                  FndCompoundReference ref = rec.getCompoundReference(i);
                  buf.append(formatLine("&1&2", ref.getName(), ref == contRef ? " (parent key)" : "", "", "", ""));
                  pushDebugIndent();
                  FndAttribute.Iterator refIterator = ref.iterator();
                  while (refIterator.hasNext()) {
                     FndAttribute att = refIterator.next();
                     String value = !att.exist() ? "Non_Existent" : att.isNull() ? "Null" : "= " + att.toString();
                     buf.append(formatLine("&1 &2 &3", att.getType().getName(), att.getName(), value, "", ""));
                  }
                  popDebugIndent();
               }
               popDebugIndent();
               printDebug(logger, buf.toString());
            }
         }
      }

      private void debugSimpleArray(StringBuilder debugMessage, FndSimpleArray arr) {
         FndSimpleArray.ElementType elemType = arr.getElementType();
         String attrType = "SimpleArray of " + elemType.getJavaType();
         if (arr.isDirty())
            attrType += " (DIRTY)";

         String attrName = arr.getName();

         if (!arr.exist()) {
            debugMessage.append(formatLine("&1 &2 Non_Existent", attrType, attrName, "", "", ""));
         }
         else if (arr.isNull()) {
            debugMessage.append(formatLine("&1 &2 Null", attrType, attrName, "", "", ""));
         }
         else {
            String value;
            try {
               if (elemType == FndSimpleArray.STRING)
                  value = formatStringArray(arr.getStringArray());
               else if (elemType == FndSimpleArray.DATE)
                  value = formatDateArray(arr.getDateArray());
               else if (elemType == FndSimpleArray.DOUBLE)
                  value = formatDoubleArray(arr.getDoubleArray());
               else if (elemType == FndSimpleArray.BOOLEAN)
                  value = formatBooleanArray(arr.getBooleanArray());
               else if (elemType == FndSimpleArray.LONG)
                  value = formatLongArray(arr.getLongArray());
               else if (elemType == FndSimpleArray.BINARY)
                  value = formatBinaryArray(arr.getBinaryArray());
               else
                  value = "Invalid FndSimpleArray.ElementType: " + elemType;
            }
            catch (ParseException e) {
               throw new IfsRuntimeException(e, Texts.SIMPLEARRAYCONVERSION, e.toString());
            }

            debugMessage.append(formatLine("&1 &2 = &3", attrType, attrName, value, "", ""));
         }
      }
   }
}