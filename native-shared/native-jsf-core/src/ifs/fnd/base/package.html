<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
 <HEAD>
  <META http-equiv=Content-Type content="text/html; charset=windows-1252">
 </HEAD>
 <BODY>Provides basic classes and exceptions.
      The most important class in this package is
      <A HREF="FndContext.html">FndContext</A>
      describing the context that is sent with every request to the server.
      The package contains utilities for debugging, translation and encryption,
      as well as definition of framework exceptions: the base exception
      <A HREF="IfsException.html">IfsException</A> and all of its subclasses.
 </BODY>
</HTML>

