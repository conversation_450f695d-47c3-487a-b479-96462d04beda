package ifs.fnd.base.capability;

import java.util.HashMap;
import java.util.Map;
import ifs.fnd.capability.Capability;

/**
 * Capability Negotiation Protocol
 * ===============================
 *
 * The capabilities that we are concerned here are supposed to be 
 * transport-level only. If we are to support application-level capabilities, an
 * application level protocol may be required.
 *
 * Capability representation
 * -------------------------
 * Capabilities are transferred between the server and the client using an
 * x-ifs-capabilities header. Structure of this header is as follows:
 *
 * x-ifs-capabilities : <capability-string>
 *
 * A capability-string always has multiple of 2 hex digits ( 0-9, A-F). Thus, it
 * always represents a bit-field that fills a multiple of bytes.
 * eg:
 * x-ifs-capabilities : 03
 * x-ifs-capabilities : A82F
 * x-ifs-capabilities : A1B2C3D4E5FF
 *
 * A capability-string represents a bit-field indexed by capabilities. In the
 * general case the bit-field indexes capabilities as described in Fig-1. If a
 * particular bit is set in the bit field, it implies that the capability
 * represented by that index is supported. Index 0 is a special case, which
 * represents the 'capability negotiation bit'. It's usage will be explain in
 * detail under section 'Capability Negotiation'.
 *
 * 7 6 5 4  3 2 1 0    14  12   10 9 8    22  20   18  15
 * x x x x  x x x x | x x x x  x x x x | x x x x  x x x x | ...
 *                   15  13   11        23  21   19  16
 * Fig-1: indices of capabilities in <capability-string>
 *
 * Following are some examples of capability strings and their capability
 * representations:
 *
 * 1. capability-string = 03
 *    bit-field representation = 0000 0011
 *    capabilities represented = {1} + capability negotiation bit
 *
 * 2. capability-string = A83F
 *    bit-field representation = 1010 1000 | 0011 1111
 *    capabilities represented = {3, 5, 7, 8, 9, 10, 11, 12, 13}
 *
 * Capability Negotiation
 * ----------------------
 * Before a client sends data to the server it has to negotiate the capabilities
 * between them. For that the client sends a request with the x-ifs-capabilities
 * header with the capability-negotiation-bit (bit index 0 in Fig-1). set in
 * capability-string. Server will respond to any such negotiation request, with
 * a zero length reply with a x-ifs-capabilities header indicating the common
 * capabilities supported by both the server and client in it's
 * capability-string. This capability-string will always have the
 * negotiation-bit set, indicating that it is the response to a negotiation
 * request.
 *
 * After the negotiation request, the client knows what capabilities are common
 * to both server and client, and can send requests that uses those
 * capabilities. They have to have the x-ifs-capabilities header with the common
 * set of capabilities supported, but should not set the negotiate bit.
 *
 * CLIENT                                                     SERVER
 *   |                                                           |
 *   |        1. Client sends a negotiation request with         | N
 *   |           the negotiation bit set in                      | E
 *   |           x-ifs-capabilities header                       | G
 *   |       -----------------------------------------}          | O
 *   |                                                           | T
 *   |        2. Server responds with the common                 | I
 *   |           capabilities. negotiation bit is set            | A
 *   |           in x-ifs-capabilities.                          | T
 *   |       {-----------------------------------------          | E
 *   |                                                           |
 *   |                                                           |
 *   |                                                           |
 *   |        3. Client sends application requests with          |
 *   |           the common capabilities in                      |
 *   |           x-ifs-capabilities header. This shouldn't       |
 *   |           include the negotiate bit.                      |
 *   |       -----------------------------------------}          |
 *   .                                                           .
 *   .                                                           .
 *   .                                                           .
 *
 */
public enum ApplicationCapability implements Capability<ApplicationCapability> {

    NEGOTIATE(7), // Negotiate Capabilities
    LPTEXT(6); // Length Prefixed Text

    /*
     * NOTE: Due to the way in which the client sends capability bits,
     * we have to use the bit indexes for each capability as given below:
     * CAPABILITY3(4),
     * CAPABILITY4(3),
     * CAPABILITY5(2),
     * CAPABILITY6(1),
     * CAPABILITY7(0),
     * CAPABILITY8(15),
     * CAPABILITY9(14),
     * CAPABILITY10(13),
     * CAPABILITY11(12),
     * CAPABILITY12(11),
     * CAPABILITY13(10),
     * CAPABILITY14(9),
     * CAPABILITY15(8),
     * CAPABILITY16(23),
     * CAPABILITY17(22),
     * CAPABILITY18(21),
     * CAPABILITY19(20),
     * CAPABILITY20(19),
     * CAPABILITY21(18),
     * CAPABILITY22(17),
     * CAPABILITY23(16);
     */
    private int bit;
    private static final Map<Integer, ApplicationCapability> lookup = new HashMap<>();

    static {
        for (ApplicationCapability tc : ApplicationCapability.values()) {
            lookup.put(tc.getBitIndex(), tc);
        }
    }

    private ApplicationCapability(int bit) {
        this.bit = bit;
    }

   @Override
    public int getBitIndex() {
        return bit;
    }

   @Override
    public ApplicationCapability lookup(int bitIndex) {
        return lookup.get(bitIndex);
    }

   @Override
    public Class<ApplicationCapability> getType() {
        return ApplicationCapability.class;
    }

   @Override
    public ApplicationCapability getEnum() {
        return this;
    }
}
