/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */

package ifs.fnd.base;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Basic exception type for all exceptions from SF.
 * Application code typically uses {@link ifs.fnd.base.ApplicationException ApplicationException}.
 * Application code must never catch IfsException, only ApplicationException
 * (and it's subclasses).
 */
public abstract class IfsException extends Exception {

   //Message is defined in throwable.

   //Error type for this excpetion.
   protected String error;

   /**
    * Application exception
    */
   public static final String APPLICATION_ERROR = "APP_ERROR";
   /**
    * System exception
    */
   public static final String SYSTEM_ERROR      = "SYS_ERROR";
   /**
    * Parse exception
    */
   public static final String PARSE_ERROR       = "PARSE_ERROR";
   /**
    * Security exception
    */
   public static final String SECURITY_ERROR    = "SECURITY";
   /**
    * Manual decision exception
    */
   public static final String MANUAL_DECISION   = "DECISION";
   /**
    * Unknown exception
    */
   public static final String UNKNOWN           = "UNKNOWN";
   /**
    * Validation exception
    */
   public static final String VALIDATION_ERROR  = "VALIDATION_ERROR";
   /**
    * Database exception
    */
   public static final String DATABASE_ERROR    = "DATABASE_ERROR";
   /**
    * User abort exception
    */
   public static final String USER_ABORT        = "USER_ABORT";
   /**
    * Encrypting/Decrypting exception
    */
   public static final String ENCRYPTION_ERROR  = "ENCRYPTION_ERROR";
   /**
    * LicenseViolation exception
    */
   public static final String LICENSE_VIOLATION_ERROR = "LICENSE_VIOLATION_ERROR";
   /**
    * TransactionIdMismatch exception
    */
   public static final String TRANSACTION_ID_MISMATCH_ERROR = "TRANSACTION_ID_MISMATCH_ERROR";
   /**
    * ConnectionDestroyed exception
    */
   public static final String CONNECTION_DESTROYED_ERROR = "CONNECTION_DESTROYED_ERROR";

   protected IfsException(String error, FndTranslatableText msg, String... p1) {
      super(msg!=null ? msg.translate(p1) : null);
      this.error  = error;
   }

   protected IfsException(Throwable cause, String error, FndTranslatableText msg, String... p1) {

      this(error, msg, p1);
      initCause(cause);
   }

   //
   // To be obsolete
   //

   private String[] params;

   protected IfsException(String error, String msg, String... p1) {
      super(msg);
      this.error  = error;
      this.params = p1;
   }

   protected IfsException(Throwable cause, String error, String msg, String... p1) {
      this(error, msg, p1);
      //if(cause instanceof RuntimeException)
      //   throw new IfsRuntimeException(cause, msg, p1, p2, p3, p4, p5);
      initCause(cause);
   }

   /**
    * Returns the translated error message.
    * @return translated error message
    */
   @Override
   public String getMessage() {
      return FndTranslation.translate(super.getMessage(), params);
   }

   /**
    * Returns the error type.
    * @return error type
    */
   public String getType() {
      return error;
   }

   /**
    * Override in subclass if necessary.
    * @return null
    */
   public String getExtraInfo() {
      return null;
   }

   /**
    * This method returns the Translation Identifier Constant of the message of the exception.<br /><br />
    * Examples:
    * <ul>
    *    <li>FNDSESSIONBEANABORT of message 'FNDSESSIONBEANABORT:Request has been aborted'</li>
    *    <li>MobmgrSeparateWo.REMOVED2 of message 'ORA-20115: MobmgrSeparateWo.REMOVED2: The Mobmgr Separate Wo object has been removed by another user.'</li>
    * </ul>
    *
    * @return Translation Identifier Constant of the message. An empty string will be returned if an identifier is not found.
    */
   protected final String getTranslationID() {
       String extraInfo = getExtraInfo();
       String message = super.getMessage();
       String translationId = "";
       if (extraInfo != null)
           translationId = extractId(extraInfo);
       if (translationId.length() == 0 && message != null)
           translationId = extractId(message);
       return translationId;
   }

   private String extractId(String message) {
       Pattern p;
       Matcher m;
       if (message.startsWith("ORA-")) {
           p = Pattern.compile("^ORA-\\d*: ?(\\w*\\.\\w*): ?.*");
       } else {
           p = Pattern.compile("^([A-Z0-9_]*):");
       }
       m = p.matcher(message);
       return m.find()? m.group(1): "";
   }
}
