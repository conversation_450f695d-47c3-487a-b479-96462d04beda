/*
 *                 IFS Research & Development
 *
 *  This program is protected by copyright law and by international
 *  conventions. All licensing, renting, lending or copying (including
 *  for private use), and all other use of the program, which is not
 *  expressively permitted by IFS Research & Development (IFS), is a
 *  violation of the rights of IFS. Such violations will be reported to the
 *  appropriate authorities.
 *
 *  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
 *  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */


package ifs.fnd.webservices;


import javax.naming.InitialContext;
import javax.naming.NamingException;

import ifs.fnd.log.*;
import ifs.fnd.sf.j2ee.FndActivityLocal;
import ifs.fnd.sf.j2ee.meta.FndJ2eeMetaCache;
import ifs.fnd.util.Str;


/**
 *
 */
public class EjbUtils
{
   private EjbUtils()
   {
   }

   public static FndActivityLocal lookupAnnotatedBean( String beanName, Logger log ) throws NamingException
   {
      InitialContext ctx = new InitialContext();

      //String jndiName = "ifsapp/" + beanName + "/local";

      // TODO: Temporary solution only!!!
      // If EJB injection doesn't work, we need to do it only once, in the static code.

      String appName = FndJ2eeMetaCache.getApplication().getName();
      if( Str.isEmpty(appName) )
      {
         if(log.warning) log.warning("Application name not defined in Meta Cache; setting 'ifsapp'");
         appName = "ifsapp";
      }
      else if(log.debug)
         log.debug("EAR application name set to '&1'", appName);

      String jndiName = appName + "/" + beanName + "/local";

      if(log.debug) log.debug("Looking up local EJB \""+jndiName+"\"");

      return (FndActivityLocal)ctx.lookup(jndiName);
   }
}

