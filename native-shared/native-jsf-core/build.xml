<?xml version="1.0" encoding="UTF-8"?>
<project name="Java Server Framework 11" default="build" basedir=".">

  <!--
  ***************************************************************************
  *                              Properties                                 *
  ***************************************************************************
  -->

  <property name="basedir"          value="."/>
  <property name="src"              value="${basedir}/src"/>
  <property name="build"            value="${basedir}/build"/>
  <property name="dist"             value="${basedir}/../../lib"/>
  <property name="doc"              value="${basedir}/doc"/>

  <property name="lib.common"       value="${basedir}/../../lib/ifs-fnd-common.jar"/>
  <property name="lib.jakarta"      value="${basedir}/../../lib/jakarta.jakartaee-api-10.0.0.jar"/>
  <property name="lib.oracle"       value="${basedir}/../../lib/ojdbc10-19.3.0.0.jar"/>
  <property name="lib.wildfly"      value="${basedir}/../../lib/ironjacamar-jdbc-1.4.12.Final.jar"/>

  <property name="j2ee.classpath"   value="${lib.jakarta}:${lib.oracle}:${lib.common}:${lib.wildfly}"/>

  <!--
  ***************************************************************************
  *                              Targets                                    *
  ***************************************************************************
  -->

  <target name="clean-build" depends="clean, build" description="Clean build"/>

  <target name="build" depends="build-sta, build-j2ee, build-j2ee-ejb"/>


  <target name="clean" description="Clean everything">
    <delete dir="${build}"/>
    <delete dir="${doc}"/>
  </target>


  <target name="-init">
    <tstamp/>
    <mkdir dir="${build}"/>
    <subant target="clean-build">
      <fileset dir="${basedir}/../native-ifs-common" includes="build.xml"/>
    </subant>
  </target>


  <target name="compile" depends="-init" description="Compile the source code">
    <echo message="Compiling JSF framework with debug"/>
    <javac srcdir="${src}"
           destdir="${build}"
           source="17"
           target="17"
           debug="on"
           debuglevel="lines,vars,source"
           optimize="yes"
           deprecation="on"
           classpath="${j2ee.classpath}"
           fork="true" 
           includeantruntime="false">
        <compilerarg value="-Xlint:unchecked"/>
    </javac>
  </target>


  <target name="build-sta" depends="compile" description="Build ifs-fnd-sta.jar">
    <echo message="Building ifs-fnd-sta.jar ..."/>
    <copy file="FndStaFramework.properties" tofile="${build}/ifs/fnd/base/FndFramework.properties" preservelastmodified="true" overwrite="true"/>
    <jar destfile="${dist}/ifs-fnd-sta.jar" basedir="${build}">
       <manifest>
          <attribute name="Class-Path" value="ifs-fnd-common.jar"/>
       </manifest>
       <exclude name="ifs/fnd/sf/j2ee/**"/>
       <exclude name="ifs/fnd/sf/ejbs/**"/>
       <exclude name="ifs/fnd/remote/j2ee/**"/>
    </jar>
  </target>


  <target name="build-j2ee" depends="compile" description="Build ifs-fnd-j2ee.jar">
    <echo message="Building ifs-fnd-j2ee.jar ..."/>
    <copy file="FndJ2eeFramework.properties" tofile="${build}/ifs/fnd/base/FndFramework.properties" preservelastmodified="true" overwrite="true"/>
    <jar destfile="${dist}/ifs-fnd-j2ee.jar" basedir="${build}">
       <manifest>
          <attribute name="Class-Path" value="ifs-fnd-common.jar"/>
       </manifest>
       <exclude name="ifs/fnd/sf/sta/**"/>
       <exclude name="ifs/fnd/sf/ejbs/**"/>
    </jar>
  </target>


  <target name="build-j2ee-ejb" depends="compile" description="Build ifs-fnd-j2ee-ejb.jar">
    <echo message="Building ifs-fnd-j2ee-ejb.jar ..."/>
    <jar destfile="${dist}/ifs-fnd-j2ee-ejb.jar" basedir="${build}">
       <include name="ifs/fnd/sf/ejbs/**"/>
    </jar>
  </target>


  <target name="doc" description="Generate API documentation">
    <delete dir="${doc}"/>
    <mkdir dir="${doc}"/>
    <javadoc
       packagenames="ifs.*"
       classpath="${j2ee.classpath}"
       sourcepath="${src}"
       destdir="${doc}"
       version="true"
       use="true"
       source="17"
       author="false"
       windowtitle="IFS Server Framework (for internal use only)"
       access="package"/>
  </target>


</project>
