/*
//                 IFS Research & Development
//
//  This program is protected by copyright law and by international
//  conventions. All licensing, renting, lending or copying (including
//  for private use), and all other use of the program, which is not
//  explicitly permitted by IFS, is a violation of the rights
//  of IFS. Such violations will be reported to the
//  appropriate authorities.
//
//  VIOLATIONS OF ANY COPYRIGHT IS PUNISHABLE BY LAW AND CAN LEAD
//  TO UP TO TWO YEARS OF IMPRISONMENT AND LIABILITY TO PAY DAMAGES.
 */
package com.ifsworld.fnd.ntfy;

import com.ifsworld.fnd.mobile.NativeContextException;
import com.ifsworld.fnd.mobile.NativeStorageException;
import com.ifsworld.fnd.mobile.PushContent;
import com.ifsworld.fnd.mobile.PushContextBase;
import com.ifsworld.fnd.mobile.PushRegContextBase;
import com.ifsworld.fnd.mobile.common.ErrorResponseBuilder;
import com.ifsworld.fnd.mobile.model.PushHubSettings;
import com.ifsworld.fnd.ntfy.push.PushDriver;
import com.ifsworld.fnd.ntfy.push.PushRegistration;
import com.windowsazure.messaging.NotificationHubClient;
import com.windowsazure.messaging.NotificationHubsException;
import io.agroal.api.AgroalDataSource;
import org.apache.logging.log4j.LogManager;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.json.Json;
import jakarta.json.JsonObject;
import jakarta.json.JsonReader;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import jakarta.ws.rs.core.SecurityContext;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import java.io.StringReader;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * <AUTHOR>
 */

@ApplicationScoped
@Path("/v1")
public class NotificationServices {

    private static final org.apache.logging.log4j.Logger LOG = LogManager.getLogger(NotificationServices.class);        

    @Inject
    @Named("fndbas")
    AgroalDataSource dataSource;

    @Context
    HttpServletRequest request;

    @Context
    SecurityContext securityContext;

    @ConfigProperty(name = "notification.version")
    String notificationVersion; // Inject the parent version from application.properties

    /*
     * Init Mobile Context
     */
    public MobileContext init(final Connection c, final JsonObject contextJson) throws PushException {
        try {
            LOG.info("Call to Mobile Notification Services");
            if (contextJson != null) {
                //Initialize the JDBC connection.
                String fndUser = DatabaseConnectionUtility.initConnection(c, contextJson, request);
                MobileContext ctx = MobileContext.with(fndUser, contextJson).build();
                return ctx;
            } else {
                throw new PushException("Invalid request with no mobile context element.");
            }
        } catch (SQLException e) {
            LOG.error("Init Notification Services", e);
            throw new PushException("Failed to init Notification Services", e);
        }
    }

    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/register")
    @POST
    public Response register(String jsonStr) {
        try (JsonReader jsonReader = Json.createReader(new StringReader(jsonStr))) {
            JsonObject requestJson = jsonReader.readObject();

            //Init Push Register Context
            PushRegContextBase ctx = PushRegContext.with(requestJson).buildForRegister();

            //Push Hub Settings by validate
            PushHubSettings hubSettings = new PushHubSettings(ctx.getHubUrl(), ctx.getHubPath()).validate();

            //Getting Push Driver for relevant Device OS
            PushDriver driver = PushDriver.createPushDriver(ctx.getDeviceOs(), hubSettings);

            //Generate a new push tag, register pns handler and return push registration
            PushRegistration result = driver.registerPnsHandle(ctx.getRegistrationId(), ctx.getPnsHandle(), 0);

            //Registration result
            return Response.status(Status.OK).entity(JsonUtilities.getPushRegistryResult(result)).build();
        } catch (PushException ex) {
            return ErrorResponseBuilder.buildErrorResponse(ex.getType(), ex);
        } catch (NativeStorageException ex) {
            return ErrorResponseBuilder.buildErrorResponse(ex.getType(), ex);
        } catch (NativeContextException ex) {
            return ErrorResponseBuilder.buildErrorResponse("PUSH_CONTENT_ERROR", ex);
        } catch (RuntimeException ex) {
            return ErrorResponseBuilder.buildErrorResponse(ex);
        }
    }

    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/unregister")
    @POST
    public Response unRegister(String jsonStr) {
        try (JsonReader jsonReader = Json.createReader(new StringReader(jsonStr))) {
            JsonObject requestJson = jsonReader.readObject();

            //Init Push Register Context
            PushRegContextBase ctx = PushRegContext.with(requestJson).buildForUnRegister();

            //Push Hub Settings by validate
            PushHubSettings hubSettings = new PushHubSettings(ctx.getHubUrl(), ctx.getHubPath()).validate();

            //Create Notification Hub Client and Delete push registration
            NotificationHubClient hubClient = PushDriver.createNotificationHubClient(hubSettings);
            hubClient.deleteRegistration(ctx.getRegistrationId());

            return Response.status(Status.OK).build();
        } catch (PushException ex) {
            return ErrorResponseBuilder.buildErrorResponse(ex.getType(), ex);
        } catch (NotificationHubsException ex) {
            return ErrorResponseBuilder.buildErrorResponse("PUSH_HUB_ERROR", ex);
        } catch (NativeStorageException ex) {
            return ErrorResponseBuilder.buildErrorResponse(ex.getType(), ex);
        } catch (NativeContextException ex) {
            return ErrorResponseBuilder.buildErrorResponse("PUSH_CONTENT_ERROR", ex);
        } catch (RuntimeException ex) {
            return ErrorResponseBuilder.buildErrorResponse("RUNTIME_ERROR", ex);
        }
    }

    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/sendApp")
    @POST
    public Response sendAppNotification(String jsonStr) {
        MobileContext ctx = null;
        Connection c = null;
        try (JsonReader jsonReader = Json.createReader(new StringReader(jsonStr))) {
            JsonObject requestJson = jsonReader.readObject();

            //Init Mobile Context
            c = dataSource.getConnection();
            ctx = init(c, requestJson.getJsonObject("Context"));

            //Send the notification
            JsonObject contentJson = requestJson.containsKey("Content") ? requestJson.getJsonObject("Content") : null;  
            PushMessageUtil.sendNotificationApp(ctx, PushContent.with(contentJson).build(), c);
            commit(c);

            return Response.status(Status.OK).build();
        } catch (PushException ex) {
            rollback(c);
            return ErrorResponseBuilder.buildErrorResponse(ex.getType(), ex);
        } catch (RuntimeException ex) {
            rollback(c);
            return ErrorResponseBuilder.buildErrorResponse("RUNTIME_ERROR", ex);
        } catch (SQLException ex) {
            rollback(c);
            return ErrorResponseBuilder.buildErrorResponse("RUNTIME_ERROR", ex);
        } finally {
            afterCall(c);
        }
    }

    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/sendHub")
    @POST
    @Transactional(Transactional.TxType.REQUIRES_NEW)
    public Response sendHubNotification(String jsonStr) {
        try (JsonReader jsonReader = Json.createReader(new StringReader(jsonStr))) {
            JsonObject requestJson = jsonReader.readObject();

            //Init Push Context
            PushContextBase pushCtx = PushContext.with(requestJson.getJsonObject("PushContext")).build();

            //Send the notification
            JsonObject contentJson = requestJson.containsKey("Content") ? requestJson.getJsonObject("Content") : null;  
            PushMessageUtil.sendNotificationHub(pushCtx, PushContent.with(contentJson).build());

            return Response.status(Status.OK).build();
        } catch (PushException ex) {
            return ErrorResponseBuilder.buildErrorResponse(ex.getType(), ex);
        } catch (RuntimeException ex) {
            return ErrorResponseBuilder.buildErrorResponse("RUNTIME_ERROR", ex);
        }
    }

    @Path("/service")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public Response getService() {
        return Response.ok("Mobile Native Notification Service is available").build();
    }

    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/version")
    @GET
    public Response getNotificationVersion() {
        JsonObject jsonResponse = Json.createObjectBuilder()
                .add("version", notificationVersion)
                .build();
        return Response.ok(jsonResponse).build();
    }

    private void rollback(Connection c) {
        try {
            if (c != null && !c.isClosed()) {
                c.rollback();
            }
        } catch (final SQLException e) {
            LOG.error("An unexpected error occurred while closing the server connection", e);
        }
    }

    private void commit(Connection c) {
        try {
            if (c != null && !c.isClosed()) {
                c.commit();
            }
        } catch (final SQLException e) {
            LOG.error("An unexpected error occurred while closing the server connection", e);
        }
    }

    private void afterCall(Connection c) {
        try {
            if (c != null && !c.isClosed()) {
                // Automatic commit will incur
                c.close();
            }
        } catch (SQLException e) {
            LOG.error("afterCall() Notification Services", e);
        }
    }

}
