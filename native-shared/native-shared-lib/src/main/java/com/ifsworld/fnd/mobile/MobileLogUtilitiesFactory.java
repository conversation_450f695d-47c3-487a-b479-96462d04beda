/*
 * ==================================================================================
 * File:              MobileLogUtilitiesFactory.java
 * Software Package:  MobileClientRuntime
 * Template Version:  8.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile;

import java.sql.Connection;

public class MobileLogUtilitiesFactory {
   /**
    * Get an instance of a <code>[MobileLogUtilities]</code>.
    */
   private MobileLogUtilitiesFactory() {
   }

   public static MobileLogUtilities getHandler(Connection connection) {
      return new MobileLogUtilitiesImpl(connection);
   }
}