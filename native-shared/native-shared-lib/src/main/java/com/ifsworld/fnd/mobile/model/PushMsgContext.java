package com.ifsworld.fnd.mobile.model;

import java.util.Map;

public class PushMsgContext {
    private static final String APP_NAME         = "APP_NAME";
    private static final String DIRECTORY_ID     = "DIRECTORY_ID";
    private static final String USER_ID          = "USER_ID";
    private static final String DEVICE_ID        = "DEVICE_ID";
    private static final String ENTITY           = "ENTITY";
    private static final String OBJKEY           = "OBJKEY";
    private static final String COMMAND          = "COMMAND";
    private static final String MESSAGE          = "MESSAGE";
    private static final String REM_ASSIST_REQ   = "REM_ASSIST_REQ";

    public String appName;
    public String userId;
    public String directoryId;
    public String deviceId;
    public String entity;
    public String objKey;
    public String command;
    public String message;
    public String remAssistReq;

    public PushMsgContext(Map<String, String> methodRef) {
        this.appName = methodRef.get(PushMsgContext.APP_NAME);
        this.directoryId = methodRef.get(PushMsgContext.DIRECTORY_ID);
        this.userId = methodRef.get(PushMsgContext.USER_ID);
        this.deviceId = methodRef.get(PushMsgContext.DEVICE_ID);
        this.entity = methodRef.get(PushMsgContext.ENTITY);
        this.objKey = methodRef.get(PushMsgContext.OBJKEY);
        this.command = methodRef.get(PushMsgContext.COMMAND);
        this.message = methodRef.get(PushMsgContext.MESSAGE);
        this.remAssistReq = methodRef.get(PushMsgContext.REM_ASSIST_REQ);
    }
}
