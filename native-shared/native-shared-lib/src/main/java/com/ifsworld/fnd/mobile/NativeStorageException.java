/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.ifsworld.fnd.mobile;
/**
 *
 * <AUTHOR>
 */
public class NativeStorageException extends Exception {
   public NativeStorageException(final String message) { super(message); }
   public NativeStorageException(final String message, final Throwable cause) {
      super(message, cause);
   }

   public String getType(){
      return "STORAGE_ERROR";
   }
}
