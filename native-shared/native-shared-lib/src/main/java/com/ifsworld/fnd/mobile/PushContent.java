/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.ifsworld.fnd.mobile;

import jakarta.json.JsonObject;

/**
 *
 * <AUTHOR>
 */
public class PushContent {
   private final int badge;
   private final String alert;
   private final String command;
   private final String remAssistReq;
   
   private PushContent(int badge, String alert, String command) {
      this(badge, alert, command, null);
   }

   private PushContent(int badge, String alert, String command, String remAssistReq) {
      this.badge = badge;
      this.alert = alert;
      this.command = command;
      this.remAssistReq = remAssistReq;
   }

   public int getBadge() {
      return badge;
   }
   
   public String getAlert() {
      return alert;
   }
   
   public String getCommand() {
      return command;
   }

   public String getRemAssistReq() {
      return remAssistReq;
   }


   public static Builder with(int badge, final String message, final String command, final String remAssistReq) {
      return new Builder(badge, message, command, remAssistReq);
   }
   public static Builder with(final String message, final String command, final String remAssistReq) {
      return new Builder(1, message, command, remAssistReq);
   }

   public static Builder with(final JsonObject contentJson) {
      if(contentJson != null) {
         int badge = contentJson.containsKey("Badge") ? contentJson.getInt("Badge") : 1;
         String message = contentJson.containsKey("Message") ? contentJson.getString("Message") : null;
         String command = contentJson.containsKey("Command") ? contentJson.getString("Command") : null;
         String remAssistReq = contentJson.containsKey("RemAssistReq") ? contentJson.getString("RemAssistReq") : null;
         return new Builder(badge, message, command, remAssistReq);
      }
      else{
         return new Builder(1, null, null, null);
      }
   }

   public static class Builder {
      PushContent pushContext = null;

      public Builder(final int badge, final String message, final String command, final String remAssistReq) {
         pushContext = new PushContent(badge, message, command, remAssistReq);
      }

      public PushContent build(){
         return pushContext;
      }
   }
}
