/*
 * ==================================================================================
 * File:              MobileEntityDependencyArray.java
 * Software Package:  MobileClientRuntime
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents;

import ifs.fnd.base.ParseException;
import ifs.fnd.base.SystemException;
import ifs.fnd.record.*;
import ifs.fnd.record.serialization.FndTokenReader;

/**
 * Array of <code>MobileEntityDependency</code>.
 */
public class MobileEntityDependencyArray extends com.ifsworld.fnd.mobile.documents.entity.mobileentitydependency.MobileEntityDependencyEntityArray {

   public MobileEntityDependencyArray() {
      super();
   }

   public MobileEntityDependencyArray(FndAttributeMeta meta) {
      super(meta);
   }

   public MobileEntityDependencyArray(FndAttributeMeta meta, FndCompoundReference parentKeyInParent) {
      super(meta, parentKeyInParent);
   }

   public boolean add(com.ifsworld.fnd.mobile.documents.MobileEntityDependency record) {
      return internalAdd(record);
   }

   public void add(int index, com.ifsworld.fnd.mobile.documents.MobileEntityDependency record) {
      internalAdd(index, record);
   }

   public void add(com.ifsworld.fnd.mobile.documents.MobileEntityDependencyArray array) {
      internalAdd(array);
   }

   public void assign(com.ifsworld.fnd.mobile.documents.MobileEntityDependencyArray from) throws SystemException {
      assign(from, true);
   }

   public boolean contains(com.ifsworld.fnd.mobile.documents.MobileEntityDependency record) {
      return internalContains(record);
   }

   public com.ifsworld.fnd.mobile.documents.MobileEntityDependency firstElement() {
      return (com.ifsworld.fnd.mobile.documents.MobileEntityDependency)internalFirstElement();
   }

   public com.ifsworld.fnd.mobile.documents.MobileEntityDependency get(int index) {
      return (com.ifsworld.fnd.mobile.documents.MobileEntityDependency)internalGet(index);
   }

   public int indexOf(com.ifsworld.fnd.mobile.documents.MobileEntityDependency record) {
      return internalIndexOf(record);
   }

   public com.ifsworld.fnd.mobile.documents.MobileEntityDependency lastElement() {
      return (com.ifsworld.fnd.mobile.documents.MobileEntityDependency)internalLastElement();
   }

   public int lastIndexOf(com.ifsworld.fnd.mobile.documents.MobileEntityDependency record) {
      return internalLastIndexOf(record);
   }

   public com.ifsworld.fnd.mobile.documents.MobileEntityDependency remove(int index) {
      return (com.ifsworld.fnd.mobile.documents.MobileEntityDependency)internalRemove(index);
   }

   public com.ifsworld.fnd.mobile.documents.MobileEntityDependency set(int index, com.ifsworld.fnd.mobile.documents.MobileEntityDependency record) {
      return (com.ifsworld.fnd.mobile.documents.MobileEntityDependency)internalSet(index, record);
   }

   public FndDetailCondition createDetailCondition(com.ifsworld.fnd.mobile.documents.MobileEntityDependency record, FndQueryReferenceCategory category) {
      return createCondition(record, category);
   }

   @Override
   public FndAbstractRecord newRecord() {
      return new com.ifsworld.fnd.mobile.documents.MobileEntityDependency();
   }

   @Override
   protected FndAbstractRecord newRecord(FndTokenReader stream) throws ParseException {
      com.ifsworld.fnd.mobile.documents.MobileEntityDependency record = new MobileEntityDependency();
      record.parse(stream);
      return record;
   }

   protected FndAttribute newAttribute(FndAttributeMeta meta) {
       return new com.ifsworld.fnd.mobile.documents.MobileEntityDependencyArray(meta);
   }
}
