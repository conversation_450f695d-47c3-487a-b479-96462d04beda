/*
 * ==================================================================================
 * File:              MobileGroupedPushStateEnumeration.java
 * Enumeration:       MobileGroupedPushState
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.enumeration;

import ifs.fnd.base.IfsException;
import ifs.fnd.base.ParseException;
import ifs.fnd.base.SystemException;
import ifs.fnd.record.*;

public final class MobileGroupedPushStateEnumeration extends FndEnumeration {

   //Enumeration entries
   public static final Enum GROUPED_PUSH_PENDING = Enum.GROUPED_PUSH_PENDING;
   public static final Enum GROUPED_PUSH_PROCESSING = Enum.GROUPED_PUSH_PROCESSING;

   public MobileGroupedPushStateEnumeration() {
      super();
   }

   public MobileGroupedPushStateEnumeration(String name) {
      super(name);
   }

   public MobileGroupedPushStateEnumeration(String name, Enum value) {
      this(name);
      setValue(value);
   }

   public MobileGroupedPushStateEnumeration(FndAttributeMeta meta) {
      super(meta);
   }

   @Override
   protected FndAttribute newAttribute(FndAttributeMeta meta) {
      return new MobileGroupedPushStateEnumeration(meta);
   }

   public void setValue(Enum value) {
      internalSetValue(value);
   }

   public Enum getValue() {
      return (Enum) internalGetValue();
   }

   @Override
   public void parseString(String value) throws ParseException {
      try {
         this.value = Enum.parseString(value);
         this.set();
         this.setExistent();
      } catch (ParseException e) {
         throwIllegalValueException(value);
      }
   }

   @Override
   protected void parseDatabaseString(String dbValue) throws ParseException {
      try {
         this.value = Enum.parseDatabaseString(dbValue);
         this.set();
         this.setExistent();
      } catch (ParseException e) {
         throwIllegalDatabaseValueException(dbValue);
      }
   }

   @Override
   public String toString() {
      Enum val = getValue();
      if (val == null) {
         return null;
      }
      return val.getValue();
   }

   @Override
   protected String toDatabaseString() {
      Enum val = getValue();
      if (val == null) {
         return null;
      }
      return val.getDatabaseValue();
   }

   @Override
   public int compareTo(FndAttribute attr) {
      if (this == attr) {
         return 0;
      }
      if (attr instanceof MobileGroupedPushStateEnumeration) {
         if (this.isNull() && attr.isNull()) {
            return 0;
         } else if (this.isNull()) {
            return 1;
         } else if (attr.isNull()) {
            return -1;
         } else {
            return getValue().compareTo(((MobileGroupedPushStateEnumeration) attr).getValue());
         }
      }
      return 1;
   }

   public Condition createEqualCondition(Enum value) {
      return new Condition(this, getEqualQueryOperator(), value);
   }

   public Condition createNotEqualCondition(Enum value) {
      return new Condition(this, getNotEqualQueryOperator(), value);
   }

   public Condition createEqualCondition(MobileGroupedPushStateEnumeration attr) {
      return new Condition(this, getEqualQueryOperator(), attr);
   }

   public Condition createNotEqualCondition(MobileGroupedPushStateEnumeration attr) {
      return new Condition(this, getNotEqualQueryOperator(), attr);
   }

   public static FndEnumerationView toEnumerationView() throws IfsException {
      FndEnumerationView view = new FndEnumerationView();
      view.name.setValue("MOBILEGROUPEDPUSHSTATE");
      view.addValue(GROUPED_PUSH_PENDING.value, GROUPED_PUSH_PENDING.displayText);
      view.addValue(GROUPED_PUSH_PROCESSING.value, GROUPED_PUSH_PROCESSING.displayText);
      return view;
   }

   public static enum Enum {
      GROUPED_PUSH_PENDING ("GroupedPushPending", "GROUPED_PUSH_PENDING", "Grouped Push Pending"),
      GROUPED_PUSH_PROCESSING ("GroupedPushProcessing", "GROUPED_PUSH_PROCESSING", "Grouped Push Processing"),
      ;

      private String value;
      private String dbValue;
      private String displayText;

      private Enum(String value, String dbValue, String displayText) {
         this.value = value;
         this.dbValue = dbValue;
         this.displayText = displayText;
      }

      public String getValue() {
         return value;
      }

      public String getDatabaseValue() {
         return dbValue;
      }

      public String getDisplayText() {
         return displayText;
      }

      @Override
      public String toString() {
         return getValue();
      }

      public static Enum parseString(String value) throws ParseException {
         if (value == null || value.length() == 0) {
            return null;
         }
         for( Enum entry : Enum.values() ) {
            if (entry.getValue().equalsIgnoreCase(value)) {
               return entry;
            }
         }
         throw new ParseException("Illegal Value");
      }

      public static Enum parseDatabaseString(String dbValue) throws ParseException {
         if (dbValue == null || dbValue.length() == 0) {
            return null;
         }
         for( Enum entry : Enum.values() ) {
            if (entry.getDatabaseValue().equalsIgnoreCase(dbValue)) {
               return entry;
            }
         }
         throw new ParseException("Illegal Database Value");
      }
   }

   public static String[] toStringArray(Enum[] enumArr) {
      if (enumArr == null) {
         return null;
      }
      String[] strArr = new String[enumArr.length];
      for (int i = 0; i < strArr.length; i++) {
         if (enumArr[i] != null) {
            strArr[i] = enumArr[i].toString();
         }
      }
      return strArr;
   }

   public static Enum[] toEnumArray(String[] strArr) throws ParseException {
      if (strArr == null) {
         return null;
      }
      Enum[] enumArr = new Enum[strArr.length];
      for (int i = 0; i < enumArr.length; i++) {
         enumArr[i] = Enum.parseString(strArr[i]);
      }
      return enumArr;
   }

   public static class Condition extends FndSimpleCondition {

      /**
       * Framework internal constructor required by Externalizable interface.
       */
      public Condition() {
      }

      private Condition(MobileGroupedPushStateEnumeration base, FndQueryOperator oper, Enum value) {
         super(base, oper, value);
      }

      private Condition(MobileGroupedPushStateEnumeration base, FndQueryOperator oper, MobileGroupedPushStateEnumeration attr) {
         super(base, oper, attr, null);
      }

      /**
       * See the {@link FndSimpleCondition#setNvlFunction(FndAbstractString, String)} method.
       */
      public void setNvlFunction(MobileGroupedPushStateEnumeration attr, Enum value) throws SystemException {
         protectedSetNvlFunction(attr, value.toString());
      }
   }
}
