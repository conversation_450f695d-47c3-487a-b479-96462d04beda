/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.ifsworld.fnd.mobile.meta;

import ifs.fnd.record.*;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class DynamicDocumentAggregate extends FndAbstractAggregate {
   FndAbstractRecord templateRecord;
   public DynamicDocumentAggregate(FndCompoundAttributeMeta meta, FndCompoundReference parentKeyInParent, FndAbstractRecord templateRecord) {
      super(meta, parentKeyInParent);
      this.templateRecord = templateRecord;
   }
   
   @Override
   protected void connectElementToParent(FndAbstractRecord element) {
      FndCompoundReferenceMeta meta = ((FndCompoundAttributeMeta) getMeta()).getParentKeyInElement();
      setParentKeyInElement(element, new FndUntypedCompoundReference(meta, getNamedAttributes(meta)));
   }

   @Override
   public boolean cascadeDelete() {
      return false;
   }

   @Override
   public boolean containsDependentDetails() {
      return false;
   }

   protected FndAttribute newAttribute(FndAttributeMeta meta) {
      return new DynamicDocumentAggregate((FndCompoundAttributeMeta) getMeta(), getParentKeyInParent(), templateRecord);
   }

   protected FndAbstractRecord getTemplateRecord() {
      return templateRecord.newInstance();
   }

   private FndAttribute[] getNamedAttributes(FndCompoundReferenceMeta meta) {
      List<FndAttribute> attributes = new ArrayList<>();
      for(int i=0; i<meta.getAttributeCount(); i++) {
         FndAttribute attr = internalGetRecord().getAttribute(meta.getAttribute(i).getName());
         if(attr!=null) {
            attributes.add(attr);
         }
      }
      return attributes.toArray(new FndAttribute[] {});
   }
}
