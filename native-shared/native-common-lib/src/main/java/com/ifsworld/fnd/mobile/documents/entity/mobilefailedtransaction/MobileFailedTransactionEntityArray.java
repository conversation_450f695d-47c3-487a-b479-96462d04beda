/*
 * ==================================================================================
 * File:              MobileFailedTransactionEntityArray.java
 * Entity:            MobileFailedTransactionEntity
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobilefailedtransaction;

import ifs.fnd.record.FndAbstractArray;
import ifs.fnd.record.FndAttributeMeta;
import ifs.fnd.record.FndCompoundReference;

/**
 * Array of <code>MobileFailedTransactionEntity</code>.
 */
public abstract class MobileFailedTransactionEntityArray extends FndAbstractArray {

   protected MobileFailedTransactionEntityArray() {
      super();
   }

   protected MobileFailedTransactionEntityArray(FndAttributeMeta meta) {
      super(meta);
   }

   protected MobileFailedTransactionEntityArray(FndAttributeMeta meta, FndCompoundReference parentKeyInParent) {
      super(meta, parentKeyInParent);
   }
}
