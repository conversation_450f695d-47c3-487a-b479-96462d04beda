/*
 * ==================================================================================
 * File:              MobileAppStateEnumeration.java
 * Enumeration:       MobileAppState
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.enumeration;

import ifs.fnd.base.IfsException;
import ifs.fnd.base.ParseException;
import ifs.fnd.base.SystemException;
import ifs.fnd.record.*;

public final class MobileAppStateEnumeration extends FndEnumeration {

   //Enumeration entries
   public static final Enum ACTIVATED = Enum.ACTIVATED;
   public static final Enum ACTIVE = Enum.ACTIVE;
   public static final Enum DISABLED = Enum.DISABLED;
   public static final Enum INIT_WAITING_FOR_GP = Enum.INIT_WAITING_FOR_GP;
   public static final Enum INIT_WAITING_FOR_MWS = Enum.INIT_WAITING_FOR_MWS;
   public static final Enum INITIALIZING = Enum.INITIALIZING;
   public static final Enum PRE_INITIALIZED = Enum.PRE_INITIALIZED;

   public MobileAppStateEnumeration() {
      super();
   }

   public MobileAppStateEnumeration(String name) {
      super(name);
   }

   public MobileAppStateEnumeration(String name, Enum value) {
      this(name);
      setValue(value);
   }

   public MobileAppStateEnumeration(FndAttributeMeta meta) {
      super(meta);
   }

   @Override
   protected FndAttribute newAttribute(FndAttributeMeta meta) {
      return new MobileAppStateEnumeration(meta);
   }

   public void setValue(Enum value) {
      internalSetValue(value);
   }

   public Enum getValue() {
      return (Enum) internalGetValue();
   }

   @Override
   public void parseString(String value) throws ParseException {
      try {
         this.value = Enum.parseString(value);
         this.set();
         this.setExistent();
      } catch (ParseException e) {
         throwIllegalValueException(value);
      }
   }

   @Override
   protected void parseDatabaseString(String dbValue) throws ParseException {
      try {
         this.value = Enum.parseDatabaseString(dbValue);
         this.set();
         this.setExistent();
      } catch (ParseException e) {
         throwIllegalDatabaseValueException(dbValue);
      }
   }

   @Override
   public String toString() {
      Enum val = getValue();
      if (val == null) {
         return null;
      }
      return val.getValue();
   }

   @Override
   protected String toDatabaseString() {
      Enum val = getValue();
      if (val == null) {
         return null;
      }
      return val.getDatabaseValue();
   }

   @Override
   public int compareTo(FndAttribute attr) {
      if (this == attr) {
         return 0;
      }
      if (attr instanceof MobileAppStateEnumeration) {
         if (this.isNull() && attr.isNull()) {
            return 0;
         } else if (this.isNull()) {
            return 1;
         } else if (attr.isNull()) {
            return -1;
         } else {
            return getValue().compareTo(((MobileAppStateEnumeration) attr).getValue());
         }
      }
      return 1;
   }

   public Condition createEqualCondition(Enum value) {
      return new Condition(this, getEqualQueryOperator(), value);
   }

   public Condition createNotEqualCondition(Enum value) {
      return new Condition(this, getNotEqualQueryOperator(), value);
   }

   public Condition createEqualCondition(MobileAppStateEnumeration attr) {
      return new Condition(this, getEqualQueryOperator(), attr);
   }

   public Condition createNotEqualCondition(MobileAppStateEnumeration attr) {
      return new Condition(this, getNotEqualQueryOperator(), attr);
   }

   public static FndEnumerationView toEnumerationView() throws IfsException {
      FndEnumerationView view = new FndEnumerationView();
      view.name.setValue("MOBILEAPPSTATE");
      view.addValue(ACTIVATED.value, ACTIVATED.displayText);
      view.addValue(ACTIVE.value, ACTIVE.displayText);
      view.addValue(DISABLED.value, DISABLED.displayText);
      view.addValue(INIT_WAITING_FOR_GP.value, INIT_WAITING_FOR_GP.displayText);
      view.addValue(INIT_WAITING_FOR_MWS.value, INIT_WAITING_FOR_MWS.displayText);
      view.addValue(INITIALIZING.value, INITIALIZING.displayText);
      view.addValue(PRE_INITIALIZED.value, PRE_INITIALIZED.displayText);
      return view;
   }

   public static enum Enum {
      ACTIVATED ("Activated", "ACTIVATED", "Activated"),
      ACTIVE ("Active", "ACTIVE", "Active"),
      DISABLED ("Disabled", "DISABLED", "Disabled"),
      INIT_WAITING_FOR_GP ("InitWaitingForGp", "INIT_WAITING_FOR_GP", "Init Waiting For GP"),
      INIT_WAITING_FOR_MWS ("InitWaitingForMws", "INIT_WAITING_FOR_MWS", "Init Waiting For Server"),
      INITIALIZING ("Initializing", "INITIALIZING", "Initializing"),
      PRE_INITIALIZED ("PreInitialized", "PRE_INITIALIZED", "Pre Initialized"),
      ;

      private String value;
      private String dbValue;
      private String displayText;

      private Enum(String value, String dbValue, String displayText) {
         this.value = value;
         this.dbValue = dbValue;
         this.displayText = displayText;
      }

      public String getValue() {
         return value;
      }

      public String getDatabaseValue() {
         return dbValue;
      }

      public String getDisplayText() {
         return displayText;
      }

      @Override
      public String toString() {
         return getValue();
      }

      public static Enum parseString(String value) throws ParseException {
         if (value == null || value.length() == 0) {
            return null;
         }
         for( Enum entry : Enum.values() ) {
            if (entry.getValue().equalsIgnoreCase(value)) {
               return entry;
            }
         }
         throw new ParseException("Illegal Value");
      }

      public static Enum parseDatabaseString(String dbValue) throws ParseException {
         if (dbValue == null || dbValue.length() == 0) {
            return null;
         }
         for( Enum entry : Enum.values() ) {
            if (entry.getDatabaseValue().equalsIgnoreCase(dbValue)) {
               return entry;
            }
         }
         throw new ParseException("Illegal Database Value");
      }
   }

   public static String[] toStringArray(Enum[] enumArr) {
      if (enumArr == null) {
         return null;
      }
      String[] strArr = new String[enumArr.length];
      for (int i = 0; i < strArr.length; i++) {
         if (enumArr[i] != null) {
            strArr[i] = enumArr[i].toString();
         }
      }
      return strArr;
   }

   public static Enum[] toEnumArray(String[] strArr) throws ParseException {
      if (strArr == null) {
         return null;
      }
      Enum[] enumArr = new Enum[strArr.length];
      for (int i = 0; i < enumArr.length; i++) {
         enumArr[i] = Enum.parseString(strArr[i]);
      }
      return enumArr;
   }

   public static class Condition extends FndSimpleCondition {

      /**
       * Framework internal constructor required by Externalizable interface.
       */
      public Condition() {
      }

      private Condition(MobileAppStateEnumeration base, FndQueryOperator oper, Enum value) {
         super(base, oper, value);
      }

      private Condition(MobileAppStateEnumeration base, FndQueryOperator oper, MobileAppStateEnumeration attr) {
         super(base, oper, attr, null);
      }

      /**
       * See the {@link FndSimpleCondition#setNvlFunction(FndAbstractString, String)} method.
       */
      public void setNvlFunction(MobileAppStateEnumeration attr, Enum value) throws SystemException {
         protectedSetNvlFunction(attr, value.toString());
      }
   }
}
