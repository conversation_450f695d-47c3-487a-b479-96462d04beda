/*
 * ==================================================================================
 * File:              MobileIgnoredTransactionEntityArray.java
 * Entity:            MobileIgnoredTransactionEntity
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobileignoredtransaction;

import ifs.fnd.record.FndAbstractArray;
import ifs.fnd.record.FndAttributeMeta;
import ifs.fnd.record.FndCompoundReference;

/**
 * Array of <code>MobileIgnoredTransactionEntity</code>.
 */
public abstract class MobileIgnoredTransactionEntityArray extends FndAbstractArray {

   protected MobileIgnoredTransactionEntityArray() {
      super();
   }

   protected MobileIgnoredTransactionEntityArray(FndAttributeMeta meta) {
      super(meta);
   }

   protected MobileIgnoredTransactionEntityArray(FndAttributeMeta meta, FndCompoundReference parentKeyInParent) {
      super(meta, parentKeyInParent);
   }
}
