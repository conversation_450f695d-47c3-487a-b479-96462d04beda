package com.ifsworld.fnd.mobile.cache;

import ifs.fnd.base.IfsException;
import ifs.fnd.record.FndRecord;
import ifs.fnd.record.FndText;
import ifs.fnd.sf.storage.FndPlsqlAccess;

import java.util.regex.Pattern;


public class CacheUtilities {

    private static final String stmt_LANGUAGELOOKUP8
            = "DECLARE \n"
            + "BEGIN \n"
            + "	:TransText := &AO.Language_SYS.Lookup(:Type, :Path, :Attribute, :Language, :MainType);\n"
            + "END;";

    private static final String stmt_APP_VERSION =
            "DECLARE\n"
                    + "    app_version_rec_   Mobile_Application_Version_API.Public_Rec;\n"
                    + "BEGIN \n"
                    + "    app_version_rec_ := &AO.Mobile_Application_Version_API.Get_Active_App_(:AppName);\n"
                    + "    :AppVersion  := app_version_rec_.app_version;\n"
                    + "END;";

    /**
     * <p>
     * <b>Remarks: languageLookup</b>
     *
     * @return void
     * @throws IfsException
     */
    public static void languageLookup(final FndText transText, FndText type, FndText path, FndText attribute, FndText language, FndText mainType) throws IfsException {
        final FndPlsqlAccess plsqlAccess = new FndPlsqlAccess();
        //Create bind variables
        FndRecord params = new FndRecord();
        params.add("TransText", (transText != null) ? transText : new FndText(), "OUT");
        params.add("Type", (type != null) ? type : new FndText(), "IN");
        params.add("Path", (path != null) ? path : new FndText(), "IN");
        params.add("Attribute", (attribute != null) ? attribute : new FndText(), "IN");
        params.add("Language", (language != null) ? language : new FndText(), "IN");
        params.add("MainType", (mainType != null) ? mainType : new FndText(), "IN");
        //Execute the PL/SQL method
        plsqlAccess.execute(stmt_LANGUAGELOOKUP8, params);
        //Return data to caller

        if (transText != null) {
            transText.setValue((FndText) params.getAttribute("TransText"));
        }
    }

    /*
     * Return active app version of the give app
     */
    public static String getActiveAppVersion(String appName) throws IfsException {
        FndPlsqlAccess plsqlAccess = new FndPlsqlAccess();
        //Create bind variables
        FndRecord params = new FndRecord();
        params.add("AppVersion", new FndText("APP_VERSION"), "OUT");
        params.add("AppName", new FndText("APP_NAME", appName), "IN");
        //Execute the PL/SQL method
        plsqlAccess.execute(stmt_APP_VERSION, params);
        //Return data to caller
        FndText appVersion = (FndText) params.getAttribute("AppVersion");
        return appVersion.getValue();
    }
}
