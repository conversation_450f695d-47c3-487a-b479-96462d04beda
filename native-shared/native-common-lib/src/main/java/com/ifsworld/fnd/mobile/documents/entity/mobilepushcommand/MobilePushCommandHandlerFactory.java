/*
 * ==================================================================================
 * File:              MobilePushCommandHandlerFactory.java
 * Entity:            MobilePushCommand
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobilepushcommand;

public class MobilePushCommandHandlerFactory {

   public static MobilePushCommandHandler getHandler() {
      return new MobilePushCommandHandler();
   }
}
