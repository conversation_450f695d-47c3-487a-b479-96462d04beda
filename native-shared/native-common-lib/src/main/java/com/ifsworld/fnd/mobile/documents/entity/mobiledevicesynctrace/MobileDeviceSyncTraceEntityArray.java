/*
 * ==================================================================================
 * File:              MobileDeviceSyncTraceEntityArray.java
 * Entity:            MobileDeviceSyncTraceEntity
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobiledevicesynctrace;

import ifs.fnd.record.FndAbstractArray;
import ifs.fnd.record.FndAttributeMeta;
import ifs.fnd.record.FndCompoundReference;

/**
 * Array of <code>MobileDeviceSyncTraceEntity</code>.
 */
public abstract class MobileDeviceSyncTraceEntityArray extends FndAbstractArray {

   protected MobileDeviceSyncTraceEntityArray() {
      super();
   }

   protected MobileDeviceSyncTraceEntityArray(FndAttributeMeta meta) {
      super(meta);
   }

   protected MobileDeviceSyncTraceEntityArray(FndAttributeMeta meta, FndCompoundReference parentKeyInParent) {
      super(meta, parentKeyInParent);
   }
}
