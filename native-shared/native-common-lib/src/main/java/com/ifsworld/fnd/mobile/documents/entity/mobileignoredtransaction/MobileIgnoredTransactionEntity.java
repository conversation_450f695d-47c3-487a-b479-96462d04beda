/*
 * ==================================================================================
 * File:              MobileIgnoredTransactionEntity.java
 * Entity:            MobileIgnoredTransaction
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobileignoredtransaction;

import ifs.fnd.base.IfsException;
import ifs.fnd.base.SystemException;
import ifs.fnd.record.*;

public abstract class MobileIgnoredTransactionEntity extends FndLUEntityView {


   public static final FndRecordMeta viewMeta = new FndRecordMeta("", "MOBILEIGNOREDTRANSACTION", "MOBILE_IGNORED_TRANSACTION", "MOBILE_IGNORED_TRANSACTION", "MobileIgnoredTransaction", "MOBILE_IGNORED_TRANSACTION_API", "MOBILE_IGNORED_TRANSACTION_TAB").setViewClassName("ifs.entity.mobileignoredtransaction.MobileIgnoredTransactionEntity").setTermPath("MobileIgnoredTransaction.MobileIgnoredTransaction");
   public final FndText transactionId = _newTransactionId((FndText) null);
   public final Reference primaryKey = new Reference(Meta.primaryKey, transactionId);

   protected MobileIgnoredTransactionEntity(FndRecordMeta meta) {
      super(meta);
      add(transactionId);
      add(primaryKey);
   }

   public void assign(MobileIgnoredTransactionEntity from) throws SystemException {
      super.assign(from);
   }

   public void transformTo(final MobileIgnoredTransactionEntity to) throws SystemException {
      transformView(to);
   }

   public static class Reference extends FndCompoundReference {

      public Reference(FndCompoundReferenceMeta ref, FndText transactionId) {
         super(ref);
         add(transactionId);
      }

      public void assign(Reference ref) throws IfsException {
         protectedAssign(ref);
      }

      public boolean isEqualTo(Reference ref) {
         return protectedIsEqualTo(ref);
      }
   }

   protected static final FndText _newTransactionId(FndText type) {
      return new FndText(Meta.transactionId);
   }

   protected static final FndTimestamp _newTransactionDate(FndTimestamp type) {
      return new FndTimestamp(Meta.transactionDate);
   }

   protected static final FndText _newAppName(FndText type) {
      return new FndText(Meta.appName);
   }

   protected static final FndText _newAppVersion(FndText type) {
      return new FndText(Meta.appVersion);
   }

   protected static final FndText _newDeviceId(FndText type) {
      return new FndText(Meta.deviceId);
   }

   protected static final FndBoolean _newTimeZoneAwareRequest(FndBoolean type) {return new FndBooleanString(Meta.timeZoneAwareRequest, "TRUE", "FALSE");}

   protected static final FndText _newUserId(FndText type) {
      return new FndText(Meta.userId);
   }

   protected static final FndText _newGroupId(FndText type) {
      return new FndText(Meta.groupId);
   }

   protected static final FndText _newDescription(FndText type) {
      return new FndText(Meta.description);
   }

   protected static final FndText _newDirectoryId(FndText type) {
      return new FndText(Meta.directoryId);
   }

   protected static final FndText _newHandlerName(FndText type) {
      return new FndText(Meta.handlerName);
   }

   protected static final FndText _newProjection(FndText type) {
      return new FndText(Meta.projection);
   }

   protected static final FndText _newMethodName(FndText type) {
      return new FndText(Meta.methodName);
   }

   protected static final FndBinary _newReqReplyData(FndBinary type) {
      return new FndBinary(Meta.reqReplyData);
   }

   protected static final FndBinary _newInputRecord(FndBinary type) {
      return new FndBinary(Meta.inputRecord);
   }

   protected static final FndNumber _newExecutionOrder(FndNumber type) {
      return new FndNumber(Meta.executionOrder);
   }

   protected static final FndText _newTranslationId(FndText type) {
      return new FndText(Meta.translationId);
   }

   public static class Meta {

      public static final FndAttributeMeta transactionId = new FndAttributeMeta(viewMeta, "TRANSACTION_ID", "TRANSACTION_ID", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 100).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta transactionDate = new FndAttributeMeta(viewMeta, "TRANSACTION_DATE", "TRANSACTION_DATE", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta appName = new FndAttributeMeta(viewMeta, "APP_NAME", "APP_NAME", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 200).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta appVersion = new FndAttributeMeta(viewMeta, "APP_VERSION", "APP_VERSION", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 20).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta deviceId = new FndAttributeMeta(viewMeta, "DEVICE_ID", "DEVICE_ID", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 100).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta timeZoneAwareRequest = new FndAttributeMeta(viewMeta, "TIME_ZONE_AWARE_REQUEST", "TIME_ZONE_AWARE_REQUEST", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta userId = new FndAttributeMeta(viewMeta, "USER_ID", "USER_ID", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 100).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta groupId = new FndAttributeMeta(viewMeta, "GROUP_ID", "GROUP_ID", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 2000).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta description = new FndAttributeMeta(viewMeta, "DESCRIPTION", "DESCRIPTION", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 2000);
      public static final FndAttributeMeta directoryId = new FndAttributeMeta(viewMeta, "DIRECTORY_ID", "DIRECTORY_ID", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 100).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta handlerName = new FndAttributeMeta(viewMeta, "HANDLER_NAME", "HANDLER_NAME", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 200);
      public static final FndAttributeMeta projection = new FndAttributeMeta(viewMeta, "PROJECTION", "PROJECTION", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 100);
      public static final FndAttributeMeta methodName = new FndAttributeMeta(viewMeta, "METHOD_NAME", "METHOD_NAME", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 200);
      public static final FndAttributeMeta reqReplyData = new FndAttributeMeta(viewMeta, "REQ_REPLY_DATA", "REQ_REPLY_DATA", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta inputRecord = new FndAttributeMeta(viewMeta, "INPUT_RECORD", "INPUT_RECORD", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta executionOrder = new FndAttributeMeta(viewMeta, "EXECUTION_ORDER", "EXECUTION_ORDER", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta translationId = new FndAttributeMeta(viewMeta, "TRANSLATION_ID", "TRANSLATION_ID", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 2000);
      public static final FndCompoundReferenceMeta primaryKey = new FndCompoundReferenceMeta(viewMeta, "ABSTRACT_MOBILE_IGNORED_TRANSACTION_KEY", new FndAttributeMeta[]{transactionId}, viewMeta, true);
   }
}
