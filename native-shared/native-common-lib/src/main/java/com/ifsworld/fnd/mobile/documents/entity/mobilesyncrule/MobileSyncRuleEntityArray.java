/*
 * ==================================================================================
 * File:              MobileSyncRuleEntityArray.java
 * Entity:            MobileSyncRuleEntity
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobilesyncrule;

import ifs.fnd.record.FndAbstractArray;
import ifs.fnd.record.FndAttributeMeta;
import ifs.fnd.record.FndCompoundReference;

/**
 * Array of <code>MobileSyncRuleEntity</code>.
 */
public abstract class MobileSyncRuleEntityArray extends FndAbstractArray {

   protected MobileSyncRuleEntityArray() {
      super();
   }

   protected MobileSyncRuleEntityArray(FndAttributeMeta meta) {
      super(meta);
   }

   protected MobileSyncRuleEntityArray(FndAttributeMeta meta, FndCompoundReference parentKeyInParent) {
      super(meta, parentKeyInParent);
   }
}
