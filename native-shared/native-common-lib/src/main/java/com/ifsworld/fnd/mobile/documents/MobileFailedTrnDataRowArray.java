/*
 * ==================================================================================
 * File:              MobileFailedTrnDataRowArray.java
 * Software Package:  MobileClientRuntime
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents;

import ifs.fnd.base.ParseException;
import ifs.fnd.base.SystemException;
import ifs.fnd.record.*;
import ifs.fnd.record.serialization.FndTokenReader;

/**
 * Array of <code>MobileFailedTrnDataRow</code>.
 */
public class MobileFailedTrnDataRowArray extends FndAbstractArray {

   public MobileFailedTrnDataRowArray() {
      super();
   }

   public MobileFailedTrnDataRowArray(FndAttributeMeta meta) {
      super(meta);
   }

   public MobileFailedTrnDataRowArray(FndAttributeMeta meta, FndCompoundReference parentKeyInParent) {
      super(meta, parentKeyInParent);
   }

   public boolean add(MobileFailedTrnDataRow record) {
      return internalAdd(record);
   }

   public void add(int index, MobileFailedTrnDataRow record) {
      internalAdd(index, record);
   }

   public void add(MobileFailedTrnDataRowArray array) {
      internalAdd(array);
   }

   public void assign(MobileFailedTrnDataRowArray from) throws SystemException {
      assign(from, true);
   }

   public boolean contains(MobileFailedTrnDataRow record) {
      return internalContains(record);
   }

   public MobileFailedTrnDataRow firstElement() {
      return (MobileFailedTrnDataRow)internalFirstElement();
   }

   public MobileFailedTrnDataRow get(int index) {
      return (MobileFailedTrnDataRow)internalGet(index);
   }

   public int indexOf(MobileFailedTrnDataRow record) {
      return internalIndexOf(record);
   }

   public MobileFailedTrnDataRow lastElement() {
      return (MobileFailedTrnDataRow)internalLastElement();
   }

   public int lastIndexOf(MobileFailedTrnDataRow record) {
      return internalLastIndexOf(record);
   }

   public MobileFailedTrnDataRow remove(int index) {
      return (MobileFailedTrnDataRow)internalRemove(index);
   }

   public MobileFailedTrnDataRow set(int index, MobileFailedTrnDataRow record) {
      return (MobileFailedTrnDataRow)internalSet(index, record);
   }

   public FndDetailCondition createDetailCondition(MobileFailedTrnDataRow record, FndQueryReferenceCategory category) {
      return createCondition(record, category);
   }

   @Override
   public FndAbstractRecord newRecord() {
      return new MobileFailedTrnDataRow();
   }

   @Override
   protected FndAbstractRecord newRecord(FndTokenReader stream) throws ParseException {
      MobileFailedTrnDataRow record = new MobileFailedTrnDataRow();
      record.parse(stream);
      return record;
   }

   protected FndAttribute newAttribute(FndAttributeMeta meta) {
       return new MobileFailedTrnDataRowArray(meta);
   }
}
