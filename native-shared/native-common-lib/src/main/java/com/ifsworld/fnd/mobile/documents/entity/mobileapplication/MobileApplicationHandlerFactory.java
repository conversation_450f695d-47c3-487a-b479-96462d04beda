/*
 * ==================================================================================
 * File:              MobileApplicationHandlerFactory.java
 * Entity:            MobileApplication
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobileapplication;

public class MobileApplicationHandlerFactory {

   public static MobileApplicationHandler getHandler() {
      return new MobileApplicationHandler();
   }
}
