/*
 * ==================================================================================
 * File:              MobileDeviceHandlerFactory.java
 * Entity:            MobileDevice
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobiledevice;

public class MobileDeviceHandlerFactory {

   public static MobileDeviceHandler getHandler() {
      return new MobileDeviceHandler();
   }
}
