/*
 * ==================================================================================
 * File:              MobileAppMethodParamHandler.java
 * Entity:            MobileAppMethodParam
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobileappmethodparam;

import ifs.fnd.base.IfsException;
import ifs.fnd.record.FndEntityState;
import ifs.fnd.record.FndQueryRecord;
import ifs.fnd.sf.storage.FndEntityHandler;
import ifs.fnd.sf.storage.FndSqlStorage;

/**
 * Implementation of the <code>MobileAppMethodParamHandler</code> handler.
 */
public class MobileAppMethodParamHandler extends FndEntityHandler {

   /**
    * <p><b>Remarks:</b>
    * @param MobileAppMethodParam value
    * @param MobileAppMethodParam condition
    * @return void
    * @throws IfsException
    */
   public void batchSave(MobileAppMethodParamEntity value, MobileAppMethodParamEntity condition, FndEntityState.Enum targetState) throws IfsException {
      beforeCall("batchSave");
      try {
         super.batchSave(value, condition, targetState);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param MobileAppMethodParamEntityArray mobileAppMethodParams
    * @return MobileAppMethodParamEntityArray
    * @throws IfsException
    */
   public MobileAppMethodParamEntityArray bulkGet(MobileAppMethodParamEntityArray mobileAppMethodParams) throws IfsException {
      beforeCall("bulkGet");
      try {
         return (MobileAppMethodParamEntityArray) super.bulkGet(mobileAppMethodParams);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param MobileAppMethodParamArray mobileAppMethodParams
    * @return void
    * @throws IfsException
    */
   public void bulkSave(final MobileAppMethodParamEntityArray mobileAppMethodParams) throws IfsException {
      beforeCall("bulkSave");
      try {
         super.bulkSave(mobileAppMethodParams);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param MobileAppMethodParamEntity mobileAppMethodParam
    * @return FndBoolean
    * @throws IfsException
    */
   public boolean exist(MobileAppMethodParamEntity mobileAppMethodParam) throws IfsException {
      beforeCall("exist");
      try {
         return super.exist(mobileAppMethodParam);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param MobileAppMethodParamEntity mobileAppMethodParam
    * @return MobileAppMethodParamEntity
    * @throws IfsException
    */
   public MobileAppMethodParamEntity get(MobileAppMethodParamEntity mobileAppMethodParam) throws IfsException {
      beforeCall("get");
      try {
         return (MobileAppMethodParamEntity) super.get(mobileAppMethodParam);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param MobileAppMethodParamEntity mobileAppMethodParam
    * @return MobileAppMethodParamEntity
    * @throws IfsException
    */
   public MobileAppMethodParamEntity populate(MobileAppMethodParamEntity mobileAppMethodParam) throws IfsException {
      beforeCall("populate");
      try {
         return (MobileAppMethodParamEntity) super.populate(mobileAppMethodParam);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param MobileAppMethodParam mobileAppMethodParam
    * @return void
    * @throws IfsException
    */
   public void prepare(final MobileAppMethodParamEntity mobileAppMethodParam) throws IfsException {
      beforeCall("prepare");
      try {
         super.getDefaults(mobileAppMethodParam);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param FndQueryRecord condition
    * @return MobileAppMethodParamEntityArray
    * @throws IfsException
    */
   public final MobileAppMethodParamEntityArray query(FndQueryRecord condition) throws IfsException {
      beforeCall("query");
      try {
         MobileAppMethodParamEntityArray array = (MobileAppMethodParamEntityArray) condition.condition.getRecord().newArrayInstance();
         super.query(condition, array);
         return array;
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param FndQueryRecord condition
    * @return MobileAppMethodParamQueryCursor
    * @throws IfsException
    */
   public final MobileAppMethodParamQueryCursor queryCursor(FndQueryRecord condition) throws IfsException {
      beforeCall("queryCursor");
      try {
         FndSqlStorage storage = new FndSqlStorage();
         return new MobileAppMethodParamQueryCursor(storage.query(condition), this);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param MobileAppMethodParam MobileAppMethodParam
    * @return void
    * @throws IfsException
    */
   public void save(final MobileAppMethodParamEntity mobileAppMethodParam) throws IfsException {
      beforeCall("save");
      try {
         super.save(mobileAppMethodParam);
      } finally {
         afterCall();
      }
   }
   private static final int METHOD_IMPLEMENTATION_META = METHOD_META_QUERY;

   /**
    * Fetch method implementation meta data. This is a list of flags for the
    * various standard entity handler methods (Query, save, etc). A value of
    * one means framework implementation whereas zero correspons to unknown
    * or custom implementation.
    * @returns Meta data for Method Implementations
    */
   public static int getMethodImplementationMeta() {
      return METHOD_IMPLEMENTATION_META;
   }
}