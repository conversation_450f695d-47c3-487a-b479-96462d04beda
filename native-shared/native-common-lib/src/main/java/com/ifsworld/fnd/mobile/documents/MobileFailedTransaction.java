/*
 * ==================================================================================
 * File:              MobileFailedTransaction.java
 * Software Package:  MobileOutQueueManagement
 * Template Version:  8.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents;

import ifs.fnd.record.*;

/**
 * This view represents the <code>MobileFailedTransaction</code> entity.
 */
public class MobileFailedTransaction extends com.ifsworld.fnd.mobile.documents.entity.mobilefailedtransaction.MobileFailedTransactionEntity {

   public static final FndRecordMeta viewMeta  = new FndRecordMeta(com.ifsworld.fnd.mobile.documents.entity.mobilefailedtransaction.MobileFailedTransactionEntity.viewMeta, "MOBILEOUTQUEUEMANAGEMENT", "MOBILE_FAILED_TRANSACTION").setViewClassName("com.ifsworld.fnd.mobile.documents.MobileFailedTransaction").setTermPath("MobileOutQueueManagement.MobileFailedTransaction");

   /**
    * TransactionDate
    */
   public final FndTimestamp transactionDate = _newTransactionDate((FndTimestamp) null);
   /**
    * Description
    */
   public final FndText      description     = _newDescription((FndText) null);
   /**
    * DeviceId
    */
   public final FndText      deviceId        = _newDeviceId((FndText) null);
   /**
    * TimeZoneAwareRequest
    */
   public final FndBoolean   timeZoneAwareRequest     = _newTimeZoneAwareRequest((FndBoolean) null);
   /**
    * UserId
    */
   public final FndText      userId          = _newUserId((FndText) null);
   /**
    * DirectoryId
    */
   public final FndText      directoryId     = _newDirectoryId((FndText) null);
   /**
    * GroupId
    */
   public final FndText      groupId         = _newGroupId((FndText) null);
   /**
    * AppName
    */
   public final FndText      appName         = _newAppName((FndText) null);
   /**
    * AppVersion
    */
   public final FndText      appVersion      = _newAppVersion((FndText) null);
   /**
    * HandlerName
    */
   public final FndText      handlerName     = _newHandlerName((FndText) null);
   /**
    * Projection
    */
   public final FndText      projection     = _newProjection((FndText) null);
   /**
    * MethodName
    */
   public final FndText      methodName      = _newMethodName((FndText) null);
   /**
    * ReqReplyData
    */
   public final FndBinary    reqReplyData    = _newReqReplyData((FndBinary) null);
   /**
    * InputRecord
    */
   public final FndBinary    inputRecord     = _newInputRecord((FndBinary) null);
   /**
    * ExecutionOrder
    */
   public final FndNumber    executionOrder  = _newExecutionOrder((FndNumber) null);
   /**
    * TranslationId
    */
   public final FndText      translationId   = _newTranslationId((FndText) null);

   public MobileFailedTransaction() {
      this(viewMeta);
   }

   protected MobileFailedTransaction(FndRecordMeta meta) {
      super(meta);
      add(transactionDate);
      add(description);
      add(deviceId);
      add(userId);
      add(directoryId);
      add(groupId);
      add(appName);
      add(appVersion);
      add(handlerName);
      add(projection);
      add(methodName);
      add(reqReplyData);
      add(inputRecord);
      add(executionOrder);
      add(translationId);
      add(timeZoneAwareRequest);
   }

   @Override
   public FndAbstractRecord newInstance() {
      return new MobileFailedTransaction();
   }

   @Override
   public FndAbstractArray newArrayInstance() {
      return new MobileFailedTransactionArray();
   }

   public static class Meta extends com.ifsworld.fnd.mobile.documents.entity.mobilefailedtransaction.MobileFailedTransactionEntity.Meta {
   }
}
