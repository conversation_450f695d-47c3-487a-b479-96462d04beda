/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.ifsworld.fnd.mobile.meta;

import ifs.fnd.base.ParseException;
import ifs.fnd.record.FndAttributeMeta;
import ifs.fnd.record.FndAttributeType;
import ifs.fnd.record.FndBinary;
import ifs.fnd.record.serialization.FndSerializeConstants;
import ifs.fnd.record.serialization.FndTokenReader;
import ifs.fnd.record.serialization.FndUtil;

import java.io.IOException;

/**
 *
 * <AUTHOR>
 */
public class FndmobBinaryAttribute extends FndBinary{

   /**
    * Create a new instance.
    */
   public FndmobBinaryAttribute() {
      super();
   }

   /**
    * Create a new instance, also specifying attribute name.
    * @param name attribute name
    */
   public FndmobBinaryAttribute(String name) {
      super(name);
   }

   /**
    * Create a new instance based on specified meta data.
    * @param meta attribute meta data
    */
   public FndmobBinaryAttribute(FndAttributeMeta meta) {
      super(meta);
   }


   /**
    * Creates a new attribute based on meta-data.
    * @param meta Meta-data to base the attribute on
    * @return The created attribute
    */
   @Override
   protected FndmobBinaryAttribute newAttribute(FndAttributeMeta meta) {
      return new FndmobBinaryAttribute(meta);
   }

   /** Parses in the value from a string
    * @param value String from which to get the value
    * @throws ParseException
    */
   @Override
   public void parseString(String value) throws ParseException {
      if (value == null || value.length() == 0) {
         this.value = null;
         setExistent();
         set();
      }
      else
         setValueData(value, defaultEncoding);
   }
   
   /**
    * Sets the data to be held.
    * @param encData a String with the encoded data.
    * @param encoding  any of the ENC_* constants of this class indicating how the
    * data is encoded.
    */
   private void setValueData(String encData, int encoding) throws ParseException {
      try {
         if (encoding == FndSerializeConstants.ENCODING_B64) {
            this.value = FndUtil.fromBase64Text(encData);
         }
         else {
            this.value = FndUtil.fromHexText(encData);
         }
         setExistent();
         set();
      }
      catch (IOException ex) {
         throw new ParseException(
            ex,
            "IOEX: Error while parsing data '&1' for attribute &2.&3",
            ex.getMessage(),
            getParentRecord().getName(),
            getName());
      }
   }
   
   /**
    * Parses an Fnd Buffer to get the attribute.
    * @param stream Stream from which to read the buffer
    * @throws ParseException Syntax error in buffer
    */
   @Override
   protected void parse(FndTokenReader stream) throws ParseException {
      FndAttributeType type;
      String name, state = null;
      char ch;
      String typeString;

      setExistent();

      ch = stream.getDelimiter();

      if (ch == FndSerializeConstants.NAME_MARKER) {
         name = stream.getToken();
         setName(name);
         ch = stream.getDelimiter();
      }

      if (ch == FndSerializeConstants.TYPE_MARKER) {
         typeString = stream.getToken();
         type = FndAttributeType.toAttributeType(typeString);
         setType(type);
         ch = stream.getDelimiter();
      }

      if (ch == FndSerializeConstants.STATUS_MARKER) {
         state = stream.getToken();
         setState(state);
         ch = stream.getDelimiter();
      }

      if (ch == FndSerializeConstants.INVALID_VALUE_MARKER) {
         stream.getToken();
         ch = stream.getDelimiter();
      }

      if (ch == FndSerializeConstants.COUNT_MARKER) {
         stream.getToken();
      }
      else {
         if(ch == FndSerializeConstants.CHANGED_VALUE_MARKER) {
            ch = stream.getDelimiter();
         }
         switch (ch) {
            case FndSerializeConstants.UNCHANGED_VALUE_MARKER :
               throw new ParseException("UNCHANGED_VALUE_MARKER: Parsing of UNCHANGED_VALUE_MARKER is not supported on server-side");
            case FndSerializeConstants.NO_VALUE_MARKER :
               unset();
               break;
            case FndSerializeConstants.NULL_VALUE_MARKER :
               value = null;
               set();
               break;
            case FndSerializeConstants.VALUE_MARKER :
               //Binary values are received via a text attributes. So we need special handling here;
               parseString(stream.getLPStringToken());
               break;
            case FndSerializeConstants.BEGIN_BUFFER_MARKER : 
               parseBuffer(stream);
               break;
            case FndSerializeConstants.END_BUFFER_MARKER :
               break;
            default :
               throw new ParseException("Expecting '*', '=' or '(' but found character code " + (int) ch);
         }
      }
   }   
}
