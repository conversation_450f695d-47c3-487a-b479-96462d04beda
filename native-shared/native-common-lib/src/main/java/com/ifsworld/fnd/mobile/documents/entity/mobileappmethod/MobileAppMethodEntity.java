/*
 * ==================================================================================
 * File:              MobileAppMethodEntity.java
 * Entity:            MobileAppMethod
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobileappmethod;

import com.ifsworld.fnd.mobile.documents.enumeration.MobileMethodTypeEnumeration;
import ifs.fnd.base.IfsException;
import ifs.fnd.base.SystemException;
import ifs.fnd.record.*;

public abstract class MobileAppMethodEntity extends FndLUEntityView {


   public static final FndRecordMeta viewMeta = new FndRecordMeta("", "MOBILEAPPMETHOD", "MOBILE_APP_METHOD", "MOBILE_APP_METHOD", "MobileAppMethod", "MOBILE_APP_METHOD_API", "MOBILE_APP_METHOD_TAB").setViewClassName("ifs.entity.mobileappmethod.MobileAppMethodEntity").setTermPath("MobileAppMethod.MobileAppMethod");
   public final FndText appName = _newAppName((FndText) null);
   public final FndText appVersion = _newAppVersion((FndText) null);
   public final FndAlpha handlerName = _newHandlerName((FndAlpha) null);
   public final FndAlpha methodName = _newMethodName((FndAlpha) null);
   public final FndText projection = _newProjection((FndText) null);
   public final Reference primaryKey = new Reference(Meta.primaryKey, appName, appVersion, handlerName, methodName, projection);
   public final ParentReferenceMobileApplicationVersion parentKeyMobileApplicationVersion = new ParentReferenceMobileApplicationVersion(Meta.parentKeyMobileApplicationVersion, appName, appVersion);

   protected MobileAppMethodEntity(FndRecordMeta meta) {
      super(meta);
      add(appName);
      add(appVersion);
      add(handlerName);
      add(methodName);
      add(projection);
      add(primaryKey);
      add(parentKeyMobileApplicationVersion);
   }

   public void assign(MobileAppMethodEntity from) throws SystemException {
      super.assign(from);
   }

   public void transformTo(final MobileAppMethodEntity to) throws SystemException {
      transformView(to);
   }

   public static class Reference extends FndCompoundReference {

      public Reference(FndCompoundReferenceMeta ref, FndText appName, FndText appVersion, FndAlpha handlerName, FndAlpha methodName, FndText projection) {
         super(ref);
         add(appName);
         add(appVersion);
         add(handlerName);
         add(methodName);
         add(projection);
      }

      public void assign(Reference ref) throws IfsException {
         protectedAssign(ref);
      }

      public boolean isEqualTo(Reference ref) {
         return protectedIsEqualTo(ref);
      }
   }

   public static class ParentReferenceMobileApplicationVersion extends FndCompoundReference {

      public ParentReferenceMobileApplicationVersion(FndCompoundReferenceMeta ref, FndText appName, FndText appVersion) {
         super(ref);
         add(appName);
         add(appVersion);
      }

      public void assign(ParentReferenceMobileApplicationVersion ref) throws IfsException {
         protectedAssign(ref);
      }

      public boolean isEqualTo(ParentReferenceMobileApplicationVersion ref) {
         return protectedIsEqualTo(ref);
      }
   }

   protected static final FndText _newAppName(FndText type) {
      return new FndText(Meta.appName);
   }

   protected static final FndText _newAppVersion(FndText type) {
      return new FndText(Meta.appVersion);
   }

   protected static final FndAlpha _newHandlerName(FndAlpha type) {
      return new FndAlpha(Meta.handlerName);
   }

   protected static final FndAlpha _newMethodName(FndAlpha type) {
      return new FndAlpha(Meta.methodName);
   }

   protected static final FndText _newProjection(FndText type) {
      return new FndText(Meta.projection);
   }

   protected static final FndText _newObjectName(FndText type) {
      return new FndText(Meta.objectName);
   }

   protected static final MobileMethodTypeEnumeration _newMethodType(MobileMethodTypeEnumeration type) {
      return new MobileMethodTypeEnumeration(Meta.methodType);
   }

   protected static final FndText _newMethodType(FndText type) {
      return new FndText(Meta.methodType$);
   }

   protected static final FndText _newPlsqlBlock(FndText type) {
      return new FndText(Meta.plsqlBlock, FndText.CLOB);
   }

   protected static final FndAlpha _newEntityName(FndAlpha type) {
      return new FndAlpha(Meta.entityName);
   }

   protected static final FndAlpha _newStateEvent(FndAlpha type) {
      return new FndAlpha(Meta.stateEvent);
   }

   protected static final FndText _newPreBlock(FndText type) {
      return new FndText(Meta.preBlock, FndText.CLOB);
   }

   protected static final FndText _newPostBlock(FndText type) {
      return new FndText(Meta.postBlock, FndText.CLOB);
   }

   protected static final FndText _newTransactionGrouping(FndText type) {
      return new FndText(Meta.transactionGrouping);
   }

   protected static final FndBoolean _newAddedByCustomer(FndBoolean type) {
      return new FndBooleanString(Meta.addedByCustomer, "TRUE", "FALSE");
   }

   public static class Meta {

      public static final FndAttributeMeta appName = new FndAttributeMeta(viewMeta, "APP_NAME", "APP_NAME", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 200).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta appVersion = new FndAttributeMeta(viewMeta, "APP_VERSION", "APP_VERSION", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 20).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta handlerName = new FndAttributeMeta(viewMeta, "HANDLER_NAME", "HANDLER_NAME", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 200).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta methodName = new FndAttributeMeta(viewMeta, "METHOD_NAME", "METHOD_NAME", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 200).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta projection = new FndAttributeMeta(viewMeta, "PROJECTION", "PROJECTION", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 100).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta objectName = new FndAttributeMeta(viewMeta, "OBJECT_NAME", "OBJECT_NAME", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 100);
      public static final FndAttributeMeta methodType = new FndAttributeMeta(viewMeta, "METHOD_TYPE", "METHOD_TYPE_DB", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY).setIidPlsqlPackage("MOBILE_METHOD_TYPE_API");
      public static final FndAttributeMeta methodType$ = new FndAttributeMeta(viewMeta, "METHOD_TYPE", "METHOD_TYPE", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY).setIidPlsqlPackage("MOBILE_METHOD_TYPE_API");
      public static final FndAttributeMeta plsqlBlock = new FndAttributeMeta(viewMeta, "PLSQL_BLOCK", "PLSQL_BLOCK", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta entityName = new FndAttributeMeta(viewMeta, "ENTITY_NAME", "ENTITY_NAME", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 100);
      public static final FndAttributeMeta stateEvent = new FndAttributeMeta(viewMeta, "STATE_EVENT", "STATE_EVENT", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 35);
      public static final FndAttributeMeta preBlock = new FndAttributeMeta(viewMeta, "PRE_BLOCK", "PRE_BLOCK", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta postBlock = new FndAttributeMeta(viewMeta, "POST_BLOCK", "POST_BLOCK", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta transactionGrouping = new FndAttributeMeta(viewMeta, "TRANSACTION_GROUPING", "TRANSACTION_GROUPING", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 2000);
      public static final FndAttributeMeta addedByCustomer = new FndAttributeMeta(viewMeta, "ADDED_BY_CUSTOMER", "ADDED_BY_CUSTOMER", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndCompoundReferenceMeta primaryKey = new FndCompoundReferenceMeta(viewMeta, "ABSTRACT_MOBILE_APP_METHOD_KEY", new FndAttributeMeta[]{appName, appVersion, handlerName, methodName, projection}, viewMeta, true);
      public static final FndCompoundReferenceMeta parentKeyMobileApplicationVersion = new FndCompoundReferenceMeta(viewMeta, "PARENT_KEY_MOBILE_APPLICATION_VERSION", new FndAttributeMeta[] {appName, appVersion}, com.ifsworld.fnd.mobile.documents.entity.mobileapplicationversion.MobileApplicationVersionEntity.viewMeta);
   }
}
