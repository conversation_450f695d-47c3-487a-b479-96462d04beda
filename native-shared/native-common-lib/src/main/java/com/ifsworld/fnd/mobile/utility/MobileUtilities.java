package com.ifsworld.fnd.mobile.utility;

import com.ifsworld.fnd.mobile.cache.FndmobMetaDataCache;
import com.ifsworld.fnd.mobile.documents.BatchSyncEntityArray;
import com.ifsworld.fnd.mobile.documents.MobileOnDemandSyncTaskLineArray;
import com.ifsworld.fnd.mobile.documents.MobilePushSyncTaskLineArray;
import com.ifsworld.fnd.mobile.meta.IDynamicDocumentUtilities;
import ifs.fnd.base.IfsException;
import ifs.fnd.base.SystemException;
import ifs.fnd.internal.FndAttributeInternals;
import ifs.fnd.internal.FndRecordInternals;
import ifs.fnd.record.*;
import ifs.fnd.sf.storage.FndPlsqlAccess;
import org.apache.logging.log4j.LogManager;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Implementation of the
 * <code>MobileUtilities</code> handler.
 */
public class MobileUtilities {

   private static final org.apache.logging.log4j.Logger LOG = LogManager.getLogger(MobileUtilities.class);

   protected MobileUtilities() {
   }
   public static final String UMA = "UMA";
   public static final String _Originator = "__ORIGINATOR";
   public static final String MOBILE_CLIENT_SECURITY = "MobileClientSecurity";
   public static final String CLIENT_PROFILE_VALUE = "ClientProfileValue";
   public static final String MOBILE_APPLICATION_PARAM = "MobileApplicationParam";
   public static final String MOBILE_USER_INFO = "MobileUserInfo";
   public static final int MAX_IN_ROWS = 50;
   public static final String DATE_FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss";
   public static final String FORCE_CLEAR_CACHE_VERSION = "0001-01-01 01:01:01";

   private static final String STMT_CHECK_VIEW
           = "DECLARE "
           + "   temp NUMBER; "
           + "BEGIN "
           + "   SELECT 1 INTO temp FROM (@VIEW_NAME@) WHERE 1=2; "
           + "EXCEPTION "
           + "  WHEN no_data_found THEN "
           + "     null; "
           + "  WHEN OTHERS THEN "
           + "     null; "
           + "END;";

   private static final double[] compressionRatios = {0.8, 0.6, 0.4};

   public static Date getFormattedDateObj(Date date) {
      try {
         SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_PATTERN);
         return sdf.parse(sdf.format(date));
      } catch (ParseException ex) {
         LOG.debug("Date formatting error, &1", ex.getMessage());
         return null;
      }
   }

   public static String mixedCaseToUnderscoreCasing(final String name) {
      return mixedCaseToUnderscoreCasing(name, false);
   }

   public static String mixedCaseToUnderscoreCasing(final String name, boolean noUnderscoreAfter$) {
      char delimiter = '_';
      if (name == null || name.length() == 0) {
         return name;
      }
      final StringBuilder buffer = new StringBuilder();
      buffer.append(name.charAt(0));
      int len = name.trim().length();
      for (int i = buffer.length(); i < len; i++) {
         if ((name.charAt(i) >= 'A' && name.charAt(i) <= 'Z')) {
            if (buffer.charAt(buffer.length() - 1) != delimiter && !(noUnderscoreAfter$ && buffer.charAt(buffer.length() - 1) == '$')) {
               buffer.append(delimiter);
            }
         }
         if (name.charAt(i) == '_') {
            if (i < len - 1) {
               buffer.append(delimiter);
            }
         } else if (name.charAt(i) > '/' || name.charAt(i) == '$') {
            buffer.append(name.charAt(i));
         }
      }
      for (int i = len; i < name.length(); i++) {
         if (name.charAt(i) == ' ') {
            buffer.append(name.charAt(i));
         }
      }
      return buffer.toString();
   }


   /**
    * Performs a call to database to check if a table/view of this record is installed and update meta cache with the information.
    *
    * @param entityRecord
    * @return true if the specified table is installed, false otherwise
    * @throws IfsException
    */
   public static boolean hasInstalled(FndAbstractRecord entityRecord) throws IfsException {
      if (entityRecord instanceof IDynamicDocumentUtilities) {
         FndmobMetaDataCache.RecordMeta meta = ((IDynamicDocumentUtilities) entityRecord).getRecordMeta();
         if (meta.hasInstalled() == null) {
            if (meta.viewMeta.getTable() != null) {
               if (MobileUtilities.checkIfViewInstalled(meta.viewMeta.getTable())) {
                  meta.setHasInstalled(Boolean.TRUE);
               } else {
                  meta.setHasInstalled(Boolean.FALSE);
               }
            } else {
               meta.setHasInstalled(Boolean.FALSE);
            }
         }
         return meta.hasInstalled();
      }
      return true;
   }

   /**
    * Performs a call to database to check if a view is installed.
    *
    * @param viewName
    * @return true if the specified table is installed, false otherwise
    * @throws SystemException
    */
   public static boolean checkIfViewInstalled(String viewName) throws SystemException {
      LOG.info("Checking if table &1 is installed (FNDMOB).", viewName);
      try {
         String sql = STMT_CHECK_VIEW.replaceAll("@VIEW_NAME@", viewName);
         FndPlsqlAccess plsqlAccess = new FndPlsqlAccess();
         plsqlAccess.execute(sql, new FndRecord());
         return true;
      } catch (IfsException e) {
         if (e instanceof SystemException) {
            LOG.debug("Exception = &1", e);
            //Most likely the table or view does not exist error
            String error = e.getMessage();
            if (error != null && (error.contains("ORA-00942"))) {
               LOG.info("Table &1 is NOT installed.", viewName);
               return false;
            }
            //Most likely the table or view does grant to IFSSYS
            if (error != null && (error.contains("ORA-06550"))) {
               LOG.info("Table &1 is NOT granted to IFSSYS.", viewName);
               return false;
            }
         }
         throw new SystemException(e, "CHKPKGINST:Failed to check if view &1 is installed: &2", viewName, e.toString());
      }
   }


   /**
    * Get table/view of this record
    */
   public static String getEntityView(FndAbstractRecord entityRecord) throws IfsException {
      if (entityRecord instanceof IDynamicDocumentUtilities) {
         FndmobMetaDataCache.RecordMeta meta = ((IDynamicDocumentUtilities) entityRecord).getRecordMeta();
         return meta.viewMeta.getTable();
      }
      return null;
   }

   // Only assign first level simple attributes
   public static void assignValues(final FndAbstractRecord from, final FndAbstractRecord to) {
      FndAttribute.Iterator attributes = from.attributes();
      while (attributes.hasNext()) {
         FndAttribute attr = attributes.next();
         int pos = FndRecordInternals.getAttributeIndex(to, attr.getName());
         if (pos >= 0) {
            if(attr.exist() && attr.isSet()) {
               FndAttributeInternals.internalSetValue(to.getAttribute(pos), FndAttributeInternals.internalGetValue(attr));
            }
         } else {
            FndRecordInternals.add(to, attr);
         }
      }
   }

   private static byte[] bufferedImage2ByteArray(BufferedImage bufferedResizedImage, String outputImageFormat) throws IOException {
      byte[] resizedImage;
      try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
         ImageIO.write(bufferedResizedImage, outputImageFormat, baos);
         baos.flush();
         resizedImage = baos.toByteArray();
      }
      return resizedImage;
   }

   /**
    * Return list of comma separated entity name list based on batchSyncEntity document array
    *
    * @param entityArray
    * @return
    */
   public static String getEntityList(BatchSyncEntityArray entityArray) {
      ArrayList resultEntityArray = new ArrayList();
      StringBuilder resultEntity = new StringBuilder("");
      for (int i = 0; i < entityArray.size(); i++) {
         String entityName = entityArray.get(i).entity.getValue();
         if (!resultEntityArray.contains(entityName)) {
            resultEntityArray.add(entityName);
            if (resultEntity.length() > 0) {
               resultEntity.append(",");
            }
            resultEntity.append(entityName);
         }
      }
      return resultEntity.toString();
   }

   /**
    * Return list of comma separated entity name list based on batchSyncEntity document array
    *
    * @param taskArray
    * @return
    */
   public static String getPushEntityList(MobilePushSyncTaskLineArray taskArray) {
      ArrayList resultEntityArray = new ArrayList();
      StringBuilder resultEntity = new StringBuilder("");
      for (int i = 0; i < taskArray.size(); i++) {
         String entityName = taskArray.get(i).entity.getValue();
         if (!resultEntityArray.contains(entityName)) {
            resultEntityArray.add(entityName);
            if (resultEntity.length() > 0) {
               resultEntity.append(",");
            }
            resultEntity.append(entityName);
         }
      }
      return resultEntity.toString();
   }

   /**
    * Return list of comma separated entity name list based on OnDemandSyncTaskLine document array
    *
    * @param taskArray
    * @return
    */
   public static String getOnDemandEntityList(MobileOnDemandSyncTaskLineArray taskArray) {
      ArrayList resultEntityArray = new ArrayList();
      StringBuilder resultEntity = new StringBuilder("");
      for (int i = 0; i < taskArray.size(); i++) {
         String entityName = taskArray.get(i).entity.getValue();
         if (!resultEntityArray.contains(entityName)) {
            resultEntityArray.add(entityName);
            if (resultEntity.length() > 0) {
               resultEntity.append(",");
            }
            resultEntity.append(entityName);
         }
      }
      return resultEntity.toString();
   }

   private static String getImageFormat(byte[] imageBytes) throws IOException {
      final ByteArrayInputStream bStream = new ByteArrayInputStream(imageBytes);

      final ImageInputStream imgStream = ImageIO.createImageInputStream(bStream);
      final Iterator<ImageReader> iter = ImageIO.getImageReaders(imgStream);

      final ImageReader imgReader = iter.next();

      return imgReader.getFormatName().toLowerCase();
   }

   /**
    * *
    * Format the top of the call stack down to and including all methods in the stopAtClassName class.
    *
    * @param ex
    * @param stopAtClassName
    * @return
    */
   public static String getTopOfStackTrace(Exception ex, String stopAtClassName) {
      StringBuffer stackTrace = new StringBuffer();
      StackTraceElement[] elements = ex.getStackTrace();
      boolean classFound = false;
      if (elements != null) {
         for (int i = 0; i < elements.length; i++) {
            StackTraceElement element = elements[i];
            if (element.getClassName().equals(stopAtClassName)) {
               classFound = true;
            } else if (classFound) {
               break;
            }
            stackTrace.append(System.lineSeparator())
                    .append("   at ")
                    .append(element.getClassName())
                    .append(".")
                    .append(element.getMethodName())
                    .append("(")
                    .append(element.getFileName())
                    .append(":")
                    .append(element.getLineNumber())
                    .append(")");
         }
      }
      return stackTrace.toString();
   }



   /*
    * Prepare Sync Request condition befor execute sync call
    */
   public static void prepareSyncRequest(FndQueryRecord queryCond) throws IfsException {
      //exclude arrays and aggregates from the sync request
      FndAbstractRecord condition = queryCond.condition.getRecord();
      FndAttribute.Iterator attributes = condition.attributes();
      while (attributes.hasNext()) {
         FndAttribute attr = attributes.next();
         if (attr.isCompound()) {
            attr.exclude();
         }
      }
   }

   public static String recordStateToTransactionType(final FndRecordState recordState) throws IfsException {
      if (recordState == FndRecordState.MODIFIED_RECORD) {
         return "UPDATE";
      } else if (recordState == FndRecordState.NEW_RECORD) {
         return "INSERT";
      } else if (recordState == FndRecordState.QUERY_RECORD) {
         return "INSERT";
      } else if (recordState == FndRecordState.REMOVED_RECORD) {
         return "DELETE";
      } else {
         return "OTHER";
      }
   }


   public static String matchingKeyName(FndAbstractRecord record, FndCompoundReference reference) {
      if(record.getPrimaryKey()==null) {
         return null;
      }
      boolean primaryKeyColumnFound = false;
      for(int i=0; i<reference.getOwnAttributeCount(); i++) {
         FndAttribute attr = reference.getOwnAttribute(i);
         if(attr.isKey()) {
            //if we have found a primary key column before and this column is a regular column then the reference is a potential parent key.
            if(!primaryKeyColumnFound && i>0) {
               return "PARENT_KEYS";
            }
            primaryKeyColumnFound = true;
         }
         //if we have found a primary key column before and this column is a regular column then the reference is a potential parent key.
         else if(!attr.isKey() && primaryKeyColumnFound) {
            return "PARENT_KEYS";
         }
      }
      if(primaryKeyColumnFound) {
         //if all primary keys included in the reference then use the primary key to filter. This will be the case for e.g. references.
         return (record.getPrimaryKey().getOwnAttributeCount()==reference.getOwnAttributeCount()) ? "PRIMARY_KEYS" : "PARENT_KEYS";
      }
      return null;
   }

   public static String formatEntityAndKeys(FndAbstractRecord record, boolean usePrimaryKey, boolean includeValues) {
      StringBuilder str = new StringBuilder();
      String entity = record.getEntity();
      int startIndex = 0;
      int endIndex = entity.length();
      if (entity.contains(".")) {
         startIndex = entity.lastIndexOf(".") + 1;
      }
      if(entity.endsWith("$")) {
         endIndex = endIndex-1;
      }
      if(startIndex>0 || endIndex<entity.length()) {
         entity = entity.substring(startIndex, endIndex);
      }
      str.append(entity).append("@");
      FndCompoundReference keys = usePrimaryKey ? record.getPrimaryKey() : record.getParentKey();
      if(keys!=null) {
         for(int i=0; i < keys.getOwnAttributeCount(); i++) {
            FndAttribute attr = keys.getOwnAttribute(i);
            if(!attr.isNull()) {
               str.append(attr.getName());
               if(includeValues) {
                  str.append("=").append(attr.toString());
               }
               str.append("^");
            }
         }
      }
      return str.toString();
   }



   //merge attribute values to List types in the target. Used to create IN conditions in the push functionality.
   //records have already been identified to have the same set of keys.
   //the method returns false if the existing List already contain more than MAX_DETAIL_ROWS values at which point the calling method should create a new record in the query list..
   //don't set MAX_DETAIL_ROWS to less than 2 during testing or you'll get exceptions.
   private static final int MAX_DETAIL_ROWS = 50;
   public static boolean mergeRecordsForPush(FndAbstractRecord target, FndAbstractRecord source) {
      FndCompoundReference targetKeys = target.getParentKey();
      FndCompoundReference sourceKeys = source.getParentKey();
      for(int i=0; i<targetKeys.getOwnAttributeCount(); i++) {
         Object targetValue = FndAttributeInternals.internalGetValue(targetKeys.getOwnAttribute(i));
         Object sourceValue = FndAttributeInternals.internalGetValue(sourceKeys.getOwnAttribute(i));
         //when we get multiple values we replace the single value with a List.
         if(!(targetValue instanceof List)) {
            List<Object> list = new ArrayList<Object>();
            list.add(targetValue);
            targetValue = list;
            FndAttributeInternals.internalSetValue(targetKeys.getOwnAttribute(i), targetValue);
         }
         if(((List)targetValue).size()>=MAX_DETAIL_ROWS) {
            return false;
         }
         ((List<Object>)targetValue).add(sourceValue);
      }
      return true;
   }

   public static String getErrorMessage(Exception ex) {
      IfsException rootException = getRootCauseException(ex);
      return rootException != null ? rootException.getMessage(): ex.getMessage();
   }

   public static IfsException getRootCauseException(Exception ex){
      IfsException rootException = ex instanceof IfsException? (IfsException)ex: null;
      while ((rootException != null) && rootException.getCause() instanceof IfsException){
         rootException = (IfsException) rootException.getCause();
      }
      return rootException;
   }

   public static String extractId(String message) {
      Pattern p;
      Matcher m;
      if (message.startsWith("ORA-")) {
         p = Pattern.compile("^ORA-\\d*: ?(\\w*\\.\\w*): ?.*");
      } else {
         p = Pattern.compile("^([A-Z0-9_]*):");
      }
      m = p.matcher(message);
      return m.find() ? m.group(1) : "";
   }
}
