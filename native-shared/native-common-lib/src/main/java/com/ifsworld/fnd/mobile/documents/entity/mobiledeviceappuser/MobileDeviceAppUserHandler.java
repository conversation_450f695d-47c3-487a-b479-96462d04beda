/*
 * ==================================================================================
 * File:              MobileDeviceAppUserHandler.java
 * Entity:            MobileDeviceAppUser
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobiledeviceappuser;

import ifs.fnd.base.IfsException;
import ifs.fnd.record.FndEntityState;
import ifs.fnd.record.FndQueryRecord;
import ifs.fnd.sf.storage.FndEntityHandler;
import ifs.fnd.sf.storage.FndSqlStorage;

/**
 * Implementation of the <code>MobileDeviceAppUserHandler</code> handler.
 */
public class MobileDeviceAppUserHandler extends FndEntityHandler {

   /**
    * <p><b>Remarks:</b>
    * @param MobileDeviceAppUser value
    * @param MobileDeviceAppUser condition
    * @return void
    * @throws IfsException
    */
   public void batchSave(MobileDeviceAppUserEntity value, MobileDeviceAppUserEntity condition, FndEntityState.Enum targetState) throws IfsException {
      beforeCall("batchSave");
      try {
         super.batchSave(value, condition, targetState);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param MobileDeviceAppUserEntityArray mobileDeviceAppUsers
    * @return MobileDeviceAppUserEntityArray
    * @throws IfsException
    */
   public MobileDeviceAppUserEntityArray bulkGet(MobileDeviceAppUserEntityArray mobileDeviceAppUsers) throws IfsException {
      beforeCall("bulkGet");
      try {
         return (MobileDeviceAppUserEntityArray) super.bulkGet(mobileDeviceAppUsers);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param MobileDeviceAppUserArray mobileDeviceAppUsers
    * @return void
    * @throws IfsException
    */
   public void bulkSave(final MobileDeviceAppUserEntityArray mobileDeviceAppUsers) throws IfsException {
      beforeCall("bulkSave");
      try {
         super.bulkSave(mobileDeviceAppUsers);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param MobileDeviceAppUserEntity mobileDeviceAppUser
    * @return FndBoolean
    * @throws IfsException
    */
   public boolean exist(MobileDeviceAppUserEntity mobileDeviceAppUser) throws IfsException {
      beforeCall("exist");
      try {
         return super.exist(mobileDeviceAppUser);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param MobileDeviceAppUserEntity mobileDeviceAppUser
    * @return MobileDeviceAppUserEntity
    * @throws IfsException
    */
   public MobileDeviceAppUserEntity get(MobileDeviceAppUserEntity mobileDeviceAppUser) throws IfsException {
      beforeCall("get");
      try {
         return (MobileDeviceAppUserEntity) super.get(mobileDeviceAppUser);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param MobileDeviceAppUserEntity mobileDeviceAppUser
    * @return MobileDeviceAppUserEntity
    * @throws IfsException
    */
   public MobileDeviceAppUserEntity populate(MobileDeviceAppUserEntity mobileDeviceAppUser) throws IfsException {
      beforeCall("populate");
      try {
         return (MobileDeviceAppUserEntity) super.populate(mobileDeviceAppUser);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param MobileDeviceAppUser mobileDeviceAppUser
    * @return void
    * @throws IfsException
    */
   public void prepare(final MobileDeviceAppUserEntity mobileDeviceAppUser) throws IfsException {
      beforeCall("prepare");
      try {
         super.getDefaults(mobileDeviceAppUser);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param FndQueryRecord condition
    * @return MobileDeviceAppUserEntityArray
    * @throws IfsException
    */
   public final MobileDeviceAppUserEntityArray query(FndQueryRecord condition) throws IfsException {
      beforeCall("query");
      try {
         MobileDeviceAppUserEntityArray array = (MobileDeviceAppUserEntityArray) condition.condition.getRecord().newArrayInstance();
         super.query(condition, array);
         return array;
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param FndQueryRecord condition
    * @return MobileDeviceAppUserQueryCursor
    * @throws IfsException
    */
   public final MobileDeviceAppUserQueryCursor queryCursor(FndQueryRecord condition) throws IfsException {
      beforeCall("queryCursor");
      try {
         FndSqlStorage storage = new FndSqlStorage();
         return new MobileDeviceAppUserQueryCursor(storage.query(condition), this);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param MobileDeviceAppUser MobileDeviceAppUser
    * @return void
    * @throws IfsException
    */
   public void save(final MobileDeviceAppUserEntity mobileDeviceAppUser) throws IfsException {
      beforeCall("save");
      try {
         super.save(mobileDeviceAppUser);
      } finally {
         afterCall();
      }
   }
   private static final int METHOD_IMPLEMENTATION_META = METHOD_META_QUERY;

   /**
    * Fetch method implementation meta data. This is a list of flags for the
    * various standard entity handler methods (Query, save, etc). A value of
    * one means framework implementation whereas zero correspons to unknown
    * or custom implementation.
    * @returns Meta data for Method Implementations
    */
   public static int getMethodImplementationMeta() {
      return METHOD_IMPLEMENTATION_META;
   }
}