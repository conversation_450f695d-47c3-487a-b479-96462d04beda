/*
 * ==================================================================================
 * File:              MobileDeviceLocationArray.java
 * Software Package:  MobileClientRuntime
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents;

import ifs.fnd.base.ParseException;
import ifs.fnd.base.SystemException;
import ifs.fnd.record.*;
import ifs.fnd.record.serialization.FndTokenReader;

/**
 * Array of <code>MobileDeviceLocation</code>.
 */
public class MobileDeviceLocationArray extends com.ifsworld.fnd.mobile.documents.entity.mobiledevicelocation.MobileDeviceLocationEntityArray {

   public MobileDeviceLocationArray() {
      super();
   }

   public MobileDeviceLocationArray(FndAttributeMeta meta) {
      super(meta);
   }

   public MobileDeviceLocationArray(FndAttributeMeta meta, FndCompoundReference parentKeyInParent) {
      super(meta, parentKeyInParent);
   }

   public boolean add(MobileDeviceLocation record) {
      return internalAdd(record);
   }

   public void add(int index, MobileDeviceLocation record) {
      internalAdd(index, record);
   }

   public void add(MobileDeviceLocationArray array) {
      internalAdd(array);
   }

   public void assign(MobileDeviceLocationArray from) throws SystemException {
      assign(from, true);
   }

   public boolean contains(MobileDeviceLocation record) {
      return internalContains(record);
   }

   public MobileDeviceLocation firstElement() {
      return (MobileDeviceLocation)internalFirstElement();
   }

   public MobileDeviceLocation get(int index) {
      return (MobileDeviceLocation)internalGet(index);
   }

   public int indexOf(MobileDeviceLocation record) {
      return internalIndexOf(record);
   }

   public MobileDeviceLocation lastElement() {
      return (MobileDeviceLocation)internalLastElement();
   }

   public int lastIndexOf(MobileDeviceLocation record) {
      return internalLastIndexOf(record);
   }

   public MobileDeviceLocation remove(int index) {
      return (MobileDeviceLocation)internalRemove(index);
   }

   public MobileDeviceLocation set(int index, MobileDeviceLocation record) {
      return (MobileDeviceLocation)internalSet(index, record);
   }

   public FndDetailCondition createDetailCondition(MobileDeviceLocation record, FndQueryReferenceCategory category) {
      return createCondition(record, category);
   }

   @Override
   public FndAbstractRecord newRecord() {
      return new MobileDeviceLocation();
   }

   @Override
   protected FndAbstractRecord newRecord(FndTokenReader stream) throws ParseException {
      MobileDeviceLocation record = new MobileDeviceLocation();
      record.parse(stream);
      return record;
   }

   protected FndAttribute newAttribute(FndAttributeMeta meta) {
       return new MobileDeviceLocationArray(meta);
   }
}
