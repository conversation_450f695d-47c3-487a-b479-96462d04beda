/*
 * ==================================================================================
 * File:              MobileAppMethodRefEntityArray.java
 * Entity:            MobileAppMethodRefEntity
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobileappmethodref;

import ifs.fnd.record.FndAbstractArray;
import ifs.fnd.record.FndAttributeMeta;
import ifs.fnd.record.FndCompoundReference;

/**
 * Array of <code>MobileAppMethodRefEntity</code>.
 */
public abstract class MobileAppMethodRefEntityArray extends FndAbstractArray {

   protected MobileAppMethodRefEntityArray() {
      super();
   }

   protected MobileAppMethodRefEntityArray(FndAttributeMeta meta) {
      super(meta);
   }

   protected MobileAppMethodRefEntityArray(FndAttributeMeta meta, FndCompoundReference parentKeyInParent) {
      super(meta, parentKeyInParent);
   }
}
