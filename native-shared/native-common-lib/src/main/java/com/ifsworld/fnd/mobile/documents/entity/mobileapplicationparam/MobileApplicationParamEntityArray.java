/*
 * ==================================================================================
 * File:              MobileApplicationParamEntityArray.java
 * Entity:            MobileApplicationParamEntity
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobileapplicationparam;

import ifs.fnd.record.FndAbstractArray;
import ifs.fnd.record.FndAttributeMeta;
import ifs.fnd.record.FndCompoundReference;

/**
 * Array of <code>MobileApplicationParamEntity</code>.
 */
public abstract class MobileApplicationParamEntityArray extends FndAbstractArray {

   protected MobileApplicationParamEntityArray() {
      super();
   }

   protected MobileApplicationParamEntityArray(FndAttributeMeta meta) {
      super(meta);
   }

   protected MobileApplicationParamEntityArray(FndAttributeMeta meta, FndCompoundReference parentKeyInParent) {
      super(meta, parentKeyInParent);
   }
}
