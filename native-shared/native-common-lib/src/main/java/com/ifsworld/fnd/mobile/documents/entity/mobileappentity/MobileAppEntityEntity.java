/*
 * ==================================================================================
 * File:              MobileAppEntityEntity.java
 * Entity:            MobileAppEntity
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobileappentity;

import ifs.fnd.base.IfsException;
import ifs.fnd.base.SystemException;
import ifs.fnd.record.*;

public abstract class MobileAppEntityEntity extends FndLUEntityView {


   public static final FndRecordMeta viewMeta = new FndRecordMeta("", "MOBILEAPPENTITY", "MOBILE_APP_ENTITY", "MOBILE_APP_ENTITY", "MobileAppEntity", "MOBILE_APP_ENTITY_API", "MOBILE_APP_ENTITY_TAB").setViewClassName("ifs.entity.mobileappentity.MobileAppEntityEntity").setTermPath("MobileAppEntity.MobileAppEntity");
   public final FndText appName = _newAppName((FndText) null);
   public final FndText appVersion = _newAppVersion((FndText) null);
   public final FndText entity = _newEntity((FndText) null);
   public final Reference primaryKey = new Reference(Meta.primaryKey, appName, appVersion, entity);
   public final ParentReferenceMobileApplicationVersion parentKeyMobileApplicationVersion = new ParentReferenceMobileApplicationVersion(Meta.parentKeyMobileApplicationVersion, appName, appVersion);

   protected MobileAppEntityEntity(FndRecordMeta meta) {
      super(meta);
      add(appName);
      add(appVersion);
      add(entity);
      add(primaryKey);
      add(parentKeyMobileApplicationVersion);
   }

   public void assign(MobileAppEntityEntity from) throws SystemException {
      super.assign(from);
   }

   public void transformTo(final MobileAppEntityEntity to) throws SystemException {
      transformView(to);
   }

   public static class Reference extends FndCompoundReference {

      public Reference(FndCompoundReferenceMeta ref, FndText appName, FndText appVersion, FndText entity) {
         super(ref);
         add(appName);
         add(appVersion);
         add(entity);
      }

      public void assign(Reference ref) throws IfsException {
         protectedAssign(ref);
      }

      public boolean isEqualTo(Reference ref) {
         return protectedIsEqualTo(ref);
      }
   }

   public static class ParentReferenceMobileApplicationVersion extends FndCompoundReference {

      public ParentReferenceMobileApplicationVersion(FndCompoundReferenceMeta ref, FndText appName, FndText appVersion) {
         super(ref);
         add(appName);
         add(appVersion);
      }

      public void assign(ParentReferenceMobileApplicationVersion ref) throws IfsException {
         protectedAssign(ref);
      }

      public boolean isEqualTo(ParentReferenceMobileApplicationVersion ref) {
         return protectedIsEqualTo(ref);
      }
   }

   protected static final FndText _newAppName(FndText type) {
      return new FndText(Meta.appName);
   }

   protected static final FndText _newAppVersion(FndText type) {
      return new FndText(Meta.appVersion);
   }

   protected static final FndText _newEntity(FndText type) {
      return new FndText(Meta.entity);
   }

   protected static final FndText _newViewName(FndText type) {
      return new FndText(Meta.viewName);
   }

   protected static final FndText _newPackageName(FndText type) {
      return new FndText(Meta.packageName);
   }

   protected static final FndText _newLuName(FndText type) {
      return new FndText(Meta.luName);
   }

   protected static final FndText _newTableName(FndText type) {
      return new FndText(Meta.tableName);
   }

   protected static final FndText _newQueryStatement(FndText type) {
      return new FndText(Meta.queryStatement, FndText.CLOB);
   }

   protected static final FndBoolean _newInsertAllowed(FndBoolean type) {
      return new FndBooleanString(Meta.insertAllowed, "TRUE", "FALSE");
   }

   protected static final FndBoolean _newUpdateAllowed(FndBoolean type) {
      return new FndBooleanString(Meta.updateAllowed, "TRUE", "FALSE");
   }

   protected static final FndBoolean _newDeleteAllowed(FndBoolean type) {
      return new FndBooleanString(Meta.deleteAllowed, "TRUE", "FALSE");
   }

   protected static final FndText _newDefaultWhere(FndText type) {
      return new FndText(Meta.defaultWhere, FndText.CLOB);
   }

   protected static final FndBoolean _newAddedByCustomer(FndBoolean type) {
      return new FndBooleanString(Meta.addedByCustomer, "TRUE", "FALSE");
   }

   protected static final FndText _newTransactionGrouping(FndText type) {
      return new FndText(Meta.transactionGrouping);
   }

   protected static final FndText _newObjversionFormat(FndText type) {
      return new FndText(Meta.objversionFormat);
   }

   protected static final FndBoolean _newIsSystemEntity(FndBoolean type) {
      return new FndBooleanString(Meta.isSystemEntity, "TRUE", "FALSE");
   }

   protected static final FndBoolean _newObjectConnectionProvider(FndBoolean type) {
      return new FndBooleanString(Meta.objectConnectionProvider, "TRUE", "FALSE");
   }

   protected static final FndText _newLuNameColumn(FndText type) {
      return new FndText(Meta.luNameColumn);
   }

   protected static final FndText _newKeyRefColumn(FndText type) {
      return new FndText(Meta.keyRefColumn);
   }

   protected static final FndText _newUserFilterName(FndText type) {
      return new FndText(Meta.userFilterName);
   }

   protected static final FndText _newUserFilterAttrNames(FndText type) {
      return new FndText(Meta.userFilterAttrNames);
   }

   protected static final FndBoolean _newLanguageDependent(FndBoolean type) {
      return new FndBooleanString(Meta.languageDependent, "TRUE", "FALSE");
   }

   protected static final FndText _newCfViewName(FndText type) {
      return new FndText(Meta.cfViewName);
   }

   protected static final FndText _newPushCustomFn(FndText type) {
      return new FndText(Meta.pushCustomFn);
   }

   protected static final FndText _newTimestampTZRef(FndText type) {
      return new FndText(Meta.timestampTZRef);
   }

   protected static final FndBoolean _newEffectivePushDetailsOnMod(FndBoolean type) {
      return new FndBooleanString(Meta.effectivePushDetailsOnMod, "TRUE", "FALSE");
   }

   public static class Meta {

      public static final FndAttributeMeta appName = new FndAttributeMeta(viewMeta, "APP_NAME", "APP_NAME", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 200).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta appVersion = new FndAttributeMeta(viewMeta, "APP_VERSION", "APP_VERSION", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 20).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta entity = new FndAttributeMeta(viewMeta, "ENTITY", "ENTITY", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 100).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta viewName = new FndAttributeMeta(viewMeta, "VIEW_NAME", "VIEW_NAME", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 200);
      public static final FndAttributeMeta packageName = new FndAttributeMeta(viewMeta, "PACKAGE_NAME", "PACKAGE_NAME", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 200);
      public static final FndAttributeMeta luName = new FndAttributeMeta(viewMeta, "LU_NAME", "LU_NAME", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 100);
      public static final FndAttributeMeta tableName = new FndAttributeMeta(viewMeta, "TABLE_NAME", "TABLE_NAME", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 200);
      public static final FndAttributeMeta queryStatement = new FndAttributeMeta(viewMeta, "QUERY_STATEMENT", "QUERY_STATEMENT", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta insertAllowed = new FndAttributeMeta(viewMeta, "INSERT_ALLOWED", "INSERT_ALLOWED", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta updateAllowed = new FndAttributeMeta(viewMeta, "UPDATE_ALLOWED", "UPDATE_ALLOWED", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta deleteAllowed = new FndAttributeMeta(viewMeta, "DELETE_ALLOWED", "DELETE_ALLOWED", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta defaultWhere = new FndAttributeMeta(viewMeta, "DEFAULT_WHERE", "DEFAULT_WHERE", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta addedByCustomer = new FndAttributeMeta(viewMeta, "ADDED_BY_CUSTOMER", "ADDED_BY_CUSTOMER", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta transactionGrouping = new FndAttributeMeta(viewMeta, "TRANSACTION_GROUPING", "TRANSACTION_GROUPING", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 2000);
      public static final FndAttributeMeta objversionFormat = new FndAttributeMeta(viewMeta, "OBJVERSION_FORMAT", "OBJVERSION_FORMAT", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 200);
      public static final FndAttributeMeta isSystemEntity = new FndAttributeMeta(viewMeta, "IS_SYSTEM_ENTITY", "IS_SYSTEM_ENTITY", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta objectConnectionProvider = new FndAttributeMeta(viewMeta, "OBJECT_CONNECTION_PROVIDER", "OBJECT_CONNECTION_PROVIDER", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta luNameColumn = new FndAttributeMeta(viewMeta, "LU_NAME_COLUMN", "LU_NAME_COLUMN", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 35);
      public static final FndAttributeMeta keyRefColumn = new FndAttributeMeta(viewMeta, "KEY_REF_COLUMN", "KEY_REF_COLUMN", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 35);
      public static final FndAttributeMeta userFilterName = new FndAttributeMeta(viewMeta, "USER_FILTER_NAME", "USER_FILTER_NAME", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 100);
      public static final FndAttributeMeta userFilterAttrNames = new FndAttributeMeta(viewMeta, "USER_FILTER_ATTR_NAMES", "USER_FILTER_ATTR_NAMES", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 4000);
      public static final FndAttributeMeta languageDependent = new FndAttributeMeta(viewMeta, "LANGUAGE_DEPENDENT", "LANGUAGE_DEPENDENT", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta cfViewName = new FndAttributeMeta(viewMeta, "CF_VIEW_NAME", "CF_VIEW_NAME", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 200);
      public static final FndAttributeMeta pushCustomFn = new FndAttributeMeta(viewMeta, "PUSH_CUSTOM_FN", "PUSH_CUSTOM_FN", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 200);
      public static final FndAttributeMeta timestampTZRef = new FndAttributeMeta(viewMeta, "TIMESTAMP_T_Z_REF", "TIMESTAMP_T_Z_REF", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 500);
      public static final FndAttributeMeta effectivePushDetailsOnMod = new FndAttributeMeta(viewMeta, "EFFECTIVE_PUSH_DETAILS_ON_MOD", "EFF_PUSH_DETAILS_ON_MOD", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);

      public static final FndCompoundReferenceMeta primaryKey = new FndCompoundReferenceMeta(viewMeta, "ABSTRACT_MOBILE_APP_ENTITY_KEY", new FndAttributeMeta[]{appName, appVersion, entity}, viewMeta, true);
      public static final FndCompoundReferenceMeta parentKeyMobileApplicationVersion = new FndCompoundReferenceMeta(viewMeta, "PARENT_KEY_MOBILE_APPLICATION_VERSION", new FndAttributeMeta[] {appName, appVersion}, com.ifsworld.fnd.mobile.documents.entity.mobileapplicationversion.MobileApplicationVersionEntity.viewMeta);
   }
}
