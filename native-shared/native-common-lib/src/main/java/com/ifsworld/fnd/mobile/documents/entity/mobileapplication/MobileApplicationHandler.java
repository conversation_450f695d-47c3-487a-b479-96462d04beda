/*
 * ==================================================================================
 * File:              MobileApplicationHandler.java
 * Entity:            MobileApplication
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobileapplication;

import ifs.fnd.base.IfsException;
import ifs.fnd.record.FndEntityState;
import ifs.fnd.record.FndQueryRecord;
import ifs.fnd.sf.storage.FndEntityHandler;
import ifs.fnd.sf.storage.FndSqlStorage;

/**
 * Implementation of the <code>MobileApplicationHandler</code> handler.
 */
public class MobileApplicationHandler extends FndEntityHandler {

   /**
    * <p><b>Remarks:</b>
    * @param MobileApplication value
    * @param MobileApplication condition
    * @return void
    * @throws IfsException
    */
   public void batchSave(MobileApplicationEntity value, MobileApplicationEntity condition, FndEntityState.Enum targetState) throws IfsException {
      beforeCall("batchSave");
      try {
         super.batchSave(value, condition, targetState);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param MobileApplicationEntityArray mobileApplications
    * @return MobileApplicationEntityArray
    * @throws IfsException
    */
   public MobileApplicationEntityArray bulkGet(MobileApplicationEntityArray mobileApplications) throws IfsException {
      beforeCall("bulkGet");
      try {
         return (MobileApplicationEntityArray) super.bulkGet(mobileApplications);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param MobileApplicationArray mobileApplications
    * @return void
    * @throws IfsException
    */
   public void bulkSave(final MobileApplicationEntityArray mobileApplications) throws IfsException {
      beforeCall("bulkSave");
      try {
         super.bulkSave(mobileApplications);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param MobileApplicationEntity mobileApplication
    * @return FndBoolean
    * @throws IfsException
    */
   public boolean exist(MobileApplicationEntity mobileApplication) throws IfsException {
      beforeCall("exist");
      try {
         return super.exist(mobileApplication);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param MobileApplicationEntity mobileApplication
    * @return MobileApplicationEntity
    * @throws IfsException
    */
   public MobileApplicationEntity get(MobileApplicationEntity mobileApplication) throws IfsException {
      beforeCall("get");
      try {
         return (MobileApplicationEntity) super.get(mobileApplication);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param MobileApplicationEntity mobileApplication
    * @return MobileApplicationEntity
    * @throws IfsException
    */
   public MobileApplicationEntity populate(MobileApplicationEntity mobileApplication) throws IfsException {
      beforeCall("populate");
      try {
         return (MobileApplicationEntity) super.populate(mobileApplication);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param MobileApplication mobileApplication
    * @return void
    * @throws IfsException
    */
   public void prepare(final MobileApplicationEntity mobileApplication) throws IfsException {
      beforeCall("prepare");
      try {
         super.getDefaults(mobileApplication);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param FndQueryRecord condition
    * @return MobileApplicationEntityArray
    * @throws IfsException
    */
   public final MobileApplicationEntityArray query(FndQueryRecord condition) throws IfsException {
      beforeCall("query");
      try {
         MobileApplicationEntityArray array = (MobileApplicationEntityArray) condition.condition.getRecord().newArrayInstance();
         super.query(condition, array);
         return array;
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param FndQueryRecord condition
    * @return MobileApplicationQueryCursor
    * @throws IfsException
    */
   public final MobileApplicationQueryCursor queryCursor(FndQueryRecord condition) throws IfsException {
      beforeCall("queryCursor");
      try {
         FndSqlStorage storage = new FndSqlStorage();
         return new MobileApplicationQueryCursor(storage.query(condition), this);
      } finally {
         afterCall();
      }
   }

   /**
    * <p><b>Remarks:</b>
    * @param MobileApplication MobileApplication
    * @return void
    * @throws IfsException
    */
   public void save(final MobileApplicationEntity mobileApplication) throws IfsException {
      beforeCall("save");
      try {
         super.save(mobileApplication);
      } finally {
         afterCall();
      }
   }
   private static final int METHOD_IMPLEMENTATION_META = METHOD_META_QUERY;

   /**
    * Fetch method implementation meta data. This is a list of flags for the
    * various standard entity handler methods (Query, save, etc). A value of
    * one means framework implementation whereas zero correspons to unknown
    * or custom implementation.
    * @returns Meta data for Method Implementations
    */
   public static int getMethodImplementationMeta() {
      return METHOD_IMPLEMENTATION_META;
   }
}