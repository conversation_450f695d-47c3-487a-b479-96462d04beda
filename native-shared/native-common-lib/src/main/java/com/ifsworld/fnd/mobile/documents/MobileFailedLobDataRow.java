/*
 * ==================================================================================
 * File:              MobileFailedLobDataRow.java
 * Software Package:  MobileClientRuntime
 * Template Version:  8.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents;

import ifs.fnd.base.SystemException;
import ifs.fnd.record.*;

/**
 * This view represents the <code>MobileFailedLobDataRow</code> entity.
 */
public class MobileFailedLobDataRow extends FndAdvancedQueryView {

   private static final String tableSql = "(SELECT " +
                                           "  transaction_id, " +
                                           "  lob_id " +
                                           "FROM " +
                                           "  &AO.mobile_failed_lob_data )";

   public static final FndRecordMeta viewMeta  = new FndRecordMeta("MOBILECLIENTRUNTIME", "MOBILE_FAILED_LOB_DATA_ROW", tableSql).setViewClassName("ifs.application.mobileclientruntime.MobileFailedLobDataRow").setTermPath("MobileClientRuntime.MobileFailedLobDataRow");

   /**
    * TransactionId
    */
   public final FndText      transactionId = new FndText(Meta.transactionId);
   /**
    * LobId
    */
   public final FndText      lobId         = new FndText(Meta.lobId);

   public MobileFailedLobDataRow() {
      this(viewMeta);
   }

   protected MobileFailedLobDataRow(FndRecordMeta meta) {
      super(meta);
      add(transactionId);
      add(lobId);
   }

   public void assign(MobileFailedLobDataRow from) throws SystemException {
      super.assign(from);
   }

   public void transformTo(final MobileFailedLobDataRow to) throws SystemException {
      transformView(to);
   }

   @Override
   public FndAbstractRecord newInstance() {
      return new MobileFailedLobDataRow();
   }

   @Override
   public FndAbstractArray newArrayInstance() {
      return new MobileFailedLobDataRowArray();
   }

   public static class Meta {
      public static final FndAttributeMeta transactionId = new FndAttributeMeta(viewMeta, "TRANSACTION_ID", "TRANSACTION_ID", FndAttributeMeta.QUERYABLE|FndAttributeMeta.PERSISTENT, 100).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta lobId = new FndAttributeMeta(viewMeta, "LOB_ID", "LOB_ID", FndAttributeMeta.QUERYABLE|FndAttributeMeta.PERSISTENT, 100).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);

   }
}
