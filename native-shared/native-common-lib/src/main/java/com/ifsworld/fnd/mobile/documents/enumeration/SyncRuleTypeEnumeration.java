/*
 * ==================================================================================
 * File:              SyncRuleTypeEnumeration.java
 * Enumeration:       SyncRuleType
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.enumeration;

import ifs.fnd.base.IfsException;
import ifs.fnd.base.ParseException;
import ifs.fnd.base.SystemException;
import ifs.fnd.record.*;

public final class SyncRuleTypeEnumeration extends FndEnumeration {

   //Enumeration entries
   public static final Enum APPLICATION = Enum.APPLICATION;
   public static final Enum SYSTEM = Enum.SYSTEM;

   public SyncRuleTypeEnumeration() {
      super();
   }

   public SyncRuleTypeEnumeration(String name) {
      super(name);
   }

   public SyncRuleTypeEnumeration(String name, Enum value) {
      this(name);
      setValue(value);
   }

   public SyncRuleTypeEnumeration(FndAttributeMeta meta) {
      super(meta);
   }

   @Override
   protected FndAttribute newAttribute(FndAttributeMeta meta) {
      return new SyncRuleTypeEnumeration(meta);
   }

   public void setValue(Enum value) {
      internalSetValue(value);
   }

   public Enum getValue() {
      return (Enum) internalGetValue();
   }

   @Override
   public void parseString(String value) throws ParseException {
      try {
         this.value = Enum.parseString(value);
         this.set();
         this.setExistent();
      } catch (ParseException e) {
         throwIllegalValueException(value);
      }
   }

   @Override
   protected void parseDatabaseString(String dbValue) throws ParseException {
      try {
         this.value = Enum.parseDatabaseString(dbValue);
         this.set();
         this.setExistent();
      } catch (ParseException e) {
         throwIllegalDatabaseValueException(dbValue);
      }
   }

   @Override
   public String toString() {
      Enum val = getValue();
      if (val == null) {
         return null;
      }
      return val.getValue();
   }

   @Override
   protected String toDatabaseString() {
      Enum val = getValue();
      if (val == null) {
         return null;
      }
      return val.getDatabaseValue();
   }

   @Override
   public int compareTo(FndAttribute attr) {
      if (this == attr) {
         return 0;
      }
      if (attr instanceof SyncRuleTypeEnumeration) {
         if (this.isNull() && attr.isNull()) {
            return 0;
         } else if (this.isNull()) {
            return 1;
         } else if (attr.isNull()) {
            return -1;
         } else {
            return getValue().compareTo(((SyncRuleTypeEnumeration) attr).getValue());
         }
      }
      return 1;
   }

   public Condition createEqualCondition(Enum value) {
      return new Condition(this, getEqualQueryOperator(), value);
   }

   public Condition createNotEqualCondition(Enum value) {
      return new Condition(this, getNotEqualQueryOperator(), value);
   }

   public Condition createEqualCondition(SyncRuleTypeEnumeration attr) {
      return new Condition(this, getEqualQueryOperator(), attr);
   }

   public Condition createNotEqualCondition(SyncRuleTypeEnumeration attr) {
      return new Condition(this, getNotEqualQueryOperator(), attr);
   }

   public static FndEnumerationView toEnumerationView() throws IfsException {
      FndEnumerationView view = new FndEnumerationView();
      view.name.setValue("SYNCRULETYPE");
      view.addValue(APPLICATION.value, APPLICATION.displayText);
      view.addValue(SYSTEM.value, SYSTEM.displayText);
      return view;
   }

   public static enum Enum {
      APPLICATION ("Application", "APPLICATION", "Application"),
      SYSTEM ("System", "SYSTEM", "System"),
      ;

      private String value;
      private String dbValue;
      private String displayText;

      private Enum(String value, String dbValue, String displayText) {
         this.value = value;
         this.dbValue = dbValue;
         this.displayText = displayText;
      }

      public String getValue() {
         return value;
      }

      public String getDatabaseValue() {
         return dbValue;
      }

      public String getDisplayText() {
         return displayText;
      }

      @Override
      public String toString() {
         return getValue();
      }

      public static Enum parseString(String value) throws ParseException {
         if (value == null || value.length() == 0) {
            return null;
         }
         for( Enum entry : Enum.values() ) {
            if (entry.getValue().equalsIgnoreCase(value)) {
               return entry;
            }
         }
         throw new ParseException("Illegal Value");
      }

      public static Enum parseDatabaseString(String dbValue) throws ParseException {
         if (dbValue == null || dbValue.length() == 0) {
            return null;
         }
         for( Enum entry : Enum.values() ) {
            if (entry.getDatabaseValue().equalsIgnoreCase(dbValue)) {
               return entry;
            }
         }
         throw new ParseException("Illegal Database Value");
      }
   }

   public static String[] toStringArray(Enum[] enumArr) {
      if (enumArr == null) {
         return null;
      }
      String[] strArr = new String[enumArr.length];
      for (int i = 0; i < strArr.length; i++) {
         if (enumArr[i] != null) {
            strArr[i] = enumArr[i].toString();
         }
      }
      return strArr;
   }

   public static Enum[] toEnumArray(String[] strArr) throws ParseException {
      if (strArr == null) {
         return null;
      }
      Enum[] enumArr = new Enum[strArr.length];
      for (int i = 0; i < enumArr.length; i++) {
         enumArr[i] = Enum.parseString(strArr[i]);
      }
      return enumArr;
   }

   public static class Condition extends FndSimpleCondition {

      /**
       * Framework internal constructor required by Externalizable interface.
       */
      public Condition() {
      }

      private Condition(SyncRuleTypeEnumeration base, FndQueryOperator oper, Enum value) {
         super(base, oper, value);
      }

      private Condition(SyncRuleTypeEnumeration base, FndQueryOperator oper, SyncRuleTypeEnumeration attr) {
         super(base, oper, attr, null);
      }

      /**
       * See the {@link FndSimpleCondition#setNvlFunction(FndAbstractString, String)} method.
       */
      public void setNvlFunction(SyncRuleTypeEnumeration attr, Enum value) throws SystemException {
         protectedSetNvlFunction(attr, value.toString());
      }
   }
}
