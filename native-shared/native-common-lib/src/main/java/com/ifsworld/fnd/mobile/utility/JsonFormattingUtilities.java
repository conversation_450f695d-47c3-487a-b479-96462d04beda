/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.ifsworld.fnd.mobile.utility;

import com.ifsworld.fnd.mobile.common.MobileCommonUtilities;
import com.ifsworld.fnd.mobile.documents.MobileSyncRule;
import com.ifsworld.fnd.mobile.meta.FndMobileAttributeMeta;
import com.ifsworld.fnd.mobile.meta.FndmobTimeZoneHandler;
import com.ifsworld.fnd.mobile.meta.FndmobTimestampAttribute;
import ifs.fnd.base.IfsException;
import ifs.fnd.internal.FndAttributeInternals;
import ifs.fnd.record.*;
import java.io.StringWriter;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
public class JsonFormattingUtilities {

   //Max message length before compress (~50k).
   //KT: This could be added as a configurable param
   public static int MAX_MESSAGE_SIZE = 50000;

   public static void appendJson(StringBuilder json, FndAbstractRecord document, boolean expandDetails, boolean excludeSystemAttributes) {
      json.append("{");
      boolean first = true;
      FndRecordState recordState = document.getState();
      FndAttribute.Iterator attributes = document.attributes();
      while (attributes.hasNext()) {
         first = appendAttribute(json, attributes.next(), first, (recordState==FndRecordState.MODIFIED_RECORD), expandDetails, excludeSystemAttributes);
      }
      json.append("}");
   }

   private static String getJsonValue(FndAttribute attr) {
      if (!attr.hasValue()) {
         return "null";
      } else if (attr instanceof FndBoolean) {
         return ((FndBoolean) attr).booleanValue(false) ? "true" : "false";
      }
      String value;
      if (attr instanceof FndDate) {
         value = ((FndDate) attr).getValue().toInstant().toString();
      } else if (attr instanceof FndTime) {
         value = ((FndTime) attr).getValue().toInstant().toString();
      } else if (attr instanceof FndmobTimestampAttribute) { // Use for timezone support
         value = ((FndmobTimestampAttribute) attr).toTimestamp().toInstant().toString();
      } else if (attr instanceof FndTimestamp) {
         value = ((FndTimestamp) attr).getValue().toInstant().toString();
         // Dirty Fix to convert 0000 to 0001 to support C# client (MOBOFF-13981)
         // https://stackoverflow.com/questions/77430683/converting-java-util-date-01-01-0001-to-java-time-localdate-returns-29-12-0000
         if(value != null){
            value = value.replace("0000-", "0001-");
         }
      } else if (attr instanceof FndAbstractNumber) {
         return attr.toString();
      } else if (attr instanceof FndAbstractArray) {
         StringBuilder json = new StringBuilder();
         appendJson(json, (FndAbstractArray) attr, null, 0, false);
         return json.length() == 0 ? "[]" : json.toString();
      } else if (attr instanceof FndAbstractAggregate) {
         StringBuilder json = new StringBuilder();
         appendJson(json, FndAttributeInternals.internalGetRecord((FndAbstractAggregate) attr), true, false);
         return json.length() == 0 ? "{}" : json.toString();
      } else {
         value = escapeSpecialJSON(attr.toString());
      }
      return "\"" + value + "\"";
   }

   /**
    * Produce a string in double quotes with backslash sequences in all the
    * right places. A backslash will be inserted within </, producing <\/,
    * allowing JSON text to be delivered in HTML. In JSON text, a string cannot
    * contain a control character or an unescaped quote or backslash.
    * @param string
    * A String
    * @return A String correctly formatted for insertion in a JSON text.
    */
   public static String escapeSpecialJSON(String string) {
      StringWriter sw = new StringWriter();
      synchronized (sw.getBuffer()) {
         try {
            if (string == null || string.length() == 0) {
               return "";
            }

            char b;
            char c = 0;
            String hexStr;
            int i;
            int len = string.length();

            for (i = 0; i < len; i += 1) {
               b = c;
               c = string.charAt(i);
               switch (c) {
                  case '\\':
                  case '"':
                     sw.write('\\');
                     sw.write(c);
                     break;
                  case '/':
                     if (b == '<') {
                        sw.write('\\');
                     }
                     sw.write(c);
                     break;
                  case '\b':
                     sw.write("\\b");
                     break;
                  case '\t':
                     sw.write("\\t");
                     break;
                  case '\n':
                     sw.write("\\n");
                     break;
                  case '\f':
                     sw.write("\\f");
                     break;
                  case '\r':
                     sw.write("\\r");
                     break;
                  default:
                     /*
                      * This does not work with the UMA. If we scape Unicode control characters they does not decode correctly.
                     if (c < ' ' || (c >= '\u0080' && c < '\u00a0')
                             || (c >= '\u2000' && c < '\u2100')) {
                        sw.write("\\u");
                        hexStr = Integer.toHexString(c);
                        sw.write("0000", 0, 4 - hexStr.length());
                        sw.write(hexStr);
                     } else {
                        sw.write(c);
                     }
                     */
                     sw.write(c);
               }
            }
            return sw.toString();

         } catch (Exception ignored) {
            // will never happen - we are writing to a string writer
            return "";
         }
      }
   }

   //method returns true if records where added to the result
   private static int appendJson(StringBuilder json, FndAbstractArray array, FndRecordState state, int startIndex, boolean includeAllRecords) {
      boolean first = true;
      int i = startIndex;
      while (i < array.getLength() && (includeAllRecords || json.length() < MAX_MESSAGE_SIZE)) {
         FndAbstractRecord record = FndAttributeInternals.internalGet(array, i);
         FndRecordState recordState = record.getState();
         if (state == null || recordState == state) {
            if (first) {
               json.append("[");
               first = false;
            } else {
               json.append(",");
            }
            appendJson(json, record, false, false);
         }
         i++;
      }
      if (!first) {
         json.append("]");
      }
      return i;
   }

   /**
    *
    * @param documentArray
    * @param state
    * @return Json string of the result set as a hierarchy_select_result or null if there was no data to send
    * @throws IfsException
    */
   public static List<String> arrayToResultSet(FndAbstractArray documentArray, FndRecordState state) throws IfsException {
      List<String> messages = new ArrayList<String>();
      FndAbstractRecord record = documentArray.size() > 0 ? FndAttributeInternals.internalGet(documentArray, 0) : documentArray.getTemplateRecord();
      for (int i = 0; i < documentArray.size();) {
         StringBuilder json = new StringBuilder();
         json.append("{\"sync\":{\"").append(MobileCommonUtilities.toCamelCase(record.getName())).append("\":");
         int size = json.length();
         i = appendJson(json, documentArray, state, i, false);
         //Only add message to queue if records where added to the message
         if (json.length() > size) {
            json.append("}}");
            messages.add(json.toString());
         }
      }
      return messages;
   }

   private static String formatRecordKey(FndAbstractRecord document) {
      StringBuilder key = new StringBuilder();
      FndCompoundReference pk = document.getPrimaryKey();
      if (pk != null) {
         FndAttribute.Iterator attributes = pk.iterator();
         while (attributes.hasNext()) {
            if (key.length() != 0) {
               key.append("^");
            }
            key.append(attributes.next().toString());
         }
      }
      if (key.length() == 0) {
         //if no primary key simply return a unique identifier for the record
         return document.toString();
      }
      return key.toString();
   }

   private static void collectRecords(FndAbstractRecord document, Map<String, Map<String, FndAbstractRecord>> recordSet) {
      String documentKey = formatRecordKey(document);
      String tableName = MobileCommonUtilities.toCamelCase(document.getName());
      Map<String, FndAbstractRecord> tableEntries = recordSet.get(tableName);
      if (tableEntries == null) {
         //first entry in this table
         tableEntries = new HashMap<String, FndAbstractRecord>();
         recordSet.put(tableName, tableEntries);
      }
      if (!tableEntries.containsKey(documentKey)) {
         //first occurance of this record
         tableEntries.put(documentKey, document);
         //loop over details and add them to the record set
         FndAttribute.Iterator details = document.details();
         while (details.hasNext()) {
            FndAttribute attr = details.next();
            if (attr instanceof FndAbstractArray) {
               FndAbstractArray array = (FndAbstractArray) attr;
               for (int i = 0; i < array.size(); i++) {
                  collectRecords(FndAttributeInternals.internalGet(array, i), recordSet);
               }
            } else if (attr instanceof FndAbstractAggregate) {
               FndAbstractAggregate aggregate = (FndAbstractAggregate) attr;
               if (aggregate.hasValue()) {
                  collectRecords(FndAttributeInternals.internalGetRecord(aggregate), recordSet);
               }
            }
         }
      }
   }

   //TODO: JSON formatting for master detail (push messages)
   public static boolean appendAttribute(StringBuilder json, FndAttribute attr, boolean first, boolean appendEmptyValues, boolean expandDetails, boolean excludeSystemAttributes) {
      if (attr instanceof FndCompoundAttribute && !expandDetails) {
         return first;
      }
      if (attr != null && ((attr.exist() && attr.isSet() && appendEmptyValues) || (attr.hasValue())) ) {
         String attributeName = MobileCommonUtilities.toCamelCase(attr.getName());
         if(isSystemAttribute(attributeName) && excludeSystemAttributes){
            return first;
         }
         if(attr.getMeta() instanceof FndMobileAttributeMeta) {
            FndMobileAttributeMeta meta = (FndMobileAttributeMeta)attr.getMeta();
            String clientAttributeName = meta.getClientAttributeName();
            if(clientAttributeName!=null) {
               attributeName = clientAttributeName;
            }
         } else if(attr instanceof FndCompoundAttribute) {
            attributeName = MobileCommonUtilities.toCamelCase(attr.getName());
         }
         if (!first) {
            json.append(",");
         }
         json.append("\"").append(attributeName).append("\":").append(getJsonValue(attr));
         first = false;
      }
      return first;
   }

   private static boolean isSystemAttribute(String attributeName) {
      if(attributeName == null){
         return false;
      }
      return attributeName.equals("ObjVersion")
              || attributeName.equals("ObjId")
              || attributeName.equals("ObjKey");
   }
}

