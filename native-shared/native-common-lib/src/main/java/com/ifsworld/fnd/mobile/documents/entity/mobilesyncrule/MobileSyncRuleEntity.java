/*
 * ==================================================================================
 * File:              MobileSyncRuleEntity.java
 * Entity:            MobileSyncRule
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobilesyncrule;

import com.ifsworld.fnd.mobile.documents.entity.mobileapplication.MobileApplicationEntity;
import com.ifsworld.fnd.mobile.documents.enumeration.SyncDeliveryTypeEnumeration;
import com.ifsworld.fnd.mobile.documents.enumeration.SyncRuleTypeEnumeration;
import ifs.fnd.base.IfsException;
import ifs.fnd.base.SystemException;
import ifs.fnd.record.*;

public abstract class MobileSyncRuleEntity extends FndLUEntityView {


   public static final FndRecordMeta viewMeta = new FndRecordMeta("", "MOBILESYNCRULE", "MOBILE_SYNC_RULE", "MOBILE_SYNC_RULE", "MobileSyncRule", "MOBILE_SYNC_RULE_API", "MOBILE_SYNC_RULE_TAB").setViewClassName("ifs.entity.mobilesyncrule.MobileSyncRuleEntity").setTermPath("MobileSyncRule.MobileSyncRule");
   public final FndText appName = _newAppName((FndText) null);
   public final FndText appVersion = _newAppVersion((FndText) null);
   public final FndText entity = _newEntity((FndText) null);
   public final Reference primaryKey = new Reference(Meta.primaryKey, appName, appVersion, entity);
   public final ParentReferenceMobileApplicationVersion parentKeyMobileApplicationVersion = new ParentReferenceMobileApplicationVersion(Meta.parentKeyMobileApplicationVersion, appName, appVersion);

   protected MobileSyncRuleEntity(FndRecordMeta meta) {
      super(meta);
      add(appName);
      add(appVersion);
      add(entity);
      add(primaryKey);
      add(parentKeyMobileApplicationVersion);
   }

   public void assign(MobileSyncRuleEntity from) throws SystemException {
      super.assign(from);
   }

   public void transformTo(final MobileSyncRuleEntity to) throws SystemException {
      transformView(to);
   }

   public static class Reference extends FndCompoundReference {

      public Reference(FndCompoundReferenceMeta ref, FndText appName, FndText appVersion, FndText entity) {
         super(ref);
         add(appName);
         add(appVersion);
         add(entity);
      }

      public void assign(Reference ref) throws IfsException {
         protectedAssign(ref);
      }

      public boolean isEqualTo(Reference ref) {
         return protectedIsEqualTo(ref);
      }
   }

   public static class ParentReferenceMobileApplicationVersion extends FndCompoundReference {

      public ParentReferenceMobileApplicationVersion(FndCompoundReferenceMeta ref, FndText appName, FndText appVersion) {
         super(ref);
         add(appName);
         add(appVersion);
      }

      public void assign(ParentReferenceMobileApplicationVersion ref) throws IfsException {
         protectedAssign(ref);
      }

      public boolean isEqualTo(ParentReferenceMobileApplicationVersion ref) {
         return protectedIsEqualTo(ref);
      }
   }

   protected static final FndText _newAppName(FndText type) {
      return new FndText(Meta.appName);
   }

   protected static final FndText _newAppVersion(FndText type) {
      return new FndText(Meta.appVersion);
   }

   protected static final FndText _newEntity(FndText type) {
      return new FndText(Meta.entity);
   }

   protected static final FndText _newHandler(FndText type) {
      return new FndText(Meta.handler);
   }

   protected static final FndText _newProjection(FndText type) {
      return new FndText(Meta.projection);
   }

   protected static final FndBoolean _newIsMetaDataDriven(FndBoolean type) {
      return new FndBooleanString(Meta.isMetaDataDriven, "1", "0");
   }

   protected static final SyncRuleTypeEnumeration _newRuleType(SyncRuleTypeEnumeration type) {
      return new SyncRuleTypeEnumeration(Meta.ruleType);
   }

   protected static final FndText _newRuleType(FndText type) {
      return new FndText(Meta.ruleType$);
   }

   protected static final SyncDeliveryTypeEnumeration _newDeliveryMethod(SyncDeliveryTypeEnumeration type) {
      return new SyncDeliveryTypeEnumeration(Meta.deliveryMethod);
   }

   protected static final FndText _newDeliveryMethod(FndText type) {
      return new FndText(Meta.deliveryMethod$);
   }

   protected static final SyncDeliveryTypeEnumeration _newCustomDeliveryMethod(SyncDeliveryTypeEnumeration type) {
      return new SyncDeliveryTypeEnumeration(Meta.customDeliveryMethod);
   }

   protected static final FndText _newCustomDeliveryMethod(FndText type) {
      return new FndText(Meta.customDeliveryMethod$);
   }

   protected static final FndText _newEffectiveDeliveryMethod(FndText type) {
      return new FndText(Meta.effectiveDeliveryMethod);
   }

   protected static final FndText _newChangedBy(FndText type) {
      return new FndText(Meta.changedBy);
   }

   protected static final FndTimestamp _newChangedDate(FndTimestamp type) {
      return new FndTimestamp(Meta.changedDate);
   }

   protected static final FndNumber _newFrequency(FndNumber type) {
      return new FndNumber(Meta.frequency);
   }

   protected static final FndNumber _newDefaultFrequency(FndNumber type) {
      return new FndNumber(Meta.defaultFrequency);
   }

   protected static final FndText _newSchedule(FndText type) {
      return new FndText(Meta.schedule);
   }

   protected static final FndText _newDefaultSchedule(FndText type) {
      return new FndText(Meta.defaultSchedule);
   }

   protected static final FndText _newOwnershipQuery(FndText type) {
      return new FndText(Meta.ownershipQuery);
   }

   protected static final FndText _newSyncGroup(FndText type) {
      return new FndText(Meta.syncGroup);
   }

   protected static final FndText _newPushGuardCondition(FndText type) {
      return new FndText(Meta.pushGuardCondition);
   }

   protected static final FndInteger _newOrdinal(FndInteger type) {
      return new FndInteger(Meta.ordinal);
   }
   protected static final FndInteger _newEffectiveOnDemandExpiry(FndInteger type) {
      return new FndInteger(Meta.effectiveOnDemandExpiry);
   }

   protected static final FndBoolean _newAddedByCustomer(FndBoolean type) {
      return new FndBooleanString(Meta.addedByCustomer, "TRUE", "FALSE");
   }

   protected static final FndText _newDataFilterMethod(FndText type) {
      return new FndText(Meta.filterMethod);
   }

   public static class Meta {

      public static final FndAttributeMeta appName = new FndAttributeMeta(viewMeta, "APP_NAME", "APP_NAME", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 200).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta appVersion = new FndAttributeMeta(viewMeta, "APP_VERSION", "APP_VERSION", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 20).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta entity = new FndAttributeMeta(viewMeta, "ENTITY", "ENTITY", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 100).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta handler = new FndAttributeMeta(viewMeta, "HANDLER", "HANDLER", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 100);
      public static final FndAttributeMeta projection = new FndAttributeMeta(viewMeta, "PROJECTION", "PROJECTION", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 100);
      public static final FndAttributeMeta isMetaDataDriven = new FndAttributeMeta(viewMeta, "IS_META_DATA_DRIVEN", "IS_META_DATA_DRIVEN", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta ruleType = new FndAttributeMeta(viewMeta, "RULE_TYPE", "RULE_TYPE_DB", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY).setIidPlsqlPackage("SYNC_RULE_TYPE_API");
      public static final FndAttributeMeta ruleType$ = new FndAttributeMeta(viewMeta, "RULE_TYPE", "RULE_TYPE", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY).setIidPlsqlPackage("SYNC_RULE_TYPE_API");
      public static final FndAttributeMeta deliveryMethod = new FndAttributeMeta(viewMeta, "DELIVERY_METHOD", "DELIVERY_METHOD_DB", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY).setIidPlsqlPackage("SYNC_DELIVERY_TYPE_API");
      public static final FndAttributeMeta deliveryMethod$ = new FndAttributeMeta(viewMeta, "DELIVERY_METHOD", "DELIVERY_METHOD", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY).setIidPlsqlPackage("SYNC_DELIVERY_TYPE_API");
      public static final FndAttributeMeta customDeliveryMethod = new FndAttributeMeta(viewMeta, "CUSTOM_DELIVERY_METHOD", "CUSTOM_DELIVERY_METHOD_DB", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT).setIidPlsqlPackage("SYNC_DELIVERY_TYPE_API");
      public static final FndAttributeMeta customDeliveryMethod$ = new FndAttributeMeta(viewMeta, "CUSTOM_DELIVERY_METHOD", "CUSTOM_DELIVERY_METHOD", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT).setIidPlsqlPackage("SYNC_DELIVERY_TYPE_API");
      public static final FndAttributeMeta effectiveDeliveryMethod = new FndAttributeMeta(viewMeta, "EFFECTIVE_DELIVERY_METHOD", "EFFECTIVE_DELIVERY_METHOD", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.SERVER_GENERATED, 100).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta changedBy = new FndAttributeMeta(viewMeta, "CHANGED_BY", "CHANGED_BY", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 100);
      public static final FndAttributeMeta changedDate = new FndAttributeMeta(viewMeta, "CHANGED_DATE", "CHANGED_DATE", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta frequency = new FndAttributeMeta(viewMeta, "FREQUENCY", "FREQUENCY", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta defaultFrequency = new FndAttributeMeta(viewMeta, "DEFAULT_FREQUENCY", "DEFAULT_FREQUENCY", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY);
      public static final FndAttributeMeta schedule = new FndAttributeMeta(viewMeta, "SCHEDULE", "SCHEDULE", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 200);
      public static final FndAttributeMeta defaultSchedule = new FndAttributeMeta(viewMeta, "DEFAULT_SCHEDULE", "DEFAULT_SCHEDULE", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 200);
      public static final FndAttributeMeta ownershipQuery = new FndAttributeMeta(viewMeta, "OWNERSHIP_QUERY", "OWNERSHIP_QUERY", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 4000);
      public static final FndAttributeMeta syncGroup = new FndAttributeMeta(viewMeta, "SYNC_GROUP", "SYNC_GROUP", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 200);
      public static final FndAttributeMeta pushGuardCondition = new FndAttributeMeta(viewMeta, "PUSH_GUARD_CONDITION", "PUSH_GUARD_CONDITION", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 4000);
      public static final FndAttributeMeta ordinal = new FndAttributeMeta(viewMeta, "ORDINAL", "ORDINAL", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta addedByCustomer = new FndAttributeMeta(viewMeta, "ADDED_BY_CUSTOMER", "ADDED_BY_CUSTOMER", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta effectiveOnDemandExpiry = new FndAttributeMeta(viewMeta, "EFFECTIVE_ON_DEMAND_EXPIRY", "EFFECTIVE_ON_DEMAND_EXPIRY", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta filterMethod = new FndAttributeMeta(viewMeta, "EFFECTIVE_FILTER_METHOD", "EFFECTIVE_FILTER_METHOD", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);

      public static final FndCompoundReferenceMeta primaryKey = new FndCompoundReferenceMeta(viewMeta, "ABSTRACT_MOBILE_SYNC_RULE_KEY", new FndAttributeMeta[]{appName, appVersion, entity}, viewMeta, true);
      public static final FndCompoundReferenceMeta parentKeyMobileApplicationVersion = new FndCompoundReferenceMeta(viewMeta, "PARENT_KEY_MOBILE_APPLICATION_VERSION", new FndAttributeMeta[] {appName, appVersion}, com.ifsworld.fnd.mobile.documents.entity.mobileapplicationversion.MobileApplicationVersionEntity.viewMeta);
   }
}
