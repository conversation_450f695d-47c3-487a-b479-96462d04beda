/*
 * ==================================================================================
 * File:              MobileProfileSplitTypeEnumeration.java
 * Enumeration:       MobileProfileSplitType
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.enumeration;

import ifs.fnd.base.IfsException;
import ifs.fnd.base.ParseException;
import ifs.fnd.base.SystemException;
import ifs.fnd.record.*;

public final class MobileProfileSplitTypeEnumeration extends FndEnumeration {

   //Enumeration entries
   public static final Enum FORM_FACTOR = Enum.FORM_FACTOR;
   public static final Enum NONE = Enum.NONE;
   public static final Enum PLATFORM = Enum.PLATFORM;
   public static final Enum PLATFORM_FORM_FACTOR = Enum.PLATFORM_FORM_FACTOR;

   public MobileProfileSplitTypeEnumeration() {
      super();
   }

   public MobileProfileSplitTypeEnumeration(String name) {
      super(name);
   }

   public MobileProfileSplitTypeEnumeration(String name, Enum value) {
      this(name);
      setValue(value);
   }

   public MobileProfileSplitTypeEnumeration(FndAttributeMeta meta) {
      super(meta);
   }

   @Override
   protected FndAttribute newAttribute(FndAttributeMeta meta) {
      return new MobileProfileSplitTypeEnumeration(meta);
   }

   public void setValue(Enum value) {
      internalSetValue(value);
   }

   public Enum getValue() {
      return (Enum) internalGetValue();
   }

   @Override
   public void parseString(String value) throws ParseException {
      try {
         this.value = Enum.parseString(value);
         this.set();
         this.setExistent();
      } catch (ParseException e) {
         throwIllegalValueException(value);
      }
   }

   @Override
   protected void parseDatabaseString(String dbValue) throws ParseException {
      try {
         this.value = Enum.parseDatabaseString(dbValue);
         this.set();
         this.setExistent();
      } catch (ParseException e) {
         throwIllegalDatabaseValueException(dbValue);
      }
   }

   @Override
   public String toString() {
      Enum val = getValue();
      if (val == null) {
         return null;
      }
      return val.getValue();
   }

   @Override
   protected String toDatabaseString() {
      Enum val = getValue();
      if (val == null) {
         return null;
      }
      return val.getDatabaseValue();
   }

   @Override
   public int compareTo(FndAttribute attr) {
      if (this == attr) {
         return 0;
      }
      if (attr instanceof MobileProfileSplitTypeEnumeration) {
         if (this.isNull() && attr.isNull()) {
            return 0;
         } else if (this.isNull()) {
            return 1;
         } else if (attr.isNull()) {
            return -1;
         } else {
            return getValue().compareTo(((MobileProfileSplitTypeEnumeration) attr).getValue());
         }
      }
      return 1;
   }

   public Condition createEqualCondition(Enum value) {
      return new Condition(this, getEqualQueryOperator(), value);
   }

   public Condition createNotEqualCondition(Enum value) {
      return new Condition(this, getNotEqualQueryOperator(), value);
   }

   public Condition createEqualCondition(MobileProfileSplitTypeEnumeration attr) {
      return new Condition(this, getEqualQueryOperator(), attr);
   }

   public Condition createNotEqualCondition(MobileProfileSplitTypeEnumeration attr) {
      return new Condition(this, getNotEqualQueryOperator(), attr);
   }

   public static FndEnumerationView toEnumerationView() throws IfsException {
      FndEnumerationView view = new FndEnumerationView();
      view.name.setValue("MOBILEPROFILESPLITTYPE");
      view.addValue(FORM_FACTOR.value, FORM_FACTOR.displayText);
      view.addValue(NONE.value, NONE.displayText);
      view.addValue(PLATFORM.value, PLATFORM.displayText);
      view.addValue(PLATFORM_FORM_FACTOR.value, PLATFORM_FORM_FACTOR.displayText);
      return view;
   }

   public static enum Enum {
      FORM_FACTOR ("FormFactor", "FormFactor", "Form factor only"),
      NONE ("None", "None", "None"),
      PLATFORM ("Platform", "Platform", "Platform only"),
      PLATFORM_FORM_FACTOR ("PlatformFormFactor", "PlatformFormFactor", "Platform and form factor"),
      ;

      private String value;
      private String dbValue;
      private String displayText;

      private Enum(String value, String dbValue, String displayText) {
         this.value = value;
         this.dbValue = dbValue;
         this.displayText = displayText;
      }

      public String getValue() {
         return value;
      }

      public String getDatabaseValue() {
         return dbValue;
      }

      public String getDisplayText() {
         return displayText;
      }

      @Override
      public String toString() {
         return getValue();
      }

      public static Enum parseString(String value) throws ParseException {
         if (value == null || value.length() == 0) {
            return null;
         }
         for( Enum entry : Enum.values() ) {
            if (entry.getValue().equalsIgnoreCase(value)) {
               return entry;
            }
         }
         throw new ParseException("Illegal Value");
      }

      public static Enum parseDatabaseString(String dbValue) throws ParseException {
         if (dbValue == null || dbValue.length() == 0) {
            return null;
         }
         for( Enum entry : Enum.values() ) {
            if (entry.getDatabaseValue().equalsIgnoreCase(dbValue)) {
               return entry;
            }
         }
         throw new ParseException("Illegal Database Value");
      }
   }

   public static String[] toStringArray(Enum[] enumArr) {
      if (enumArr == null) {
         return null;
      }
      String[] strArr = new String[enumArr.length];
      for (int i = 0; i < strArr.length; i++) {
         if (enumArr[i] != null) {
            strArr[i] = enumArr[i].toString();
         }
      }
      return strArr;
   }

   public static Enum[] toEnumArray(String[] strArr) throws ParseException {
      if (strArr == null) {
         return null;
      }
      Enum[] enumArr = new Enum[strArr.length];
      for (int i = 0; i < enumArr.length; i++) {
         enumArr[i] = Enum.parseString(strArr[i]);
      }
      return enumArr;
   }

   public static class Condition extends FndSimpleCondition {

      /**
       * Framework internal constructor required by Externalizable interface.
       */
      public Condition() {
      }

      private Condition(MobileProfileSplitTypeEnumeration base, FndQueryOperator oper, Enum value) {
         super(base, oper, value);
      }

      private Condition(MobileProfileSplitTypeEnumeration base, FndQueryOperator oper, MobileProfileSplitTypeEnumeration attr) {
         super(base, oper, attr, null);
      }

      /**
       * See the {@link FndSimpleCondition#setNvlFunction(FndAbstractString, String)} method.
       */
      public void setNvlFunction(MobileProfileSplitTypeEnumeration attr, Enum value) throws SystemException {
         protectedSetNvlFunction(attr, value.toString());
      }
   }
}
