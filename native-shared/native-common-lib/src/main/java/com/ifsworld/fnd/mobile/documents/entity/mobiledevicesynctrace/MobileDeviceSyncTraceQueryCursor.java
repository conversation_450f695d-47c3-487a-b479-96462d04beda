/*
 * ==================================================================================
 * File:              MobileDeviceSyncTraceQueryCursor.java
 * Entity:            MobileDeviceSyncTrace
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobiledevicesynctrace;

import ifs.fnd.base.IfsException;
import ifs.fnd.base.SystemException;
import ifs.fnd.sf.storage.FndEntityHandler;
import ifs.fnd.sf.storage.FndQueryCursor;

/**
 * An open query cursor of <code>MobileDeviceSyncTrace</code>.
 */
public class MobileDeviceSyncTraceQueryCursor {

   private FndQueryCursor cursor;
   private FndEntityHandler entityHandler;

   /**
    * Create a new instance of MobileDeviceSyncTraceQueryCursor.
    */
   protected MobileDeviceSyncTraceQueryCursor(FndQueryCursor cursor, FndEntityHandler entityHandler) {
      this.cursor = cursor;
      this.entityHandler = entityHandler;
   }

   /**
    * Moves the cursor to the next MobileDeviceSyncTrace record.
    */
   public MobileDeviceSyncTraceEntity next() throws IfsException {
      if (entityHandler != null) {
         return (MobileDeviceSyncTraceEntity) entityHandler.nextRecord(cursor);
      } else {
         return null;
      }
   }

   /**
    * Close this cursor and its database connection.
    */
   public void close() throws SystemException {
      if (cursor != null) {
         cursor.close();
      }
   }

   /**
    * Check if result of last fetched value from the result set was null.
    * @return true if result was null, false otherwise.
    * @throws IfsException
    */
   public boolean resultWasNull() throws IfsException {
      if (cursor != null) {
         return cursor.resultWasNull();
      } else {
         return true;
      }
   }
}
