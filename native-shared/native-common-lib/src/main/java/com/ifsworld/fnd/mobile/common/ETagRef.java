/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.ifsworld.fnd.mobile.common;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 *
 * <AUTHOR>
 */
public class ETagRef
   {
      public String objId = null;
      public String objVersion = null;

      public static final String emptyETag = "W/\"*\"";     
      
      private static final Pattern ETAG_PATTERN = Pattern.compile("(?=[^W/\"])(.*)(?=[$\"])");
      private static final Logger LOG = LogManager.getLogger();
      
      public ETagRef(String eTag){
         init(eTag);
      }    

      public ETagRef(){
      }  
      
      private void init(final String eTag){
         if(eTag!=null){
            final Matcher matcher = ETAG_PATTERN.matcher(eTag);
            if (matcher.find()) {
               String[] eTagParts = matcher.group().split(":");
               if(eTagParts!=null && eTagParts.length >0){
                  objId = eTagParts[0];
               }            
               if(eTagParts!=null && eTagParts.length == 2){
                  objVersion = eTagParts[1];
               }
            }
            else {
               LOG.warn("Invalid ETag value detected.");
            }
         }
      } 
      
      public String getEtag(){
         if(objId!=null){
            return String.format("W/\"%s:%s\"", objId, objVersion);
         }
         else{
            return emptyETag;
         }
      }      
   }