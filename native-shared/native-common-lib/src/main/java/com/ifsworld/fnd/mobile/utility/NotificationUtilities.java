package com.ifsworld.fnd.mobile.utility;

import com.ifsworld.fnd.mobile.*;
import com.ifsworld.fnd.mobile.cache.FndmobAppParamCache;
import com.ifsworld.fnd.mobile.documents.MobileApplicationParam;
import com.ifsworld.fnd.mobile.model.PushHubSettings;
import com.ifsworld.fnd.mobile.model.PushDeviceSettings;
import com.ifsworld.fnd.mobile.remote.NativeNotificationAccess;
import ifs.fnd.base.IfsException;

import java.sql.Connection;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class NotificationUtilities {

    public static void sendToUserDevice(MobileContextBase mobileContext, PushContent content, Connection connection) throws NativeStorageException {
        List<PushDeviceSettings> devices = PushDbAccess.getActivePushDeviceSettings(mobileContext.getAppName(), mobileContext.getUserId(), mobileContext.getDeviceId(), connection);
        validate(devices, connection);
        sendToUserDevices(mobileContext, devices, content, connection);
    }

    private static void sendToUserDevices(MobileContextBase mobileContext, List<PushDeviceSettings> devices, PushContent content, Connection connection) throws NativeStorageException {
        try {
            String connectionString = FndmobAppParamCache.get(mobileContext.getAppName(), "NOTIFICATION_HUB_URL");
            String hubPath = FndmobAppParamCache.get(mobileContext.getAppName(), "NOTIFICATION_HUB_PATH");
            PushHubSettings hubSettings =  new PushHubSettings(connectionString, hubPath).validate();
            Iterator recordRulesIterator = devices.iterator();
            while (recordRulesIterator.hasNext()) {
                PushDeviceSettings device = (PushDeviceSettings) recordRulesIterator.next();
                sendToNotificationHub(mobileContext, hubSettings, device, content, connection);
            }
        }
        catch (IfsException e) {
            throw new RuntimeException(e);
        }
    }

    private static void sendToNotificationHub(MobileContextBase mobileCtx, PushHubSettings hubSettings, PushDeviceSettings device, PushContent content, Connection connection) throws NativeStorageException {
        PushContextBase pushCtx = PushContextBase.with(hubSettings.getConnectionString(),
                hubSettings.getHubPath(),
                device.getOS(),
                device.getTag(),
                device.getApiVersion()).build();
        NativeNotificationAccess.getInstance().sendWithHubDetails(mobileCtx, pushCtx, content);
    }

    private static void validate(List<PushDeviceSettings> devices, Connection connection) throws NativeStorageException {
        if (devices == null || devices.size() == 0) {
            throw new NativeStorageException("Sending notification skipped. Push enabled app device not found!");
        }
    }
}
