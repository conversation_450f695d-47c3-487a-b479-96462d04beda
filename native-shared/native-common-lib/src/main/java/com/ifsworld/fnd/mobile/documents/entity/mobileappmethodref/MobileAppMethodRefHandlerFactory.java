/*
 * ==================================================================================
 * File:              MobileAppMethodRefHandlerFactory.java
 * Entity:            MobileAppMethodRef
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobileappmethodref;

public class MobileAppMethodRefHandlerFactory {

   public static MobileAppMethodRefHandler getHandler() {
      return new MobileAppMethodRefHandler();
   }
}
