package com.ifsworld.fnd.mobile.cache;

import ifs.fnd.base.IfsException;
import ifs.fnd.record.FndRecord;
import ifs.fnd.record.FndText;
import ifs.fnd.sf.storage.FndPlsqlAccess;
import java.util.regex.Pattern;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.Collections;
import java.util.LinkedHashMap;
import ifs.fnd.base.ApplicationException;
import ifs.fnd.base.IfsException;
import ifs.fnd.log.LogMgr;
import ifs.fnd.log.Logger;

public class SiteTimeZoneCache {
    private static final Map<String, String> timeZones =
            Collections.synchronizedMap(new LinkedHashMap<String, String>());
    private static final String stmt_SITE_LOOKUP
            = "BEGIN\n" +
            "? := Mobile_Client_SYS.Get_Time_Zone_Code_(?);\n" +
            "END;";

    private static Date cacheVersion = new Date();
    private static boolean checkForChange = false;

    public static String getTimeZone(final String siteId) {
        Logger log = LogMgr.getApplicationLogger();
        if (siteId != null) {
            try {
                String siteTimeZone = timeZones.get(siteId);

                if (siteTimeZone == null || checkForChange) {
                    siteTimeZone = readSiteTimeZoneFromDatabase(siteId);
                    if (siteTimeZone == null) {
                        log.error("Empty timezone fetched from the database for the Site ID: " + siteId);
                    }
                timeZones.put(siteId, siteTimeZone);
                checkForChange = false;
                }
                return siteTimeZone;
            } catch (IfsException ex) {
                log.error("Error when fetching site timezone for Site ID: " + siteId + ". Exception: " + ex.getMessage(), ex);
            }
        }
        //We do not handle NULL here and allow caller to deal with it
        return null;
    }

    private static String readSiteTimeZoneFromDatabase(final String siteId) throws IfsException {
        FndPlsqlAccess plsqlAccess = new FndPlsqlAccess();
        //Create bind variables
        FndRecord params = new FndRecord();
        params.add("SiteTimeZone", new FndText("SiteTimeZone"), "OUT");
        params.add("SiteId", new FndText("SiteId", siteId), "IN");
        //Execute the PL/SQL method
        plsqlAccess.execute(stmt_SITE_LOOKUP, params);
        //Return data to caller
        FndText siteTimeZone = (FndText) params.getAttribute("SiteTimeZone");
        return siteTimeZone.getValue();
    }

    public static void clearCache(Date cacheVersionData) {
        Logger log = LogMgr.getApplicationLogger();
            if (cacheVersionData != null && (cacheVersion == null || cacheVersion.compareTo(cacheVersionData) != 0)) {
                log.debug("Clearing fndmob Site Time Zone cache");
                checkForChange = true;
                cacheVersion = cacheVersionData;
                timeZones.clear();
            } else {
                log.debug("No need to clear fndmob Site Time Zone cache");
            }
    }
}
