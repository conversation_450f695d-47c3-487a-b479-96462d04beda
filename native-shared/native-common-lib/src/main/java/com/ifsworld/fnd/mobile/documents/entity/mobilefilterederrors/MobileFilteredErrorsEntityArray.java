/*
 * ==================================================================================
 * File:              MobileFilteredErrorsEntityArray.java
 * Entity:            MobileFilteredErrorsEntity
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobilefilterederrors;

import ifs.fnd.record.FndAbstractArray;
import ifs.fnd.record.FndAttributeMeta;
import ifs.fnd.record.FndCompoundReference;

/**
 * Array of <code>MobileFilteredErrorsEntity</code>.
 */
public abstract class MobileFilteredErrorsEntityArray extends FndAbstractArray {

   protected MobileFilteredErrorsEntityArray() {
      super();
   }

   protected MobileFilteredErrorsEntityArray(FndAttributeMeta meta) {
      super(meta);
   }

   protected MobileFilteredErrorsEntityArray(FndAttributeMeta meta, FndCompoundReference parentKeyInParent) {
      super(meta, parentKeyInParent);
   }
}
