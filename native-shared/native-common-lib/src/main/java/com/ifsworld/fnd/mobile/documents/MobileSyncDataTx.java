/*
 * ==================================================================================
 * File:              MobileSyncDataTx.java
 * Software Package:  MobileClientRuntime
 * Template Version:  8.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents;

import com.ifsworld.fnd.mobile.documents.entity.mobilesyncdatatx.MobileSyncDataTxEntity;
import com.ifsworld.fnd.mobile.documents.enumeration.MobileRecordSyncStateEnumeration;
import ifs.fnd.record.*;

/**
 * This view represents the <code>MobileSyncDataTx</code> entity.
 */
public class MobileSyncDataTx extends com.ifsworld.fnd.mobile.documents.entity.mobilesyncdatatx.MobileSyncDataTxEntity {

   public static final FndRecordMeta viewMeta  = new FndRecordMeta(com.ifsworld.fnd.mobile.documents.entity.mobilesyncdatatx.MobileSyncDataTxEntity.viewMeta, "MOBILECLIENTRUNTIME", "MOBILE_SYNC_DATA_TX").setViewClassName("com.ifsworld.fnd.mobile.documents.MobileSyncDataTx").setTermPath("MobileClientRuntime.MobileSyncDataTx");

   /**
    * ReferencedOjbversion
    */
   public final FndText      referencedOjbversion = MobileSyncDataTxEntity._newReferencedOjbversion((FndText) null);
   /**
    * PrimaryKeys
    */
   public final FndText      primaryKeys          = MobileSyncDataTxEntity._newPrimaryKeys((FndText) null);
   /**
    * ParentKeys
    */
   public final FndText      parentKeys           = MobileSyncDataTxEntity._newParentKeys((FndText) null);
   /**
    * SyncState
    */
   public final MobileRecordSyncStateEnumeration syncState = MobileSyncDataTxEntity._newSyncState((MobileRecordSyncStateEnumeration) null);
   /**
    * Processing
    */
   public final FndBoolean   processing           = MobileSyncDataTxEntity._newProcessing((FndBoolean) null);
   /**
    * ClientKeys
    */
   public final FndText      clientKeys           = MobileSyncDataTxEntity._newClientKeys((FndText) null);
   /**
    * ModifiedColumns
    */
   public final FndText      modifiedColumns      = MobileSyncDataTxEntity._newModifiedColumns((FndText) null);
   /**
    * KeyRef
    */
   public final FndText      keyRef               = MobileSyncDataTxEntity._newKeyRef((FndText) null);

   public MobileSyncDataTx() {
      this(viewMeta);
   }

   protected MobileSyncDataTx(FndRecordMeta meta) {
      super(meta);
      add(referencedOjbversion);
      add(primaryKeys);
      add(parentKeys);
      add(syncState);
      add(processing);
      add(clientKeys);
      add(modifiedColumns);
      add(keyRef);
   }

   @Override
   public FndAbstractRecord newInstance() {
      return new MobileSyncDataTx();
   }

   @Override
   public FndAbstractArray newArrayInstance() {
      return new MobileSyncDataTxArray();
   }

   public static class Meta extends com.ifsworld.fnd.mobile.documents.entity.mobilesyncdatatx.MobileSyncDataTxEntity.Meta {
   }
}
