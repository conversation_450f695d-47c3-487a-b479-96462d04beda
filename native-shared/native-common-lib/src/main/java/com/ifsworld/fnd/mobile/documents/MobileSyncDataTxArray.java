/*
 * ==================================================================================
 * File:              MobileSyncDataTxArray.java
 * Software Package:  MobileClientRuntime
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents;

import ifs.fnd.base.ParseException;
import ifs.fnd.base.SystemException;
import ifs.fnd.record.*;
import ifs.fnd.record.serialization.FndTokenReader;

/**
 * Array of <code>MobileSyncDataTx</code>.
 */
public class MobileSyncDataTxArray extends com.ifsworld.fnd.mobile.documents.entity.mobilesyncdatatx.MobileSyncDataTxEntityArray {

   public MobileSyncDataTxArray() {
      super();
   }

   public MobileSyncDataTxArray(FndAttributeMeta meta) {
      super(meta);
   }

   public MobileSyncDataTxArray(FndAttributeMeta meta, FndCompoundReference parentKeyInParent) {
      super(meta, parentKeyInParent);
   }

   public boolean add(MobileSyncDataTx record) {
      return internalAdd(record);
   }

   public void add(int index, MobileSyncDataTx record) {
      internalAdd(index, record);
   }

   public void add(MobileSyncDataTxArray array) {
      internalAdd(array);
   }

   public void assign(MobileSyncDataTxArray from) throws SystemException {
      assign(from, true);
   }

   public boolean contains(MobileSyncDataTx record) {
      return internalContains(record);
   }

   public MobileSyncDataTx firstElement() {
      return (MobileSyncDataTx)internalFirstElement();
   }

   public MobileSyncDataTx get(int index) {
      return (MobileSyncDataTx)internalGet(index);
   }

   public int indexOf(MobileSyncDataTx record) {
      return internalIndexOf(record);
   }

   public MobileSyncDataTx lastElement() {
      return (MobileSyncDataTx)internalLastElement();
   }

   public int lastIndexOf(MobileSyncDataTx record) {
      return internalLastIndexOf(record);
   }

   public MobileSyncDataTx remove(int index) {
      return (MobileSyncDataTx)internalRemove(index);
   }

   public MobileSyncDataTx set(int index, MobileSyncDataTx record) {
      return (MobileSyncDataTx)internalSet(index, record);
   }

   public FndDetailCondition createDetailCondition(MobileSyncDataTx record, FndQueryReferenceCategory category) {
      return createCondition(record, category);
   }

   @Override
   public FndAbstractRecord newRecord() {
      return new MobileSyncDataTx();
   }

   @Override
   protected FndAbstractRecord newRecord(FndTokenReader stream) throws ParseException {
      MobileSyncDataTx record = new MobileSyncDataTx();
      record.parse(stream);
      return record;
   }

   protected FndAttribute newAttribute(FndAttributeMeta meta) {
       return new MobileSyncDataTxArray(meta);
   }
}
