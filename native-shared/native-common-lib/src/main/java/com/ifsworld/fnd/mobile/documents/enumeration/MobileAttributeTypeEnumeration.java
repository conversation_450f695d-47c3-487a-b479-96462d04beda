/*
 * ==================================================================================
 * File:              MobileAttributeTypeEnumeration.java
 * Enumeration:       MobileAttributeType
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.enumeration;

import ifs.fnd.base.IfsException;
import ifs.fnd.base.ParseException;
import ifs.fnd.base.SystemException;
import ifs.fnd.record.*;

public final class MobileAttributeTypeEnumeration extends FndEnumeration {

   //Enumeration entries
   public static final Enum BINARY = Enum.BINARY;
   public static final Enum BOOLEAN = Enum.BOOLEAN;
   public static final Enum CURRENCY = Enum.CURRENCY;
   public static final Enum DATABASE_TIMESTAMP = Enum.DATABASE_TIMESTAMP;
   public static final Enum DATE = Enum.DATE;
   public static final Enum DECIMAL = Enum.DECIMAL;
   public static final Enum ENUMERATION = Enum.ENUMERATION;
   public static final Enum ENUMERATION_LOOKUP = Enum.ENUMERATION_LOOKUP;
   public static final Enum INTEGER = Enum.INTEGER;
   public static final Enum LONG_TEXT = Enum.LONG_TEXT;
   public static final Enum LOWERCASE_TEXT = Enum.LOWERCASE_TEXT;
   public static final Enum NUMBER = Enum.NUMBER;
   public static final Enum OBJID = Enum.OBJID;
   public static final Enum OBJVERSION = Enum.OBJVERSION;
   public static final Enum PERCENTAGE = Enum.PERCENTAGE;
   public static final Enum TEXT = Enum.TEXT;
   public static final Enum TIME = Enum.TIME;
   public static final Enum TIMESTAMP = Enum.TIMESTAMP;
   public static final Enum TIMESTAMP_UTC = Enum.TIMESTAMP_UTC;
   public static final Enum UPPERCASE_TEXT = Enum.UPPERCASE_TEXT;
   public static final Enum STRUCTURE = Enum.STRUCTURE;

   public MobileAttributeTypeEnumeration() {
      super();
   }

   public MobileAttributeTypeEnumeration(String name) {
      super(name);
   }

   public MobileAttributeTypeEnumeration(String name, Enum value) {
      this(name);
      setValue(value);
   }

   public MobileAttributeTypeEnumeration(FndAttributeMeta meta) {
      super(meta);
   }

   @Override
   protected FndAttribute newAttribute(FndAttributeMeta meta) {
      return new MobileAttributeTypeEnumeration(meta);
   }

   public void setValue(Enum value) {
      internalSetValue(value);
   }

   public Enum getValue() {
      return (Enum) internalGetValue();
   }

   @Override
   public void parseString(String value) throws ParseException {
      try {
         this.value = Enum.parseString(value);
         this.set();
         this.setExistent();
      } catch (ParseException e) {
         throwIllegalValueException(value);
      }
   }

   @Override
   protected void parseDatabaseString(String dbValue) throws ParseException {
      try {
         this.value = Enum.parseDatabaseString(dbValue);
         this.set();
         this.setExistent();
      } catch (ParseException e) {
         throwIllegalDatabaseValueException(dbValue);
      }
   }

   @Override
   public String toString() {
      Enum val = getValue();
      if (val == null) {
         return null;
      }
      return val.getValue();
   }

   @Override
   protected String toDatabaseString() {
      Enum val = getValue();
      if (val == null) {
         return null;
      }
      return val.getDatabaseValue();
   }

   @Override
   public int compareTo(FndAttribute attr) {
      if (this == attr) {
         return 0;
      }
      if (attr instanceof MobileAttributeTypeEnumeration) {
         if (this.isNull() && attr.isNull()) {
            return 0;
         } else if (this.isNull()) {
            return 1;
         } else if (attr.isNull()) {
            return -1;
         } else {
            return getValue().compareTo(((MobileAttributeTypeEnumeration) attr).getValue());
         }
      }
      return 1;
   }

   public Condition createEqualCondition(Enum value) {
      return new Condition(this, getEqualQueryOperator(), value);
   }

   public Condition createNotEqualCondition(Enum value) {
      return new Condition(this, getNotEqualQueryOperator(), value);
   }

   public Condition createEqualCondition(MobileAttributeTypeEnumeration attr) {
      return new Condition(this, getEqualQueryOperator(), attr);
   }

   public Condition createNotEqualCondition(MobileAttributeTypeEnumeration attr) {
      return new Condition(this, getNotEqualQueryOperator(), attr);
   }

   public static FndEnumerationView toEnumerationView() throws IfsException {
      FndEnumerationView view = new FndEnumerationView();
      view.name.setValue("MOBILEATTRIBUTETYPE");
      view.addValue(BINARY.value, BINARY.displayText);
      view.addValue(BOOLEAN.value, BOOLEAN.displayText);
      view.addValue(CURRENCY.value, CURRENCY.displayText);
      view.addValue(DATABASE_TIMESTAMP.value, DATABASE_TIMESTAMP.displayText);
      view.addValue(DATE.value, DATE.displayText);
      view.addValue(DECIMAL.value, DECIMAL.displayText);
      view.addValue(ENUMERATION.value, ENUMERATION.displayText);
      view.addValue(ENUMERATION_LOOKUP.value, ENUMERATION_LOOKUP.displayText);
      view.addValue(INTEGER.value, INTEGER.displayText);
      view.addValue(LONG_TEXT.value, LONG_TEXT.displayText);
      view.addValue(LOWERCASE_TEXT.value, LOWERCASE_TEXT.displayText);
      view.addValue(NUMBER.value, NUMBER.displayText);
      view.addValue(OBJID.value, OBJID.displayText);
      view.addValue(OBJVERSION.value, OBJVERSION.displayText);
      view.addValue(PERCENTAGE.value, PERCENTAGE.displayText);
      view.addValue(TEXT.value, TEXT.displayText);
      view.addValue(TIME.value, TIME.displayText);
      view.addValue(TIMESTAMP.value, TIMESTAMP.displayText);
      view.addValue(TIMESTAMP_UTC.value, TIMESTAMP_UTC.displayText);
      view.addValue(UPPERCASE_TEXT.value, UPPERCASE_TEXT.displayText);
      view.addValue(STRUCTURE.value, STRUCTURE.displayText);
      return view;
   }

   public static enum Enum {
      BINARY ("Binary", "BINARY", "Binary"),
      BOOLEAN ("Boolean", "BOOLEAN", "Boolean"),
      CURRENCY ("Currency", "CURRENCY", "Currency"),
      DATABASE_TIMESTAMP ("DatabaseTimestamp", "DATABASE_TIMESTAMP", "Database Timestamp"),
      DATE ("Date", "DATE", "Date"),
      DECIMAL ("Decimal", "DECIMAL", "Decimal"),
      ENUMERATION ("Enumeration", "ENUMERATION", "Enumeration"),
      ENUMERATION_LOOKUP ("EnumerationLookup", "ENUMERATION_LOOKUP", "Enumeration Lookup"),
      INTEGER ("Integer", "INTEGER", "Integer"),
      LONG_TEXT ("LongText", "LONG_TEXT", "Long Text"),
      LOWERCASE_TEXT ("LowercaseText", "LOWERCASE_TEXT", "Lowercase Text"),
      NUMBER ("Number", "NUMBER", "Number"),
      OBJID ("Objid", "OBJID", "Objid"),
      OBJVERSION ("Objversion", "OBJVERSION", "Objversion"),
      PERCENTAGE ("Percentage", "PERCENTAGE", "Percentage"),
      TEXT ("Text", "TEXT", "Text"),
      TIME ("Time", "TIME", "Time"),
      TIMESTAMP ("Timestamp", "TIMESTAMP", "Timestamp"),
      TIMESTAMP_UTC ("TimestampUTC", "TIMESTAMP_UTC", "Timestamp UTC"),
      UPPERCASE_TEXT ("UppercaseText", "UPPERCASE_TEXT", "Uppercase Text"),
      STRUCTURE ("Structure", "STRUCTURE", "Structure"),
      ;

      private String value;
      private String dbValue;
      private String displayText;

      private Enum(String value, String dbValue, String displayText) {
         this.value = value;
         this.dbValue = dbValue;
         this.displayText = displayText;
      }

      public String getValue() {
         return value;
      }

      public String getDatabaseValue() {
         return dbValue;
      }

      public String getDisplayText() {
         return displayText;
      }

      @Override
      public String toString() {
         return getValue();
      }

      public static Enum parseString(String value) throws ParseException {
         if (value == null || value.length() == 0) {
            return null;
         }
         for( Enum entry : Enum.values() ) {
            if (entry.getValue().equalsIgnoreCase(value)) {
               return entry;
            }
         }
         throw new ParseException("Illegal Value");
      }

      public static Enum parseDatabaseString(String dbValue) throws ParseException {
         if (dbValue == null || dbValue.length() == 0) {
            return null;
         }
         for( Enum entry : Enum.values() ) {
            if (entry.getDatabaseValue().equalsIgnoreCase(dbValue)) {
               return entry;
            }
         }
         throw new ParseException("Illegal Database Value");
      }
   }

   public static String[] toStringArray(Enum[] enumArr) {
      if (enumArr == null) {
         return null;
      }
      String[] strArr = new String[enumArr.length];
      for (int i = 0; i < strArr.length; i++) {
         if (enumArr[i] != null) {
            strArr[i] = enumArr[i].toString();
         }
      }
      return strArr;
   }

   public static Enum[] toEnumArray(String[] strArr) throws ParseException {
      if (strArr == null) {
         return null;
      }
      Enum[] enumArr = new Enum[strArr.length];
      for (int i = 0; i < enumArr.length; i++) {
         enumArr[i] = Enum.parseString(strArr[i]);
      }
      return enumArr;
   }

   public static class Condition extends FndSimpleCondition {

      /**
       * Framework internal constructor required by Externalizable interface.
       */
      public Condition() {
      }

      private Condition(MobileAttributeTypeEnumeration base, FndQueryOperator oper, Enum value) {
         super(base, oper, value);
      }

      private Condition(MobileAttributeTypeEnumeration base, FndQueryOperator oper, MobileAttributeTypeEnumeration attr) {
         super(base, oper, attr, null);
      }

      /**
       * See the {@link FndSimpleCondition#setNvlFunction(FndAbstractString, String)} method.
       */
      public void setNvlFunction(MobileAttributeTypeEnumeration attr, Enum value) throws SystemException {
         protectedSetNvlFunction(attr, value.toString());
      }
   }
}
