/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.ifsworld.fnd.mobile.cache;

import com.ifsworld.fnd.mobile.documents.MobileApplicationParam;
import com.ifsworld.fnd.mobile.documents.MobileApplicationParamArray;
import com.ifsworld.fnd.mobile.documents.MobileApplicationVersion;
import com.ifsworld.fnd.mobile.documents.entity.mobileapplicationparam.MobileApplicationParamHandler;
import com.ifsworld.fnd.mobile.documents.entity.mobileapplicationparam.MobileApplicationParamHandlerFactory;
import ifs.fnd.base.IfsException;
import ifs.fnd.log.LogMgr;
import ifs.fnd.log.Logger;
import ifs.fnd.record.FndCondition;
import ifs.fnd.record.FndQueryRecord;
import org.apache.logging.log4j.LogManager;

import java.util.*;

/**
 * <AUTHOR>
 */
public class FndmobAppParamCache {

    private static final org.apache.logging.log4j.Logger LOG = LogManager.getLogger(FndmobCustomFieldCache.class);
    private static final Map<String, AppParamMeta> appParamCache = new HashMap<>();

    public static class AppParamMeta {

        private final String appName;
        private final String appVersion;
        private final Map<String, MobileApplicationParam> items;
        private Date cacheVersion;

        public AppParamMeta(String appVersion, String appName, Map<String, MobileApplicationParam> items) {
            this.appName = appVersion;
            this.appVersion = appName;
            this.items = items;
        }

        public String appName() {
            return appName;
        }

        public String appVersion() {
            return appVersion;
        }

        public Map<String, MobileApplicationParam> getParameters() {
            return items;
        }
    }

    static public String get(String appName, String parameter) throws IfsException {
        String appVersion = CacheUtilities.getActiveAppVersion(appName);
        String cacheKey = formatTwoPartKey(appName, appVersion);
        AppParamMeta appMeta = appParamCache.get(cacheKey);

        if (appMeta == null) {
            Map<String, MobileApplicationParam> paramMap = new HashMap<>();
            appMeta = new AppParamMeta(appName, appVersion, paramMap);
            MobileApplicationParamArray result = readApplicationParameterValues(appName, appVersion, new String[]{parameter}, appMeta);
            if (result != null) {
                return result.firstElement().value.getValue();
            } else {
                return null;
            }
        } else {
            return getParamInfo(appName, appVersion, parameter, appMeta).value.getValue();
        }
    }

    public static String formatTwoPartKey(String appName, String appVersion) {
        StringBuilder key = new StringBuilder(256);
        return key.append(appName).append("^").append(appVersion).toString();
    }

    private static MobileApplicationParam getParamInfo(String appName, String appVersion, String parameter, AppParamMeta appMeta) {
        MobileApplicationParam appParam = appMeta.items.get(parameter);
        if (appParam != null) {
            if (appParam.objVersion.getValue().equals(String.valueOf(appMeta.cacheVersion))) {
                return appParam;
            } else {
               return getValueFromDB(appName, appVersion, parameter, appMeta);
            }
        }
        else{
            return getValueFromDB(appName, appVersion, parameter, appMeta);
        }
    }

    private static MobileApplicationParam getValueFromDB(String appName, String appVersion, String parameter, AppParamMeta appMeta){
        Logger log = LogMgr.getApplicationLogger();
        MobileApplicationParamArray result = readApplicationParameterValues(appName, appVersion, new String[]{parameter}, appMeta); //fetch from DB
        if (result != null && result.size() > 0) {
            return result.firstElement();
        } else {
            log.error("Empty list fetched from the database ");
            return null;
        }
    }

    private static MobileApplicationParamArray readApplicationParameterValues(String appName, String appVersion, String[] parameters, AppParamMeta appMeta) {
        if (parameters.length == 0) {
            return null;
        }
        try {
            MobileApplicationParamHandler appParamHandler = MobileApplicationParamHandlerFactory.getHandler();
            MobileApplicationParam condition = new MobileApplicationParam();
            FndCondition appNameVersionCond = condition.appName.createEqualCondition(appName).and(condition.appVersion.createEqualCondition(appVersion));
            FndCondition paraNameCond = null;
            for (int i = 0; i < parameters.length; i++) {
                if (paraNameCond == null) {
                    paraNameCond = condition.parameter.createEqualCondition(parameters[i]);
                } else {
                    paraNameCond = paraNameCond.or(condition.parameter.createEqualCondition(parameters[i]));
                }
            }
            condition.addCondition(appNameVersionCond.and(paraNameCond));

            FndQueryRecord queryApplicationParam = new FndQueryRecord(condition);
            MobileApplicationParamArray appParamArray = new MobileApplicationParamArray();
            appParamHandler.query(queryApplicationParam, appParamArray);

            if (appParamArray != null && appParamArray.size() > 0) {
                // Store the fetched parameters into the map
                for (int i = 0; i < appParamArray.size(); i++) {
                    MobileApplicationParam param = appParamArray.get(i);
                    param.objVersion.setValue(String.valueOf(appMeta.cacheVersion));
                    appMeta.items.put(param.parameter.toString(), param);
                }
                LOG.debug("Cached {} parameters for app {} version {}", appMeta.items.size(), appName, appVersion);
            }

            return appParamArray;
        } catch (IfsException ex) {
            LOG.debug("MobileApplicationParameters: &1", ex);
            return null;
        }
    }

    /**
     * @param appName
     * @param appVersion
     * @param cacheVersion
     */
    public static void clearCache(String appName, String appVersion, Date cacheVersion) {
        LOG.debug("Clearing fndmob app param cache");
        String cacheKey = formatTwoPartKey(appName, appVersion);
        AppParamMeta appMeta = appParamCache.get(cacheKey);

        if (appMeta != null && (appMeta.cacheVersion == null || appMeta.cacheVersion.compareTo(cacheVersion) != 0)) {
            appMeta.cacheVersion = cacheVersion;
            LOG.debug("Cache for {} version {} cleared", appName, appVersion);
        } else {
            //Added for the purpose of initializing AppParamMeta and setting the cache version initially.
            if (appMeta == null) {
                Map<String, MobileApplicationParam> paramMap = new HashMap<>();
                appMeta = new AppParamMeta(appName, appVersion, paramMap);
                appMeta.cacheVersion = cacheVersion;
                appParamCache.put(formatTwoPartKey(appName, appVersion), appMeta);
            }
            LOG.debug("Cache for {} version {} is already up-to-date", appName, appVersion);
        }
    }

}
