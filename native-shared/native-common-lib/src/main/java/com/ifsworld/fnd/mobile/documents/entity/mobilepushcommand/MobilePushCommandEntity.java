/*
 * ==================================================================================
 * File:              MobilePushCommandEntity.java
 * Entity:            MobilePushCommand
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobilepushcommand;

import com.ifsworld.fnd.mobile.documents.enumeration.MobilePushMsgTypeEnumeration;
import ifs.fnd.base.IfsException;
import ifs.fnd.base.SystemException;
import ifs.fnd.record.*;

public abstract class MobilePushCommandEntity extends FndLUEntityView {


   public static final FndRecordMeta viewMeta = new FndRecordMeta("", "MOBILEPUSHCOMMAND", "MOBILE_PUSH_COMMAND", "MOBILE_PUSH_COMMAND", "MobilePushCommand", "MOBILE_PUSH_COMMAND_API", "MOBILE_PUSH_COMMAND_TAB").setViewClassName("ifs.entity.mobilepushcommand.MobilePushCommandEntity").setTermPath("MobilePushCommand.MobilePushCommand");
   public final FndText appName = _newAppName((FndText) null);
   public final FndText appVersion = _newAppVersion((FndText) null);
   public final FndText entity = _newEntity((FndText) null);
   public final MobilePushMsgTypeEnumeration messageType = _newMessageType((MobilePushMsgTypeEnumeration) null);
   public final Reference primaryKey = new Reference(Meta.primaryKey, appName, appVersion, entity, messageType);
   public final ParentReferenceMobileSyncRule parentKeyMobileSyncRule = new ParentReferenceMobileSyncRule(Meta.parentKeyMobileSyncRule, appName, appVersion, entity);

   protected MobilePushCommandEntity(FndRecordMeta meta) {
      super(meta);
      add(appName);
      add(appVersion);
      add(entity);
      add(messageType);
      add(primaryKey);
      add(parentKeyMobileSyncRule);
   }

   public void assign(MobilePushCommandEntity from) throws SystemException {
      super.assign(from);
   }

   public void transformTo(final MobilePushCommandEntity to) throws SystemException {
      transformView(to);
   }

   public static class Reference extends FndCompoundReference {

      public Reference(FndCompoundReferenceMeta ref, FndText appName, FndText appVersion, FndText entity, MobilePushMsgTypeEnumeration messageType) {
         super(ref);
         add(appName);
         add(appVersion);
         add(entity);
         add(messageType);
      }

      public void assign(Reference ref) throws IfsException {
         protectedAssign(ref);
      }

      public boolean isEqualTo(Reference ref) {
         return protectedIsEqualTo(ref);
      }
   }

   public static class ParentReferenceMobileSyncRule extends FndCompoundReference {

      public ParentReferenceMobileSyncRule(FndCompoundReferenceMeta ref, FndText appName, FndText appVersion, FndText entity) {
         super(ref);
         add(appName);
         add(appVersion);
         add(entity);
      }

      public void assign(ParentReferenceMobileSyncRule ref) throws IfsException {
         protectedAssign(ref);
      }

      public boolean isEqualTo(ParentReferenceMobileSyncRule ref) {
         return protectedIsEqualTo(ref);
      }
   }

   protected static final FndText _newAppName(FndText type) {
      return new FndText(Meta.appName);
   }

   protected static final FndText _newAppVersion(FndText type) {
      return new FndText(Meta.appVersion);
   }

   protected static final FndText _newEntity(FndText type) {
      return new FndText(Meta.entity);
   }

   protected static final MobilePushMsgTypeEnumeration _newMessageType(MobilePushMsgTypeEnumeration type) {
      return new MobilePushMsgTypeEnumeration(Meta.messageType);
   }

   protected static final FndText _newMessageType(FndText type) {
      return new FndText(Meta.messageType$);
   }

   protected static final FndText _newCommand(FndText type) {
      return new FndText(Meta.command);
   }

   protected static final FndText _newCommandVariables(FndText type) {
      return new FndText(Meta.commandVariables);
   }

   protected static final FndText _newVariableMapping(FndText type) {
      return new FndText(Meta.variableMapping);
   }

   public static class Meta {

      public static final FndAttributeMeta appName = new FndAttributeMeta(viewMeta, "APP_NAME", "APP_NAME", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 200).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta appVersion = new FndAttributeMeta(viewMeta, "APP_VERSION", "APP_VERSION", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 20).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta entity = new FndAttributeMeta(viewMeta, "ENTITY", "ENTITY", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 100).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta messageType = new FndAttributeMeta(viewMeta, "MESSAGE_TYPE", "MESSAGE_TYPE_DB", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED).setIidPlsqlPackage("MOBILE_PUSH_MSG_TYPE_API");
      public static final FndAttributeMeta messageType$ = new FndAttributeMeta(viewMeta, "MESSAGE_TYPE", "MESSAGE_TYPE", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED).setIidPlsqlPackage("MOBILE_PUSH_MSG_TYPE_API");
      public static final FndAttributeMeta command = new FndAttributeMeta(viewMeta, "COMMAND", "COMMAND", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 200);
      public static final FndAttributeMeta commandVariables = new FndAttributeMeta(viewMeta, "COMMAND_VARIABLES", "COMMAND_VARIABLES", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 200);
      public static final FndAttributeMeta variableMapping = new FndAttributeMeta(viewMeta, "VARIABLE_MAPPING", "VARIABLE_MAPPING", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 200);
      public static final FndCompoundReferenceMeta primaryKey = new FndCompoundReferenceMeta(viewMeta, "ABSTRACT_MOBILE_PUSH_COMMAND_KEY", new FndAttributeMeta[]{appName, appVersion, entity, messageType}, viewMeta, true);
      public static final FndCompoundReferenceMeta parentKeyMobileSyncRule = new FndCompoundReferenceMeta(viewMeta, "PARENT_KEY_MOBILE_SYNC_RULE", new FndAttributeMeta[] {appName, appVersion, entity}, com.ifsworld.fnd.mobile.documents.entity.mobilesyncrule.MobileSyncRuleEntity.viewMeta);
   }
}
