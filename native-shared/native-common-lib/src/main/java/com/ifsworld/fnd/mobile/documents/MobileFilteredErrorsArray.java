/*
 * ==================================================================================
 * File:              MobileFilteredErrorsArray.java
 * Software Package:  MobileOutQueueManagement
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents;

import ifs.fnd.base.ParseException;
import ifs.fnd.base.SystemException;
import ifs.fnd.record.*;
import ifs.fnd.record.serialization.FndTokenReader;

/**
 * Array of <code>MobileFilteredErrors</code>.
 */
public class MobileFilteredErrorsArray extends com.ifsworld.fnd.mobile.documents.entity.mobilefilterederrors.MobileFilteredErrorsEntityArray {

   public MobileFilteredErrorsArray() {
      super();
   }

   public MobileFilteredErrorsArray(FndAttributeMeta meta) {
      super(meta);
   }

   public MobileFilteredErrorsArray(FndAttributeMeta meta, FndCompoundReference parentKeyInParent) {
      super(meta, parentKeyInParent);
   }

   public boolean add(MobileFilteredErrors record) {
      return internalAdd(record);
   }

   public void add(int index, MobileFilteredErrors record) {
      internalAdd(index, record);
   }

   public void add(MobileFilteredErrorsArray array) {
      internalAdd(array);
   }

   public void assign(MobileFilteredErrorsArray from) throws SystemException {
      assign(from, true);
   }

   public boolean contains(MobileFilteredErrors record) {
      return internalContains(record);
   }

   public MobileFilteredErrors firstElement() {
      return (MobileFilteredErrors)internalFirstElement();
   }

   public MobileFilteredErrors get(int index) {
      return (MobileFilteredErrors)internalGet(index);
   }

   public int indexOf(MobileFilteredErrors record) {
      return internalIndexOf(record);
   }

   public MobileFilteredErrors lastElement() {
      return (MobileFilteredErrors)internalLastElement();
   }

   public int lastIndexOf(MobileFilteredErrors record) {
      return internalLastIndexOf(record);
   }

   public MobileFilteredErrors remove(int index) {
      return (MobileFilteredErrors)internalRemove(index);
   }

   public MobileFilteredErrors set(int index, MobileFilteredErrors record) {
      return (MobileFilteredErrors)internalSet(index, record);
   }

   public FndDetailCondition createDetailCondition(MobileFilteredErrors record, FndQueryReferenceCategory category) {
      return createCondition(record, category);
   }

   @Override
   public FndAbstractRecord newRecord() {
      return new MobileFilteredErrors();
   }

   @Override
   protected FndAbstractRecord newRecord(FndTokenReader stream) throws ParseException {
      MobileFilteredErrors record = new MobileFilteredErrors();
      record.parse(stream);
      return record;
   }

   protected FndAttribute newAttribute(FndAttributeMeta meta) {
       return new MobileFilteredErrorsArray(meta);
   }
}
