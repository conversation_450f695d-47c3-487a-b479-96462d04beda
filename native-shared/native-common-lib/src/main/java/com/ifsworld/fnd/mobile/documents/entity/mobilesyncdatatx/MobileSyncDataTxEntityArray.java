/*
 * ==================================================================================
 * File:              MobileSyncDataTxEntityArray.java
 * Entity:            MobileSyncDataTxEntity
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobilesyncdatatx;

import ifs.fnd.record.FndAbstractArray;
import ifs.fnd.record.FndAttributeMeta;
import ifs.fnd.record.FndCompoundReference;

/**
 * Array of <code>MobileSyncDataTxEntity</code>.
 */
public abstract class MobileSyncDataTxEntityArray extends FndAbstractArray {

   protected MobileSyncDataTxEntityArray() {
      super();
   }

   protected MobileSyncDataTxEntityArray(FndAttributeMeta meta) {
      super(meta);
   }

   protected MobileSyncDataTxEntityArray(FndAttributeMeta meta, FndCompoundReference parentKeyInParent) {
      super(meta, parentKeyInParent);
   }
}
