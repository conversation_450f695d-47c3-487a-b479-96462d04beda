package com.ifsworld.fnd.mobile.common;

import org.apache.logging.log4j.LogManager;

import jakarta.json.Json;
import jakarta.ws.rs.core.Response;

/**
 * <AUTHOR>
 */

public class ErrorResponseBuilder {

    //Unhandled exception occurred
    private static String UNHANDLED_EXCEPTION = "UNHANDLED_EXCEPTION";
    private static final org.apache.logging.log4j.Logger LOG = LogManager.getLogger(ErrorResponseBuilder.class);

    public static Response buildErrorResponse(final Exception exception) {
        return buildErrorResponse(UNHANDLED_EXCEPTION, exception);
    }

     public static Response buildErrorResponse(final String code, final Exception exception) {
        final String body = Json.createObjectBuilder()
                .add("error", Json.createObjectBuilder()
                        .add("code", code)
                        .add("message", MobileCommonUtilities.getDetailError(exception)))
                .build().toString();
        LOG.error(code, exception);
        return Response
                .status(500)
                .entity(body)
                .header("Content-Type", "application/json")
                .build();
    }
}
