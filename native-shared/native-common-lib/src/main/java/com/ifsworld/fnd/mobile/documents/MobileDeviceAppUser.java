/*
 * ==================================================================================
 * File:              MobileDeviceAppUser.java
 * Software Package:  MobileClientRuntime
 * Template Version:  8.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents;

import com.ifsworld.fnd.mobile.documents.enumeration.GpsEnablingTypeEnumeration;
import com.ifsworld.fnd.mobile.documents.enumeration.MobileAppStateEnumeration;
import com.ifsworld.fnd.mobile.documents.enumeration.MobileClientLogLevelEnumeration;
import com.ifsworld.fnd.mobile.documents.enumeration.MobileClientSyncIntEnumeration;
import ifs.fnd.record.*;

/**
 * This view represents the <code>MobileDeviceAppUser</code> entity.
 */
public class MobileDeviceAppUser extends com.ifsworld.fnd.mobile.documents.entity.mobiledeviceappuser.MobileDeviceAppUserEntity {

   public static final FndRecordMeta viewMeta = new FndRecordMeta(com.ifsworld.fnd.mobile.documents.entity.mobiledeviceappuser.MobileDeviceAppUserEntity.viewMeta, "MOBILECLIENTRUNTIME", "MOBILE_DEVICE_APP_USER").setViewClassName("ifs.application.mobileclientruntime.MobileDeviceAppUser").setTermPath("MobileClientRuntime.MobileDeviceAppUser");

   /**
    * DirectoryId
    */
   public final FndText directoryId = _newDirectoryId((FndText) null);
   /**
    * AppVersion
    */
   public final FndText appVersion = _newAppVersion((FndText) null);
   /**
    * SynchTraceLevel
    */
   public final FndText synchTraceLevel = _newSynchTraceLevel((FndText) null);
   /**
    * SynchTraceCategories
    */
   public final FndText synchTraceCategories = _newSynchTraceCategories((FndText) null);
   /**
    * GpsEnabled
    */
   public final GpsEnablingTypeEnumeration gpsEnabled = _newGpsEnabled((GpsEnablingTypeEnumeration) null);
   /**
    * Created
    */
   public final FndTimestamp created = _newCreated((FndTimestamp) null);
   /**
    * LastAccess
    */
   public final FndTimestamp lastAccess = _newLastAccess((FndTimestamp) null);
   /**
    * ClientVersion
    */
   public final FndText clientVersion = _newClientVersion((FndText) null);
   /**
    * State
    */
   public final MobileAppStateEnumeration state = _newState((MobileAppStateEnumeration) null);
   /**
    * PushEnabled
    */
   public final FndBoolean pushEnabled = _newPushEnabled((FndBoolean) null);
   /**
    * PushHandleValidTo
    */
   public final FndTimestamp pushHandleValidTo = _newPushHandleValidTo((FndTimestamp) null);
   /**
    * PushTag
    */
   public final FndText pushTag = _newPushTag((FndText) null);
   /**
    * NotificationHubApiVersion
    */
   public final FndText pushApiVersion = _newPushApiVersion((FndText) null);
   /**
    * PushRegistrationId
    */
   public final FndText pushRegistrationId = _newPushRegistrationId((FndText) null);
   /**
    * PreActivate
    */
   public final FndBoolean preActivate = _newPreActivate((FndBoolean) null);
   /**
    * EnableDbViewer
    */
   public final FndBoolean enableDbViewer = _newEnableDbViewer((FndBoolean) null);
   /**
    * BugReporter
    */
   public final FndBoolean bugReporter = _newBugReporter((FndBoolean) null);
   /**
    * BugReporterSendData
    */
   public final FndBoolean bugReporterSendData = _newBugReporterSendData((FndBoolean) null);
   /**
    * ClientLogLevel
    */
   public final MobileClientLogLevelEnumeration clientLogLevel = _newClientLogLevel((MobileClientLogLevelEnumeration) null);
   /**
    * DefaultSyncInt
    */
   public final MobileClientSyncIntEnumeration defaultSyncInt = _newDefaultSyncInt((MobileClientSyncIntEnumeration) null);
   /**
    * UseBaseProfile
    */
   public final FndBoolean useBaseProfile = _newUseBaseProfile((FndBoolean) null);
   /**
    * Language
    */
   public final FndText language = _newLanguage((FndText) null);
   /**
    * RuntimeCacheVersion
    */
   public final FndTimestamp runtimeCacheVersion = _newRuntimeCacheVersion((FndTimestamp) null);

   public MobileDeviceAppUser() {
      this(viewMeta);
   }

   protected MobileDeviceAppUser(FndRecordMeta meta) {
      super(meta);
      add(directoryId);
      add(appVersion);
      add(synchTraceLevel);
      add(synchTraceCategories);
      add(gpsEnabled);
      add(created);
      add(lastAccess);
      add(clientVersion);
      add(state);
      add(pushEnabled);
      add(pushHandleValidTo);
      add(pushTag);
      add(pushApiVersion);
      add(pushRegistrationId);
      add(preActivate);
      add(enableDbViewer);
      add(bugReporter);
      add(bugReporterSendData);
      add(clientLogLevel);
      add(defaultSyncInt);
      add(useBaseProfile);
      add(language);
      add(runtimeCacheVersion);
   }

   @Override
   public FndAbstractRecord newInstance() {
      return new MobileDeviceAppUser();
   }

   @Override
   public FndAbstractArray newArrayInstance() {
      return new MobileDeviceAppUserArray();
   }

   public static class Meta extends com.ifsworld.fnd.mobile.documents.entity.mobiledeviceappuser.MobileDeviceAppUserEntity.Meta {
   }
}
