/*
 * ==================================================================================
 * File:              MobileOutMessageUtilitiesImpl.java
 * Software Package:  mobileclientruntime
 * Template Version:  8.0
 * Generator Version: @GeneratorVersion@
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.utility;

import com.ifsworld.fnd.mobile.documents.MobileFailedTransaction;
import com.ifsworld.fnd.mobile.documents.MobileFilteredErrors;
import com.ifsworld.fnd.mobile.documents.MobileFilteredErrorsArray;
import com.ifsworld.fnd.mobile.documents.MobileOutMessage;
import com.ifsworld.fnd.mobile.documents.MobileOutMessageArray;
import com.ifsworld.fnd.mobile.documents.MobileIgnoredTransaction;
import ifs.fnd.base.ApplicationException;
import ifs.fnd.base.IfsException;
import ifs.fnd.record.*;
import ifs.fnd.sf.storage.FndEntityHandler;
import ifs.fnd.sf.storage.FndPlsqlAccess;
import org.apache.logging.log4j.LogManager;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Locale;
import java.util.TimeZone;

import static com.ifsworld.fnd.mobile.utility.MobileUtilities.DATE_FORMAT_PATTERN;

/**
 * Implementation of the
 * <code>MobileOutMessageUtilities</code> handler.
 */
public class MobileOutMessageUtilitiesImpl extends MobileOutMessageUtilitiesImplBase {

    private static final org.apache.logging.log4j.Logger LOG = LogManager.getLogger(MobileOutMessageUtilitiesImpl.class);

    private static final String STMT_FT_DATA_ROWS =
            "BEGIN " +
                    "   Mobile_Failed_Input_Data_API.Insert_New_Data_Row_(:transaction_id, :attr_name, :attr_value, :attr_type);\n" +
                    "END;";

    private static final String STMT_FT_LOB_ROWS =
            "BEGIN " +
                    "   Mobile_Failed_Lob_Data_API.Insert_New_Data_Row_(:transaction_id, :lob_id);\n" +
                    "END;";

    public MobileOutMessageUtilitiesImpl() {
    }

    /**
     * <p><b>Remarks:</b>
     *
     * @return void
     * @throws IfsException
     */
    @Override
    public void saveMobileFailedTransaction(MobileFailedTransaction record, FndAbstractRecord requestRec) throws IfsException {
        record.userId.setValue(record.userId.getValue("").toUpperCase());
        FndEntityHandler handler = new FndEntityHandler() {
        };

        //Add response to out queue
        MobileOutMessage msg = new MobileOutMessage();
        msg.appName.setValue(record.appName);
        msg.appVersion.setValue(record.appVersion);
        msg.deviceId.setValue(record.deviceId);
        msg.userId.setValue(record.userId);
        msg.relatedMessageId.parseString(record.transactionId.getValue());

        MobileOutMessageArray messages = new MobileOutMessageArray();
        handler.query(new FndQueryRecord(msg), messages);
        if (messages.size() > 1) {
            throw new ApplicationException("DUPLICATE_MESSAGES: Duplicate Mobile Out Messages due to incomplete database deployment");
        }

        if (messages.size() > 0) {
            msg = messages.firstElement();
        }

        msg.transactionType.setValue("OTHER");
        msg.messageSize.setValue(100);
        handler.save(msg);
        record.transactionId.setValue(java.util.UUID.randomUUID().toString());

        //first check if this error should be ignored
        if (record.translationId.hasValue()) {
            MobileFilteredErrors filter = new MobileFilteredErrors();
            filter.path.setValue(record.translationId);
            filter.appName.setValue(record.appName);
            filter.filterEnabled.setValue(true);

            MobileFilteredErrorsArray result = new MobileFilteredErrorsArray();
            handler.query(new FndQueryRecord(filter), result);

            if (result.size() > 0) {
                //Store in Ignored error table
                saveAsIgnoredTransaction(record);
                return;
            }
        }

        //Save Failed Transaction
        handler.save(record);

       //Update Mobile Failed Input & Lob Data table with request attributes
       updateTransactionData(record, requestRec);
    }

    /*
     * Update FT Nested Table with Input & lob Data Attributes
     */
    private static void updateTransactionData(final MobileFailedTransaction transaction, FndAbstractRecord requestRec) throws IfsException {
        if(requestRec!=null) {
            FndAttribute.Iterator attributes = requestRec.attributes();
            while (attributes.hasNext()) {
                FndAttribute attr = attributes.next();
                if (attr.getName().toLowerCase().contains("_temp_lob_id".toLowerCase())) { //TODO Add a column to method_params to store original param type(BLOB/CLOB) and then this should be update to validate the column type.
                    //Update Mobile Failed Lob Data table (with BLOB or CLOB id)
                    updateTransactionLobData(transaction, attr);
                } else {
                    //Exclude Binary and compound type attributes
                    if (!attr.isCompound() && attr.isSet()) {
                        //Update Mobile Failed Input Data table with request attributes
                        updateTransactionInData(transaction, attr);
                    }
                }
            }
        }
    }

    /*
     * Update FT Nested Table with Input Data Attributes
     */
    private static void updateTransactionInData(final MobileFailedTransaction transaction, FndAttribute attr) throws IfsException {
        //Call PLSQL Access to insert data
        FndRecord params = new FndRecord();
        params.add("TRANSACTION_ID", transaction.transactionId.getValue(), "IN");
        params.add("ATTR_NAME", attr.getName(), "IN");
        params.add("ATTR_VALUE", getAttrValue(attr), "IN");
        params.add("ATTR_TYPE", attr.getType().getName(), "IN");
        FndPlsqlAccess plsqlAccess = new FndPlsqlAccess();
        try {
            plsqlAccess.execute(STMT_FT_DATA_ROWS, params);
        } catch (IfsException ex2) {
            //Should not re throw this back to client.
            //If this happened then this Error cannot be re processed via the FT screen.
            //Instead BG job will use InputRecord blob to handle it
            LOG.debug("Error updateTransactionInData. ", ex2);
        }
    }

   /*
    * Update FT Nested Table with Lob Data Attributes
    */
    private static void updateTransactionLobData(final MobileFailedTransaction transaction, FndAttribute attr) throws IfsException {
        FndRecord params = new FndRecord();
        params.add("TRANSACTION_ID", transaction.transactionId.getValue(), "IN");
        params.add("LOB_ID", getAttrValue(attr), "IN");
        FndPlsqlAccess plsqlAccess = new FndPlsqlAccess();
        try {
            plsqlAccess.execute(STMT_FT_LOB_ROWS, params);
        } catch (IfsException ex2) {
            LOG.debug("Error updateTransactionLobData. ", ex2);
        }
    }

    //Store in Ignored error table
    private void saveAsIgnoredTransaction(MobileFailedTransaction failedTransaction) throws IfsException {
        FndEntityHandler handler = new FndEntityHandler() {
        };

        MobileIgnoredTransaction ignoredTransaction = new MobileIgnoredTransaction();
        ignoredTransaction.transactionId.setValue(failedTransaction.transactionId);
        ignoredTransaction.transactionDate.setValue(failedTransaction.transactionDate);
        ignoredTransaction.appName.setValue(failedTransaction.appName);
        ignoredTransaction.appVersion.setValue(failedTransaction.appVersion);
        ignoredTransaction.userId.setValue(failedTransaction.userId);
        ignoredTransaction.deviceId.setValue(failedTransaction.deviceId);
        ignoredTransaction.groupId.setValue(failedTransaction.groupId);
        ignoredTransaction.directoryId.setValue(failedTransaction.directoryId);
        ignoredTransaction.description.setValue(failedTransaction.description);
        ignoredTransaction.methodName.setValue(failedTransaction.methodName);
        ignoredTransaction.projection.setValue(failedTransaction.projection);
        ignoredTransaction.handlerName.setValue(failedTransaction.handlerName);
        ignoredTransaction.reqReplyData.setValue(failedTransaction.reqReplyData);
        ignoredTransaction.inputRecord.setValue(failedTransaction.inputRecord);
        ignoredTransaction.executionOrder.setValue(failedTransaction.executionOrder);
        ignoredTransaction.translationId.setValue(failedTransaction.translationId);
        ignoredTransaction.timeZoneAwareRequest.setValue(failedTransaction.timeZoneAwareRequest);
        handler.save(ignoredTransaction);
    }

    private static String getAttrValue(FndAttribute attr) {
        if (!attr.hasValue()) {
            return null;
        }
        String value;
        if (attr instanceof FndBinary) {
            //We do not allow modify binary type values in FT editor.
            //So we do not show the content. But we add it to the table for visibility to admin
            return null;
        } else if (attr instanceof FndDate) {
            DateFormat dateFormatter = new SimpleDateFormat(DATE_FORMAT_PATTERN, Locale.US);
            dateFormatter.setTimeZone(TimeZone.getDefault());
            value = dateFormatter.format(((FndDate) attr).getValue());
        } else if (attr instanceof FndTime) {
            DateFormat dateFormatter = new SimpleDateFormat(DATE_FORMAT_PATTERN, Locale.US);
            dateFormatter.setTimeZone(TimeZone.getDefault());
            value = dateFormatter.format(((FndTime) attr).getValue());
        } else if (attr instanceof FndTimestamp) {
            DateFormat dateFormatter = new SimpleDateFormat(DATE_FORMAT_PATTERN, Locale.US);
            dateFormatter.setTimeZone(TimeZone.getDefault());
            value = dateFormatter.format(((FndTimestamp) attr).getValue());
        } else {
            value = attr.toString();
        }
        return value;
    }
}