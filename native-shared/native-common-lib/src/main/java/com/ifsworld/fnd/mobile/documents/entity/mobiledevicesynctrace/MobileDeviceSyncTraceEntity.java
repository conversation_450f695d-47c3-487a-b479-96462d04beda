/*
 * ==================================================================================
 * File:              MobileDeviceSyncTraceEntity.java
 * Entity:            MobileDeviceSyncTrace
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobiledevicesynctrace;

import ifs.fnd.base.IfsException;
import ifs.fnd.base.SystemException;
import ifs.fnd.record.*;

public abstract class MobileDeviceSyncTraceEntity extends FndLUEntityView {


   public static final FndRecordMeta viewMeta = new FndRecordMeta("", "MOBILEDEVICESYNCTRACE", "MOBILE_DEVICE_SYNC_TRACE", "MOBILE_DEVICE_SYNC_TRACE", "MobileDeviceSyncTrace", "MOBILE_DEVICE_SYNC_TRACE_API", "MOBILE_DEVICE_SYNC_TRACE_TAB").setViewClassName("ifs.entity.mobiledevicesynctrace.MobileDeviceSyncTraceEntity").setTermPath("MobileDeviceSyncTrace.MobileDeviceSyncTrace");
   public final FndText traceId = _newTraceId((FndText) null);
   public final Reference primaryKey = new Reference(Meta.primaryKey, traceId);

   protected MobileDeviceSyncTraceEntity(FndRecordMeta meta) {
      super(meta);
      add(traceId);
      add(primaryKey);
   }

   public void assign(MobileDeviceSyncTraceEntity from) throws SystemException {
      super.assign(from);
   }

   public void transformTo(final MobileDeviceSyncTraceEntity to) throws SystemException {
      transformView(to);
   }

   public static class Reference extends FndCompoundReference {

      public Reference(FndCompoundReferenceMeta ref, FndText traceId) {
         super(ref);
         add(traceId);
      }

      public void assign(Reference ref) throws IfsException {
         protectedAssign(ref);
      }

      public boolean isEqualTo(Reference ref) {
         return protectedIsEqualTo(ref);
      }
   }

   protected static final FndText _newTraceId(FndText type) {
      return new FndText(Meta.traceId);
   }

   protected static final FndText _newDeviceId(FndText type) {
      return new FndText(Meta.deviceId);
   }

   protected static final FndText _newUserId(FndText type) {
      return new FndText(Meta.userId);
   }

   protected static final FndText _newAppName(FndText type) {
      return new FndText(Meta.appName);
   }

   protected static final FndText _newAppVersion(FndText type) {
      return new FndText(Meta.appVersion);
   }

   protected static final FndNumber _newSequenceId(FndNumber type) {
      return new FndNumber(Meta.sequenceId);
   }

   protected static final FndText _newTrace(FndText type) {
      return new FndText(Meta.trace, FndText.CLOB);
   }

   protected static final FndText _newActivity(FndText type) {
      return new FndText(Meta.activity);
   }

   protected static final FndText _newMethod(FndText type) {
      return new FndText(Meta.method);
   }

   protected static final FndText _newTraceType(FndText type) {
      return new FndText(Meta.traceType);
   }

   protected static final FndTimestamp _newCreated(FndTimestamp type) {
      return new FndTimestamp(Meta.created);
   }

   public static class Meta {

      public static final FndAttributeMeta traceId = new FndAttributeMeta(viewMeta, "TRACE_ID", "TRACE_ID", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 100).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta deviceId = new FndAttributeMeta(viewMeta, "DEVICE_ID", "DEVICE_ID", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 100);
      public static final FndAttributeMeta userId = new FndAttributeMeta(viewMeta, "USER_ID", "USER_ID", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 100);
      public static final FndAttributeMeta appName = new FndAttributeMeta(viewMeta, "APP_NAME", "APP_NAME", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 200);
      public static final FndAttributeMeta appVersion = new FndAttributeMeta(viewMeta, "APP_VERSION", "APP_VERSION", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT | FndAttributeMeta.MANDATORY, 20);
      public static final FndAttributeMeta sequenceId = new FndAttributeMeta(viewMeta, "SEQUENCE_ID", "SEQUENCE_ID", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta trace = new FndAttributeMeta(viewMeta, "TRACE", "TRACE", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT);
      public static final FndAttributeMeta activity = new FndAttributeMeta(viewMeta, "ACTIVITY", "ACTIVITY", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 100);
      public static final FndAttributeMeta method = new FndAttributeMeta(viewMeta, "METHOD", "METHOD", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 100);
      public static final FndAttributeMeta traceType = new FndAttributeMeta(viewMeta, "TRACE_TYPE", "TRACE_TYPE", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 50);
      public static final FndAttributeMeta created = new FndAttributeMeta(viewMeta, "CREATED", "CREATED", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndCompoundReferenceMeta primaryKey = new FndCompoundReferenceMeta(viewMeta, "ABSTRACT_MOBILE_DEVICE_SYNC_TRACE_KEY", new FndAttributeMeta[]{traceId}, viewMeta, true);
   }
}
