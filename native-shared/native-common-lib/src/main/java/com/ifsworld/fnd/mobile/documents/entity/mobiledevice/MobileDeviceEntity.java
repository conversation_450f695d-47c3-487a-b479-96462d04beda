/*
 * ==================================================================================
 * File:              MobileDeviceEntity.java
 * Entity:            MobileDevice
 * Template Version:  9.0
 * Generator Version: @GeneratorVersion@
 *
 * DO NOT EDIT this file. It was generated and will be overwritten at next generation
 *
 * ==================================================================================
 */
package com.ifsworld.fnd.mobile.documents.entity.mobiledevice;

import com.ifsworld.fnd.mobile.documents.enumeration.MobileDeviceStateEnumeration;
import ifs.fnd.base.IfsException;
import ifs.fnd.base.SystemException;
import ifs.fnd.record.*;

public abstract class MobileDeviceEntity extends FndLUEntityView {


   public static final FndRecordMeta viewMeta = new FndRecordMeta("", "MOBILEDEVICE", "MOBILE_DEVICE", "MOBILE_DEVICE", "MobileDevice", "MOBILE_DEVICE_API", "MOBILE_DEVICE_TAB").setViewClassName("ifs.entity.mobiledevice.MobileDeviceEntity").setTermPath("MobileDevice.MobileDevice");
   public final FndText deviceId = _newDeviceId((FndText) null);
   public final Reference primaryKey = new Reference(Meta.primaryKey, deviceId);

   protected MobileDeviceEntity(FndRecordMeta meta) {
      super(meta);
      add(deviceId);
      add(primaryKey);
   }

   public void assign(MobileDeviceEntity from) throws SystemException {
      super.assign(from);
   }

   public void transformTo(final MobileDeviceEntity to) throws SystemException {
      transformView(to);
   }

   public static class Reference extends FndCompoundReference {

      public Reference(FndCompoundReferenceMeta ref, FndText deviceId) {
         super(ref);
         add(deviceId);
      }

      public void assign(Reference ref) throws IfsException {
         protectedAssign(ref);
      }

      public boolean isEqualTo(Reference ref) {
         return protectedIsEqualTo(ref);
      }
   }

   protected static final FndText _newDeviceId(FndText type) {
      return new FndText(Meta.deviceId);
   }

   protected static final FndText _newIdentifier(FndText type) {
      return new FndText(Meta.identifier);
   }

   protected static final MobileDeviceStateEnumeration _newDeviceState(MobileDeviceStateEnumeration type) {
      return new MobileDeviceStateEnumeration(Meta.deviceState);
   }

   protected static final FndText _newDeviceState(FndText type) {
      return new FndText(Meta.deviceState$);
   }

   protected static final FndText _newPlatform(FndText type) {
      return new FndText(Meta.platform);
   }

   protected static final FndText _newDescription(FndText type) {
      return new FndText(Meta.description);
   }

   protected static final FndText _newOs(FndText type) {
      return new FndText(Meta.os);
   }

   public static class Meta {

      public static final FndAttributeMeta deviceId = new FndAttributeMeta(viewMeta, "DEVICE_ID", "DEVICE_ID", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 100).unsetFlag(FndAttributeMeta.UPDATE_ALLOWED);
      public static final FndAttributeMeta identifier = new FndAttributeMeta(viewMeta, "IDENTIFIER", "IDENTIFIER", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 100);
      public static final FndAttributeMeta deviceState = new FndAttributeMeta(viewMeta, "DEVICE_STATE", "DEVICE_STATE_DB", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT).setIidPlsqlPackage("MOBILE_DEVICE_STATE_API");
      public static final FndAttributeMeta deviceState$ = new FndAttributeMeta(viewMeta, "DEVICE_STATE", "DEVICE_STATE", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT).setIidPlsqlPackage("MOBILE_DEVICE_STATE_API");
      public static final FndAttributeMeta platform = new FndAttributeMeta(viewMeta, "PLATFORM", "PLATFORM", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 100);
      public static final FndAttributeMeta description = new FndAttributeMeta(viewMeta, "DESCRIPTION", "DESCRIPTION", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 500);
      public static final FndAttributeMeta os = new FndAttributeMeta(viewMeta, "OS", "OS", FndAttributeMeta.QUERYABLE | FndAttributeMeta.PERSISTENT, 100);
      public static final FndCompoundReferenceMeta primaryKey = new FndCompoundReferenceMeta(viewMeta, "ABSTRACT_MOBILE_DEVICE_KEY", new FndAttributeMeta[]{deviceId}, viewMeta, true);
   }
}
