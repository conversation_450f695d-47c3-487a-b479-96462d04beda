/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.ifsworld.fnd.mobile.cache;

import com.ifsworld.fnd.mobile.documents.MobileApplicationVersion;
import com.ifsworld.fnd.mobile.documents.MobileApplicationVersionArray;
import com.ifsworld.fnd.mobile.documents.MobilePushCommand;
import com.ifsworld.fnd.mobile.documents.MobilePushCommandArray;
import com.ifsworld.fnd.mobile.documents.MobileSyncRule;
import com.ifsworld.fnd.mobile.documents.MobileSyncRuleArray;
import com.ifsworld.fnd.mobile.documents.MobilePushMsgText;
import com.ifsworld.fnd.mobile.documents.MobilePushMsgTextArray;
import static com.ifsworld.fnd.mobile.cache.FndmobMetaDataCache.formatTwoPartKey;

import com.ifsworld.fnd.mobile.documents.enumeration.SyncRuleTypeEnumeration;
import com.ifsworld.fnd.mobile.utility.MobileUtilities;
import ifs.fnd.base.ApplicationException;
import ifs.fnd.base.IfsException;
import ifs.fnd.record.FndQueryRecord;
import ifs.fnd.sf.FndServerContext;
import ifs.fnd.sf.storage.FndEntityHandler;
import java.util.*;
import ifs.fnd.record.FndText;
import org.apache.logging.log4j.LogManager;

/**
 *
 * <AUTHOR>
 */
public class FndmobSyncRuleCache {

   private static final org.apache.logging.log4j.Logger LOG = LogManager.getLogger(FndmobSyncRuleCache.class);
   private static final Map<String, SyncRuleMeta> syncRuleCache = new HashMap<>();

   public static class SyncRuleMeta {

      private final String appName;
      private final String appVersion;
      private final Date syncRuleCacheVersion;
      private final Map<String, SyncRuleMetaItem> items;
      private Date cacheVersion;

      public long lastUpdateTimestamp = 0;
      boolean cacheNeedsRefreshing = true;
      boolean checkForChange = false;

      public SyncRuleMeta(MobileApplicationVersion applicationVersion, Map<String, SyncRuleMetaItem> items) {
         this.syncRuleCacheVersion = applicationVersion.syncRuleCacheVersion.getValue();
         this.appName = applicationVersion.appName.getValue();
         this.appVersion = applicationVersion.appVersion.getValue();
         this.items = items;
      }

      public String appName() {
         return appName;
      }

      public String appVersion() {
         return appVersion;
      }

      public Date syncRuleCacheVersion() {
         return syncRuleCacheVersion;
      }

      public Map<String, SyncRuleMetaItem> getRulesMeta() {
         return items;
      }

      public Map<String, MobileSyncRule> getRules() {
         Map<String, MobileSyncRule> result = new HashMap<>();
         for (SyncRuleMetaItem meta : items.values()) {
            result.put(meta.rule.entity.getValue(), meta.rule);
         }
         return result;
      }

      public List<MobilePushMsgText> getPushMessages(String entity) throws IfsException {
         String lng = FndServerContext.getCurrentLanguage();
         List<MobilePushMsgText> resultMessages = new ArrayList<>();
         if (items.keySet().contains(entity)) {
            SyncRuleMetaItem meta = items.get(entity);
            if (meta != null && meta.messages != null && meta.messages.size() > 0) {
               fetchMessages(meta, resultMessages, lng);
            }
         }
         return resultMessages;
      }

      public List<MobilePushCommand> getPushCommands(String entity) throws IfsException {
         List<MobilePushCommand> resultCommands = new ArrayList<>();
         if (items.keySet().contains(entity)) {
            SyncRuleMetaItem meta = items.get(entity);
            if (meta != null && meta.commands != null && meta.commands.size() > 0) {
               for (int i = 0; i < meta.commands.size(); i++) {
                  MobilePushCommand command = meta.commands.get(i);
                  resultCommands.add(command);
               }
            }
         }
         return resultCommands;
      }

      private void fetchMessages(SyncRuleMetaItem meta, List<MobilePushMsgText> resultMessages, String lng) throws IfsException {
         MobilePushMsgTextArray newMessages = new MobilePushMsgTextArray();
         MobilePushMsgTextArray baseMessages = fetchBaseMessages(meta);
         for (int i = 0; i < baseMessages.size(); i++) {
            MobilePushMsgText baseMessage = baseMessages.get(i);
            //Find the translation in the messages
            boolean translationFound = false;
            for (int j = 0; j < meta.messages.size(); j++) {
               MobilePushMsgText translatedMessage = meta.messages.get(j);
               if (translatedMessage.language.getValue("").equals(lng)) {
                  resultMessages.add(translatedMessage);
                  translationFound = true;
               }
            }
            if (!translationFound) {
               //Lookup the translation and store (first time)
               try {
                  MobilePushMsgText lookupMessage = (MobilePushMsgText) baseMessage.clone();
                  lookupMessage.language.setValue(lng);
                  //Lookup for translation and store value.
                  FndText messageText = new FndText("TYPE", null);
                  StringBuffer path = new StringBuffer()
                          .append(appName).append(".")
                          .append(meta.rule.entity.getValue()).append(".")
                          .append(lookupMessage.messageType.getValue().getDatabaseValue());
                  CacheUtilities.languageLookup(
                          messageText, new FndText("TYPE", "Messages"),
                          new FndText("Path", path.toString()),
                          new FndText("Attribute", "Text"),
                          new FndText("Language", lng),
                          new FndText("MainType", "MOBILE"));
                  //Store value if it's not empty. otherwise keep the base text
                  if (messageText.hasValue()) {
                     lookupMessage.messageText.setValue(messageText);
                  }
                  //Store message in the cache
                  newMessages.add(lookupMessage);
                  resultMessages.add(lookupMessage);
               } catch (CloneNotSupportedException ex) {
                  LOG.error("fetchMessages", ex);
                  //Should we store copied version of message to stop calling Lookup again?
               }
            }
         }

         //Update the cache
         if (newMessages.size() > 0) {
            meta.messages.add(newMessages);
         }
      }

      private MobilePushMsgTextArray fetchBaseMessages(SyncRuleMetaItem meta) throws IfsException {
         MobilePushMsgTextArray baseMessages = new MobilePushMsgTextArray();
         for (int i = 0; i < meta.messages.size(); i++) {
            MobilePushMsgText translatedMessage = meta.messages.get(i);
            if (translatedMessage.language.getValue("*").equals("*")) {
               baseMessages.add(translatedMessage);
            }
         }
         return baseMessages;
      }

      public MobileSyncRule getRule(String entity) throws ApplicationException {
         if (items.keySet().contains(entity)) {
            SyncRuleMetaItem meta = items.get(entity);
            return meta != null ? meta.rule : null;
         } else {
            return null;
         }
      }
   }

   public static class SyncRuleMetaItem {

      public MobileSyncRule rule = new MobileSyncRule();
      public MobilePushMsgTextArray messages = new MobilePushMsgTextArray();
      public MobilePushCommandArray commands = new MobilePushCommandArray();
   }

   static public MobileSyncRule get(String appName, String appVersion, String entity) throws ApplicationException {
      String key = formatKey(appName, appVersion);
      SyncRuleMeta ruleMeta = syncRuleCache.get(key);
      if (ruleMeta == null || cacheNeedsRefreshing(appName, appVersion)) {
         refreshCache(appName, appVersion);
      }
      ruleMeta = syncRuleCache.get(key);
      if (ruleMeta != null) {
         return ruleMeta.getRule(entity);
      } else {
         raiseAppNotFoundException(appName, appVersion);
         return null;
      }
   }

   static public Collection<MobileSyncRule> get(String appName, String appVersion) throws ApplicationException {
      String key = formatKey(appName, appVersion);
      SyncRuleMeta ruleMeta = syncRuleCache.get(key);
      if (ruleMeta == null || cacheNeedsRefreshing(appName, appVersion)) {
         refreshCache(appName, appVersion);
      }
      ruleMeta = syncRuleCache.get(key);
      if (ruleMeta != null) {
         Map<String, MobileSyncRule> rules = ruleMeta.getRules();
         if (rules != null) {
            return rules.values();
         } else {
            return null;
         }
      } else {
         raiseAppNotFoundException(appName, appVersion);
         return null;
      }
   }

   public static List<MobilePushMsgText> getPushMessages(String appName, String appVersion, String entity) throws IfsException {
      String key = formatKey(appName, appVersion);
      SyncRuleMeta ruleMeta = syncRuleCache.get(key);
      if (ruleMeta == null || cacheNeedsRefreshing(appName, appVersion)) {
         refreshCache(appName, appVersion);
      }
      ruleMeta = syncRuleCache.get(key);
      if (ruleMeta != null) {
         return ruleMeta.getPushMessages(entity);
      } else {
         raiseAppNotFoundException(appName, appVersion);
         return null;
      }

   }

   public static List<MobilePushCommand> getPushCommands(String appName, String appVersion, String entity) throws IfsException {
      String key = formatKey(appName, appVersion);
      SyncRuleMeta ruleMeta = syncRuleCache.get(key);
      if (ruleMeta == null || cacheNeedsRefreshing(appName, appVersion)) {
         refreshCache(appName, appVersion);
      }
      ruleMeta = syncRuleCache.get(key);
      if (ruleMeta != null) {
         return ruleMeta.getPushCommands(entity);
      } else {
         raiseAppNotFoundException(appName, appVersion);
         return null;
      }

   }

   static class SyncRuleComparator implements Comparator<MobileSyncRule> {

      @Override
      public int compare(MobileSyncRule o1, MobileSyncRule o2) {
         return o1.ordinal.compareTo(o2.ordinal);
      }

   }

   //Should we store those grouped sync rules in the cache for performance reason?
   //return sorted list in order to control sync order of entities
   static public List<MobileSyncRule> get(String appName, String appVersion, SyncRuleTypeEnumeration.Enum type) throws ApplicationException {
      String key = formatKey(appName, appVersion);
      SyncRuleMeta ruleMeta = syncRuleCache.get(key);
      if (ruleMeta == null || cacheNeedsRefreshing(appName, appVersion)) {
         refreshCache(appName, appVersion);
      }
      ruleMeta = syncRuleCache.get(key);
      if (ruleMeta != null && ruleMeta.getRules() != null) {
         List<MobileSyncRule> returnRules = new ArrayList<>();
         for (MobileSyncRule rule : ruleMeta.getRules().values()) {
            if (rule.ruleType.getValue() == type) {
               returnRules.add(rule);
            }
         }
         returnRules.sort(new SyncRuleComparator());
         return returnRules;
      } else {
         raiseAppNotFoundException(appName, appVersion);
         return null;
      }
   }

//   //Should we store those grouped sync rules in the cache for performance reason?
//   //return sorted list in order to control sync order of entities
//   static public List<MobileSyncRule> get(String appName, String appVersion, List<String> deliveryMethods) throws ApplicationException {
//      String key = formatKey(appName, appVersion);
//      SyncRuleMeta ruleMeta = syncRuleCache.get(key);
//      if (ruleMeta == null || cacheNeedsRefreshing(appName, appVersion)) {
//         refreshCache(appName, appVersion);
//      }
//      ruleMeta = syncRuleCache.get(key);
//      if (ruleMeta != null && ruleMeta.getRules() != null) {
//         List<MobileSyncRule> returnRules = new ArrayList<>();
//         for (MobileSyncRule rule : ruleMeta.getRules().values()) {
//            if (deliveryMethods.contains(rule.effectiveDeliveryMethod.getValue())){
//               returnRules.add(rule);
//            }
//         }
//         returnRules.sort(new SyncRuleComparator());
//         return returnRules;
//      } else {
//         raiseAppNotFoundException(appName, appVersion);
//         return null;
//      }
//   }

   private static void raiseAppNotFoundException(String appName, String appVersion) throws ApplicationException {
      throw new ApplicationException("Application not found. Name=&1, Version=&2", appName, appVersion);
   }

   static synchronized void refreshCache(String appName, String appVersion) {
      if (!cacheNeedsRefreshing(appName, appVersion)) { //another thread has already updated the cache
         return;
      }
      try {
         String appKey = formatTwoPartKey(appName, appVersion);
         FndEntityHandler handler = new FndEntityHandler() {
         };

         MobileApplicationVersionArray appResult = new MobileApplicationVersionArray();
         MobileApplicationVersion appCondition = new MobileApplicationVersion();
         appCondition.excludeQueryResults();
         appCondition.appName.setValue(appName);
         appCondition.appVersion.setValue(appVersion);
         appCondition.objVersion.include();
         appCondition.syncRuleCacheVersion.include();
         appCondition.appName.include();
         appCondition.appVersion.include();
         handler.query(new FndQueryRecord(appCondition), appResult);

         if (appResult.size() == 0) {
            if (syncRuleCache.containsKey(appKey)) {
               syncRuleCache.remove(appKey);
            }
            return;
         }

         MobileSyncRuleArray result = new MobileSyncRuleArray();
         MobileSyncRule condition = new MobileSyncRule();
         FndQueryRecord query = new FndQueryRecord(condition);
         condition.appName.setValue(appName);
         condition.appVersion.setValue(appVersion);
         query.orderBy.setValue("APP_NAME, APP_VERSION, ORDINAL");
         handler.query(query, result);
         Map<String, SyncRuleMetaItem> items = new Hashtable<>();
         for (int i = 0; i < result.size(); i++) {
            SyncRuleMetaItem ruleItem = new SyncRuleMetaItem();
            //Sync Rule
            ruleItem.rule = result.get(i);
            //Push Messages
            MobilePushMsgTextArray messages = new MobilePushMsgTextArray();
            MobilePushMsgText msgCon = new MobilePushMsgText();
            FndQueryRecord queryMsg = new FndQueryRecord(msgCon);
            msgCon.appName.setValue(appName);
            msgCon.appVersion.setValue(appVersion);
            msgCon.entity.setValue(ruleItem.rule.entity);
            handler.query(queryMsg, messages);
            for (int k = 0; k < messages.size(); k++) {
               messages.get(k).language.setValue("*"); // Prog Lang
            }
            ruleItem.messages = messages;

            MobilePushCommandArray commands = new MobilePushCommandArray();
            MobilePushCommand commandCond = new MobilePushCommand();
            FndQueryRecord queryCommand = new FndQueryRecord(commandCond);

            commandCond.appName.setValue(appName);
            commandCond.appVersion.setValue(appVersion);
            commandCond.entity.setValue(ruleItem.rule.entity);
            handler.query(queryCommand, commands);
            ruleItem.commands = commands;

            //Add Rule Meta Item
            items.put(ruleItem.rule.entity.getValue(), ruleItem);
         }
         MobileApplicationVersion mav = appResult.firstElement();
         SyncRuleMeta ruleMeta = new SyncRuleMeta(mav, items);
         ruleMeta.cacheNeedsRefreshing = false;
         ruleMeta.lastUpdateTimestamp = System.currentTimeMillis();
         ruleMeta.cacheVersion = mav.syncRuleCacheVersion.hasValue()
                 ? MobileUtilities.getFormattedDateObj(mav.syncRuleCacheVersion.getValue()) : null;
         syncRuleCache.put(formatKey(appName, appVersion), ruleMeta);
      } catch (IfsException e) {
         //for some reason we couldn't connect to the database or a parsing exception occurred, ignore the exception and retry the next time the server is called
         LOG.debug("Error updating mobile sync rules cache. " + e);
      }
   }

   private static boolean cacheNeedsRefreshing(String appName, String appVersion) {
      SyncRuleMeta ruleMeta = syncRuleCache.get(formatKey(appName, appVersion));
      if (ruleMeta == null) {
         return true;
      }
      if (ruleMeta.checkForChange) {
         try {
            MobileApplicationVersion condition = new MobileApplicationVersion();
            condition.excludeQueryResults();
            condition.appName.setValue(appName);
            condition.appVersion.setValue(appVersion);
            condition.syncRuleCacheVersion.include();
            FndEntityHandler handler = new FndEntityHandler() {
            };
            MobileApplicationVersionArray result = new MobileApplicationVersionArray();
            handler.query(new FndQueryRecord(condition), result);
            for (int i = 0; i < result.size(); i++) {
               MobileApplicationVersion record = result.get(i);
               if (!record.syncRuleCacheVersion.getValue(new Date(0)).equals(ruleMeta.syncRuleCacheVersion())) {
                  ruleMeta.cacheNeedsRefreshing = true;
               }
            }
            ruleMeta.checkForChange = false;
         } catch (IfsException e) {
            //shouldn't happen, but in case it does invalidate cache
            ruleMeta.cacheNeedsRefreshing = true;
            ruleMeta.checkForChange = false;
         }
      }
      return ruleMeta.cacheNeedsRefreshing;
   }

   public static String formatKey(String appName, String appVersion) {
      StringBuilder key = new StringBuilder(256);
      return key.append(appName).append("^").append(appVersion).append("^").toString();
   }

   /**
    *
    * @param appName
    * @param appVersion
    * @param cacheVersion
    */
   public static void clearCache(String appName, String appVersion, Date cacheVersion) {
      LOG.debug("Clearing fndmob sync rules cache");

      //Clear cache by validating cache version
      SyncRuleMeta syncRuleMeta = syncRuleCache.get(formatKey(appName, appVersion));

      if (syncRuleMeta != null && (syncRuleMeta.cacheVersion == null || syncRuleMeta.cacheVersion.compareTo(cacheVersion) != 0)) {
         syncRuleMeta.checkForChange = true;
         syncRuleMeta.cacheVersion = cacheVersion;
      }
   }
}
